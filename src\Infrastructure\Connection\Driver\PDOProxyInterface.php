<?php

declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\Driver;

/**
 * Interface PDOProxy
 *
 * @method StatementProxy|false prepare(string $statement, array $driver_options = [])
 * @method bool                 beginTransaction()
 * @method bool                 commit()
 * @method bool                 rollBack()
 */
interface PDOProxyInterface
{
    /**
     * @param int    $count    Number of items
     * @param string $prefix   Param prefix
     * @param string $dataType Data type
     *
     * @return string
     */
    public function paramsForList(int $count, string $prefix = '?', string $dataType): string;
}
