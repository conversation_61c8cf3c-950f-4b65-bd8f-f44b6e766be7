import React, {useState} from 'react';
import Translation from '@wayfair/translation';
import PropTypes from 'prop-types';
import {Button, Block, Checkbox, Dropdown, IconV2 as Icon, Column, Grid, TextInput} from '@wayfair/homebase-extranet';
import './qa_bulk_action_section.scss';
import CurationToolShapes from './curation_tool_shapes';
import {faLevelUpAlt, faThumbtack} from '@fortawesome/free-solid-svg-icons';

const QABulkActionSection = ({
  rejectionReasons,
  handleApprove,
  handleReject,
  handleSelectAll,
  selectedRows,
  checkedAll,
  checkedIndeterminate,
  isAutomaticCurationPostQaEnabled,
}) => {
  const [rejectionReason, setRejectionReason] = useState(null);
  const [rejectionNotes, setRejectionNotes] = useState('');
  const [stickyScroll, setStickyScroll] = useState(false);

  const isRejectButtonDisabled = () => {
    return (
      selectedRows.length === 0 ||
      rejectionReason === null ||
      rejectionNotes === '' ||
      rejectionNotes === null
    );
  };

  const isApproveButtonDisabled = () => {
    return selectedRows.length === 0;
  };

  const hasStickyScroll = () => {
    if (stickyScroll) {
      return 'x-StickyHeader ex-StickyHeader--css-sticky is-sticking';
    }

    return '';
  };

  return (
    <div className={`QABulkActionHeader ${hasStickyScroll()}`}>
      <Block>
        <Grid>
          <Column size={2}>
            <div className="bulkActionsContainer">
              <span className="bulkActions">
                <Translation msgid="CurationTool.QABulkActionTitle" />

                <Icon
                  onClick={() => setStickyScroll(!stickyScroll)}
                  icon={stickyScroll ? faLevelUpAlt : faThumbtack}
                />
              </span>

              <Checkbox
                label={
                  <Translation msgid="CurationTool.QABulkActionSelectAllCheckbox" />
                }
                onChange={e => handleSelectAll(e.target.checked)}
                checked={checkedAll}
                indeterminate={checkedIndeterminate}
              />
            </div>
          </Column>

          <Column size={10}>
            <div className="QABulkActionFlex">
              <div className="QABulkActionRejectContainer">
                <Button
                  onClick={handleApprove}
                  disabled={isApproveButtonDisabled()}
                >
                  <Translation msgid="CurationTool.QABulkActionApproveButton" />
                </Button>
              </div>
            </div>
          </Column>
        </Grid>
      </Block>
    </div>
  );
};

QABulkActionSection.propTypes = {
  rejectionReasons: PropTypes.arrayOf(CurationToolShapes.reasonShape)
    .isRequired,
  handleApprove: PropTypes.func.isRequired,
  handleReject: PropTypes.func.isRequired,
  handleSelectAll: PropTypes.func.isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  checkedAll: PropTypes.bool.isRequired,
  checkedIndeterminate: PropTypes.bool.isRequired,
  isAutomaticCurationPostQaEnabled: PropTypes.bool,
};

QABulkActionSection.defaultProps = {
  isAutomaticCurationPostQaEnabled: false,
};

export default QABulkActionSection;
