<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision;

class Assortment_Curation_Decision {
  /**
   * @var string
   */
  private $sku;

  /**
   * @var bool
   */
  private $should_move_to_header_brand;

  /**
   * @var bool
   */
  private $should_move_to_tail_brand;

  /**
   * @var Assortment_Curation_Move_To_Brand_Decision_Data|null
   */
  private $move_to_brand_decision_data;

  /**
   * @param string                                               $sku                         sku
   * @param bool                                                 $should_move_to_header_brand if should move to a header brand
   * @param bool                                                 $should_move_to_tail_brand   if should move to a tail brand
   * @param Assortment_Curation_Move_To_Brand_Decision_Data|null $move_to_brand_decision_data move dection data
   */
  public function __construct(
      string $sku,
      bool $should_move_to_header_brand,
      bool $should_move_to_tail_brand,
      Assortment_Curation_Move_To_Brand_Decision_Data $move_to_brand_decision_data = null
  ) {
    $this->sku                         = $sku;
    $this->should_move_to_header_brand = $should_move_to_header_brand;
    $this->should_move_to_tail_brand   = $should_move_to_tail_brand;
    $this->move_to_brand_decision_data = $move_to_brand_decision_data;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return bool
   */
  public function should_move_to_header_brand() : bool {
    return $this->should_move_to_header_brand;
  }

  /**
   * @return bool
   */
  public function should_move_to_tail_brand() : bool {
    return $this->should_move_to_tail_brand;
  }

  /**
   * @return Assortment_Curation_Move_To_Brand_Decision_Data|null
   */
  public function get_move_to_brand_decision_data() : ?Assortment_Curation_Move_To_Brand_Decision_Data {
    return $this->move_to_brand_decision_data;
  }
}
