<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Product_Context_Collection;

use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\ApiProductContextCollectionNextgen;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Environment;

class ProductContextCollectionNextgenApiClient implements ApiProductContextCollectionNextgen {

  private const DEV_URL  = 'kube-product-context-collection-api-nextgen.service.intradsm1.sdeconsul.csnzoo.com';
  private const PROD_URL = 'kube-product-context-collection-api-nextgen.service.intraiad1.consul.csnzoo.com';

  /**
   * @var string
   */
  private $baseUrl;

  /**
   * @var LoggerInterface
   */
  private $loggerObj;

  /**
   * Product_Context_Collection_Api_Client constructor.
   *
   * @param LoggerInterface $loggerObj      Logger
   * @param string $environment Environment
   */
  public function __construct(LoggerInterface $loggerObj, string $environment) {
    $this->baseUrl = $environment === Environment::DEVELOPMENT ? self::DEV_URL : self::PROD_URL;
    $this->loggerObj   = $loggerObj;
  }

  /**
   * @param string ...$skus SKUs
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function findProductContextCollection(string ...$skus) : array {
    return $this->makeRequest('/find', ['skus' => $skus]);
  }

  /**
   * @param string[] $skus list of SKUS to get collisions
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function findSkuCollisions(array $skus) : array {
    return $this->makeRequest('/find-collisions', ['skus' => $skus]);
  }

  /**
   * @param string     $path    path
   * @param array|null $payload payload
   *
   * @return array
   * @throws API_Request_Exception
   */
  private function makeRequest(string $path, ?array $payload = null) : array {
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
    ];

    $curlResource = curl_init();

    if ($curlResource === false) { /** @phpstan-ignore-line */
      $this->loggerObj->error('Failed curl initialization');
      throw new API_Request_Exception('Failed curl initialization');
    }

    $endpoint = $this->baseUrl . $path;
    curl_setopt($curlResource, CURLOPT_URL, $endpoint);
    curl_setopt($curlResource, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curlResource, CURLOPT_HEADER, 0);
    curl_setopt($curlResource, CURLOPT_RETURNTRANSFER, 1);

    $json_payload = null;
    if ($payload !== null) {
      $json_payload = \json_encode($payload);
      curl_setopt($curlResource, CURLOPT_POSTFIELDS, $json_payload);
    }

    $this->loggerObj->info(sprintf('GET "%s"', $endpoint));
    if (!empty($json_payload)) {
      $this->loggerObj->info(sprintf('BODY: %s', $json_payload));
    }
    $response = curl_exec($curlResource);

    if (curl_errno($curlResource) > 0) {
      throw new \Exception('CURL encountered an error:  ' . curl_error($curlResource));
    }

    $this->loggerObj->info(sprintf('Nextgen api RESPONSE: %s', $response));
    curl_close($curlResource);

    return $this->decodeResponse($response);
  }

  /**
   * @param string $response response body decode
   *
   * @return array
   * @throws API_Request_Exception
   */
  private function decodeResponse(string $response) : array {
    $decodeResponse = \json_decode($response, true);
    if (is_null($decodeResponse)) {
      $this->loggerObj->error('Response data is an invalid JSON: ' . $response);
      throw new \Exception('Response data is an invalid JSON: ' . $response);
    }

    if (isset($decodeResponse['code'])) {
      $errorMesage = sprintf("Response error: %d - %s", $decodeResponse['code'], $decodeResponse['message']);
      $this->loggerObj->error($errorMesage);
      throw new \Exception($errorMesage);
    }

    return $decodeResponse;
  }
}
