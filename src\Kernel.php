<?php
/**
 * Kernel is the main class that glues all application parts together.
 */

namespace App;

use Psr\Log\LoggerAwareInterface;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use function class_exists;
use function class_implements;
use function dirname;
use function in_array;
use function mb_strpos;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    public function getProjectDir(): string
    {
        return dirname(__DIR__);
    }

    protected function build(ContainerBuilder $container): void
    {
        $container->addCompilerPass(
            new class() implements CompilerPassInterface {
                /**
                 * Modify the container before it is dumped to PHP code.
                 *
                 * @param ContainerBuilder $container
                 * @return void
                 */
                public function process(ContainerBuilder $container): void
                {
                    if ($container->has('Psr\Log\LoggerInterface')) {
                        foreach ($container->getDefinitions() as $definition) {
                            $definitionClass = $definition->getClass();

                            if ($definitionClass !== null
                                && (
                                    mb_strpos($definitionClass, 'App\\')
                                    || mb_strpos(
                                        $definitionClass,
                                        'WF\\'
                                    )
                                )
                                && $definition->isAbstract() === false
                                && $definition->hasMethodCall('setLogger') === false
                                && class_exists($definitionClass)
                                && in_array(LoggerAwareInterface::class, class_implements($definitionClass), true)
                            ) {
                                $definition->addMethodCall(
                                    'setLogger',
                                    [new Reference('Psr\Log\LoggerInterface')]
                                );
                            }
                        }
                    }
                }
            }
        );
    }
}
