<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use Psr\Log\LoggerInterface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_Postgresql_DAO;
use WF\Shared\Traits\Logging_Trait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Context_Data_Service {
  use Logging_Trait;

  public const CURATED_MAX_DAYS_AGO = 30;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory
   */
  private $factory;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_Postgresql_DAO
   */
  private $postgresql_dao;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Manufacturer_Service
   */
  private $context_data_manufacturer_service;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service
   */
  private $context_data_decision_service;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory              $factory                           the data factory
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_DAO                                  $dao                               the DAO
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_Postgresql_DAO                       $postgresql_dao                    the postgresql_dao
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Manufacturer_Service $context_data_manufacturer_service Context Data Manufacturer Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service     $context_data_decision_service     Context Data Decision Service
   * @param LoggerInterface|null                                                                        $logger                            Logger
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                                    $featureToggles                   Feature toggle
   */
  public function __construct(
      Context_Data_Factory $factory,
      Context_Data_DAO $dao,
      Context_Data_Postgresql_DAO $postgresql_dao,
      Context_Data_Manufacturer_Service $context_data_manufacturer_service,
      Context_Data_Decision_Service $context_data_decision_service,
      FeatureTogglesInterface $featureToggles,
      ?LoggerInterface $logger = null
  ) {
    $this->factory                           = $factory;
    $this->dao                               = $dao;
    $this->postgresql_dao                    = $postgresql_dao;
    $this->context_data_manufacturer_service = $context_data_manufacturer_service;
    $this->context_data_decision_service     = $context_data_decision_service;
    $this->logger                            = $logger;
    $this->featureToggles                    = $featureToggles;
  }

  /**
   * @param int   $batch_id Batch ID
   * @param array $skus     the SKUs to look a context data for
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Collection
   */
  public function context_data_for_skus(int $batch_id, array $skus) : Context_Data_Collection {
    $this->log_info('Loading context data for SKUs', ['batch_id' => $batch_id, 'skus' => $skus]);

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      $context_skus = $this->postgresql_dao->get_context_skus_manufacturer_data($batch_id, $skus, self::CURATED_MAX_DAYS_AGO);
    } else {
      $context_skus = $this->dao->get_context_skus_manufacturer_data($batch_id, $skus, self::CURATED_MAX_DAYS_AGO);
    }

    $result = $this->factory->create_collection();

    foreach ($context_skus as $sku => $row) {
      $final_ma_id = $this->context_data_manufacturer_service->get_final_manufacturer_id($row['manufacturer_id'], $row['ma_brw_id'], $row['source_id'], $row['recently_curated_manufacturer_id']);

      if ($final_ma_id !== null) {
        $this->log_info('Looking for closes match', ['batch_id' => $batch_id]);
        $match = $this->context_data_decision_service->find_closest_match($batch_id, $final_ma_id, $row['price_tier']);

        $result->set_for_sku($sku, $match);
      }
    }

    return $result;
  }
}
