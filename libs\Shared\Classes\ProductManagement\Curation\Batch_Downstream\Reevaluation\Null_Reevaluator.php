<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

class Null_Reevaluator implements Reevaluator_Interface {
  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch that should be reevaluated
   *
   * @return void
   */
  public function reevaluate(Batch $batch) {

  }
}
