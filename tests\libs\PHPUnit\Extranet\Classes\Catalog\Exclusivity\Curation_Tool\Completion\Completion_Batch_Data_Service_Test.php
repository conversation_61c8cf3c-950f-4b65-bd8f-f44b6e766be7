<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
//use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;

class Completion_Batch_Data_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Completion_Batch_Data_Storage
     */
    private $dao;

    /**
     * @var Batch_Management_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    private int $batch_id = 1;
    private array $skus = ['sku1', 'sku2'];
    private array $batch_data = [
        'AssignedEmID' => 1,
        'StatusID' => 1,
        'BrandCatalogID' => 1,
        'CurationBatchProcessTypeID' => 1,
        'CreatedAt' => null,
        'ApprovedBy' => 1,
        'ApprovedAt' => null
    ];
    private array $type_data = [
        1 => 'Process Type - 1',
        2 => 'Process Type - 2',
        3 => 'Process Type - 3'
    ];

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Completion_Batch_Data_Storage::class);
        $this->dao_psql = $this->prophesize(Batch_Management_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Completion_Batch_Data_Service(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_get_qa_batch_skus_with_exclude_reason_feature_toggle_off(): void
    {
        $actual_result = new Curation_Automation_Unwhitelabel($this->batch_id, $this->skus, true);

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->get_qa_batch_skus_with_exclude_reason($this->batch_id)->willReturn($this->skus);

        $result = $this->subject->get_qa_batch_skus_with_exclude_reason($this->batch_id);

        $this->assertEquals($result, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_get_qa_batch_skus_with_exclude_reason_feature_toggle_on(): void
    {
        $actual_result = new Curation_Automation_Unwhitelabel($this->batch_id, $this->skus, true);

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->get_qa_batch_skus_with_exclude_reason($this->batch_id)->willReturn($this->skus);

        $result = $this->subject->get_qa_batch_skus_with_exclude_reason($this->batch_id);

        $this->assertEquals($result, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_get_feature_toggle_off(): void
    {
        $actual_result = new Completion_Batch_Data(
            $this->batch_id,
            1,
            1,
            1,
            1,
            'Process Type - 1',
            null,
            1,
            null
        );

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->get_batch_data($this->batch_id)->willReturn($this->batch_data);

        $this->dao->get_process_types()->willReturn($this->type_data);

        $result = $this->subject->get($this->batch_id);

        $this->assertEquals($result, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_get_feature_toggle_on(): void
    {
        $actual_result = new Completion_Batch_Data(
            $this->batch_id,
            1,
            1,
            1,
            1,
            'Process Type - 1',
            null,
            1,
            null
        );

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->get_batch_data($this->batch_id)->willReturn($this->batch_data);

        $this->dao_psql->get_process_types()->willReturn($this->type_data);

        $result = $this->subject->get($this->batch_id);

        $this->assertEquals($result, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_get_function_exception(): void
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->get_batch_data($this->batch_id)->willReturn([]);

        $this->dao->get_process_types()->willReturn([]);

        $this->expectException(Completion_Batch_Not_Found_Exception::class);

        $this->subject->get($this->batch_id);
    }
}
