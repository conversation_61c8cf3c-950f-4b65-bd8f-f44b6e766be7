<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Curation\ExclusivityAssortment\Domain\Price\Services\PriceOptionLoader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;

use function array_key_exists;
use function sprintf;

class PriceOptionsController extends AbstractBaseController
{
    /**
     * @param PriceOptionLoader $price_option_loader the price options loader
     * @param Curation_Tool_DAO $curation_tool_dao
     * @param Curation_Price_Loader $curation_price_loader curation_price_loader
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/get_price_options", methods={"GET"})
     */
    public function __invoke(
        PriceOptionLoader $price_option_loader,
        Curation_Tool_DAO $curation_tool_dao,
        Curation_Price_Loader $curation_price_loader,
        Request $request
    ): JsonResponse {
        $sku = $request->query->get('sku');

        try {
            $this->info('Loading Price options', ['sku' => $sku]);

            $options = $price_option_loader->getPriceOptions($sku);
            $key = 'Price';
            // @phpstan-ignore-next-line
            if (empty($options) || !array_key_exists($key, $options[0]) || !$curation_price_loader->is_price_valid($options[0][$key])) {
                $wsc = $curation_tool_dao->get_skus_prices([$sku]);
                if (array_key_exists($sku, $wsc) && (int)$wsc[$sku] > 0) {
                    $options[0][$key] = sprintf('%.2f', $wsc[$sku]);
                }
            }

            return $this->json(['price_options' => $options]);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('An exception occurred during loading price options for SKU "%s"', $sku),
                ['sku' => $sku, 'exception' => $exception]
            );

            throw $exception;
        }
    }
}
