<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

class Completion_QA_Automated_Service_Test extends TestCase
{
    public const STATUS_AUTOMATED_QA_IN_PROGRESS = 9;
    public const STATUS_AUTOMATED_QA_COMPLETE = 10;
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
     */
    private $batch_data_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service
     */
    private $vi_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
     */
    private $batch_status_updated;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker
     */
    private $qa_batch_checker;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected
     */
    private $notify_curation_rejected;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Service
     */
    private $subject;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage
     */
    private $automation_item_storage;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage
     */
    private $storage;

    /**
     * @var Curation_Only_Batch_Service_Postgres_Storage
     */
    private $postgres_storage;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO Curation_Automation_Item_PostgreSQL_DAO
     */
    private $curation_automation_item_postgres_dao;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;
    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->batch_data_service = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->vi_service = $this->prophesize(Completion_Verification_Item_Service::class);
        $this->batch_status_updated = $this->prophesize(Completion_Batch_Status_Updater::class);
        $this->qa_batch_checker = $this->prophesize(Completion_QA_Automated_Batch_Checker::class);
        $this->notify_curation_rejected = $this->prophesize(Completion_QA_Notify_Curation_Rejected::class);
        $this->automation_item_storage = $this->prophesize(Curation_Automation_Item_Storage::class);
        $this->storage = $this->prophesize(Curation_Only_Batch_Service_Storage::class);
        $this->postgres_storage = $this->prophesize(Curation_Only_Batch_Service_Postgres_Storage::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->curation_automation_item_postgres_dao = $this->prophesize(Curation_Automation_Item_PostgreSQL_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->subject = new Completion_QA_Automated_Service(
            $this->batch_data_service->reveal(),
            $this->vi_service->reveal(),
            $this->batch_status_updated->reveal(),
            $this->qa_batch_checker->reveal(),
            $this->notify_curation_rejected->reveal(),
            $this->automation_item_storage->reveal(),
            $this->storage->reveal(),
            $this->postgres_storage->reveal(),
            $this->curation_automation_item_postgres_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_false_when_is_not_completed()
    {
        $batch_id = 1;
        $employee_id = 1;

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(true);
        $this->qa_batch_checker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        $actual_result = $this->subject->complete($batch_id, $employee_id);

        $this->assertEquals(false, $actual_result);
    }

    /**
     * @param array $batch_data Batch data
     *
     * @test
     *
     * @dataProvider get_batch_data
     *
     * @return void
     */
    public function it_returns_true_when_is_completed(array $batch_data)
    {
        $batch_id = 1;
        $employee_id = 1;


        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qa_batch_checker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data $batch */
        $batch = $this->prophesize(Completion_Batch_Data::class);
        $batch->getStatus()->willReturn($batch_data['StatusID']);
        $batch->getAssignedEmployeeId()->willReturn($batch_data['AssignedEmID']);

        $this->batch_data_service->get($batch_id)->willReturn($batch)->shouldBeCalled();

        $this->vi_service->update_locked_data_if_null($batch_id, $employee_id)->willReturn(true);

        $actual_result = $this->subject->complete($batch_id, $employee_id);

        $this->assertEquals(true, $actual_result);
        $this->batch_status_updated->changeStatus(
            $batch_id,
            static::STATUS_AUTOMATED_QA_IN_PROGRESS,
            $employee_id
        )->shouldNotHaveBeenCalled();
    }

    /**
     * @param array $batch_data Batch data
     *
     * @test
     *
     * @dataProvider get_batch_data
     *
     * @return void
     */
    public function it_returns_true_when_it_is_completed(array $batch_data)
    {
        $batch_id = 1;
        $employee_id = 1;


        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(false);
        $this->qa_batch_checker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data $batch */
        $batch = $this->prophesize(Completion_Batch_Data::class);
        $batch->getStatus()->willReturn($batch_data['StatusID']);
        $batch->getAssignedEmployeeId()->willReturn($batch_data['AssignedEmID']);

        $this->batch_data_service->get($batch_id)->willReturn($batch)->shouldBeCalled();

        $this->vi_service->update_locked_data_if_null($batch_id, $employee_id)->willReturn(true);

        $actual_result = $this->subject->complete($batch_id, $employee_id);

        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_calls_update_batch_status_when_is_not_updated()
    {
        $batch_id = 1;
        $employee_id = 1;
        $process_type_name = 'Automated';
        $created_at = new DateTime();
        $batch_data = new Completion_Batch_Data(
            $batch_id,
            static::STATUS_AUTOMATED_QA_IN_PROGRESS,
            $brandCatalogID = 1,
            $employee_id,
            Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
            $process_type_name,
            $created_at,
            null,
            null
        );

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qa_batch_checker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        $this->batch_data_service->get($batch_id)->willReturn($batch_data)->shouldBeCalled();

        $this->vi_service->update_locked_data_if_null($batch_id, $employee_id)->willReturn(false);

        $this->batch_status_updated->changeStatus(
            $batch_id,
            static::STATUS_AUTOMATED_QA_COMPLETE,
            $employee_id
        )->willReturn()->shouldBeCalled();

        $this->subject->complete($batch_id, $employee_id);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_excluded_skus()
    {
        $batch_id = 1;

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel $batch */
        $batch = $this->prophesize(Curation_Automation_Unwhitelabel::class);
        $batch->get_batch_id()->willReturn(1);
        $batch->get_skus()->willReturn([]);
        $batch->get_unwhitelabel()->willReturn(true);

        $this->batch_data_service->get_qa_batch_skus_with_exclude_reason($batch_id)->willReturn($batch)->shouldBeCalled();

        $actual_result = $this->subject->unwhitelabelExcludedSkus($batch_id);

        $this->assertEquals(true, $actual_result);
    }

    public function it_returns_excluded_skus_feature_toggle_on()
    {
        $batch_id = 1;

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel $batch */
        $batch = $this->prophesize(Curation_Automation_Unwhitelabel::class);
        $batch->get_batch_id()->willReturn(1);
        $batch->get_skus()->willReturn([]);
        $batch->get_unwhitelabel()->willReturn(true);

        $this->batch_data_service->get_qa_batch_skus_with_exclude_reason($batch_id)->willReturn($batch)->shouldBeCalled();

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $actual_result = $this->subject->unwhitelabelExcludedSkus($batch_id);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_if_all_sku_approved()
    {
        $batch_id = 1;

        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qa_batch_checker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        $actual_result = $this->subject->isAllSkuApproved($batch_id);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_if_it_has_skus_with_updated()
    {
        $batch_id = 1;

        $this->qa_batch_checker->batchHasUpdatedSkus($batch_id)->willReturn(true);

        $actual_result = $this->subject->hasSkusWithUpdated($batch_id);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_if_partial_white_lable_status_updated()
    {
        $batch_id = 1;
        $employee_id = 1;
        $actual_result = true;
        $this->batch_status_updated->changeStatus($batch_id, Batch::STATUS_AUTOMATED_RE_DOWNSTREAMED, $employee_id)->willReturn('');
        $this->subject->markPartialWhiteLabelRequest($batch_id, $employee_id);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_mark_partial_white_label_sentwlat_feature_toggle_off()
    {
        $batch_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->mark_batch_as_sent(1, 1, true)->shouldBeCalled();
        $this->storage->set_verification_id_for_skus(1, 1)->shouldBeCalled();
        $this->subject->markPartialWhiteLabelSentWLAt($batch_id);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_mark_partial_white_label_sentwlat_feature_toggle_on()
    {
        $batch_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->postgres_storage->create_verification_id()->willReturn(1);
        $this->postgres_storage->mark_batch_as_sent(1, 1, true)->shouldBeCalled();
        $this->postgres_storage->set_verification_id_for_skus(1, 1)->shouldBeCalled();
        $this->subject->markPartialWhiteLabelSentWLAt($batch_id);
    }

    /**
    * @return array
    */
    public function get_batch_data(): array
    {
        return [
            [
                [
                    'StatusID' => static::STATUS_AUTOMATED_QA_COMPLETE,
                    'AssignedEmID' => 1,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
                ]
            ],
            [
                [
                    'StatusID' => static::STATUS_AUTOMATED_QA_IN_PROGRESS,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
                ]
            ],
        ];
    }
}
