<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader;

use function sprintf;

class ImageController extends AbstractBaseController
{
    /**
     * @param Curation_Image_Loader $image_loader the image loader
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/get_images", methods={"GET"})
     */
    public function __invoke(Curation_Image_Loader $image_loader, Request $request): JsonResponse
    {
        $sku = $request->query->get('sku');
        try {
            $this->info(
                'Loading images for SKU',
                ['sku' => $sku, 'employee_id' => $this->getEmployeeId()]
            );

            return $this->json(['images' => $image_loader->get_images_for_sku($sku)]);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('An exception occurred during loading images for SKU "%s"', $sku),
                ['sku' => $sku, 'exception' => $exception]
            );

            throw $exception;
        }
    }
}
