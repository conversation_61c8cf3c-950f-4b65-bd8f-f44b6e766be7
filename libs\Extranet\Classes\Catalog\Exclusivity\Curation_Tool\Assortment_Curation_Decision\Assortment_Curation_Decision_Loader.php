<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision;

use App\Application\Logger\LoggerTrait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Psr\Log\LogLevel;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Storage\Assortment_Curation_Decision_Loader_Postgres_DAO;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader;

class Assortment_Curation_Decision_Loader {
  use LoggerTrait;

  /**
   * @var Assortment_Decision_DTO_Loader
   */
  private $dto_loader;

  /**
   * @var Assortment_Curation_Decision_Matcher
   */
  private $decision_matcher;

  /**
   * @var Assortment_Curation_Decision_Loader_Storage
   */
  private $dao;

  /**
   * @var Assortment_Curation_Decision_Loader_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @var Assortment_Curation_Decision_Collection[]
   */
  private $memo = [];

  private FeatureTogglesInterface $featureToggles;

  /**
   * Assortment_Curation_Decision_Loader constructor.
   *
   * @param Assortment_Decision_DTO_Loader                    $dto_loader       decision dto loader
   * @param Assortment_Curation_Decision_Matcher              $decision_matcher decision matcher
   * @param Assortment_Curation_Decision_Loader_Storage       $dao              storage
   * @param Assortment_Curation_Decision_Loader_Postgres_DAO  $dao_psql         storage psql
   * @param FeatureTogglesInterface                           $featureToggles   featureToggles
   */
  public function __construct(
      Assortment_Decision_DTO_Loader                   $dto_loader,
      Assortment_Curation_Decision_Matcher             $decision_matcher,
      Assortment_Curation_Decision_Loader_Storage      $dao,
      Assortment_Curation_Decision_Loader_Postgres_DAO $dao_psql,
      FeatureTogglesInterface                          $featureToggles
  ) {
    $this->dto_loader       = $dto_loader;
    $this->decision_matcher = $decision_matcher;
    $this->dao              = $dao;
    $this->dao_psql         = $dao_psql;
    $this->featureToggles   = $featureToggles;
  }

  /**
   * @param int   $batch_id batch id
   * @param array $skus     skus
   *
   * @return Assortment_Curation_Decision_Collection
   */
  public function assortment_decision_data_for_skus(int $batch_id, array $skus) {
    $memo_key = $this->get_cache_key($batch_id, $skus);

    if (isset($this->memo[$memo_key])) {
      return $this->memo[$memo_key];
    }

    $assortment_decisions = $this->dto_loader->get_decisions_for_skus($skus);
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $sku_price_tiers = $this->dao_psql->get_curation_price_tiers_for_skus($skus);
    } else {
      $sku_price_tiers = $this->dao->get_curation_price_tiers_for_skus($skus);
    }
    $parent_children_skus = $this->dao->get_child_skus($skus);
    $curation_decisions   = [];

    foreach ($skus as $sku) {
      $children_skus       = $parent_children_skus[$sku] ?? null;
      $assortment_decision = $assortment_decisions->get_for_sku($sku);
      $sku_price_tier      = $sku_price_tiers[$sku] ?? null;
      $curation_decision   = $this->get_assortment_decision($sku, $assortment_decision, $batch_id, $sku_price_tier);
      if ($curation_decision !== null) {
        $curation_decisions[] = $curation_decision;
      } elseif ($children_skus) {
        $curation_decision_children = $this->get_assortment_decision_children($sku, $children_skus, $assortment_decisions, $batch_id, $sku_price_tiers);
        if ($curation_decision_children !== null) {
          $curation_decisions[] = $curation_decision_children;
        }
      }
    }
    $this->memo[$memo_key] = new Assortment_Curation_Decision_Collection($curation_decisions);

    return $this->memo[$memo_key];
  }

  /**
   * @param string                  $sku                 SKU
   * @param Assortment_Decision_DTO $assortment_decision Assortment Decision
   * @param int                     $batch_id            Batch ID
   * @param int|null                $sku_price_tier      SKU Price Tier
   *
   * @return Assortment_Curation_Decision
   */
  private function get_assortment_decision(string $sku, Assortment_Decision_DTO $assortment_decision, int $batch_id, ?int $sku_price_tier) : ?Assortment_Curation_Decision {
      $curation_decision = null;

    if ($assortment_decision->should_move_to_header_brand()) {
        if (!empty($assortment_decision->get_target_manufacturer_id())) {
            $matched_decision_data = $this->decision_matcher->find_closest_match(
                $batch_id,
                $assortment_decision->get_target_manufacturer_id(),
                $sku_price_tier
            );
            if (!empty($matched_decision_data)) {
                $curation_decision = new Assortment_Curation_Decision(
                    $sku,
                    $assortment_decision->should_move_to_header_brand(),
                    false,
                    $matched_decision_data
                );
            }
        }
    } elseif ($assortment_decision->should_move_to_tail_brand()) {
      $move_decision = null;

      if (!empty($assortment_decision->get_target_manufacturer_id())) {
        $move_decision = new Assortment_Curation_Move_To_Brand_Decision_Data(
            $assortment_decision->get_target_manufacturer_id(),
            0,
            0,
            0
        );
      }

      $curation_decision = new Assortment_Curation_Decision(
          $sku,
          false,
          $assortment_decision->should_move_to_tail_brand(),
          $move_decision
      );
    }

    return $curation_decision;
  }

  /**
   * @param string                             $sku                  Parent SKU
   * @param string[]                           $children_skus        Children SKUs
   * @param Assortment_Decision_DTO_Collection $assortment_decisions Assortment Decisions
   * @param int                                $batch_id             Batch ID
   * @param array                              $sku_price_tiers      SKU price Tiers
   *
   * @return Assortment_Curation_Decision
   */
  private function get_assortment_decision_children(string $sku, array $children_skus, Assortment_Decision_DTO_Collection $assortment_decisions, int $batch_id, array $sku_price_tiers
  ) : ?Assortment_Curation_Decision {
    $previous_curation_decision  = null;
    $curation_decision_for_child = null;
    foreach ($children_skus as $child_sku) {
      $assortment_decision         = $assortment_decisions->get_for_sku($child_sku);
      $curation_decision_for_child = $this->get_assortment_decision($sku, $assortment_decision, $batch_id, $sku_price_tiers[$child_sku]);
      if ($previous_curation_decision === $curation_decision_for_child || $previous_curation_decision === null) {
        $previous_curation_decision = $curation_decision_for_child;
        continue;
      }
      $curation_decision_for_child = null;
    }

    return $curation_decision_for_child;
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return string
   */
  private function get_cache_key(int $batch_id, array $skus) : string {
    return sha1(sprintf('%s_%s', $batch_id, implode(',', $skus)));
  }
}
