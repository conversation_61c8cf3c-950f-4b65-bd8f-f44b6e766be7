import '@homebase/core/dist/reset.css';
import '@homebase/core/dist/internaltoolsVars.css';
import '@wayfair/homebase-enterprise-reset/dist/index.extranet.css';
import '@wayfair/homebase-extranet/dist/index.css';
import React from 'react';
import {render} from 'react-dom';
import bootstrap from '@wayfair/bootstrap';
import Translation from '@wayfair/translation';
import CurationPlatformContainer from './Application/CurationPlatformContainer';
import AccessDeniedEntry from './Application/AccessDeniedEntry';
import ToolDisabledEntry from "./Application/ToolDisabledEntry";
import NotAuthorizedEntry from "./Application/NotAuthorizedEntry";
import CurationPage from "./Components/curation_page"
import QAPage from "./Components/qa_page"
import { internaltools, ThemeProvider } from '@homebase/core';
import HomebaseProvider from '@homebase/provider';



// eslint-disable-next-line no-undef, new-cap
document.title = Translation({msgid: 'defaultPageTitle'});

const Entries: {[k: string]: any} = {
  AccessDeniedEntry,
  ToolDisabledEntry,
  NotAuthorizedEntry,
  CurationPage,
  QAPage,
};

bootstrap(({key, id, props}: {key: string; id: string; props: any}) => {
  const EntryComponent = Entries[key];

  render(
      <HomebaseProvider
          locale='en-US'
          activeTheme='internaltools'
          cdnUrl="https://secure.img1-fg.wfcdn.com/"
          imageKey="silt-saLrTfxX*WAiLMp"
      >
        <ThemeProvider theme={internaltools}>
    <CurationPlatformContainer component={EntryComponent} {...props} />
        </ThemeProvider>
      </HomebaseProvider>,
    // eslint-disable-next-line no-undef
    document.getElementById(id)
  );
});
