<?php

declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Exception\Production_Tracking_Status_Not_Updated_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Curation_Production_Tracking_Service {

  /**
   * @var Curation_Production_Tracking_DAO
   */
  private $storage_product_tracking_sql_dao;

  /**
   * @var Production_Tracking_Process_Factory
   */
  private $pt_factory;


  /**
   * @var Curation_Production_Tracking_PostgreSQL_DAO
   */
  private $storage_product_tracking_psql_dao;


  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * Curation_Production_Tracking_Service constructor.
   *
   * @param Curation_Production_Tracking_DAO $storage_product_tracking_sql_dao  storage_product_tracking_sql_dao
   * @param Production_Tracking_Process_Factory  $pt_factory PT Factory
   * @param Curation_Production_Tracking_PostgreSQL_DAO $storage_product_tracking_psql_dao  storage_product_tracking_psql_dao
   * @param FeatureTogglesInterface $featureToggles  featureToggles
   */
  public function __construct(
      Curation_Production_Tracking_DAO $storage_product_tracking_sql_dao,
      Production_Tracking_Process_Factory $pt_factory,
      Curation_Production_Tracking_PostgreSQL_DAO $storage_product_tracking_psql_dao,
      FeatureTogglesInterface $featureToggles

  ) {
    $this->storage_product_tracking_sql_dao = $storage_product_tracking_sql_dao;
    $this->pt_factory = $pt_factory;
    $this->storage_product_tracking_psql_dao = $storage_product_tracking_psql_dao;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return int[]
   */
  public function get_project_ids(int $batch_id) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      return $this->storage_product_tracking_psql_dao->get_project_ids($batch_id);
    } else {
      return $this->storage_product_tracking_sql_dao->get_project_ids($batch_id);
    }
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return void
   * @throws Production_Tracking_Status_Not_Updated_Exception
   */
  public function downstream_projects(int $batch_id) : void {
    $project_ids = $this->get_project_ids($batch_id);

    if (!empty($project_ids)) {
      $processor = $this->pt_factory->get_processor();
      $processor->update_production_tracking_status($project_ids);
    }
  }
}
