<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Tool_Curation_Decision;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Matcher;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Move_To_Brand_Decision_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Storage\Assortment_Curation_Decision_Loader_Postgres_DAO;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader;

class Assortment_Curation_Decision_Loader_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Assortment_Decision_DTO_Loader
     */
    private $dto_loader;

    /**
     * @var Assortment_Curation_Decision_Matcher
     */
    private $decision_matcher;

    /**
     * @var Assortment_Curation_Decision_Loader_Storage
     */
    private $dao;

    /**
     * @var Assortment_Curation_Decision_Loader_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var Assortment_Decision_DTO
     */
    private $assortment_decision;

    /**
     * @var Assortment_Decision_DTO_Collection
     */
    private $assortment_decision_collection;

    /**
     * @var Assortment_Curation_Decision_Loader
     */
    private $subject;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dto_loader = $this->prophesize(Assortment_Decision_DTO_Loader::class);
        $this->decision_matcher = $this->prophesize(Assortment_Curation_Decision_Matcher::class);
        $this->dao = $this->prophesize(Assortment_Curation_Decision_Loader_Storage::class);
        $this->dao_psql = $this->prophesize(Assortment_Curation_Decision_Loader_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->assortment_decision = $this->prophesize(Assortment_Decision_DTO::class);
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(false);
        $this->assortment_decision->should_move_to_header_brand()->willReturn(false);
        $this->assortment_decision->get_sku()->willReturn('ABC');
        $this->assortment_decision->get_target_manufacturer_id()->willReturn(123);

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_curation_price_tiers_for_skus(['ABC'])->willReturn(['ABC' => 4]);
        $this->dao->get_child_skus(['ABC'])->willReturn([]);

        $this->assortment_decision_collection = $this->prophesize(Assortment_Decision_DTO_Collection::class);
        $this->assortment_decision_collection->get_for_sku(Argument::any())->willReturn($this->assortment_decision);

        $this->dto_loader->get_decisions_for_skus(Argument::any())->willReturn($this->assortment_decision_collection);

        $this->subject = new Assortment_Curation_Decision_Loader(
            $this->dto_loader->reveal(),
            $this->decision_matcher->reveal(),
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_gives_higher_priority_to_move_to_header_decision(): void
    {
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(true);
        $this->assortment_decision->should_move_to_header_brand()->willReturn(true);

        $this->decision_matcher->find_closest_match(Argument::cetera())->willReturn($this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class));

        $result = $this->subject->assortment_decision_data_for_skus(123, ['ABC']);

        $curation_decision = $result->get_for_sku('ABC');

        $this->assertInstanceOf(Assortment_Curation_Decision::class, $curation_decision);

        $this->assertTrue($curation_decision->should_move_to_header_brand());
        $this->assertFalse($curation_decision->should_move_to_tail_brand());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_does_not_add_anything_when_not_possible_to_match(): void
    {
        $this->assortment_decision->should_move_to_header_brand()->willReturn(true);
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(false);

        $this->decision_matcher->find_closest_match(Argument::cetera())->willReturn(null);

        $result = $this->subject->assortment_decision_data_for_skus(123, ['ABC']);

        $curation_decision = $result->get_for_sku('ABC');

        $this->assertFalse($curation_decision->should_move_to_header_brand());
        $this->assertFalse($curation_decision->should_move_to_tail_brand());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_adds_move_to_tail_decision_when_assortment_decision_says_so(): void
    {
        $this->assortment_decision->should_move_to_header_brand()->willReturn(false);
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(true);

        $result = $this->subject->assortment_decision_data_for_skus(123, ['ABC']);

        $curation_decision = $result->get_for_sku('ABC');

        $this->assertFalse($curation_decision->should_move_to_header_brand());
        $this->assertTrue($curation_decision->should_move_to_tail_brand());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_adds_move_to_header_decision_when_assortment_decision_says_so_feature_toggle_off(): void
    {
        $this->assortment_decision->should_move_to_header_brand()->willReturn(true);
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(false);
        $this->assortment_decision->get_target_manufacturer_id()->willReturn(333);

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_curation_price_tiers_for_skus(['ABC'])->willReturn(['ABC' => 3]);

        $decision_data = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $this->decision_matcher->find_closest_match(123, 333, 3)->willReturn($decision_data);

        $result = $this->subject->assortment_decision_data_for_skus(123, ['ABC']);

        $curation_decision = $result->get_for_sku('ABC');

        $this->assertTrue($curation_decision->should_move_to_header_brand());
        $this->assertFalse($curation_decision->should_move_to_tail_brand());
        $this->assertEquals($decision_data->reveal(), $curation_decision->get_move_to_brand_decision_data());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_adds_move_to_header_decision_when_assortment_decision_says_so_feature_toggle_on(): void
    {
        $this->assortment_decision->should_move_to_header_brand()->willReturn(true);
        $this->assortment_decision->should_move_to_tail_brand()->willReturn(false);
        $this->assortment_decision->get_target_manufacturer_id()->willReturn(333);

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_curation_price_tiers_for_skus(['ABC'])->willReturn(['ABC' => 3]);

        $decision_data = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $this->decision_matcher->find_closest_match(123, 333, 3)->willReturn($decision_data);

        $result = $this->subject->assortment_decision_data_for_skus(123, ['ABC']);

        $curation_decision = $result->get_for_sku('ABC');

        $this->assertTrue($curation_decision->should_move_to_header_brand());
        $this->assertFalse($curation_decision->should_move_to_tail_brand());
        $this->assertEquals($decision_data->reveal(), $curation_decision->get_move_to_brand_decision_data());
    }
}
