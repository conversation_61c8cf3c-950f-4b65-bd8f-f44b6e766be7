/**
 * QA Status filter for the curation page
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Dropdown} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {
  QA_STATUS_PENDING,
  QA_STATUS_ACCEPTED,
  QA_STATUS_UPDATED,
  QA_FILTER_PENDING,
  QA_FILTER_PENDING_WITH_SUGGESTED_STYLES,
  QA_FILTER_PENDING_WITHOUT_SUGGESTED_STYLES,
  QA_FILTER_APPROVED,
  QA_FILTER_REJECTED,
  QA_FILTER_UPDATED,
  CURATION_SAVED_DECISION
} from './curation_tool_shapes';

const OPTION_PENDING = {
  value: QA_FILTER_PENDING,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionPendingDecision" />
  ),
};

const OPTION_PENDING_WITH_SUGGESTED_STYLES = {
  value: QA_FILTER_PENDING_WITH_SUGGESTED_STYLES,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionPendingDecisionWithSuggestedStyles" />
  ),
};

const OPTION_PENDING_WITHOUT_SUGGESTED_STYLES = {
  value: QA_FILTER_PENDING_WITHOUT_SUGGESTED_STYLES,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionPendingDecisionWithoutSuggestedStyle" />
  ),
};

const OPTION_PENDING_APPROVED = {
  value: QA_FILTER_APPROVED,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionApproved" />
  ),
};

const OPTION_PENDING_REJECTED = {
  value: QA_FILTER_REJECTED,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionRejected" />
  ),
};

const OPTION_PENDING_UPDATED = {
  value: QA_FILTER_UPDATED,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionUpdatedDecision" />
  ),
};

const OPTION_SAVED_DECISION = {
  value: CURATION_SAVED_DECISION,
  text: (
    <Translation msgid="CurationTool.QACurationStatusFilterOptionSavedDecision" />
  ),
};

const getFilterOptions = (
  canFilterByHavingSuggestedStyles,
  isQAPage
) => {
  if (canFilterByHavingSuggestedStyles) {
      return isQAPage
        ? [
            OPTION_PENDING,
            OPTION_PENDING_WITH_SUGGESTED_STYLES,
            OPTION_PENDING_WITHOUT_SUGGESTED_STYLES,
            OPTION_PENDING_UPDATED,
            OPTION_PENDING_APPROVED,
          ]
        : [
            OPTION_PENDING,
            OPTION_PENDING_WITH_SUGGESTED_STYLES,
            OPTION_PENDING_WITHOUT_SUGGESTED_STYLES,
            OPTION_SAVED_DECISION,
          ];
  }

  if (isQAPage) {
    return [OPTION_PENDING, OPTION_PENDING_UPDATED, OPTION_PENDING_APPROVED, OPTION_PENDING_REJECTED];
  }

  return [OPTION_PENDING, OPTION_SAVED_DECISION];
};

const getFilterLabel = (isQAPage) => {

  if (isQAPage) {
    return <Translation msgid="CurationTool.QACurationStatusQAStatus" />;
  }

  return (
    <Translation msgid="CurationTool.QACurationStatusCurationStatus" />
  );
};

const curationFilterFunctions = {
  [QA_FILTER_PENDING]: curationItems =>
    curationItems.filter(item => !item.savedAt),
  [QA_FILTER_PENDING_WITH_SUGGESTED_STYLES]: curationItems =>
    curationItems.filter(
      item => !item.savedAt && item.suggestedStyles.length > 0
    ),
  [QA_FILTER_PENDING_WITHOUT_SUGGESTED_STYLES]: curationItems =>
    curationItems.filter(
      item => !item.savedAt && item.suggestedStyles.length === 0
    ),
  [QA_FILTER_APPROVED]: curationItems =>
    curationItems.filter(item => item.qaStatus === QA_STATUS_ACCEPTED),
  [QA_FILTER_REJECTED]: curationItems =>
    curationItems.filter(
      item =>
        item.qaStatus !== QA_STATUS_PENDING &&
        item.qaStatus !== QA_STATUS_ACCEPTED
    ),
  [QA_FILTER_UPDATED]: curationItems =>
    curationItems.filter(item => item.qaStatus === QA_STATUS_UPDATED),
  [CURATION_SAVED_DECISION]: curationItems =>
    curationItems.filter(item => item.savedAt),
};

const qaFilterFunctions = {
  ...curationFilterFunctions,
  [QA_FILTER_PENDING]: curationItems =>
    curationItems.filter(item => item.qaStatus === QA_STATUS_PENDING),
};

const getOptionLabel = option => (option ? option.text : '');
const getOptionValue = option => (option ? option.value : null);

const QACurationStatusFilter = ({
  value,
  onChange,
  canFilterByHavingSuggestedStyles,
  isAutomaticCurationPostQaEnabled,
  isQAPage,
}) => {
  return (
    <Dropdown
      label={getFilterLabel(isQAPage)}
      options={getFilterOptions(
        canFilterByHavingSuggestedStyles,
        isQAPage
      )}
      value={getOptionValue(value)}
      onValueChange={option => {
        const value = option ? option.value : null;
        onChange(value);
      }}
      getOptionLabel={getOptionLabel}
    />
  );
};

QACurationStatusFilter.propTypes = {
  onChange: PropTypes.func.isRequired,
  canFilterByHavingSuggestedStyles: PropTypes.bool,
  isAutomaticCurationPostQaEnabled: PropTypes.bool,
  value: PropTypes.oneOf([
    QA_FILTER_PENDING,
    QA_FILTER_PENDING_WITH_SUGGESTED_STYLES,
    QA_FILTER_PENDING_WITHOUT_SUGGESTED_STYLES,
    QA_FILTER_APPROVED,
    QA_FILTER_REJECTED,
  ]),
  isQAPage: PropTypes.bool,
};

QACurationStatusFilter.defaultProps = {
  canFilterByHavingSuggestedStyles: false,
  isAutomaticCurationPostQaEnabled: false,
  value: null,
};

export const CurationStatusFilter = QACurationStatusFilter;
export const QA_FILTER_DEFAULT = null;
export const getCurationPageQAFilterFunction = QAFilterValue =>
  curationFilterFunctions[QAFilterValue];
export const getQAPageQAFilterFunction = QAFilterValue =>
  qaFilterFunctions[QAFilterValue];

const exports = {
  CurationStatusFilter,
  getCurationPageQAFilterFunction,
  getQAPageQAFilterFunction,
  QA_FILTER_DEFAULT,
};

export default exports;
