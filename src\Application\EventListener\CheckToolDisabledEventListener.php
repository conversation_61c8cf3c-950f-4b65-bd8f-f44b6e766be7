<?php

declare(strict_types=1);

namespace App\Application\EventListener;

use App\Application\Exception\ToolDisabledException;
use App\Application\Service\Security\ToolAvailabilityCheckerInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;

use function mb_strpos;

class CheckToolDisabledEventListener
{
    private ToolAvailabilityCheckerInterface $security;

    public function __construct(ToolAvailabilityCheckerInterface $security)
    {
        $this->security = $security;
    }

    public function onKernelController(ControllerEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $routeName = $event->getRequest()->attributes->get('_route');

        // skip tool disabled checks for health and liveliness probes
        if (mb_strpos($routeName, 'ping') === 0) {
            return;
        }
        // skip UI rendering requests
        if ($routeName === 'static_content_bundle') {
            return;
        }

        if ($this->security->isToolDisabled()) {
            throw new ToolDisabledException();
        }
    }
}
