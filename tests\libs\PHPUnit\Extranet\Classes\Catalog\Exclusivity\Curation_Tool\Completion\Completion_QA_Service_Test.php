<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_<PERSON>\Completion\Completion_Verification_Item_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

class Completion_QA_Service_Test extends TestCase
{
    public const BATCH_STATUS_IN_PROGRESS = 3;
    public const BATCH_STATUS_CURATION_COMPLETE = 4;
    public const BATCH_STATUS_QA_COMPLETE = 5;
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
     */
    private $batchDataService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service
     */
    private $viService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
     */
    private $batchStatusUpdater;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_Checker
     */
    private $qaBatchChecker;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected
     */
    private $notifyCurationRejected;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Service
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->batchDataService = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->viService = $this->prophesize(Completion_Verification_Item_Service::class);
        $this->batchStatusUpdater = $this->prophesize(Completion_Batch_Status_Updater::class);
        $this->qaBatchChecker = $this->prophesize(Completion_QA_Batch_Checker::class);
        $this->notifyCurationRejected = $this->prophesize(Completion_QA_Notify_Curation_Rejected::class);

        $this->subject = new Completion_QA_Service(
            $this->batchDataService->reveal(),
            $this->viService->reveal(),
            $this->batchStatusUpdater->reveal(),
            $this->qaBatchChecker->reveal(),
            $this->notifyCurationRejected->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_false_when_is_not_completed()
    {
        $batchId = 1;
        $employeeId = 1;

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(true);
        $this->qaBatchChecker->getCompletionQaStatus($batchId)->willReturn($result->reveal());

        $actualResult = $this->subject->complete($batchId, $employeeId);

        $this->assertEquals(false, $actualResult);
    }

    /**
     * @param array $batchData Batch data
     *
     * @test
     *
     * @dataProvider get_batch_data
     *
     * @return void
     */
    public function it_returns_true_when_is_completed(array $batchData)
    {
        $batchId = 1;
        $employeeId = 1;


        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qaBatchChecker->getCompletionQaStatus($batchId)->willReturn($result->reveal());

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data $batch */
        $batch = new Completion_Batch_Data(
            $batchId,
            $batchData['StatusID'],
            1,
            $batchData['AssignedEmID'],
            $batchData['CurationBatchProcessTypeID'],
            $batchData['CurationBatchProcessTypeName'],
            $batchData['CreatedAt'],
            $batchData['ApproverEmpId'],
            $batchData['ApprovedAt'],
        );

        $this->assertEquals($batch->getProcessTypeID(), $batchData['CurationBatchProcessTypeID']);
        $this->batchDataService->get($batchId)->willReturn($batch)->shouldBeCalled();

        $actualResult = $this->subject->complete($batchId, $employeeId);

        $this->assertEquals(true, $actualResult);
        $this->batchStatusUpdater->changeStatus(
            $batchId,
            static::BATCH_STATUS_CURATION_COMPLETE,
            $employeeId
        )->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_calls_update_batch_status_when_is_not_updated()
    {
        $batchId = 1;
        $employeeId = 1;
        $processTypeName = 'Manual';
        $createdAt = new DateTime();
        $batchData = new Completion_Batch_Data(
            $batchId,
            static::BATCH_STATUS_CURATION_COMPLETE,
            $brandCatalogID = 1,
            $employeeId,
            Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
            $processTypeName,
            $createdAt,
            null,
            null
        );

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status $result */
        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qaBatchChecker->getCompletionQaStatus($batchId)->willReturn($result->reveal());

        $this->batchDataService->get($batchId)->willReturn($batchData)->shouldBeCalled();

        $this->viService->update_locked_data_if_null($batchId, $employeeId)->willReturn(true);

        $this->batchStatusUpdater->changeStatus(
            $batchId,
            static::BATCH_STATUS_QA_COMPLETE,
            $employeeId
        )->willReturn()->shouldBeCalled();

        $this->subject->complete($batchId, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_if_all_sku_approved()
    {
        $batch_id = 1;

        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qaBatchChecker->getCompletionQaStatus($batch_id)->willReturn($result->reveal());

        $actual_result = $this->subject->isAllSkuApproved($batch_id);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @return array
     */
    public function get_batch_data(): array
    {
        return [
            [
                [
                    'StatusID' => static::BATCH_STATUS_QA_COMPLETE,
                    'AssignedEmID' => 1,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => 1,
                    'ApprovedAt' => new DateTime(),
                ]
            ],
            [
                [
                    'StatusID' => static::BATCH_STATUS_IN_PROGRESS,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
            [
                [
                    'StatusID' => static::BATCH_STATUS_IN_PROGRESS,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => null,
                    'CurationBatchProcessTypeName' => null,
                    'CreatedAt' => null,
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
        ];
    }
}
