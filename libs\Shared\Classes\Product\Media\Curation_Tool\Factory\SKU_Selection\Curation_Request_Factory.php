<?php
/**
 * Curation Request Factory
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection;

use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Source_Model;
use WF\Shared\ProductManagement\Populator\Populator;

class Curation_Request_Factory {
  /**
   * @var \WF\Shared\ProductManagement\Populator\Populator
   */
  private $populator;

  /**
   * Curation_Request_Factory constructor.
   *
   * @param \WF\Shared\ProductManagement\Populator\Populator $populator populator
   */
  public function __construct(
      Populator $populator
  ) {
    $this->populator = $populator;
  }

  /**
   * @param array $map Model map
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model
   */
  public function create_request_model(array $map = []) : Curation_Request_Model {
    $curation_request = new Curation_Request_Model();

    if (count($map) > 0) {
      $curation_request = $this->populator->populate($curation_request, $map);
    }

    return $curation_request;
  }

  /**
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container $requirement_container Requirement Container
   * @param string                                                                                                  $sku                   SKU
   * @param array                                                                                                   $map                   SKU Data
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Source_Model
   */
  public function create_request_sku_source_model(
      Requirement_Container $requirement_container,
      string $sku,
      array $map
  ) : Curation_Request_SKU_Source_Model {
    return new Curation_Request_SKU_Source_Model(
        $requirement_container,
        $sku,
        $map
    );
  }

}
