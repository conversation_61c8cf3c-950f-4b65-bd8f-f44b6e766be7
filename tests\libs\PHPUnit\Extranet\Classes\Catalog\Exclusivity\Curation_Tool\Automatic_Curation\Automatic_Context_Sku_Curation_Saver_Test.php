<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Context_Sku_Curation_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;

class Automatic_Context_Sku_Curation_Saver_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Curation_Decision_Service
     */
    private $curationDecisionService;

    /**
     * @var Automatic_Context_Sku_Curation_Saver
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->curationDecisionService = $this->prophesize(Curation_Decision_Service::class);

        $this->subject = new Automatic_Context_Sku_Curation_Saver(
            $this->curationDecisionService->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_saves_eligible_skus()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = 12;
        $granularStyleId = 10;
        $employeeId = 10;
        $curationItemType = Curation_Item_Type::context();

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_style_id()->willReturn($styleId);
        $curationItem->get_final_sub_style_id()->willReturn($substyleId);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $curationItem->get_type()->willReturn($curationItemType);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(
            $batchId,
            [$sku],
            $priceTier,
            $styleId,
            $substyleId,
            $manufacturerId,
            $granularStyleId,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_context_sku()
        )->shouldBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_already_saved()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = 'today';
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = 12;
        $granularStyleId = 10;
        $employeeId = 10;
        $curationItemType = Curation_Item_Type::context();

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_style_id()->willReturn($styleId);
        $curationItem->get_final_sub_style_id()->willReturn($substyleId);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $curationItem->get_type()->willReturn($curationItemType);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_not_context_type()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = 12;
        $employeeId = 10;
        $granularStyleId = 10;
        $curationItemType = Curation_Item_Type::simple();

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_style_id()->willReturn($styleId);
        $curationItem->get_final_sub_style_id()->willReturn($substyleId);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $curationItem->get_type()->willReturn($curationItemType);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_without_final_manufacturer()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = null;
        $granularStyleId = 10;
        $employeeId = 10;
        $curationItemType = Curation_Item_Type::context();

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_style_id()->willReturn($styleId);
        $curationItem->get_final_sub_style_id()->willReturn($substyleId);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $curationItem->get_type()->willReturn($curationItemType);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_without_final_style()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = null;
        $granularStyleId = null;
        $employeeId = 10;
        $curationItemType = Curation_Item_Type::context();

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_style_id()->willReturn($styleId);
        $curationItem->get_final_sub_style_id()->willReturn($substyleId);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $curationItem->get_type()->willReturn($curationItemType);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }
}
