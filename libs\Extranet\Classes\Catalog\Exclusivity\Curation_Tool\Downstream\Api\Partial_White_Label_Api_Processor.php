<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

declare(strict_types = 1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\Downstream_Fail_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\White_Label_Batch_Already_Exists_Exception;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch as Batch_Model;

class Partial_White_Label_Api_Processor extends White_Label_Api_Processor {
  /**
   * partial white label payload.
   */
  protected const CURATION_WL_CONFIGURATION = [
      'changeCollection'  => true,
      'changePartNumber'  => false,
      'changeEAN'         => false,
      'changeDisplaySku'  => false,
      'enableScrubbing'   => true,
      'brandCatalogId'    => null
  ];

  /**
   * @param Batch_Model $batch_model Batch
   *
   * @return bool
   */
  public function processPartialWhiteLabel(Batch_Model $batch_model) : int {
    $request_Failed = 400;
    try {
       $request_succeeded = $this->process_white_label_next_gen($batch_model, true);
        return $request_succeeded;
    } catch (White_Label_Batch_Already_Exists_Exception|Downstream_Fail_Exception $e) {
      $this->logger->error($e->getMessage());
      return $request_Failed;
    }

  }
}
