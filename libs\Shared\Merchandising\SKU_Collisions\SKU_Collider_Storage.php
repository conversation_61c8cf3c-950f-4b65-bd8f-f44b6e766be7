<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\SKU_Collisions;

interface SKU_Collider_Storage {
  /**
   * @param string[] $skus calculate kit collisions for the list of given skus
   *
   * @return string[]
   */
  public function getKitCollisions(array $skus) : array;

  /**
   * @param int   $collectionId Collection ID
   * @param array $skusExcluded SKUs to exclude
   *
   * @return string[]
   */
  public function getCollectionCollisions(int $collectionId, array $skusExcluded) : array;

  /**
   * @param string[] $skus SKUs
   *
   * @return string[]
   */
  public function getPerigoldWhiteLabelSkus(array $skus) : array;
}
