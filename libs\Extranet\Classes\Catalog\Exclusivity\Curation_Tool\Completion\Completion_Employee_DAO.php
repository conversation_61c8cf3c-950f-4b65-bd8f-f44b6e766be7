<?php

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Infrastructure\Connection\DatabaseConstantsInterface;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use WF\Shared\Helpers\SQL;

class Completion_Employee_DAO implements Completion_Employee_Storage
{
    private ProductConnection $pdo_product;

    /**
     * @param ProductConnection $pdo_product
     */
    public function __construct(ProductConnection $pdo_product)
    {
        $this->pdo_product = $pdo_product;
    }


    /**
     * @param int $employee_id Employee ID
     *
     * @return array
     */
    public function get_employee_data(int $employee_id): array
    {
        $sql = '
          SELECT
            e.EmID AS employee_id,
            e.EmFirstName AS first_name,
            e.EmLastName AS last_name,
            e.EmEmailAddress AS email_address
          FROM csn_hr.dbo.tblEmployee e WITH (NOLOCK)
          WHERE e.EmID = :em_id
        ';

        $hints = [
            'em_id' => SQL::int
        ];

        $statement = $this->pdo_product->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);

        $statement->bindValue(':em_id', $employee_id, PDO::PARAM_INT);

        if ($statement->execute() === false) {
            throw new ExecutionException(
                sprintf('Cannot load employee: %s', implode(',', $statement->errorInfo()))
            );
        }

        $result = $statement->fetch();

        if ($result === false) {
            throw new ExecutionException(
                sprintf('Failed to find employee: %s', $employee_id)
            );
        }

        return [
            'email' => $result['email_address'],
            'name' => $result['first_name'] . ' ' . $result['last_name'],
        ];
    }
}
