<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Api\Production_Tracking_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Processor;
use WF\Secrets\Filesystem\FileNotFoundException;
use WF\Secrets\SecretProviderInterface;

final class Production_Tracking_Process_Factory implements LoggerAwareInterface {

  use LoggerAwareTrait;
  use LoggerTrait;

  public const CURATION_PRODUCTION_TRACKING_API_CLIENT_ID     = 'curation-production-tracking-api-client-id';
  public const CURATION_PRODUCTION_TRACKING_API_CLIENT_SECRET = 'curation-production-tracking-api-client-secret';

  /**
   * @var Legacy_Production_Tracking_Processor
   */
  private $legacy_pt_processor;

  /**
   * @var Production_Tracking_Api_Processor
   */
  private $pt_api_processor;

  private SecretProviderInterface $secretProvider;

  private bool $curationProductionTrackingApi;
  /**
   * @param Legacy_Production_Tracking_Processor $legacy_pt_processor Legacy PT Processor
   * @param Production_Tracking_Api_Processor    $pt_api_processor    PT API Processor
   * @param SecretProviderInterface              $secretProvider      Secrets Provider Service
   */
  public function __construct(
      Legacy_Production_Tracking_Processor $legacy_pt_processor,
      Production_Tracking_Api_Processor $pt_api_processor,
      SecretProviderInterface $secretProvider
  ) {
    $this->legacy_pt_processor = $legacy_pt_processor;
    $this->pt_api_processor    = $pt_api_processor;
    $this->secretProvider = $secretProvider;
    $this->curationProductionTrackingApi = true;
  }

  /**
   * @return Production_Tracking_Processor
   */
  public function get_processor() : Production_Tracking_Processor {
      if($this->curationProductionTrackingApi) {
      try {
        $client_id = $this->secretProvider->get(self::CURATION_PRODUCTION_TRACKING_API_CLIENT_ID);
        $client_secret = $this->secretProvider->get(self::CURATION_PRODUCTION_TRACKING_API_CLIENT_SECRET);
      } catch (FileNotFoundException $exception) {
        $client_id = null;
        $client_secret = null;
      }
      // Checking if we can get the client ID and Secret
      if (empty($client_id) || empty($client_secret)) {

        // Logging error here because we need to have alert for this even though we are returning the older processor if this check fail
        $this->logger->error('API Client or Secret could not be found for PT API Integration with Curation Tool.');

        return $this->legacy_pt_processor;
      }

      return $this->pt_api_processor;
    }

    return $this->legacy_pt_processor;
  }
}
