<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Automatic_Class_Curation_Configuration_Item {
  /**
   * @var int
   */
  private $class_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $price_tier; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $style_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $substyle_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $manufacturer_id; /** @phpstan-ignore-line */

  /**
   * Class_Curation_Automatic_Configuration constructor
   */
  private function __construct() {
    /**
     * Can be created only by PDO
     */
  }

  /**
   * @return int
   */
  public function get_class_id() : int {
    return $this->class_id;
  }

  /**
   * @return int
   */
  public function get_price_tier() : int {
    return $this->price_tier;
  }

  /**
   * @return int
   */
  public function get_style_id() : int {
    return $this->style_id;
  }

  /**
   * @return int
   */
  public function get_substyle_id() : int {
    return $this->substyle_id;
  }

  /**
   * @return int
   */
  public function get_manufacturer_id() : int {
    return $this->manufacturer_id;
  }
}