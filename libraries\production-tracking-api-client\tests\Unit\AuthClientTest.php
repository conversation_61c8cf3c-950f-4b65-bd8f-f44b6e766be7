<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Tests\Unit;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use WF\Curation\ProductionTrackingApi\Auth\AuthClient;
use WF\Curation\ProductionTrackingApi\ClientConfig;

class AuthClientTest extends TestCase
{
    private const NUMBER_OF_RETRIES = 1;
    private const CONNECTION_TIMEOUT = 1;
    private const CLIENT_ID = 'client_id';
    private const CLIENT_SECRET = 'client_secret';
    private const API_URL = 'apiwayfaircom.csnzoo.com';
    private const AUTH_URL = 'https://ssoauthwayfaircom.csnzoo.com';

    /**
     * @var AuthClient
     */
    private $subject;

    /**
     * @var ClientConfig
     */
    private $clientConfig;

    /**
     * @var MockObject|LoggerInterface
     */
    private $logger;

    protected function setUp(): void
    {
        parent::setUp();

        /* @var LoggerInterface $logger */
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->clientConfig = new ClientConfig(
            $this->logger,
            self::NUMBER_OF_RETRIES,
            self::CONNECTION_TIMEOUT,
            self::CLIENT_ID,
            self::CLIENT_SECRET,
            self::AUTH_URL,
            self::API_URL
        );
        $this->subject = new AuthClient($this->clientConfig);
    }

    /**
     * @test Log Error After Connection Failure
     */
    public function itShouldLogErrorAfterConnectionFailure(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = AuthHttpClient::createConnectionTimeoutClientMock();
        $this->clientConfig->setAuthHttpClient(self::AUTH_URL, $httpClient);

        $response = $this->subject->retrieveToken();

        $this->assertNull($response);
    }


    /**
     * @test   Should Return Successful Response
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnASuccessfulResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('info');

        $httpClient = AuthHttpClient::createSuccessfulClientMock();

        $this->clientConfig->setAuthHttpClient(self::AUTH_URL, $httpClient);

        $response = $this->subject->retrieveToken();

        $this->assertNotEmpty($response);
    }

    /**
     * @test   Should return validation error response
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnValidationErrorResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = AuthHttpClient::createValidationErrorClientMock();

        $this->clientConfig->setAuthHttpClient(self::AUTH_URL, $httpClient);

        $response = $this->subject->retrieveToken();

        $this->assertNull($response);
    }
}
