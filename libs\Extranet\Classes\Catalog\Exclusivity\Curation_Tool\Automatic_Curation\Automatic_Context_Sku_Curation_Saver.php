<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;

class Automatic_Context_Sku_Curation_Saver implements Automatic_Curation_Saver {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service
   */
  private $curationDecisionService;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service $curationDecisionService Curation decision service
   */
  public function __construct(Curation_Decision_Service $curationDecisionService) {
    $this->curationDecisionService = $curationDecisionService;
  }

  /**
   * @param int                                                                      $batchId    Batch ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections   Section data
   * @param int                                                                      $employeeId Employee ID
   *
   * @return int
   * @throws \Exception
   */
  public function execute(int $batchId, array $sections, int $employeeId) : int {
    $newContextSkus = $this->getNewContextSkuForAutomaticSaving($sections);

    if (empty($newContextSkus)) {
      return 0;
    }

    $this->saveNewContextSku($batchId, $employeeId, $newContextSkus);

    return count($newContextSkus);
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Section data
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  private function getNewContextSkuForAutomaticSaving(array $sections) : array {
    $newContextSkus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {

        if ($this->isEligibleForAutomaticSave($curationItem)) {
          $newContextSkus[] = $curationItem;
        }
      }
    }

    return $newContextSkus;
  }

  /**
   * @param int                                                                            $batchId        Batch Id
   * @param int                                                                            $employeeId     Employee ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $newContextSkus Section data
   *
   * @return void
   * @throws \Exception
   */
  private function saveNewContextSku(int $batchId, int $employeeId, array $newContextSkus) {
    $savedAt = date('Y-m-d H:i:s');

    foreach ($newContextSkus as $curationItem) {
      // if we couldn't determine context sku brand and style, don't save it automatically
      if (empty($curationItem->get_final_brand_id()) || empty($curationItem->get_final_style_id())) {
        continue;
      }

      $this->curationDecisionService->save_curated(
          $batchId,
          [$curationItem->get_sku()],
          $curationItem->get_price_tier(),
          $curationItem->get_final_style_id(),
          $curationItem->get_final_sub_style_id(),
          $curationItem->get_final_brand_id(),
          $curationItem->get_final_granular_style_id(),
          $savedAt,
          $employeeId,
          Curation_Decision_Source::automatic_by_context_sku()
      );
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $curationItem Curation Item
   *
   * @return bool
   */
  private function isEligibleForAutomaticSave(Curation_Item $curationItem) : bool {
    return $curationItem->get_type() === Curation_Item_Type::context() && empty($curationItem->get_saved_at());
  }
}
