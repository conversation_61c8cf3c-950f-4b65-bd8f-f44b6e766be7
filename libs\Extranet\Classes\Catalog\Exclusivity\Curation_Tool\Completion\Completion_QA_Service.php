<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

class Completion_QA_Service {

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
   */
  private $batchDataService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service
   */
  private $viService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
   */
  private $batchStatusUpdater;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_Checker
   */
  private $qaB<PERSON><PERSON>hecker;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected $notifyCurationRejected Completion_QA_Email_Notifier
   */
  private $notifyCurationRejected;


  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service          $batchDataService       Completion_Batch_Data_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service   $viService              Completion_Verification_Item_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater        $batchStatusUpdater     Completion_Batch_Status_Updater
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_Checker            $qaBatchChecker         QA Batch Checker
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected $notifyCurationRejected Completion_QA_Notify_Curation_Rejected
   */
  public function __construct(
      Completion_Batch_Data_Service $batchDataService,
      Completion_Verification_Item_Service $viService,
      Completion_Batch_Status_Updater $batchStatusUpdater,
      Completion_QA_Batch_Checker $qaBatchChecker,
      Completion_QA_Notify_Curation_Rejected $notifyCurationRejected
  ) {
    $this->batchDataService       = $batchDataService;
    $this->viService              = $viService;
    $this->batchStatusUpdater     = $batchStatusUpdater;
    $this->qaBatchChecker         = $qaBatchChecker;
    $this->notifyCurationRejected = $notifyCurationRejected;
  }

  /**
   * @param int $batchId    Batch Id
   * @param int $employeeId Employee Id
   *
   * @return bool
   */
  public function complete(int $batchId, int $employeeId) : bool {
    $checkResult = $this->qaBatchChecker->getCompletionQaStatus($batchId);

    if ($checkResult->is_pending()) {
      return false;
    }

    $batchData = $this->batchDataService->get($batchId);

    // if the batch is not in status curation complete, then don't change it
    if ($batchData->getStatus() !== Batch::STATUS_MANUAL_CURATION_COMPLETE) {
      return true;
    }

    if (!$checkResult->is_approved()) {
      $this->markBatchInProgress($batchId, $employeeId, $batchData->getAssignedEmployeeId());
      return true;
    }

    $this->markBatchCompleted($batchId, $employeeId);

    // Get fresh batch data and check its status
    $batchData = $this->batchDataService->get($batchId);
    if ($batchData->getStatus() === Batch::STATUS_MANUAL_QA_COMPLETE) {
      $this->viService->update_locked_data_if_null($batchId, $employeeId);
    }

    return true;
  }

  /**
   * @param int $batchId            Batch Id
   * @param int $employeeId         Employee Id
   * @param int $assignedEmployeeID Assigned Employee ID
   *
   * @return void
   */
  private function markBatchInProgress(int $batchId, int $employeeId, int $assignedEmployeeID = null) {
    $this->batchStatusUpdater->changeStatus($batchId, Batch::STATUS_MANUAL_IN_PROGRESS, $employeeId);

    if (!empty($assignedEmployeeID)) {
      $this->notifyCurationRejected->send($batchId, $assignedEmployeeID, $employeeId);
    }
  }

  /**
   * @param int $batchId    Batch Id
   * @param int $employeeId Employee Id
   *
   * @return void
   */
  private function markBatchCompleted(int $batchId, int $employeeId) {
    $this->batchStatusUpdater->changeStatus($batchId, Batch::STATUS_MANUAL_QA_COMPLETE, $employeeId);
  }

  /**
   *
   * @param int $batchId BatchId
   *
   * @return bool true if all skus are approved
   */
  public function isAllSkuApproved(int $batchId): bool {
    $checkResult = $this->qaBatchChecker->getCompletionQaStatus($batchId);

    return $checkResult->is_approved();
  }
}
