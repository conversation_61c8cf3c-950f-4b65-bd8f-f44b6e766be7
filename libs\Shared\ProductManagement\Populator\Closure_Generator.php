<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\ProductManagement\Populator;

class Closure_Generator {
  /**
   * @param string $fqcn subject for populator closure generation
   *
   * @return \Closure
   */
  public function generate(string $fqcn) : \Closure {
    $properties = $this->get_properties($fqcn);

    return $this->generate_populator_closure($fqcn, $properties);
  }

  /**
   * @param string $fqcn fully qualified class name
   *
   * @return array
   */
  private function get_properties(string $fqcn) : array {
    $enumerator_function = function () use ($fqcn) {
      return get_class_vars($fqcn);
    };
    $bound_enumerator = \Closure::bind($enumerator_function, null, $fqcn);

    return $bound_enumerator();
  }

  /**
   * @param string $fqcn             fully qualified class name
   * @param array  $class_properties class properties to use for population
   *
   * @return \Closure
   */
  private function generate_populator_closure(string $fqcn, array $class_properties) : \Closure {
    $populator = function ($subject, array $map) use ($class_properties) {
      foreach ($class_properties as $property => $default_value) {
        /** @phpstan-ignore-next-line */
        $subject->$property = $map[$property] ?? $default_value;
      }
    };

    return \Closure::bind($populator, null, $fqcn);
  }
}
