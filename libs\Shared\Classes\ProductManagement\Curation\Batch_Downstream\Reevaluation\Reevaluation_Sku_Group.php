<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Reevaluation_Sku_Group {
  /**
   * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU[]
   */
  private $skus = [];

  /**
   * @var string[]
   */
  private $parents = [];

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku sku
   *
   * @return void
   */
  public function add_sku(Verified_Batch_SKU $verified_batch_sku) {
    $this->skus[$verified_batch_sku->get_sku()] = $verified_batch_sku;

    foreach ($verified_batch_sku->get_parents() as $parent) {
      if (!empty($parent) && !in_array($parent, $this->parents)) {
        $this->parents[] = $parent;
      }
    }
  }

  /**
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU[]
   */
  public function get_skus() : array {
    return array_values($this->skus);
  }

  /**
   * @return array
   */
  public function get_parents() : array {
    return $this->parents;
  }

  /**
   * @return string
   */
  public function to_string() {
    $sku_info = [];

    foreach ($this->skus as $sku) {
      $sku_info[] = sprintf(
          'id: %d, sku: %s, is_eligible: %s, is_collision: %s, kit_component: %s, kit_parents: %s, collection: %d ',
          $sku->get_id(),
          $sku->get_sku(),
          $sku->is_eligible() ? 'yes' : 'no',
          $sku->is_collision() ? 'yes' : 'no',
          $sku->is_kit_component() ? 'yes' : 'no',
          implode(', ', $sku->get_parents()),
          $sku->get_collection_id()
      );
    }

    $info = sprintf('Group - %d skus: ' . PHP_EOL . '%s' . PHP_EOL, count($this->skus), implode(PHP_EOL, $sku_info));

    return $info;
  }
}
