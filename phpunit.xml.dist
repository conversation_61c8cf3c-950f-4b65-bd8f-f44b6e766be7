<?xml version="1.0" encoding="UTF-8"?>
<!-- https://phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         defaultTestSuite="UnitTestSuite"
         bootstrap="vendor/autoload.php"
         convertDeprecationsToExceptions="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="false"
         convertWarningsToExceptions="false"
         executionOrder="default"
         resolveDependencies="true"
>
    <php>
        <ini name="error_reporting" value="-1"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <ini name="xdebug.mode" value="coverage"/>
        <server name="APP_ENV" value="test" force="true" />
        <server name="SHELL_VERBOSITY" value="-1" />
        <env name="XDEBUG_MODE" value="coverage" />
        <env name="SYMFONY_DEPRECATIONS_HELPER" value="disabled"/>
        <env name="WF_INI_PATH" value="docker/local/wayfair/etc/wf-config.ini"/>
        <ini name="memory_limit" value="-1"/>
        <server name="WF_ENV" value="dev" force="true" />
        <env name="APP_SECRET" value="1"/>
        <server name="KERNEL_CLASS" value="App\Kernel" />
    </php>

    <testsuites>
        <testsuite name="UnitTestSuite">
            <directory>tests/Unit</directory>
            <directory>tests/libs</directory>
        </testsuite>
        <testsuite name="IntegrationTestSuite">
            <directory>tests/Integration</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory>src</directory>
            <directory>libs</directory>
        </include>
        <exclude>
            <file>src/Kernel.php</file>
            <file>src/Application/Controller/InternalController.php</file>
            <file>src/Infrastructure/Connection/QueryFailedException.php</file>
            <file>**/*DAO.php</file>
            <directory>src/Application/DTO</directory>
            <directory>src/Application/Exception</directory>
            <directory>src/Infrastructure/Exception</directory>
        </exclude>
    </coverage>
</phpunit>
