<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use APP\Domain\Model\FeatureToggle;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Traits\Logging_Trait;

class Context_Data_Decision_Service {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory
   */
  private $factory;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service
   */
  private $region_service;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $curation_tool_dao;

  /**
   * Memoization for loading all possible options
   *
   * @var array
   */
  private $cached_curation_decisions = [];

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
  */
  private $curation_tool_postgres_dao;

  private FeatureTogglesInterface $feature_toggle;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory    $factory                    the data factory
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service                 $region_service             Region Service
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                       $curation_tool_dao          Curation Tool DAO
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO $curation_tool_postgres_dao Curation Tool Postgres DAO
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                          $feature_toggle             Feature Toggle
   * @param LoggerInterface|null                                                                        $logger                     Logger
   */
  public function __construct(Context_Data_Factory $factory, Region_Service $region_service, Curation_Tool_DAO $curation_tool_dao, Curation_Tool_Postgres_DAO $curation_tool_postgres_dao, FeatureTogglesInterface $feature_toggle, ?LoggerInterface $logger = null) {
    $this->factory                    = $factory;
    $this->region_service             = $region_service;
    $this->curation_tool_dao          = $curation_tool_dao;
    $this->logger                     = $logger;
    $this->curation_tool_postgres_dao = $curation_tool_postgres_dao;
    $this->feature_toggle             = $feature_toggle;
  }

  /**
   * Starting from the price tier and manufacturer, it tries to get the closest curation decision available
   *
   * @param int $batch_id        Batch ID
   * @param int $manufacturer_id Manufacturer ID
   * @param int $price_tier      Price Tier
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data
   * @throws \WF\Shared\Exception\Invalid_Argument_Exception
   */
  public function find_closest_match(int $batch_id, int $manufacturer_id, int $price_tier) : Context_Data {
    $this->log_info('Loading all possible curation decisions', ['batch_id' => $batch_id]);
    $possible_decisions = $this->get_all_possible_curation_decisions($batch_id);
    $match                 = null;
    $match_price_tier_diff = 0;

    foreach ($possible_decisions as $row) {
      if ($row['manufacturer_id'] !== $manufacturer_id) {
        continue;
      }

      $current_price_tier_diff = abs($row['price_tier'] - $price_tier);
      // if it is an exact match, stop looking
      if ($current_price_tier_diff === 0) {
        $match = $row;
        break;
      }

      // if it is the first match for the target manufacturer OR if it is has a match, but the current one is closer
      if ($match === null || $current_price_tier_diff < $match_price_tier_diff) {
        $match                 = $row;
        $match_price_tier_diff = $current_price_tier_diff;
      }
    }

    if (empty($match)) {
      return $this->factory->create_empty();
    }

    return $this->factory->create($match['manufacturer_id'], $match['style_id'], $match['substyle_id'], $match['price_tier']);
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  private function get_all_possible_curation_decisions(int $batch_id) : array {
    /**
     * load from memory if is was already cached
     */
    if (!empty($this->cached_curation_decisions[$batch_id])) {
      return $this->cached_curation_decisions[$batch_id];
    }

    $region = $this->region_service->get_region($batch_id);
    if ($this->feature_toggle->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $result = $this->curation_tool_postgres_dao->get_manufacturers($region->getId());
    } else {
      $result = $this->curation_tool_dao->get_manufacturers($region->getId());
    }
    // store in memory - memoization
    $this->cached_curation_decisions[$batch_id] = $result;

    return $result;
  }
}