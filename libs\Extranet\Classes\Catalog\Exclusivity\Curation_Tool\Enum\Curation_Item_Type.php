<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Curation_Item_Type extends AbstractEnumeration {
  /**
   * @var string
   */
  private static $simple = "simple";

  /**
   * @var string
   */
  private static $context = "context";

  /**
   * @var string
   */
  private static $candidate = "candidate";

  /**
   * @var string
   */
  private static $shared = "shared";

  /**
   * @var string
   */
  private static $kit_parent = "kit_parent";

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public static function simple() : self {
    return self::get_value_for_option(self::$simple); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public static function context() : self {
    return self::get_value_for_option(self::$context); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public static function candidate() : self {
    return self::get_value_for_option(self::$candidate); /** @phpstan-ignore-line */
  }


  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public static function shared() : self {
    return self::get_value_for_option(self::$shared); /** @phpstan-ignore-line */
  }


  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public static function kit_parent() : self {
    return self::get_value_for_option(self::$kit_parent); /** @phpstan-ignore-line */
  }
}
