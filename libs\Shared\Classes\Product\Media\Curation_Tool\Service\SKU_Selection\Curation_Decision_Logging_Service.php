<?php

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection;

use WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection\Curation_Decision_Logging_BigQuery_DAO;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Decision_Logging_Model;
use Psr\Log\LoggerInterface;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Curation_Decision_Logging_Service {

      /**
      * @var LoggerInterface
      */
     private $logger;

    private Curation_Decision_Logging_BigQuery_DAO $dao;

    /**
    * Constructor
    *
    *@param Curation_Decision_Logging_BigQuery_DAO $dao Curation_Decision_Logging_BigQuery_DAO
    *@param LoggerInterface $logger Logger
     */
    public function __construct(Curation_Decision_Logging_BigQuery_DAO $dao ,LoggerInterface $logger)
    {
        $this->dao = $dao;
        $this->logger = $logger;
    }

    public function insertDataBigQuery(): string
    {
        $this->logger->info('Inserting data to BigQuery from service class');
        return $this->dao->insertDataBigQuery();
    }

    /**
    * @param Curation_Decision_Logging_Model[] &$curationDecisionLoggingModelList
    * @param Curation_Request_SKU_Base_Model $curation_request_sku_base_model
    * @param Verified_Batch_SKU $verified_batch_sku
    * @param Batch $batch
    */
    public function createListOfCurationDecisionLoggingModel(array &$curationDecisionLoggingModelList , Curation_Request_SKU_Base_Model $curation_request_sku_base_model , Verified_Batch_SKU $verified_batch_sku , Batch $batch) : void
    {
        $this->logger->info('Creating a new Curation_Decision_Logging_Model for SKU: ' . $curation_request_sku_base_model->get_sku());
        $curationDecisionLoggingModel = new Curation_Decision_Logging_Model($curation_request_sku_base_model, $verified_batch_sku, $batch);
        $curationDecisionLoggingModelList[] = $curationDecisionLoggingModel;
    }

    /**
    * @param Curation_Decision_Logging_Model[] $curationDecisionLoggingModelList
    */
    public function insertAllSkusToBigQuery(array $curationDecisionLoggingModelList): void
    {
        $this->logger->info('Inside service to insert all SKUs to BigQuery and size of the list is: ' . count($curationDecisionLoggingModelList));
        $this->dao->insertAllSkusToBigQueryTable($curationDecisionLoggingModelList);
    }
}

