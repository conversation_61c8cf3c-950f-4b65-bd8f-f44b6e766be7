<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;


class Batch_Persister implements Batch_Persister_Interface {
  /**
   * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO
   */
  private $dao;


  /**
   * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO
   */
  private $batch_sku_postgresql_dao;


  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;


  /**
   * Batch_Persister constructor.
   *
   * @param \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO $dao DAO
   * @param \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO $psql_dao DAO
   * @param FeatureTogglesInterface $featureToggles  featureToggles
   */
  public function __construct(Batch_SKU_DAO $dao,
                              Batch_SKU_Postgresql_DAO $psql_dao,
                              FeatureTogglesInterface $featureToggles) {
    $this->dao = $dao;
    $this->batch_sku_postgresql_dao=$psql_dao;
    $this->featureToggles = $featureToggles;
  }

  /**
   * Persists the given batch to the database, so it receives the batch id
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch
   *
   * @return void
   */
  public function persist_batch(Batch $batch) {
    if ($batch->get_id() === null) {
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $id = $this->batch_sku_postgresql_dao->save_batch($batch);
      }else{
        $id = $this->dao->save_batch($batch);
      }
      $batch->set_id($id);
    }
  }

  /**
   * Cleans up the persisted batch from a database. It is used when something had failed and db needs a cleanup so no batches are left hanging.
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch
   *
   * @return void
   */
  public function attempt_batch_cleanup(Batch $batch) {
    if ($batch->get_id() !== null){
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $records = $this->batch_sku_postgresql_dao->cleanup_batch($batch);
      }else{
        $records = $this->dao->cleanup_batch($batch);
      }
      if($records) {
        $batch->set_id(null);
      }
    }
  }
}
