<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface;

class Context_Data_Decision_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory|\Prophecy\Prophecy\ObjectProphecy
     */
    private $factory;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service|\Prophecy\Prophecy\ObjectProphecy
     */
    private $region_service;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $curation_tool_dao;


    /**
     * @var \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $region;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data|\Prophecy\Prophecy\ObjectProphecy
     */
    private $context_data;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service
     */
    private $subject;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $curation_tool_postgres_dao;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->factory = $this->prophesize(Context_Data_Factory::class);
        $this->region_service = $this->prophesize(Region_Service::class);
        $this->curation_tool_dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->curation_tool_postgres_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->region = $this->prophesize(World_Region_Interface::class);
        $this->context_data = $this->prophesize(Context_Data::class);

        $this->factory->create(Argument::any())->willReturn($this->context_data);
        $this->factory->create_empty()->willReturn($this->context_data->reveal());
        $this->region->getId()->willReturn(1);
        $this->region_service->get_region(Argument::any())->willReturn($this->region);

        $this->subject = new Context_Data_Decision_Service($this->factory->reveal(), $this->region_service->reveal(), $this->curation_tool_dao->reveal(), $this->curation_tool_postgres_dao->reveal(), $this->featureToggles->reveal());
    }

    /**
     * @test
     * @dataProvider closest_match_expectations
     *
     * @param array $data     Data
     * @param array $expected Expected
     *
     * @return void
     */
    public function it_finds_closest_match(array $data, array $expected)
    {
        $this->curation_tool_dao->get_manufacturers(Argument::cetera())->willReturn($data['options']);

        if (!empty($expected)) {
            $this->factory->create($expected['manufacturer_id'], $expected['style_id'], $expected['substyle_id'], $expected['price_tier'])
                ->willReturn($this->context_data)
                ->shouldBeCalled();
        } else {
            $this->factory->create_empty()
                ->willReturn($this->context_data)
                ->shouldBeCalled();
        }

        $this->subject->find_closest_match($data['batch_id'], $data['manufacturer_id'], $data['price_tier']);
    }

    /**
     * @test
     * @dataProvider closest_match_expectations
     *
     * @param array $data     Data
     * @param array $expected Expected
     *
     * @return void
     */
    public function it_finds_closest_match_feature_toggle_on(array $data, array $expected)
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->curation_tool_postgres_dao->get_manufacturers(Argument::cetera())->willReturn($data['options']);

        if (!empty($expected)) {
            $this->factory->create($expected['manufacturer_id'], $expected['style_id'], $expected['substyle_id'], $expected['price_tier'])
                ->willReturn($this->context_data)
                ->shouldBeCalled();
        } else {
            $this->factory->create_empty()
                ->willReturn($this->context_data)
                ->shouldBeCalled();
        }

        $this->subject->find_closest_match($data['batch_id'], $data['manufacturer_id'], $data['price_tier']);
    }

    /**
     * @return array
     */
    public function closest_match_expectations()
    {
        return [
            'empty array' => [
                ['batch_id' => 1, 'manufacturer_id' => 1, 'price_tier' => 1, 'options' => []],
                []
            ],
            'no match' => [
                [
                    'batch_id' => 1, 'manufacturer_id' => 1, 'price_tier' => 1,
                    'options' => [
                        ['manufacturer_id' => 2, 'style_id' => 1, 'substyle_id' => 1, 'price_tier' => 1]
                    ]
                ],
                []
            ],
            'exact match' => [
                [
                    'batch_id' => 1, 'manufacturer_id' => 1, 'price_tier' => 2,
                    'options' => [
                        ['manufacturer_id' => 1, 'style_id' => 1, 'substyle_id' => 1, 'price_tier' => 1],
                        ['manufacturer_id' => 1, 'style_id' => 2, 'substyle_id' => 2, 'price_tier' => 2],
                        ['manufacturer_id' => 1, 'style_id' => 3, 'substyle_id' => 3, 'price_tier' => 3]
                    ]
                ],
                [
                    'manufacturer_id' => 1, 'style_id' => 2, 'substyle_id' => 2, 'price_tier' => 2
                ]
            ],
            'exact match, different manufacturer' => [
                [
                    'batch_id' => 1, 'manufacturer_id' => 1, 'price_tier' => 2,
                    'options' => [
                        ['manufacturer_id' => 2, 'style_id' => 1, 'substyle_id' => 1, 'price_tier' => 1],
                        ['manufacturer_id' => 2, 'style_id' => 2, 'substyle_id' => 2, 'price_tier' => 2],
                        ['manufacturer_id' => 2, 'style_id' => 3, 'substyle_id' => 3, 'price_tier' => 3]
                    ]
                ],
                [

                ]
            ],
            'closest match' => [
                [
                    'batch_id' => 1, 'manufacturer_id' => 1, 'price_tier' => 2,
                    'options' => [
                        ['manufacturer_id' => 1, 'style_id' => 1, 'substyle_id' => 1, 'price_tier' => 1],
                        ['manufacturer_id' => 1, 'style_id' => 3, 'substyle_id' => 3, 'price_tier' => 3]
                    ]
                ],
                [
                    'manufacturer_id' => 1, 'style_id' => 1, 'substyle_id' => 1, 'price_tier' => 1
                ]
            ],
        ];
    }
}
