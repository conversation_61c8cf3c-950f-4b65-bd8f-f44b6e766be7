<?php

namespace App\Application\Request\ParamConverter;

use App\Application\Exception\BatchLoadFailureException;
use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;

use function is_subclass_of;

class CompletionBatchParamConverter implements ParamConverterInterface, LoggerAwareInterface
{
    use LoggerTrait;
    use LoggerAwareTrait;

    private Completion_Batch_Data_Service $completionBatchDataService;
    private TokenStorageInterface $tokenStorage;

    public function __construct(
        Completion_Batch_Data_Service $completionBatchDataService,
        TokenStorageInterface $tokenStorage
    ) {
        $this->completionBatchDataService = $completionBatchDataService;
        $this->tokenStorage = $tokenStorage;
    }

    private function getEmployeeId(): ?int
    {
        $token = $this->tokenStorage->getToken();
        /** @var \WF\PartnerHome\User\Authentication\SymfonyBundle\Entity\PartnerHomeUser|null $user */
        $user = $token !== null ? $token->getUser() : null;

        return $user !== null ? $user->employeeId() : null;
    }

    /**
     * Stores the object in the request.
     *
     * @param ParamConverter $configuration Contains the name, class and options of the object
     *
     * @return bool True if the object has been successfully set, else false
     */
    public function apply(Request $request, ParamConverter $configuration): bool
    {
        $name = $configuration->getName();

        if (null === $request->attributes->get($name, false)) {
            $configuration->setIsOptional(true);
        }

        $batchId = $request->query->getInt('batch_id', 0);

        $object = null;

        if ($batchId !== 0) {
            $object = $this->getBatchData($batchId);
        } elseif ($configuration->isOptional() === false) {
            throw new BadRequestHttpException('Missing or invalid required parameter "batch_id"');
        }

        $request->attributes->set($name, $object);

        return true;
    }

    /**
     * Checks if the object is supported.
     *
     * @return bool True if the object is supported, else false
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === Completion_Batch_Data::class
            || is_subclass_of($configuration->getClass(), Completion_Batch_Data::class);
    }

    /**
     * @param int $batchId
     * @return Completion_Batch_Data
     * @throws BatchLoadFailureException
     */
    protected function getBatchData(int $batchId): Completion_Batch_Data
    {
        $this->info(
            'Loading batch data',
            [
                'batch_id' => $batchId,
                'employee_id' => $this->getEmployeeId()
            ]
        );

        try {
            $object = $this->completionBatchDataService->get($batchId);
        } catch (Throwable $exception) {
            $this->error(
                'Failed to load batch data',
                [
                    'batch_id' => $batchId,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception
                ]
            );

            throw new BatchLoadFailureException();
        }

        return $object;
    }
}
