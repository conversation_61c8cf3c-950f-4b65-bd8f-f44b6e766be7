/**
 * This component represents a single row of the Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Column, Grid, Text, TEXT_ALIGNMENTS, WIDTHS, FLEX_KEYWORDS} from '@wayfair/homebase-extranet';
import CurationSkuCode from './curation_sku_code';
import CurationSkuImage from './curation_sku_image';
import CurationSkuSupplier from './curation_sku_supplier';
import CurationSkuPrice from './curation_sku_price';
import CurationSkuManufacturer from './curation_sku_manufacturer';
import CurationSkuName from './curation_sku_name';
import QACurationSkuDecision from './qa_curation_sku_decision';
import './qa_sku_row.scss';
import CurationToolShapes, {
  QA_STATUS_PENDING,
  QA_STATUS_ACCEPTED,
  QA_STATUS_REJECTED,
  QA_STATUS_UPDATED,
} from './curation_tool_shapes';
import QASaveColumn from './qa_save_column';
import QACurationStyleDecisionNote from './qa_curation_style_decision_note';

import CurationSkuDecision from './curation_sku_decision';
import {Loading} from "@homebase/core";

class QASkuRow extends React.Component {
  static propTypes = {
    curationConfig: CurationToolShapes.curationConfigShape.isRequired,
    curationItem: CurationToolShapes.curationItemShape.isRequired,
    isChanged: PropTypes.bool,
    isSelected: PropTypes.bool,
    onSelectionChange: PropTypes.func,
    onSave: PropTypes.func,
    onCurationItemDecisionChange: PropTypes.func,
    isAutomaticCurationPostQaEnabled: PropTypes.bool,
    isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
    suggestedStyleRejectionReasons: PropTypes.arrayOf(
      CurationToolShapes.reasonShape
    ),
  };

  static defaultProps = {
    isSelected: false,
    isChanged: false,
    isAutomaticCurationPostQaEnabled: false,
    suggestedStyleRejectionReasons: [],
    onSelectionChange() {},
    onSave() {},
    onCurationItemDecisionChange() {},
  };

  state = {
    isChangeEnabled: false,
  };


  shouldDecisionBeDisabled = () =>
    this.props.isAssortmentWorkflowOffshoreUser &&
    this.props.curationItem.shouldMoveToHeaderBrand;

  isStyleSelectedReasonRequired = () => {
    if (this.isExcludedFromWhiteLabeling()) {
      return false;
    }

    if (this.hasNoSuggestedStylesAndSubstyles()) {
      return false;
    }

    if (
      !this.isSuggestedStyleAndSubstyleSelected() &&
      !this.hasStyleRejectionReason()
    ) {
      return true;
    }

    return false;
  };

  hasNoSuggestedStylesAndSubstyles = () => {
    return (
      this.props.curationItem.suggestedStyles.length === 0 &&
      this.props.curationItem.suggestedSubstyles.length === 0
    );
  };

  isSuggestedStyleAndSubstyleSelected = () => {
    return (
      this.props.curationItem.suggestedStyles.includes(
        this.props.curationItem.decision.styleId
      ) &&
      this.props.curationItem.suggestedSubstyles.includes(
        this.props.curationItem.decision.substyleId
      )
    );
  };

  hasStyleRejectionReason = () => {
    return (
      null != this.props.curationItem.decision.suggestedStyleRejectionReasonId
    );
  };

  isExcludedFromWhiteLabeling = () => {
    return null != this.props.curationItem.decision.exclusionReasonId;
  };

  isEditable = () => !this.props.curationItem.readonly;

  isCheckboxEnabled = () =>
    this.isEditable() &&
    (this.props.curationItem.qaStatus === QA_STATUS_PENDING ||
      this.props.curationItem.qaStatus === QA_STATUS_REJECTED);

  isSaveEnabled = () => {
    const isSaveEnabled =
      this.props.isChanged &&
      (this.props.curationItem.decision.exclusionReasonId > 0 ||
        this.props.curationItem.decision.manufacturerId > 0) &&
      !this.isStyleSelectedReasonRequired();

    return isSaveEnabled;
  };

  hasStyleSuggestions = () => !!this.props.curationItem.suggestedStyles.length;

  onChange = value => {
    this.setState({isChangeEnabled: value});
  };

  isDecisionChanged(currentDecision, nextDecision) {
    return (
      currentDecision.exclusionReasonId !== nextDecision.exclusionReasonId ||
      currentDecision.priceTier !== nextDecision.priceTier ||
      currentDecision.styleId !== nextDecision.styleId ||
      currentDecision.substyleId !== nextDecision.substyleId ||
      currentDecision.manufacturerId !== nextDecision.manufacturerId ||
      currentDecision.suggestedStyleRejectionReasonId !==
        nextDecision.suggestedStyleRejectionReasonId
    );
  }

  isStyleMatchSuggested = () =>
    this.props.curationItem.decision &&
    this.props.curationItem.decision.isSuggestedStyleMatched;

  render() {
    return (
      <div className="QASkuRow">
        <Grid>
          <Column size={WIDTHS.WIDTH_1} flexDirection={FLEX_KEYWORDS.COLUMN}>
            <CurationSkuCode
              sku={this.props.curationItem.sku}
              relatedKits={this.props.curationItem.relatedKits}
              isPredictedWinner={this.props.curationItem.isPredictedWinner}
              isEditable={this.isCheckboxEnabled()}
              isSelected={this.props.isSelected}
              onSelectionChange={this.props.onSelectionChange}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuName
              name={this.props.curationItem.name}
              url={this.props.curationItem.url}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuManufacturer
              name={this.props.curationItem.manufacturer}
              brwid={this.props.curationItem.manufacturerBrwId}
              brandType={this.props.curationItem.brandType}
              brandName={this.props.curationItem.brandName}
              isHoldoutManufacturer={
                this.props.curationItem.isHoldoutManufacturer
              }
              isWhitelabelDownstream={
                this.props.curationItem.isWhitelabelDownstream
              }
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.class}</Text>
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuSupplier
              names={this.props.curationItem.suppliers}
              isCanadian={this.props.curationItem.isCanadianSupplier}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuPrice
              sku={this.props.curationItem.sku}
              price={this.props.curationItem.price}
              priceOptionsCount={this.props.curationItem.priceOptionsCount}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_2}>
            <CurationSkuImage
              image={this.props.curationItem.image}
              sku={this.props.curationItem.sku}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_3} alignSelf={FLEX_KEYWORDS.CENTER}>
            {this.isEditable() && this.state.isChangeEnabled ? (
              <CurationSkuDecision
                isRecentlyCloned={this.props.curationItem.isRecentlyCloned}
                lastCloneDate={this.props.curationItem.lastCloneDate}
                canMakeDecision={!this.shouldDecisionBeDisabled()}
                curationConfig={this.props.curationConfig}
                decision={this.props.curationItem.decision}
                isWrongContinent={this.props.curationItem.isWrongContinent}
                suggestedStyles={this.props.curationItem.suggestedStyles}
                suggestedSubstyles={this.props.curationItem.suggestedSubstyles}
                suggestedStyleRejectionReasons={
                  this.props.suggestedStyleRejectionReasons
                }
                onDecisionChange={decision => {
                  this.props.onCurationItemDecisionChange(
                    this.props.curationItem.sku,
                    decision
                  );
                }}
                shouldMoveToHeaderBrand={
                  this.props.curationItem.shouldMoveToHeaderBrand
                }
                shouldMoveToTailBrand={
                  this.props.curationItem.shouldMoveToTailBrand
                }
                isAssortmentWorkflowOffshoreUser={
                  this.props.isAssortmentWorkflowOffshoreUser
                }
              />
            ) : (
              <QACurationSkuDecision
                isRecentlyCloned={this.props.curationItem.isRecentlyCloned}
                lastCloneDate={this.props.curationItem.lastCloneDate}
                curationConfig={this.props.curationConfig}
                decision={this.props.curationItem.decision}
                hasStyleSuggestions={this.hasStyleSuggestions()}
                isAutomaticCurationPostQaEnabled={
                  this.props.isAutomaticCurationPostQaEnabled
                }
              />
            )}
          </Column>
          <Column size={WIDTHS.WIDTH_1} flexDirection={FLEX_KEYWORDS.COLUMN}>
            {this.isEditable() && (
              this.props.curationItem.isSaving ? <Loading/> : <QASaveColumn
                onSave={qaStatus =>
                  this.props.onSave({
                    sku: this.props.curationItem.sku,
                    qaStatus,
                  })
                }
                onChange={this.onChange}
                isApproved={
                  this.props.curationItem.qaStatus === QA_STATUS_ACCEPTED
                }
                isUpdated={
                  this.props.curationItem.qaStatus === QA_STATUS_UPDATED
                }
                isPending={
                  this.props.curationItem.qaStatus === QA_STATUS_PENDING
                }
                isSaveEnabled={this.isSaveEnabled()}
                isChangeEnabled={this.state.isChangeEnabled}
                isAutomaticCurationPostQaEnabled={
                  this.props.isAutomaticCurationPostQaEnabled
                }
              />
            )}
            {this.hasStyleSuggestions() &&
              !this.isStyleMatchSuggested() &&
              this.props.curationItem.decision.styleDecisionNotes && (
                <QACurationStyleDecisionNote
                  decisionNote={
                    this.props.curationItem.decision.styleDecisionNotes
                  }
                />
              )}
          </Column>
        </Grid>
      </div>
    );
  }
}

export default QASkuRow;
