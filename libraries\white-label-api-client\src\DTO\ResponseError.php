<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\DTO;

class ResponseError extends Response
{
    /**
     * @var int
     */
    private $errorCode;

    /**
     * @var string
     */
    private $errorMessage;

    /**
     * ResponseError constructor.
     *
     * @param bool   $isRequestSuccessful
     * @param int    $errorCode
     * @param string $errorMessage
     */
    public function __construct(bool $isRequestSuccessful, int $errorCode, string $errorMessage)
    {
        parent::__construct($isRequestSuccessful);

        $this->errorCode = $errorCode;
        $this->errorMessage = $errorMessage;
    }

    /**
     * @return int
     */
    public function getErrorCode(): int
    {
        return $this->errorCode;
    }

    /**
     * @return string
     */
    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }
}
