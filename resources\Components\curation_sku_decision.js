/**
 * Manage exclusion and style decisions in curation tool
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import Translation from '@wayfair/translation';
import PropTypes from 'prop-types';
import {Dropdown, Alert, Column, FLEX_KEYWORDS, Grid, Flex, Text, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import CurationToolShapes from './curation_tool_shapes';
import { rebrandProject } from './curation_page';

const COL_EXCLUSION_WIDTH_VERTICAL = 6;
const COL_EXCLUSION_WIDTH_HORIZONTAL = 2;
const COL_STYLE_WIDTH_VERTICAL = 6;
const COL_STYLE_WIDTH_HORIZONTAL = 10;

const BRAND_READONLY_OPTIONS_COUNT = 1;

class CurationSkuDecision extends React.Component {
  static propTypes = {
    curationConfig: CurationTool<PERSON>ha<PERSON>.curationConfigShape.isRequired,
    decision: CurationToolShapes.decisionShape.isRequired,
    isRecentlyCloned: PropTypes.bool,
    lastCloneDate: PropTypes.string,
    displayHorizontal: PropTypes.bool,
    onDecisionChange: PropTypes.func,
    isWrongContinent: PropTypes.bool,
    suggestedStyles: PropTypes.arrayOf(PropTypes.number),
    suggestedSubstyles: PropTypes.arrayOf(PropTypes.number),
    shouldMoveToHeaderBrand: PropTypes.bool,
    shouldMoveToTailBrand: PropTypes.bool,
    isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
    canMakeDecision: PropTypes.bool,
    locatedArea: PropTypes.string,
    changeCheckedElement: PropTypes.bool,
    curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
      .isRequired,
    selectedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
    suggestedStyleRejectionReasons: PropTypes.arrayOf(
      CurationToolShapes.reasonShape
    ).isRequired,
  };

  static defaultProps = {
    decision: {
      priceTier: null,
      styleId: null,
      substyleId: null,
      manufacturerId: null,
      exclusionReasonId: null,
      automaticExcludedReason: null,
      suggestedStyleRejectionReasonId: null,
    },
    lastCloneDate: null,
    isRecentlyCloned: false,
    displayHorizontal: false,
    isWrongContinent: false,
    suggestedStyles: [],
    suggestedSubstyles: [],
    onDecisionChange() {},
    shouldMoveToHeaderBrand: false,
    shouldMoveToTailBrand: false,
    isAssortmentWorkflowOffshoreUser: false,
    canMakeDecision: true,
    locatedArea: 'sku',
    suggestedStyleRejectionReasons: [],
    changeCheckedElement: false,
    curationItems: [],
    selectedRows: [],
  };

  shouldComponentUpdate(nextProps) {
    // check if curation decision has changed
    return (
      this.props.decision.exclusionReasonId !==
        nextProps.decision.exclusionReasonId ||
      this.props.decision.priceTier !== nextProps.decision.priceTier ||
      this.props.decision.styleId !== nextProps.decision.styleId ||
      this.props.decision.substyleId !== nextProps.decision.substyleId ||
      this.props.decision.manufacturerId !==
        nextProps.decision.manufacturerId ||
      this.props.decision.suggestedStyleRejectionReasonId !==
        nextProps.decision.suggestedStyleRejectionReasonId ||
      this.props.changeCheckedElement !== nextProps.changeCheckedElement
    );
  }

  sortStyles = (styleA, styleB) => {
    if (styleA.text < styleB.text) {
      return -1;
    }
    if (styleA.text > styleB.text) {
      return 1;
    }

    // names must be equal
    return 0;
  };



  getStyles = () => {
    const suggestedStyles = this.props.suggestedStyles;

    // if there are no suggested styles, keep the initial style list
    if (suggestedStyles.length === 0) {
      return this.props.curationConfig.styles;
    }

    // prepend suggested style texts with #<rank> and sort the new list, so suggestions are first
    return this.props.curationConfig.styles
      .map(style => {
        const index = suggestedStyles.indexOf(style.value);
        if (index < 0) {
          return style;
        }

        const text = `#${index + 1} ${style.text}`;

        return {...style, text};
      })
      .sort(this.sortStyles);
  };

  getSubstyles = () => {
    const suggestedSubstyles = this.props.suggestedSubstyles;

    const substyles = this.props.curationConfig.substyles.filter(
      item => item.styleId === this.props.decision.styleId
    );

    // if there are no suggested substyles, keep the initial substyle list (filtered by selected style)
    if (suggestedSubstyles.length === 0) {
      return substyles;
    }

    // prepend suggested substyle texts with #<rank> and sort the new list, so suggestions are first
    let suggestionNumber = 1;
    return substyles
      .map(substyle => {
        const index = suggestedSubstyles.indexOf(substyle.value);
        if (index < 0) {
          return substyle;
        }

        const text = `#${suggestionNumber++} ${substyle.text}`;

        return {...substyle, text};
      })
      .sort(this.sortStyles);
  };

  getManufacturers = () => {
    return this.props.curationConfig.manufacturers.filter(item =>
      this.isValidManufacturer(
        item.value,
        this.props.decision.priceTier,
        this.props.decision.substyleId
      )
    );
  };

  isValidManufacturer = (manufacturerId, priceTier, substyleId) => {
    return this.props.curationConfig.styleManufacturers.some(
      styleManufacturer =>
        styleManufacturer.substyleId === substyleId &&
        styleManufacturer.manufacturerId === manufacturerId &&
        styleManufacturer.priceTier === priceTier
    );
  };

  getSuggestedStyleRejectionReasons = () => {
    return this.props.suggestedStyleRejectionReasons.map(reason => {
      return {
        text: reason.name,
        value: reason.id,
      };
    });
  };

  handleExclusionReasonChange = exclusionReason => {
    const exclusionReasonId = exclusionReason?.value;
    const priceTier = this.props.decision.priceTier;
    const styleId = this.props.decision.styleId;
    const substyleId = this.props.decision.substyleId;
    const manufacturerId = this.props.decision.manufacturerId;
    const suggestedStyleRejectionReasonId = this.props.decision
      .suggestedStyleRejectionReasonId;

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  handlePriceTierChange = priceTierOption => {
    const exclusionReasonId = null;
    const priceTier = priceTierOption ? priceTierOption.value : 1;
    const styleId = this.props.decision.styleId;
    const substyleId = this.props.decision.substyleId;
    const suggestedStyleRejectionReasonId = this.props.decision
      .suggestedStyleRejectionReasonId;

    const validManufacturers = this.props.curationConfig.manufacturers.filter(
      item => this.isValidManufacturer(item.value, priceTier, substyleId)
    );

    const manufacturerId =
      validManufacturers.length > 0 ? validManufacturers[0].value : null;

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  handleStyleChange = style => {
    const exclusionReasonId = null;
    const priceTier = this.props.decision.priceTier;
    const styleId = style ? style.value : null;
    const substyleId = null;
    const manufacturerId = null;
    const suggestedStyleRejectionReasonId = null;

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  handleSubstyleChange = substyle => {
    const exclusionReasonId = null;
    const priceTier = this.props.decision.priceTier;
    const styleId = this.props.decision.styleId;
    const substyleId = substyle ? substyle.value : null;
    const suggestedStyleRejectionReasonId = null;

    let manufacturerId = null;
    if (rebrandProject?.id !== undefined && rebrandProject?.name !== undefined && this.assortmentDecisionManufacturerId !== null) {
      manufacturerId = this.assortmentDecisionManufacturerId;
    } else {
      const validManufacturers = this.props.curationConfig.manufacturers.filter(
        item => this.isValidManufacturer(item.value, priceTier, substyleId)
      );
      manufacturerId = validManufacturers.length > 0 ? validManufacturers[0].value : null;
    }

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  handleBrandChange = manufacturer => {
    const exclusionReasonId = null;
    const priceTier = this.props.decision.priceTier;
    const styleId = this.props.decision.styleId;
    const substyleId = this.props.decision.substyleId;
    const manufacturerId = manufacturer ? manufacturer.value : null;
    const suggestedStyleRejectionReasonId = this.props.decision
      .suggestedStyleRejectionReasonId;

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  handleSuggestedStyleRejectionReasonChange = reason => {
    const exclusionReasonId = null;
    const priceTier = this.props.decision.priceTier;
    const styleId = this.props.decision.styleId;
    const substyleId = this.props.decision.substyleId;
    const manufacturerId = this.props.decision.manufacturerId;
    const suggestedStyleRejectionReasonId = reason ? reason.value : null;

    this.props.onDecisionChange({
      exclusionReasonId,
      priceTier,
      styleId,
      substyleId,
      manufacturerId,
      suggestedStyleRejectionReasonId,
    });
  };

  isSubstyleDisabled = () => {
    return (
      !this.getSubstyles().length || this.isStyleDecisionDropdownsDisabled()
    );
  };

  isBrandDisabled = () => {
    return (
      !this.getManufacturers().length || this.isStyleDecisionDropdownsDisabled()
    );
  };

  isSuggestedStyleReasonDisabled = () => {
    if (!this.props.decision.styleId || !this.props.decision.substyleId) {
      return true;
    }

    let result = null;

    if (this.props.locatedArea === 'sku') {
      result = this.isSingleSkuReasonAvailable();
    }

    if (this.props.locatedArea === 'header') {
      result = this.isHeaderReasonNotAvailable();
    }

    return result || this.isStyleDecisionDropdownsDisabled();
  };

  isHeaderReasonNotAvailable = () => {
    let needSelectStyleRejectedReason = true;
    this.props.curationItems.forEach(item => {
      if (!this.props.selectedRows.includes(item.sku)) {
        return;
      }
      if (
        (item.suggestedStyles.length > 0 &&
          !item.suggestedStyles.includes(this.props.decision.styleId)) ||
        (item.suggestedSubstyles.length > 0 &&
          !item.suggestedSubstyles.includes(this.props.decision.substyleId))
      ) {
        needSelectStyleRejectedReason = false;
      }
    });

    return needSelectStyleRejectedReason;
  };

  isSingleSkuReasonAvailable = () => {
    if (
      this.props.suggestedStyles.length === 0 &&
      this.props.suggestedSubstyles.length === 0
    ) {
      return true;
    }

    if (
      !this.props.suggestedStyles.includes(this.props.decision.styleId) ||
      !this.props.suggestedSubstyles.includes(this.props.decision.substyleId)
    ) {
      return false;
    }

    return true;
  };

  isBrandReadonly = () => {
    return this.getManufacturers().length === BRAND_READONLY_OPTIONS_COUNT;
  };

  isStyleDecisionDropdownsDisabled = () =>
    (!this.props.canMakeDecision && !this.props.displayHorizontal) ||
    this.props.decision.exclusionReasonId;

  getColumnExclusionWidth = () => {
    return this.props.displayHorizontal
      ? COL_EXCLUSION_WIDTH_HORIZONTAL
      : COL_EXCLUSION_WIDTH_VERTICAL;
  };

  getColumnStyleWidth = () => {
    return this.props.displayHorizontal
      ? COL_STYLE_WIDTH_HORIZONTAL
      : COL_STYLE_WIDTH_VERTICAL;
  };

  getOptionLabel = option => (option ? option.text : '');

  getStyleLabel = () =>
    this.props.suggestedStyles.length > 0 ? (
      <Translation msgid="CurationTool.primaryStyleWithDSSuggestions" />
    ) : (
      <Translation msgid="CurationTool.primaryStyleDropdownLabel" />
    );

  getSubstyleLabel = () =>
    this.props.suggestedSubstyles &&
    this.props.suggestedSubstyles.length > 0 ? (
      <Translation msgid="CurationTool.secondarySubstyleWithDSSuggestions" />
    ) : (
      <Translation msgid="CurationTool.secondaryStyleDropdownLabel" />
    );


  componentDidMount() {
    if(this.props.isDefaultStyleForDsModal && this.props.decision.styleId===null && this.props.decision.substyleId===null && !this.props.decision.exclusionReasonId) {
      let selectedStyleDs  = this.getStyles().find(item => item.text.includes("#1"))
      if(selectedStyleDs) {
        this.handleStyleChange(selectedStyleDs)
      }
      setTimeout(()=>{
        let selectedSubStyleDs  = this.getSubstyles().find(item => item.text.includes("#1"))
        if(selectedSubStyleDs) {
          this.handleSubstyleChange(selectedSubStyleDs)
        }
      },200)
    }
  }

  getExcludeFromWlValue = () =>
    this.props.curationConfig.exclusionReasons.find(
      reason => reason.value === this.props.decision.exclusionReasonId
    );

  getPriceTierValue = () =>
    this.props.curationConfig.priceTiers.find(
      item => item.value === this.props.decision.priceTier
    );

  getStyleValue = () =>
    this.getStyles().find(item => item.value === this.props.decision.styleId);

  getSubstyleValue = () =>
    this.getSubstyles().find(
      item => item.value === this.props.decision.substyleId
    ) || null;

  assortmentDecisionManufacturerId = null;
  getManufacturerValue = () => {
     if (rebrandProject?.id !== undefined && rebrandProject?.name !== undefined) {
      if (this.assortmentDecisionManufacturerId === null) {
        this.assortmentDecisionManufacturerId = this.props.decision.manufacturerId;
      }
      return this.props.curationConfig.manufacturers.find(
        item => item.value === this.assortmentDecisionManufacturerId
      );
    }
    return this.props.curationConfig.manufacturers.find(
      item => item.value === this.props.decision.manufacturerId
    );
  };

  getSuggestedStyleRejectionReasonValue = () => {
    const dropDownValue = {
      text: '',
      value: null,
    };

    const prop = this.props.suggestedStyleRejectionReasons.find(
      item => item.id === this.props.decision.suggestedStyleRejectionReasonId
    );

    if (prop) {
      dropDownValue.text = prop.name;
      dropDownValue.value = prop.id;
    }

    return dropDownValue;
  };

  getHeaderBrandAlertVariation = () =>
    this.props.isAssortmentWorkflowOffshoreUser ? 'alert' : 'warning';

  render() {
    const isStyleDecisionDropdownsDisabled = this.isStyleDecisionDropdownsDisabled();

    return (
      <Grid>
        <Column
          size={this.getColumnExclusionWidth()}
          flexDirection={FLEX_KEYWORDS.COLUMN}
        >
          {this.props.isWrongContinent && (
            <Alert variation="warning">
              <Translation msgid="CurationTool.CurationSkuDecisionWarningWrongContinent" />
            </Alert>
          )}
          {this.props.isRecentlyCloned && (
            <Alert variation="warning">
              <Translation msgid="CurationTool.SkuRecentlyClonedMessage" />
            </Alert>
          )}
          <Dropdown
            label={
              <Translation msgid="CurationTool.excludeFromWLDropdownLabel" />
            }
            options={this.props.curationConfig.exclusionReasons}
            getOptionLabel={this.getOptionLabel}
            value={this.getExcludeFromWlValue()}
            onValueChange={this.handleExclusionReasonChange}
          />
          {this.props.lastCloneDate && (
            <Text align={TEXT_ALIGNMENTS.CENTER}>
              <Translation
                msgid="CurationTool.ClonedOnDate"
                params={{
                  lastCloneDate: this.props.lastCloneDate,
                }}
              />
            </Text>
          )}
          {this.props.shouldMoveToHeaderBrand && (
            <Alert variation={this.getHeaderBrandAlertVariation()}>
              <Translation msgid="CurationTool.alertMoveToHeaderBrand" />
            </Alert>
          )}
          {this.props.shouldMoveToTailBrand && (
            <Alert variation="warning">
              <Translation msgid="CurationTool.alertMoveToTailBrand" />
            </Alert>
          )}
        </Column>
        <Column size={this.getColumnStyleWidth()}>
          <Flex
            justifyContent={FLEX_KEYWORDS.SPACE_BETWEEN}
            flexWrap={
              this.props.displayHorizontal
                ? FLEX_KEYWORDS.NOWRAP
                : FLEX_KEYWORDS.WRAP
            }
          >
            <Dropdown
              isFullWidth
              label={
                <Translation msgid="CurationTool.priceTierDropdownLabel" />
              }
              options={this.props.curationConfig.priceTiers}
              getOptionLabel={this.getOptionLabel}
              value={this.getPriceTierValue()}
              onValueChange={this.handlePriceTierChange}
              disabled={isStyleDecisionDropdownsDisabled}
            />
            <Dropdown
              isFullWidth
              label={this.getStyleLabel()}
              options={this.getStyles()}
              getOptionLabel={this.getOptionLabel}
              value={this.getStyleValue()}
              onValueChange={this.handleStyleChange}
              disabled={isStyleDecisionDropdownsDisabled}
            />
            <Dropdown
              isFullWidth
              label={this.getSubstyleLabel()}
              options={this.getSubstyles()}
              value={this.getSubstyleValue()}
              getOptionLabel={this.getOptionLabel}
              disabled={this.isSubstyleDisabled()}
              onValueChange={this.handleSubstyleChange}
            />
            <Dropdown
              isFullWidth
              label={
                <Translation msgid="CurationTool.brandDropdownLabel" />
              }
              options={this.getManufacturers()}
              getOptionLabel={this.getOptionLabel}
              value={this.getManufacturerValue()}
              disabled={this.isBrandDisabled()}
              readOnly={this.isBrandReadonly()}
              onValueChange={this.handleBrandChange}
            />

            <Dropdown
              label={
                <Translation msgid="CurationTool.suggestedStyleFeedbackModalReason" />
              }
              name="suggestedStyleRejectionReasons"
              options={this.getSuggestedStyleRejectionReasons()}
              getOptionLabel={this.getOptionLabel}
              value={this.getSuggestedStyleRejectionReasonValue()}
              onValueChange={this.handleSuggestedStyleRejectionReasonChange}
              disabled={this.isSuggestedStyleReasonDisabled()}
              isFullWidth
            />
          </Flex>
        </Column>
      </Grid>
    );
  }
}

export default CurationSkuDecision;
