<?php
/**
 * email_helper.php
 *
 * A bunch of convenience functions that use the email service.
 * At worst, it saves you from having to instantiate your own
 * PuREST class stuff. At best it'll save a lot of time because
 * common logic can be put in here instead of writing boilerplate
 * stuff every time you need to do something :)
 *
 * Every function in here should use the send_email function to
 * actually send the e-mail. If you want to use the old sendmail
 * system, you can use send_email in utility functions. Template
 * emails should use the relevant helper or email_request_functions.
 *
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Helpers;

use WF\BrandWorkflows\PuREST\Contract\ClientInterface;
use WF\BrandWorkflows\PuREST\Contract\RequestFactoryInterface;

class Email_Helper {
  public const SERVICE = 'emails';
  private ClientInterface $client;
  private RequestFactoryInterface $requestFactory;

  public function __construct(ClientInterface $client, RequestFactoryInterface $requestFactory) {
    $this->client = $client;
    $this->requestFactory = $requestFactory;
  }

  /**
   * Sends email. This is the granddaddy of functions if you need complete control over the e-mail.
   * As such there are no function defaults, so it's on you to make sure that you fill EVERY argument.
   * There is also minimal verification so make sure that you're ONLY passing in the correct value types.
   * NO NULLS.
   *
   * There is probably a better convenience function in the file if you need it--feel free to use
   * this instead, but you can cut down on a lot of shooting yourself in the foot you use a premade function.
   * Or even better, add your function to the file so anyone else can also use it.
   *
   * If you set $log to false, the e-mail will NEVER hit the db, so you will have no idea if the e-mail
   * failed or not. Be warned. (It should still log to kibana but you don't know if it was YOUR e-mail)
   *
   * If you specify an emid, make damned sure that it already exists in tblEmailOut or you will have big
   * problems. If you're not sure, just leave it blank and one will automatically get generated when it
   * gets written to the DB.
   *
   * Specifying an mmid signals that there is already a record in tblEmailMessage for the email you're
   * sending. If there isn't, it won't fail spectacularly, but there won't be a link between the record
   * in tblEmailOut and tblEmailMessage. Leave blank if you're not sure.
   *
   * @param string $email_id          the emid in tblEmailOut (DANGER!)
   * @param string $cuid              the customer ID
   * @param string $message_id        the mmid in tblEmailMessage
   * @param string $order_id          an associated order ID
   * @param array  $from              ["name" => Bob Barker, "email" => <EMAIL>]
   * @param array  $to                array of above array formats (empty array for none)
   * @param array  $cc                array of above array formats (empty array for none)
   * @param array  $bcc               array of above array formats (empty array for none)
   * @param string $subject           subject of email
   * @param string $html              html content
   * @param string $text              text content
   * @param string $kana              kana content
   * @param string $kana_id           kana id
   * @param string $file_path         the path to a file you want to attach (unix share, not Windows)
   * @param string $data_attach       some encoded data to send as an attachment (csv, base64 image, etc.)
   * @param string $data_name         the name of the file, if you're sending it as a data attachment
   * @param string $data_mime_type    the MIME type if the data attachment you're sending (text/csv, image/jpeg, etc.)
   * @param string $data_encoding     the encoding for the data attachment (normally it's 'quoted-printable' if it's not base64)
   * @param string $scheduled         when to send the e-mail, empty or 'Y-m-d H:i:s' format, America/New_York timezone
   * @param bool   $send_in_dev       whether you want do send this even in dev
   * @param bool   $log               whether you want to log this to the DB (DANGER!)
   * @param array  $reply_to          ["name" => Bob Barker, "email" => <EMAIL>]
   * @param int    $emtid             The template identifier for the email
   * @param string $intent_identifier Intent identifier for deduping in storm sendmail
   *
   * @throws \Exception
   *
   * @return string mmreqid
   */
  public function send_email(
      $email_id,
      $cuid,
      $message_id,
      $order_id,
      array $from,
      array $to,
      array $cc,
      array $bcc,
      $subject,
      $html,
      $text,
      $kana,
      $kana_id,
      $file_path,
      $data_attach,
      $data_name,
      $data_mime_type,
      $data_encoding,
      $scheduled,
      $send_in_dev,
      $log,
      $reply_to = null,
      $emtid = -1,
      $intent_identifier = null
  ) {
    $body = array(
                    'emid' => $email_id,
                    'cuid' => $cuid,
                    'mmid' => $message_id,
                    'mmorid' => $order_id,
                    'fileAttachmentPath' => $file_path,
                    'dataAttachment' => $data_attach,
                    'dataAttachmentName' => $data_name,
                    'dataAttachmentMimeType' => $data_mime_type,
                    'dataAttachmentEncoding' => $data_encoding,
                    'kanaContent' => $kana,
                    'kanaid' => $kana_id,
                    'HTMLContent' => $html,
                    'textContent' => $text,
                    'from' => self::fix_address($from),
                    'subject' => $subject,
                    'retries' => 0,
                    'log' => '',
                    'scheduled' => $scheduled,
                    'send' => $send_in_dev ? 'send' : 'nosend',
                    'skipLog' => $log ? 'false' : 'true',
                    'replyTo' => !empty($reply_to) ? self::fix_address($reply_to) : null,
                    'emtid' => $emtid,
                    'intent_identifier' => !empty($intent_identifier) ? $intent_identifier : self::create_intent_identifier()
                  );

    $body['recipients'] = array('bcc' => [], 'to' => [], 'cc' => []);

    foreach ($to as $toadd) {
      $body['recipients']['to'][] = self::fix_address($toadd);
    }

    foreach ($cc as $ccadd) {
      $body['recipients']['cc'][] = self::fix_address($ccadd);
    }

    foreach ($bcc as $bccadd) {
      $body['recipients']['bcc'][] = self::fix_address($bccadd);
    }

    $result = $this->client->request(
        $this->requestFactory->createRequest(self::SERVICE, 'queue_json', json_encode($body))
    );

    if (!isset($result['code']) || $result['code'] !== 200) {
      throw new \Exception("Failed to send email! code:" . (isset($result['code']) ? $result['code'] : "null" . " via email helper/send email"));
    }
    return $result['body']['mmreqid'];
  }

  /**
   * Set the name of an array equal to the e-mail if the name doesn't exist and trim everything up
   *
   * @param array $address ["name" => Bob Barker, "email" => <EMAIL>]
   *
   * @throws \Exception
   *
   * @return array of above format
   */
  private static function fix_address(array $address) {
    if (!isset($address['email']) || empty($address['email'])) {
      throw new \Exception("Address must contain email!");
    }

    $fixed_address = [];
    $fixed_address['email'] = trim($address['email']);
    if (!isset($address['name']) || empty($address['name'])) {
      $fixed_address['name'] = $fixed_address['email'];
    } else {
      $fixed_address['name'] = trim($address['name']);
    }

    return $fixed_address;
  }

  /**
   * Return an identifier of intent to dedupe storm sendmail sends
   *
   * @return string
   */
  public static function create_intent_identifier(): string {
    return chr(random_int(ord('a'), ord('z'))) . md5((string) microtime(true));
  }

}
