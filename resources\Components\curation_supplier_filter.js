/**
 * QA Status filter for the curation page
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Dropdown} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';

const CurationSupplierFilter = ({values, onChange, options}) => {
  return (
    <Dropdown
      isMulti
      placeholder={Translation({
          msgid: 'CurationTool.CurationSupplierFilterPlaceholder'
      })}
      options={options}
      onValueChange={onChange}
      value={values}
      label={Translation({
        msgid: 'CurationTool.CurationSupplierFilterLabel',
      })}
    />
  );
};

CurationSupplierFilter.propTypes = {
  onChange: PropTypes.func.isRequired,
  values: PropTypes.array,
  options: PropTypes.array.isRequired,
};

CurationSupplierFilter.defaultProps = {
  values: [],
};

export default CurationSupplierFilter;
