<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Mytsyk <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Curation_Batch_Data_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch as BatchModel;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;

class Batch_Management_DAO implements Completion_Batch_Data_Storage,
                                      Completion_Verification_Item_Storage,
                                      Completion_Batch_Status_Updater_Storage,
                                      Completion_QA_Batch_SKU_Storage
{
    use Logging_Trait;

    private ProductConnection $pdo;

    /**
     * @param ProductConnection $pdo
     * @param LoggerInterface|null $logger
     */
    public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null)
    {
        $this->pdo = $pdo;
        $this->logger = $logger;
    }

  /**
   * @param int $batch_id    Batch ID
   * @param int $status_id   Status ID
   * @param int $employee_id Employee ID
   *
   * @return void
   * @throws ExecutionException
   */
  public function change_status(int $batch_id, int $status_id, int $employee_id) : void {
    $this->info(
        sprintf('Applying to Batch with ID "%d" the new Status "%d"', $batch_id, $status_id),
        ['batch_id' => $batch_id, 'status_id' => $status_id, 'employee_id' => $employee_id]
    );

    $this->pdo->beginTransaction();

    $sql = '
    DECLARE @status_id INT = :status_id
    DECLARE @batch_id INT = :batch_id
    UPDATE csn_product.dbo.tblCurationBatch WITH(ROWLOCK) SET StatusID = @status_id
    WHERE ID = @batch_id

    INSERT INTO csn_product.dbo.tblCurationBatchStatusHistory
    (BatchID, StatusID, EmployeeID)
    VALUES (@batch_id, @status_id, :employee_id)
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValue(':status_id', $status_id, PDO::PARAM_INT);
    $statement->bindValue(':employee_id', $employee_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $this->pdo->rollBack();
      $errorMessage = sprintf(
        'Cannot change batch status to %d: %s',
        $batch_id,
        implode(',', $statement->errorInfo())
      );

      $exception = ExecutionException::forStatement($statement, $errorMessage);
      $this->log_throwable_error(
          $exception,
          $errorMessage,
          ['batch_id' => $batch_id, 'status_id' => $status_id, 'employee_id' => $employee_id]
      );
      throw $exception;
    }

    if ($this->pdo->commit() === false) {
            $errorMessage = sprintf(
                'Cannot commit batch status change to %d: %s',
                $batch_id,
                implode(',', $statement->errorInfo())
            );
      $exception = ExecutionException::forStatement($statement, $errorMessage);
      $this->log_throwable_error(
          $exception,
          $errorMessage,
          ['batch_id' => $batch_id, 'status_id' => $status_id, 'employee_id' => $employee_id]
      );
      throw $exception;
    }
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_batch_data(int $batch_id) : array {
    $this->info(
        sprintf('Loading Batch data for BatchID "%d"', $batch_id),
        ['batch_id' => $batch_id]
    );

    $sql = 'SELECT
              batch.AssignedEmID,
              batch.StatusID,
              batch.BrandCatalogID,
              batch.CurationBatchProcessTypeID,
              batch.CreatedAt,
              approve_event.EmployeeID ApprovedBy,
              approve_event.CreatedAt ApprovedAt
            FROM csn_product.dbo.tblCurationBatch batch WITH(NOLOCK)
            LEFT JOIN (SELECT BatchID, StatusID, EmployeeID, CreatedAt = max(CreatedAt) 
                  FROM csn_product.dbo.tblCurationBatchStatusHistory 
                  GROUP BY BatchID, EmployeeID, StatusId
                  ) approve_event ON approve_event.BatchID = batch.ID AND approve_event.StatusId in (:manual_qa_completed, :automated_qa_complete)
            WHERE batch.ID = :batch_id';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, \PDO::PARAM_INT);
    $statement->bindValue(':manual_qa_completed', BatchModel::STATUS_MANUAL_QA_COMPLETE, \PDO::PARAM_INT);
    $statement->bindValue(':automated_qa_complete', BatchModel::STATUS_AUTOMATED_QA_COMPLETE, \PDO::PARAM_INT);

    if (!$statement->execute()) {
      $errorMessage = sprintf('Cannot get batch data %d: %s', $batch_id, implode(',', $statement->errorInfo()));
            $exception = ExecutionException::forStatement($statement, $errorMessage);
      $this->log_throwable_error(
          $exception,
          $errorMessage,
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    $result = $statement->fetch();

    return $result !== false ? $result : [];
  }

  /**
   * @param int $batch_id  Batch ID
   * @param int $qa_status QA Status
   *
   * @return array
   */
  public function get_qa_batch_skus(int $batch_id, int $qa_status) : array {
    $this->info(
        sprintf('Loading QA Batch SKUs for BatchID "%d"', $batch_id),
        ['batch_id' => $batch_id, 'qa_status' => $qa_status]
    );

    $sql = '
      SELECT
        item.ViSKU AS sku,
        qa.Message AS message
      FROM csn_product.dbo.tblVerificationItem item WITH(NOLOCK)
      OUTER APPLY (
        SELECT TOP 1 history.Message FROM csn_product.dbo.tblVerificationItemQAStatusHistory history WITH(NOLOCK)
        WHERE history.VerificationItemID = item.ViID
        ORDER BY history.ID DESC
      ) AS qa
      WHERE item.ViQaStatusID = :qa_status AND item.ViBatchID = :batch_id';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, \PDO::PARAM_INT);
    $statement->bindValue(':qa_status', $qa_status, \PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load batch skus');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load batch skus for ButchID "%d"', $batch_id),
          ['batch_id' => $batch_id, 'qa_status' => $qa_status]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_qa_batch_skus_with_exclude_reason(int $batch_id) : array {
    $this->info(
        sprintf('Loading Batch SKUs with Exclude reason for BatchID "%d"', $batch_id),
        [
          'batch_id' => $batch_id
        ]
    );

    $sql = '
      SELECT
        ViSKU
      FROM csn_product.dbo.tblVerificationItem item WITH(NOLOCK)
      WHERE
        ViBatchID = :batch_id
        AND ViQaStatusID = 5
        AND ViExcludedReasonID IS NOT NULL
        AND ViExcludedReasonID != 0';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot Batch SKUs with Exclude reason');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot Batch SKUs with Exclude reason for BatchID "%d"', $batch_id),
          [
            'batch_id' => $batch_id
          ]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @return array
   */
  public function get_process_types(): array {
    $sql = '
    SELECT
      ID,
      name
    FROM csn_product.dbo.tblplCurationBatchProcessType WITH(NOLOCK)
    ';

    $statement = $this->pdo->prepare($sql);

    if ($statement->execute() === false) {
      $errorMessage = 'Cannot load Process types';
      $exception = ExecutionException::forStatement($statement, $errorMessage);
      $this->log_throwable_error(
          $exception,
          $errorMessage,
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_KEY_PAIR);
  }

    /**
     * Update LockedDate and LockedEmtID information if it is null
     *
     * @param int $batch_id Batch ID
     * @param int $employee_id Employee ID
     *
     * @return bool
     */
    public function update_locked_data_if_null(int $batch_id, int $employee_id): bool
    {
        $sql = '
        DECLARE @batchId INT = :batch_id
        DECLARE @employeeId INT = :employee_id

        UPDATE csn_product.dbo.tblVerificationItem
        SET 
          ViLockedEmID = CASE WHEN ViLockedDate IS NULL THEN @employeeId ELSE ViLockedEmID END,
          ViLockedDate = CASE WHEN ViLockedDate IS NULL THEN GETDATE() ELSE ViLockedDate END
        WHERE
          ViBatchID = @batchId
        ';

        $statement = $this->pdo->prepare($sql);
        $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);
        $statement->bindValue('employee_id', $employee_id, PDO::PARAM_INT);

        if ($statement->execute() === false) {
            $exception = ExecutionException::forStatement($statement, 'Failed to update verification item locked info');
            $this->log_throwable_error(
                $exception,
                sprintf('Failed to update verification item locked info for batchId "%s"', $batch_id),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $employee_id,
                    'sql' => $sql
                ]
            );

            throw $exception;
        }

        return true;
    }


  /**
   * @param int $emp_id Employee ID
   *
   * @return array
   */
  public function get_batch_list(int $emp_id) : array {
    $this->info(
        sprintf('Loading Batch list for BatchID "%d"', $emp_id),
        ['emp_id' => $emp_id]
    );

    $sql = 'SELECT
              batch.ID as batch_id,
              batch.AssignedEmID,
              batch.StatusID
            FROM csn_product.dbo.tblCurationBatch batch WITH(NOLOCK)
            WHERE batch.AssignedEmID = :AssignedEmID';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':AssignedEmID', $emp_id, \PDO::PARAM_INT);
//    $statement->bindValue(':manual_qa_completed', BatchModel::STATUS_MANUAL_QA_COMPLETE, \PDO::PARAM_INT);
//    $statement->bindValue(':automated_qa_complete', BatchModel::STATUS_AUTOMATED_QA_COMPLETE, \PDO::PARAM_INT);

    if (!$statement->execute()) {
      $errorMessage = sprintf('Cannot get batch data %d: %s', $emp_id, implode(',', $statement->errorInfo()));
      $exception = ExecutionException::forStatement($statement, $errorMessage);
      $this->log_throwable_error(
          $exception,
          $errorMessage,
          ['emp_id' => $emp_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

}
