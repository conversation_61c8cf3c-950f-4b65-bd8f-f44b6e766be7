<?php

/**
 * Class to access Curation GraphQL API
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\Graphql;

use App\Application\Logger\LoggerTrait;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Psr\Log\LoggerAwareTrait;
use Throwable;
use WF\Shared\Logging\Logger;
use function array_key_exists;
use function array_merge;
use function sprintf;

class CurationGraphApi
{
    use LoggerTrait;
    use LoggerAwareTrait;
    public const APPLICATION_JSON = 'application/json';

    public const BRAND_CATALOG_BY_BATCH_ID = '
    query getCurationBatchesByIds($batch_ids: [Int]) {
        getCurationBatchesByIds(batchIds: $batch_ids) {
          brandCatalog{
            brandCatalogId
          }
        }
    }';

    public const SKU_AND_CONTEXT_COLLECTION_BY_BATCH_ID = '
    query getCurationBatchesByIds($batch_ids: [Int]) {
        getCurationBatchesByIds(batchIds: $batch_ids) {
          batchProducts{
            sku
            contextCollection{
              contextCollectionId
            }
          }
        }
    }';

    public const PART_COST_API_GRAPHQL_QUERY = '
    query supplierPartCostData($supplier_part_ids: Int) {
      supplierPartCostData(
        search: { filters: { supplierPartIds: { equalTo: [$supplier_part_ids] } } }
      ) {
        baseCostTerms {
          supplierPartId
          baseCost
          procurementMethodTypeId
          currency
          regionId
        }
      }
    }';

    private GraphClient $graph_client;

    private int $RETRY_ATTEMPTS = 0;
    
    /**
     * Curation Graph API constructor.
     *
     * @param GraphClient $graph_client GraphQL client
    */
    public function __construct(GraphClient $graph_client, Logger $logger)
    {
        $this->graph_client = $graph_client;
        $this->logger = $logger;
    }

    /**
     * @throws GraphException
     * @throws Throwable
     */
    private function sendGraphQLRequest(string $query, array $variables, array $options = []): array
    {
        try {
            $response = $this->graph_client->getJsonResponseToQuery(
                $query,
                $variables,
                array_merge(
                    [
                        'Accept' => self::APPLICATION_JSON,
                        'Content-Type' => self::APPLICATION_JSON,
                    ],
                    $options
                ),
                true,
                10
            );
            if (array_key_exists('data', $response)) {
                return $response['data'];
            }
    
            $this->info('No data returned from the response');
            return [];
        } catch (Throwable $error) {
            $this->error('Unable to call curation graph service', ['query' => $query, 'variables' => $variables, 'error' => $error->getMessage(), 'trace' => $error->getTrace()]);
            throw $error;
        }
    }

    /**
     * @throws GraphException
     */
    private function sendCostApiGraphQLRequest(string $query, array $variables, string $token, string $url): array
    {
        $responseData = [];

        if ($this->RETRY_ATTEMPTS < 5) { // retrying Cost-API for 5 times to refresh the token and try again
            try {
                $response = $this->graph_client->getCostApiJsonResponseToQuery(
                    $query,
                    $url,
                    true,
                    10,
                    $variables,
                    array_merge(
                        [
                            'Accept' => self::APPLICATION_JSON,
                            'Authorization' => 'Bearer ' . $token
                        ],
                    ),
                );

                $this->logger->warn('sendCostApiGraphQLRequest is called and response is ---------->', [$response]);
                if (array_key_exists('data', $response)) {
                    $this->logger->warn('$response[data] is ---------->', [$response['data']]);
                    $responseData = $response['data'];
                }
            } catch (Exception $exception) {
                if ($exception instanceof ClientException && $exception->getCode() === 401) {
                    $this->RETRY_ATTEMPTS++;
                    // If 401 Unauthorized error occurs, the token may have expired, so refresh the token and try again
                    $this->logger->warn('Access token has expired. Refreshing the token and retrying...');
                    $this->clearAccessToken(); // Clear the expired token from cache

                    return $this->sendCostApiGraphQLRequest($query, $variables, $token, $url); // Retry with a fresh token
                } else {
                    $this->logger->error(
                        sprintf('Failed to load cost api  data: %s', $exception->getMessage()),
                        ['response' => $variables, 'exception' => $exception]
                    );
                }
            }
        } else {
            $this->error('Cost-API Access token has expired. retried 5 times ');
        }
        return $responseData;
    }

    public function get_brand_catalog_by_batch_id(array $batch_ids)
    {
        $response = $this->sendGraphQLRequest(self::BRAND_CATALOG_BY_BATCH_ID, ['batch_ids' => $batch_ids]);
        if ($response) {
            $batchDetails = $response['getCurationBatchesByIds'][0];
            // Get the brand catalog id
            $brand_catalog_id = $batchDetails['brandCatalog']['brandCatalogId'];
            return $brand_catalog_id;
        }
        return null;
    }

    /**
     * @throws Throwable
     */
    public function get_sku_and_context_collect_id_by_batch_id(array $batch_ids): array
    {
        $response = $this->sendGraphQLRequest(self::SKU_AND_CONTEXT_COLLECTION_BY_BATCH_ID, ['batch_ids' => $batch_ids]);
        $result = [];
        $batch_details = $response['getCurationBatchesByIds'][0];
        $batch_products = $batch_details['batchProducts'];
        // Get the brand catalog id
        foreach ($batch_products as $batch_product) {
            $sku = $batch_product['sku'];
            $context_xn_id = $batch_product['contextCollection']['contextCollectionId'];
            $item = [
                'sku' => $sku,
                'context_xn_id' => $context_xn_id
            ];

            // Add the item to the result array
            $result[] = $item;
        }
        return $result;
    }

    /**
     * @throws GraphException
     */
    public function get_part_cost_by_supplier_part_id_using_graphql_api(int $supplier_part_id, string $token, string $url): array
    {
        $this->logger->warn('get_part_cost_by_supplier_part_id_using_graphql_api is called....');
        return $this->sendCostApiGraphQLRequest(self::PART_COST_API_GRAPHQL_QUERY, ['supplier_part_ids' => $supplier_part_id], $token, $url);
    }
}
