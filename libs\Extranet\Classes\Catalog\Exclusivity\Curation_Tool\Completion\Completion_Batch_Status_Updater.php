<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Completion_Batch_Status_Updater {

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater_Storage
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Comple<PERSON>\Completion_Batch_Status_Updater_Storage $dao <PERSON>mple<PERSON>_Batch_Status_Updater_Storage
   */
  public function __construct(Completion_Batch_Status_Updater_Storage $dao, Batch_Management_Postgres_DAO $dao_psql, FeatureTogglesInterface $featureToggles) {
    $this->dao = $dao;
    $this->dao_psql = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batchId    Batch Id
   * @param int $status     Status
   * @param int $employeeId Employee Id
   *
   * @return void
   */
  public function changeStatus(int $batchId, int $status, int $employeeId) {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $this->dao_psql->change_status($batchId, $status, $employeeId);
    } else {
      $this->dao->change_status($batchId, $status, $employeeId);
    }
  }
}
