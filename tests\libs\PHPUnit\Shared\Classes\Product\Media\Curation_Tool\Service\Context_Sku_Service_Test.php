<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Mytsyk <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\Product\Media\Curation_Tool\Service;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\Context_SKU_Factory;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Service;
use WF\Shared\Classes\ProductManagement\WhiteLabel\White_Label_Brand_Picklist;
use WF\Shared\DAOs\Product\Media\Curation_Tool\Context_Sku_DAO;
use WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU;

class Context_Sku_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Shared\DAOs\Product\Media\Curation_Tool\Context_Sku_DAO
     */
    private $dao;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Factory\Context_SKU_Factory
     */
    private $factory;

    /**
     * @var \WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU
     */
    private $context_sku;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Service
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Context_Sku_DAO::class);
        $this->factory = $this->prophesize(Context_SKU_Factory::class);
        $this->context_sku = $this->prophesize(Context_SKU::class);

        $this->subject = new Context_SKU_Service($this->dao->reveal(), $this->factory->reveal());
    }

    /**
     * @param string $input_sku          Input SKU
     * @param string $context_sku        Context SKU
     * @param int    $context_collection Context Collection
     *
     * @return void
     *
     * @test
     *
     * @dataProvider return_context_sku_data_provider
     */
    public function return_context_sku(string $input_sku, string $context_sku, int $context_collection)
    {
        $this->dao->get_context_sku($input_sku, White_Label_Brand_Picklist::STANDARD)
            ->willReturn([['PrSKU' => $context_sku, 'PrXnID' => $context_collection]]);
        $this->dao->upsert_context_collection_sku($input_sku, $context_sku, $context_collection)->willReturn(null);

        $this->factory->create($input_sku, $context_sku, $context_collection)->willReturn($this->context_sku);

        $this->context_sku->get_context_sku()->willReturn($context_sku);
        $this->context_sku->get_xn_id()->willReturn($context_collection);

        $this->assertInstanceOf(
            Context_SKU::class,
            $this->subject->get_context_sku($input_sku)
        );
    }

    /**
     * @return array
     */
    public function return_context_sku_data_provider()
    {
        return [
            ['A', 'C', 100],
            ['B', 'D', 500],
        ];
    }

    /**
     * @param string $input_sku          Input SKU
     * @param string $context_sku        Context SKU
     * @param int    $context_collection Context Collection
     *
     * @return void
     *
     * @test
     *
     * @dataProvider look_for_legacy_brand_sku_when_non_wl_not_found_provider
     */
    public function look_for_legacy_brand_sku_when_non_wl_not_found(string $input_sku, string $context_sku, int $context_collection)
    {
        $this->dao->get_context_sku($input_sku, White_Label_Brand_Picklist::STANDARD)
            ->willReturn([]);
        $this->dao->get_context_sku($input_sku, White_Label_Brand_Picklist::LEGACY_WHITE_LABEL_BRAND)
            ->willReturn([['PrSKU' => $context_sku, 'PrXnID' => $context_collection]]);
        $this->dao->upsert_context_collection_sku($input_sku, $context_sku, $context_collection)->willReturn(null);

        $this->factory->create($input_sku, $context_sku, $context_collection)->willReturn($this->context_sku);

        $this->context_sku->get_context_sku()->willReturn($context_sku);
        $this->context_sku->get_xn_id()->willReturn($context_collection);

        $this->assertInstanceOf(
            Context_SKU::class,
            $this->subject->get_context_sku($input_sku)
        );
    }

    /**
     * @return array
     */
    public function look_for_legacy_brand_sku_when_non_wl_not_found_provider()
    {
        return [
            ['A', 'C', 100],
            ['B', 'D', 500],
        ];
    }

    /**
     * @return array
     */
    public function update_context_skus_data_provider()
    {
        return [
            ['A', 'C', 100],
            ['B', 'D', 500],
        ];
    }
}
