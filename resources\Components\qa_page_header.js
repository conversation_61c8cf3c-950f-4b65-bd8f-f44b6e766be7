/**
 * Header of the Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {Checkbox} from '@wayfair/homebase-extranet';
import {Button, IconV2 as Icon, Text, TEXT_ALIGNMENTS, TEXT_STYLE} from '@wayfair/homebase-extranet';
import {Grid, Column} from '@wayfair/homebase-extranet';
import {withStickyHeader} from '@wayfair/homebase-extranet';
import './qa_page_header.scss';
import CurationToolProgressBar from './curation_tool_progress_bar';
import {faPlus, faMinus} from '@fortawesome/free-solid-svg-icons';

const isButtonSaveAllEnabled = (checkedAll, checkedIndeterminate) => {
  return checkedAll || checkedIndeterminate;
};

const QAPageHeader = ({
  title,
  onSelectAll,
  checkedAll,
  checkedIndeterminate,
  onSaveAllClick,
  onExpandClick,
  isExpanded,
  skusSavedCount,
  skusTotalCount,
  isReadOnlyMode,
}) => (
  <header className="QAPageHeader">
    {!isExpanded ? (
      <Grid alignItems="flex-start">
        <Column size={3} resetChildStyles>
          <Button
            onClick={() => onExpandClick(!isExpanded)}
            title={title}
            text
            fullWidth
          >
            <div className="text_xxl text_bold text_overflow">
              <Icon icon={faPlus} />{' '}{title}
            </div>
          </Button>
        </Column>
        <Column size={1}>
          <CurationToolProgressBar
            savedCount={skusSavedCount}
            totalCount={skusTotalCount}
          />
        </Column>
      </Grid>
    ) : (
      <React.Fragment>
        <Grid alignItems="flex-start">
          <Column size={3} resetChildStyles>
            <Button
              onClick={() => onExpandClick(!isExpanded)}
              title={title}
              text
              fullWidth
            >
              <div className="text_xxl text_bold text_overflow">
                <Icon icon={faMinus} />{' '}{title}
              </div>
            </Button>
          </Column>
          <Column size={1}>
            <CurationToolProgressBar
              savedCount={skusSavedCount}
              totalCount={skusTotalCount}
            />
          </Column>
          {!isReadOnlyMode && (
            <Column size={9}>
              <div className="text_right">
                <Button
                  disabled={
                    !isButtonSaveAllEnabled(checkedAll, checkedIndeterminate)
                  }
                  onClick={onSaveAllClick}
                >
                  <Translation msgid="CurationTool.qAPageHeaderApproveAll" />
                </Button>
              </div>
            </Column>
          )}
        </Grid>
        <Grid>
          <Column size={1}>
            <Text fontStyle="bold">
              {!isReadOnlyMode && (
                <Checkbox
                  label={Translation({
                    msgid: 'CurationTool.qAPageHeaderSku',
                  })}
                  onChange={e => onSelectAll(e.target.checked)}
                  checked={checkedAll}
                  indeterminate={checkedIndeterminate}
                />
              )}
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderName" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderManufacturer" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderClass" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderSupplier" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderPrice" />
            </Text>
          </Column>
          <Column size={2}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderImage" />
            </Text>
          </Column>
          <Column size={3}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderCurationStatus" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.qAPageHeaderActions" />
            </Text>
          </Column>
        </Grid>
      </React.Fragment>
    )}
  </header>
);

QAPageHeader.propTypes = {
  title: PropTypes.string.isRequired,
  checkedIndeterminate: PropTypes.bool,
  checkedAll: PropTypes.bool,
  isExpanded: PropTypes.bool,
  skusSavedCount: PropTypes.number,
  skusTotalCount: PropTypes.number,
  onSelectAll: PropTypes.func.isRequired,
  onSaveAllClick: PropTypes.func.isRequired,
  onExpandClick: PropTypes.func.isRequired,
  isReadOnlyMode: PropTypes.bool,
};

QAPageHeader.defaultProps = {
  checkedIndeterminate: false,
  checkedAll: false,
  isExpanded: true,
  skusSavedCount: 0,
  skusTotalCount: 0,
  isReadOnlyMode: false,
};

export default withStickyHeader(QAPageHeader);
