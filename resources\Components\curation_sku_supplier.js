/**
 * Shows suppliers for SKU
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import './curation_sku_supplier.scss';

class CurationSkuSupplier extends React.Component {
  static propTypes = {
    names: PropTypes.array.isRequired,
    isCanadian: PropTypes.bool,
  };

  static defaultProps = {
    isCanadian: false,
  };

  shouldComponentUpdate() {
    return false;
  }

  render() {
    return (
      <div className="text_center long_column">
        {this.props.names.join(', ')}
        {this.props.isCanadian && (
          <svg className={`u-icon canadian_supplier_icon`}>
            <use xlinkHref={`#wf-icon-canada`} />
          </svg>
        )}
      </div>
    );
  }
}

export default CurationSkuSupplier;
