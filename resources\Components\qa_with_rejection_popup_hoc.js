/**
 * Rejection reason popup for the Curation QA Tool
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {Modal, Button, TextInput, Dropdown, Block, SPACING} from '@wayfair/homebase-extranet';
import curationToolShapes, {QA_STATUS_ACCEPTED} from './curation_tool_shapes';

const WithRejectionPopupHoc = WrappedComponent => {
  class WithRejectionPopup extends React.Component {
    static propTypes = {
      onSave: PropTypes.func.isRequired,
      isAutomaticCurationPostQaEnabled: PropTypes.bool.isRequired,
      rejectionReasons: PropTypes.arrayOf(curationToolShapes.reasonShape)
        .isRequired,
    };

    state = {
      isOpen: false,
      rejectionReason: null,
      rejectionNotes: '',
      sku: '',
      context: {},
    };

    close = () =>
      this.setState({isOpen: false, rejectionReason: null, rejectionNotes: ''});
    open = (sku, context) => this.setState({isOpen: true, sku, context});

    handleChangeRejectionNotes = e => {
      const value = e.target.value;

      this.setState({rejectionNotes: value});
    };

    generateQADecisionHandler = originalHandler => context => {
      const {qaStatus, sku} = context;
      return originalHandler(context);
    };

    getRejectionReasonMessage = () => {
      const {rejectionReason, rejectionNotes} = this.state;

      if (rejectionNotes.trim().length === 0) {
        return rejectionReason.name;
      }

      return `${rejectionReason.name} - ${rejectionNotes.trim()}`;
    };

    closeAndCallOriginalHandlerWithReason = originalHandler => () => {
      const withReason = {
        ...this.state.context,
        reason: this.getRejectionReasonMessage(),
      };
      this.close();

      originalHandler(withReason);
    };

    handleValueChange = rejectionReason => this.setState({rejectionReason});

    render() {
      const {onSave} = this.props;
      const passThroughProps = {
        ...this.props,
        onSave: this.generateQADecisionHandler(onSave),
      };

      return (
        <React.Fragment>
          <Modal
            title={
              <Translation
                msgid="CurationTool.WithRejectionPopupRejectionReasonForX"
                params={{thisstatesku: this.state.sku}}
              />
            }
            scrollLock
            isOpen={this.state.isOpen}
            onRequestClose={this.close}
          >
            <Dropdown
              label={
                <Translation msgid="CurationTool.WithRejectionPopupRejectionDropdownReasonLabel" />
              }
              value={this.state.rejectionReason}
              onValueChange={this.handleValueChange}
              options={this.props.rejectionReasons}
              getOptionLabel={option => (option ? option.name : '')}
            />
            <Block pt={SPACING.SPACE_LARGE}>
              <TextInput
                label={
                  <Translation msgid="CurationTool.WithRejectionPopupNotesLabel" />
                }
                onChange={this.handleChangeRejectionNotes}
                isTextarea
              />
            </Block>
            <Block pt={SPACING.SPACE_LARGE} pb={SPACING.SPACE_MEDIUM}>
              <Button
                onClick={this.closeAndCallOriginalHandlerWithReason(onSave)}
                disabled={!this.state.rejectionReason}
              >
                <Translation msgid="CurationTool.WithRejectionPopupReject" />
              </Button>
              <Button secondary onClick={this.close}>
                <Translation msgid="CurationTool.WithRejectionPopupRejectCancelLabel" />
              </Button>
            </Block>
          </Modal>
          <WrappedComponent {...passThroughProps} />
        </React.Fragment>
      );
    }
  }

  return WithRejectionPopup;
};

export default WithRejectionPopupHoc;
