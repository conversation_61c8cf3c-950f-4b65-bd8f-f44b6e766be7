<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

class Context_Data_Factory {
  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Collection
   */
  public function create_collection() : Context_Data_Collection {

    return new Context_Data_Collection();
  }

  /**
   * @param int $manufacturer_id Manufacturer ID
   * @param int $style_id        Style ID
   * @param int $substyle_id     Substyle ID
   * @param int $price_tier      Price Tier
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data
   */
  public function create(int $manufacturer_id, int $style_id, int $substyle_id, int $price_tier) : Context_Data {
    return new Context_Data($manufacturer_id, $style_id, $substyle_id, $price_tier);
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data
   */
  public function create_empty() : Context_Data {
    return new Context_Empty_Data();
  }
}