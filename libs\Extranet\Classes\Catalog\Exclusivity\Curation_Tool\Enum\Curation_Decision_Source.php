<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Curation_Decision_Source extends AbstractEnumeration implements \JsonSerializable {
  /**
   * csn_product.dbo.tblplVerificationDecisionSource
   */

  /**
   * @var array
   */
  private static $values = [
      'Manual'                            => 1,
      'Automatic by Context SKU'          => 2,
      'Automatic by class'                => 3,
      'Automatic by style'                => 4,
      'Automatic by assortment decision'  => 5,
  ];

  /**
   * @var string|null
   */
  private $label;

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source
   */
  public static function manual() : self {
    return self::get_value_for_option(self::$values['Manual']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\<PERSON>\Curation_Decision_Source
   */
  public static function automatic_by_context_sku() : self {
    return self::get_value_for_option(self::$values['Automatic by Context SKU']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source
   */
  public static function automatic_by_class() : self {
    return self::get_value_for_option(self::$values['Automatic by class']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source
   */
  public static function automatic_by_style() : self {
    return self::get_value_for_option(self::$values['Automatic by style']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source
   */
  public static function automatic_by_assortment_decision() : self {
    return self::get_value_for_option(self::$values['Automatic by assortment decision']); /** @phpstan-ignore-line */
  }

  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source
   *
   * @throws \InvalidArgumentException
   */
  public static function create(int $option) : self {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $option);
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function label() : string {
    // if the label is requested for the first time, compute it and save the result
    if ($this->label === null) {
      $this->label = array_search((int)$this->value(), self::$values, true);
    }

    return $this->label;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'label' => $this->label(),
        'value' => $this->value()
    ];
  }
}
