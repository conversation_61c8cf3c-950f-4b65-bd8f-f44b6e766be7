<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\ProductManagement\Populator\Populator;

class Batch_Factory_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Shared\ProductManagement\Populator\Populator
     */
    private $populator;

    /**
     * @var \WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->populator = $this->prophesize(Populator::class);

        $this->subject = new Batch_Factory($this->populator->reveal());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_creates_batch_model()
    {
        $this->assertInstanceOf(Batch::class, $this->subject->create());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_does_not_populate_batch_model_when_map_is_empty()
    {
        $this->subject->create([]);

        $this->populator->populate(new Batch(), [])->shouldNotBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_does_populate_batch_model_when_map_is_not_empty()
    {
        $batch = new Batch();
        $this->populator->populate($batch, ['dummy'])->willReturn($batch);

        $this->subject->create(['dummy']);

        $this->populator->populate($batch, ['dummy'])->shouldHaveBeenCalled();
    }
}
