<?php declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation;

use GuzzleHttp\Client;
use Psr\Cache\InvalidArgumentException;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;
use function json_encode;

class Client_Authenticator_Service {
  private const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 3600;

  private const AUTH_ENDPOINT    = 'oauth/token';
  private const ACCESS_TOKEN_KEY = 'access_token';
  private const RESPONSE_KEY     = 'response';
  private const EXPIRES_IN_KEY   = 'expires_in';

  /**
   * @var Client
   */
  private Client $httpClient;

  /**
   * @var LoggerInterface
   */
  protected LoggerInterface $logger;

  /**
   * @var AdapterInterface
   */
  private AdapterInterface $cache;

  /**
   * @var string
   */
  private string $clientId;

  /**
   * @var string
   */
  private string $authServiceUrl;

  /**
   * @var string
   */
  private string $clientSecret;

  /**
   * Client_Authenticator_Service constructor.
   *
   * @param LoggerInterface   $logger       Logger
   * @param AdapterInterface  $cache        Cache
   * @param Client            $httpClient   HTTP Client
   * @param string            $clientId     Client ID
   * @param string            $clientSecret Client Secret
   * @param string            $ssoUrl       Wayfair SSO url, for ex. https://sso.auth.wayfair.com/
   */
  public function __construct(
      LoggerInterface   $logger,
      AdapterInterface  $cache,
      Client            $httpClient,
      string            $clientId,
      string            $clientSecret,
      string            $ssoUrl
  ) {
    $this->logger     = $logger;
    $this->cache      = $cache;
    $this->httpClient = $httpClient;
    $this->clientId   = $clientId;
    $this->clientSecret = $clientSecret;

    $this->authServiceUrl = $ssoUrl . self::AUTH_ENDPOINT;
  }

  /**
   * Gets an access token from the Authentication Service.
   *
   * @return string
   * @throws InvalidArgumentException
   */
  public function getToken() : string { /** @phpstan-ignore-line */
    $content = $this->getCachedToken();
    if (!is_null($content)) {
      return $content[self::RESPONSE_KEY][self::ACCESS_TOKEN_KEY];
    }

    $response = $this->httpClient->post(
        $this->authServiceUrl,
        [
            'body'    => json_encode(
                [
                    'client_id'     => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type'    => 'client_credentials'
                ]
            ),
            'headers' => [
                'content-type' => 'application/json'
            ]
        ]
    );
    $content  = json_decode((string)$response->getBody(), true);

    if (!array_key_exists(self::ACCESS_TOKEN_KEY, $content) || !array_key_exists(self::EXPIRES_IN_KEY, $content)) {
      $this->logger->error(
          'wf_auth response did not contain an access token or expiration',
          ['contents' => $content]
      );
      throw new RuntimeException('Failed getting access token from auth service', $response->getStatusCode());
    }

    if ($response->getStatusCode() !== Response::HTTP_OK) {
      $this->logger->error(
          'Failed with status ' . $response->getStatusCode() . ' when connecting to authentication service ',
          [
              'authServiceUrl' => $this->authServiceUrl,
              'responseBody'   => $content
          ]
      );
      throw new RuntimeException('Failed getting access token from auth service', $response->getStatusCode());
    }
    $this->cacheToken($content);

    $this->logger->info('Token obtained successfully');

    return $content[self::ACCESS_TOKEN_KEY];
  }

  /**
   * @return array<string,mixed>|null
   * @throws InvalidArgumentException
   */
  private function getCachedToken() : ?array { /** @phpstan-ignore-line */
    $cacheValue = $this->cache->getItem($this->clientSecret);
    if (!$cacheValue->isHit()) {
      return null;
    }

    $this->logger->info('wf_auth token has been retrieved from cache');

    return json_decode($cacheValue->get(), true);
  }

  /**
   * @param array<string> $response Response
   *
   * @return void
   * @throws InvalidArgumentException
   */
  private function cacheToken(array $response) : void { /** @phpstan-ignore-line */
    $tokenData = [
        self::RESPONSE_KEY => $response,
        'expiration_time'  => time() + (int)$response[self::EXPIRES_IN_KEY]
    ];

    $tokenCache = $this->cache->getItem($this->clientSecret);
    $tokenCache->expiresAfter((int)$response[self::EXPIRES_IN_KEY] - self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS);
    $tokenCache->set(json_encode($tokenData));
    if (!$this->cache->save($tokenCache)) {
      $this->logger->warning('Failed to cache wf_auth response');
    } else {
      $this->logger->info('wf_auth response successfully cached');
    }
  }
}
