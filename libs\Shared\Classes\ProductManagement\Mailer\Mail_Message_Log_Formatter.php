<?php
/**
 * Formats email message for logging
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

class Mail_Message_Log_Formatter {
  /**
   * Formats email message for logging
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $message message to format for log
   *
   * @return array
   */
  public function format(Mail_Message $message) {
    $result = $message->to_array();

    //Do not include actual content
    $result['attachments'] = array_map(
        function ($attachment) {
          return array_slice($attachment, 1);
        }, $result['attachments']
    );

    return $result;
  }
}
