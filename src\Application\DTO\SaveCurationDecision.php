<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\DTO;

class SaveCurationDecision extends AbstractCurationRequest
{
    protected const FIELD_SKUS = 'skus';
    protected const FIELD_EXCLUSION_REASON_ID = 'exclusion_reason_id';
    protected const FIELD_PRICE_TIER = 'price_tier';
    protected const FIELD_STYLE_ID = 'style_id';
    protected const FIELD_SUB_STYLE_ID = 'substyle_id';
    protected const FIELD_BRAND_ID = 'brand_id';
    protected const FIELD_SUGGESTED_NOTES = 'suggested_notes';
    protected const FIELD_SUGGESTED_REASON = 'suggested_reason';
    protected const FIELD_GRANULAR_STYLE_ID = 'granular_style_id';

    /**
     * @var string[]
     */
    protected array $skus;
    protected int $exclusionReasonId;
    protected int $brandId;
    protected int $styleId;
    protected int $subStyleId;
    protected int $priceTier;
    private string $suggestedNotes;
    private string $suggestedReason;
    private int $granularStyleId;

    /**
     * @param int $batchId
     * @param string[] $skus
     * @param int $exclusionReason
     * @param int $brandId
     * @param int $styleId
     * @param int $subStyleId
     * @param int $granularFinalStyleId
     * @param int $priceTier
     * @param string $suggestedNotes
     * @param string $suggestedReason
     */
    public function __construct(
        int $batchId = 0,
        array $skus = [],
        int $exclusionReason = 0,
        int $brandId = 0,
        int $styleId = 0,
        int $subStyleId = 0,
        int $granularFinalStyleId = 0,
        int $priceTier = 0,
        string $suggestedNotes = '',
        string $suggestedReason = ''
    ) {
        parent::__construct($batchId);

        $this->skus = $skus;
        $this->exclusionReasonId = $exclusionReason;
        $this->brandId = $brandId;
        $this->styleId = $styleId;
        $this->subStyleId = $subStyleId;
        $this->priceTier = $priceTier;
        $this->suggestedNotes = $suggestedNotes;
        $this->suggestedReason = $suggestedReason;
        $this->granularStyleId = $granularFinalStyleId;
    }

    /**
     * @return string[]
     */
    public function getSkus(): array
    {
        return $this->skus;
    }

    /**
     * @return int
     */
    public function getExclusionReasonId(): int
    {
        return $this->exclusionReasonId;
    }

    /**
     * @return int
     */
    public function getBrandId(): int
    {
        return $this->brandId;
    }

    /**
     * @return int
     */
    public function getStyleId(): int
    {
        return $this->styleId;
    }

    /**
     * @return int
     */
    public function getSubStyleId(): int
    {
        return $this->subStyleId;
    }

    /**
     * @return int
     */
    public function getPriceTier(): int
    {
        return $this->priceTier;
    }

    /**
     * @return string
     */
    public function getSuggestedNotes(): string
    {
        return $this->suggestedNotes;
    }

    /**
     * @return string
     */
    public function getSuggestedReason(): string
    {
        return $this->suggestedReason;
    }

    /**
     * @return int
     */
    public function getGranularStyleId(): int
    {
        return $this->granularStyleId;
    }

    /**
     * @param array $params
     * @return self
     */
    public static function fromArray(array $params): self
    {
        return new self(
            (int)($params[self::FIELD_BATCH_ID] ?? 0),
            $params[self::FIELD_SKUS] ?? [],
            (int)($params[self::FIELD_EXCLUSION_REASON_ID] ?? 0),
            (int)($params[self::FIELD_BRAND_ID] ?? 0),
            (int)($params[self::FIELD_STYLE_ID] ?? 0),
            (int)($params[self::FIELD_SUB_STYLE_ID] ?? 0),
            (int)($params[self::FIELD_GRANULAR_STYLE_ID] ?? 0),
            (int)($params[self::FIELD_PRICE_TIER] ?? 0),
            (string)($params[self::FIELD_SUGGESTED_NOTES] ?? ''),
            (string)($params[self::FIELD_SUGGESTED_REASON] ?? '')
        );
    }
}
