<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Mytsyk <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator;
use WF\Shared\ProductManagement\Populator\Populator;

class Curator_Loader {

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\Factory
   */
  private $factory;

  /**
   * @var \WF\Shared\ProductManagement\Populator\Populator
   */
  private $populator;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO
   */
  private $dao;

  /**
   * Batch_Finder constructor.
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\Factory           $factory   Factory
   * @param \WF\Shared\ProductManagement\Populator\Populator                          $populator Populator
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO   $dao       DAO
   */
  public function __construct(Factory $factory, Populator $populator, Curator_Loader_DAO $dao) {
    $this->factory   = $factory;
    $this->populator = $populator;
    $this->dao       = $dao;
  }

  /**
   * @param int $em_id Employee ID
   *
   * @return \WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator
   */
  public function get_curator(int $em_id) : Curator {
    $curator_data    = $this->dao->get_curator($em_id);
    $curator = $this->factory->create_curator();

    $this->populator->populate($curator, $curator_data);

    return $curator;
  }
}
