<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use StdClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage\Curation_QA_Postgres_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;

class Curation_QA_Decision_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer|\Prophecy\Prophecy\ObjectProphecy
     */
    private $replacer;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Service
     */
    private $subject;

    /**
     * @return void
     */

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage\Curation_QA_Postgres_DAO
     */
    private $dao_qa_psql;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
     */
    private $batch_data_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated;
     */
    private $notify_curation_updated;

    protected function setUp(): void
    {
        $this->batch_data_service = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->dao = $this->prophesize(Curation_Decision_DAO::class);
        $this->storage = $this->prophesize(Curation_QA_Decision_Storage::class);
        $this->replacer = $this->prophesize(Curation_QA_Kit_Parent_Replacer::class);
        $this->notify_curation_updated = $this->prophesize(Completion_QA_Notify_Curation_Updated::class);
        $this->dao_psql = $this->prophesize(Curation_Decision_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->dao_qa_psql = $this->prophesize(Curation_QA_Postgres_DAO::class);

        $completionBatchData = $this->prophesize(Completion_Batch_Data::class);

        $this->batch_data_service->get(Argument::type('int'))->willReturn($completionBatchData->reveal());
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->subject = new Curation_QA_Decision_Service(
            $this->batch_data_service->reveal(),
            $this->dao->reveal(),
            $this->storage->reveal(),
            $this->replacer->reveal(),
            $this->notify_curation_updated->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal(),
            $this->dao_qa_psql->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_uses_kit_children_instead_of_their_provided_parents()
    {
        $batch_id = 443;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->replacer->replace_kit_parents_with_children($batch_id, ['KitSKU1'])->willReturn(['ChildSKU1', 'ChildSKU2']);

        $this->subject->approve($batch_id, ['KitSKU1'], 123);
        $this->subject->reject($batch_id, ['KitSKU1'], 123, 'test');
        $this->subject->update(
            $batch_id,
            ['KitSKU1'],
            123,
            1,
            2,
            3,
            4,
        );
        $this->subject->excludeFromWL($batch_id, ['KitSKU1'], 123, 1);


        $this->dao_qa_psql->save_decision(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_skus() === ['ChildSKU1', 'ChildSKU2']
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(2);

        $this->dao_qa_psql->save_decision_and_styles(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_skus() === ['ChildSKU1', 'ChildSKU2']
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);

        $this->dao_qa_psql->save_decision_and_exclude_from_wl(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_skus() === ['ChildSKU1', 'ChildSKU2']
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);
    }

    /**
     * @test
     * @return void
     */
    public function it_saves_decisions()
    {
        $batch_id = 443;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->replacer->replace_kit_parents_with_children($batch_id, ['KitSKU1'])->willReturn(['ChildSKU1', 'ChildSKU2']);

        $this->subject->approve($batch_id, ['KitSKU1'], 123);
        $this->subject->reject($batch_id, ['KitSKU1'], 123, 'test');
        $this->subject->excludeFromWL($batch_id, ['KitSKU1'], 123, 1);

        $this->dao_qa_psql->save_decision(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_employee_id() === 123
                        && $arg->get_batch_id() === 443
                        && $arg->get_qa_status() === Curation_QA_Status::approved()
                        && $arg->get_reason() === ''
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);

        $this->dao_qa_psql->save_decision(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_employee_id() === 123
                        && $arg->get_batch_id() === 443
                        && $arg->get_qa_status() === Curation_QA_Status::rejected()
                        && $arg->get_reason() === 'test'
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);
    }

    /**
     * @test
     * @return void
     */
    public function it_saves_decisions_and_styles()
    {
        $batch_id = 443;
        $this->replacer->replace_kit_parents_with_children($batch_id, ['KitSKU1'])->willReturn(['ChildSKU1', 'ChildSKU2']);

        $this->subject->update(
            $batch_id,
            ['KitSKU1'],
            123,
            1,
            2,
            3,
            4,
        );

        $this->storage->save_decision_and_styles(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_employee_id() === 123
                        && $arg->get_batch_id() === 443
                        && $arg->get_qa_status() === Curation_QA_Status::updated()
                        && $arg->get_price_tier_override() === 1
                        && $arg->get_final_style_id() === 2
                        && $arg->get_final_substyle_id() === 3
                        && $arg->get_final_brand_id() === 4
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);
    }

    /**
     * @test
     * @return void
     */
    public function it_saves_decision_and_exclude_from_wl()
    {
        $batch_id = 443;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->replacer->replace_kit_parents_with_children($batch_id, ['KitSKU1'])->willReturn(['ChildSKU1', 'ChildSKU2']);

        $this->subject->excludeFromWL($batch_id, ['KitSKU1'], 123, 1);

        $this->dao_qa_psql->save_decision_and_exclude_from_wl(
            Argument::that(
                static function ($arg) {
                    return $arg instanceof Curation_QA_Decision
                        && $arg->get_employee_id() === 123
                        && $arg->get_batch_id() === 443
                        && $arg->get_qa_status() === Curation_QA_Status::updated()
                        && $arg->get_exclusion_reason() === 1
                    ;
                }
            )
        )->shouldHaveBeenCalledTimes(1);
    }

    /**
     * @test
     * @return void
     */
    public function test_is_ready_to_save()
    {
        $batch_id = 1;
        $skus = ['SKU1'];
        $this->storage->check_if_ready_to_save($skus, $batch_id)->willReturn(true)->shouldBeCalled();
        $this->subject->is_ready_to_save($skus, $batch_id);
    }

    /**
     * @test
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_update_feature_toggle_off()
    {
        $batch_id = 1;
        $skus = ['SKU1'];

        $batch_info = $this->get_batch_info_details();
        $style = $this->get_style_details();
        $sub_style = $this->get_sub_style_details();
        $batch_data = $this->get_batch_data_details();

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->getBatchinfo($skus, $batch_id)->willReturn($batch_info)->shouldBeCalled();
        $this->dao->getStleByeId(1)->willReturn($style)->shouldBeCalled();
        $this->dao->getSubStleByID(1)->willReturn($sub_style)->shouldBeCalled();
        $this->replacer->replace_kit_parents_with_children($batch_id, $skus)->willReturn(['ChildSKU1', 'ChildSKU2'])->shouldBeCalled();
        $this->batch_data_service->get($batch_id)->willReturn($batch_data)->shouldBeCalled();
        $this->notify_curation_updated->send(
            $batch_id,
            $style,
            $sub_style,
            $style,
            $sub_style,
            $skus,
            1,
            1
        )->shouldBeCalled();

        $this->subject->update(
            1,
            $skus,
            1,
            1,
            1,
            1,
            1
        );
    }

    /**
     * @test
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_update_feature_toggle_on()
    {
        $batch_id = 1;
        $skus = ['SKU1'];

        $batch_info = $this->get_batch_info_details();
        $style = $this->get_style_details();
        $sub_style = $this->get_sub_style_details();
        $batch_data = $batch_data = $this->get_batch_data_details();

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->getBatchinfo($skus, $batch_id)->willReturn($batch_info)->shouldBeCalled();
        $this->dao_psql->getStleByeId(1)->willReturn($style)->shouldBeCalled();
        $this->dao_psql->getSubStleByID(1)->willReturn($sub_style)->shouldBeCalled();
        $this->replacer->replace_kit_parents_with_children($batch_id, $skus)->willReturn(['ChildSKU1', 'ChildSKU2'])->shouldBeCalled(1);
        $this->batch_data_service->get($batch_id)->willReturn($batch_data)->shouldBeCalled();
        $this->notify_curation_updated->send(
            $batch_id,
            $style,
            $sub_style,
            $style,
            $sub_style,
            $skus,
            1,
            1
        )->shouldBeCalled();

        $this->subject->update(
            1,
            $skus,
            1,
            1,
            1,
            1,
            1
        );
    }

    /**
     * @test
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_update_multiple_feature_toggle_off()
    {
        $batch_id = 1;
        $skus = ['SKU1', 'SKU2'];

        $batches_info = $this->get_batches_info_details();
        $style = $this->get_style_details();
        $sub_style = $this->get_sub_style_details();
        $old_style = $this->get_old_style_details();
        $batch_data = $batch_data = $this->get_batch_data_details();

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->getBatchinfoList($skus, $batch_id)->willReturn($batches_info)->shouldBeCalled();
        $this->dao->getStleByeId(1)->willReturn($style)->shouldBeCalled();
        $this->dao->getSubStleByID(1)->willReturn($sub_style)->shouldBeCalled();
        $this->replacer->replace_kit_parents_with_children($batch_id, $skus)->willReturn(['ChildSKU1', 'ChildSKU2'])->shouldBeCalled();
        $this->batch_data_service->get($batch_id)->willReturn($batch_data)->shouldBeCalled();
        $this->notify_curation_updated->sendMailForBulkData(
            $batch_id,
            $old_style,
            $style,
            $sub_style,
            $skus,
            1,
            1
        )->shouldBeCalled();

        $this->subject->update(
            1,
            $skus,
            1,
            1,
            1,
            1,
            1
        );
    }

    /**
     * @test
     * @return void
     * @throws Completion_Batch_Not_Found_Exception
     */
    public function test_update_multiple_feature_toggle_on()
    {
        $batch_id = 1;
        $skus = ['SKU1', 'SKU2'];

        $batches_info = $this->get_batches_info_details();
        $style = $this->get_style_details();
        $sub_style = $this->get_sub_style_details();
        $old_style = $this->get_old_style_details();
        $batch_data = $this->get_batch_data_details();

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->getBatchinfoList($skus, $batch_id)->willReturn($batches_info)->shouldBeCalled();
        $this->dao_psql->getStleByeId(1)->willReturn($style)->shouldBeCalled();
        $this->dao_psql->getSubStleByID(1)->willReturn($sub_style)->shouldBeCalled();
        $this->replacer->replace_kit_parents_with_children($batch_id, $skus)->willReturn(['ChildSKU1', 'ChildSKU2'])->shouldBeCalled();
        $this->batch_data_service->get($batch_id)->willReturn($batch_data)->shouldBeCalled();
        $this->notify_curation_updated->sendMailForBulkData(
            $batch_id,
            $old_style,
            $style,
            $sub_style,
            $skus,
            1,
            1
        )->shouldBeCalled();

        $this->subject->update(
            1,
            $skus,
            1,
            1,
            1,
            1,
            1
        );
    }

    private function get_batch_info_details(): StdClass
    {
        $batch_info = new StdClass();
        $batch_info->ViFinalStyleID = 1;
        $batch_info->ViFinalSubStyleID = 1;
        return $batch_info;
    }

    private function get_batches_info_details(): array
    {
        return [
            [
                'ViSKU' => 'SKU1',
                'ViFinalStyleID' => 1,
                'ViFinalSubStyleID' => 1
            ],
            [
                'ViSKU' => 'SKU2',
                'ViFinalStyleID' => 1,
                'ViFinalSubStyleID' => 1
            ]
        ];
    }
    private function get_old_style_details(): array
    {
        return [
            (object)[
                'sku' => 'SKU2',
                'style' => 'Style-1',
                'subStyle' => 'Sub Style-1'
            ],
            (object)[
                'sku' => 'SKU2',
                'style' => 'Style-1',
                'subStyle' => 'Sub Style-1'
            ]
        ];
    }
    private function get_style_details(): StdClass
    {
        $style = new StdClass();
        $style->VsID = 1;
        $style->VsName = 'Style-1';
        return $style;
    }

    private function get_sub_style_details(): StdClass
    {
        $sub_style = new StdClass();
        $sub_style->VssID = 1;
        $sub_style->VssName = 'Sub Style-1';
        return $sub_style;
    }
    private function get_batch_data_details(): Completion_Batch_Data
    {
        return new Completion_Batch_Data(
            1,
            1,
            1,
            1,
            1,
            'Process Type - 1',
            null,
            1,
            null
        );
    }
}
