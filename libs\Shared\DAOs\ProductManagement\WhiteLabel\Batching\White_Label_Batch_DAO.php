<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use WF\Shared\Classes\ProductManagement\WhiteLabel\White_Label_Batch_State;
use WF\Shared\Helpers\SQL;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_Batch;
use PDO;

class White_Label_Batch_DAO {
  private ProductConnection $pdo;


  /**
   * White_Label_Batch_DAO constructor.
   *
   * @param ProductConnection $pdo
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;

  }

  /**
   * @param int $batch_id the batch id to load info for
   *
   * @return array
   */
  public function get_batch(int $batch_id) : array {
    $sql = '
      SELECT
        wlb.ID AS id,
        wlb.StateID AS state_id,
        wlb.BrandCatalogID AS brand_catalog_id,
        wlb.CreatedAt AS created_at,
        wlb.UpdatedAt AS updated_at,
        verificationItem.CloningPrtID AS pt_ticket_id,
        wlb.isNewStagingProcess AS is_new_staging_process
      FROM csn_product.dbo.tblWhiteLabelBatch wlb WITH (NOLOCK)
      OUTER APPLY (
          SELECT TOP 1 v.CloningPrtID FROM csn_product.dbo.tblVerification v WITH(NOLOCK)
          JOIN csn_product.dbo.tblVerificationItem vi WITH(NOLOCK) ON vi.ViVerificationID = v.VerificationID
          WHERE vi.ViBatchID = wlb.ID
      ) verificationItem
      WHERE
        wlb.ID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Cannot load batch: %s', implode(',', $statement->errorInfo())));
    }

    return $statement->fetchAll()[0] ?? [];
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_Batch $batch the batch for insertion
   *
   * @return void
   */
  public function insert_batch(Downstreamable_Batch $batch) {
    $inserted_skus          = [];
    $batch_id               = $batch->get_id();
    $is_new_staging_process = true;

    foreach ($batch->get_skus() as $sku) {
      $inserted_skus[] = [
          'WhiteLabelBatchID' => $batch_id,
          'SKU'               => $sku->get_sku(),
          'IsExcluded'        => $sku->get_is_excluded_from_wl(),
          'TargetMaID'        => $sku->get_target_ma_id(),
      ];
    }

    $column_map = [
        'WhiteLabelBatchID' => SQL::int,
        'SKU'               => SQL::nvarchar(8),
        'IsExcluded'        => SQL::bit,
        'TargetMaID'        => SQL::int,
    ];

    $sql = '
      SET NOCOUNT ON
      INSERT INTO csn_product.dbo.tblWhiteLabelBatch(ID, StateID, BrandCatalogID, CreatedAt, UpdatedAt, IsNewStagingProcess)
        VALUES (:batch_id, :batch_state_id, :brand_catalog_id, SYSDATETIME(), SYSDATETIME(), :is_new_staging_process)
    ';
    $sql .= SQLBulkHelper::getCteXmlSql($column_map, 'skus');
    $sql .= '
      INSERT INTO csn_product.dbo.tblWhiteLabelBatchSKU(WhiteLabelBatchID, SKU, IsExcluded, TargetMaID, CreatedAt, UpdatedAt)
      SELECT
        s.WhiteLabelBatchID,
        s.SKU,
        s.IsExcluded,
        s.TargetMaID,
        SYSDATETIME(),
        SYSDATETIME()
      FROM
        skus s
    ';

    $this->pdo->beginTransaction();

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('skus', SQLBulkHelper::getXmlAsString($inserted_skus, $column_map), PDO::PARAM_STR);
    $statement->bindValue('batch_id', $batch->get_id(), PDO::PARAM_INT);
    $statement->bindValue('batch_state_id', White_Label_Batch_State::created()->value(), PDO::PARAM_INT);
    $statement->bindValue('brand_catalog_id', $batch->get_brand_catalog_id(), PDO::PARAM_INT);
    $statement->bindValue('is_new_staging_process', $is_new_staging_process, PDO::PARAM_BOOL);

    if (!$statement->execute()) {
      $this->pdo->rollBack();
      throw new ExecutionException('Cannot insert new white label batch' . implode(';', $statement->errorInfo()));
    }

    $this->pdo->commit();
  }

}
