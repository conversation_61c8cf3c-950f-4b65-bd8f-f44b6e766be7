<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Http;

use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;

final class HttpClientFactory
{
    /**
     * @var RetryDecider
     */
    private static $retryDecider;

    /**
     * HttpClientFactory constructor.
     */
    private function __construct()
    {
    }

    /**
     * @param string $baseUri
     * @param int $maxRetries
     * @param float $connectionTimeout
     * @param Client|null $httpClient
     *
     * @return Client
     */
    public static function create(
        string $baseUri,
        int $maxRetries,
        float $connectionTimeout,
        ?Client $httpClient
    ): Client {
        if ($httpClient !== null) {
            $handlerStack = $httpClient->getConfig('handler');
        } else {
            $handlerStack = HandlerStack::create();
        }
        $handlerStack->push(self::getRetryMiddle<PERSON>($maxRetries), 'retry');

        if ($httpClient === null) {
            $options = [
                'base_uri' => $baseUri,
                'http_errors' => false,
                'connect_timeout' => $connectionTimeout,
                'handler' => $handlerStack
            ];

            return new Client($options);
        }

        return $httpClient;
    }

    /**
     * @param int $maxRetries
     * @return callable
     */
    private static function getRetryMiddleware(int $maxRetries): callable
    {
        return Middleware::retry(
            function ($retry, $request, $response, $exception) use ($maxRetries) {
                $decider = self::getRetryDecider($maxRetries);
                return $decider->shouldRetryConnection($retry, $request, $response, $exception);
            }
        );
    }

    /**
     * @param int $maxRetries
     * @return RetryDecider
     */
    private static function getRetryDecider(int $maxRetries): RetryDecider
    {
        if (self::$retryDecider === null) {
            self::$retryDecider = new RetryDecider($maxRetries);
        }

        return self::$retryDecider;
    }
}
