<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Http;

use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Throwable;

class RetryDecider
{
    /**
     * @var int
     */
    private $maxRetries;

    /**
     * RetryDecider constructor.
     * @param  int  $maxRetries
     */
    public function __construct(int $maxRetries)
    {
        $this->maxRetries = $maxRetries;
    }

    /**
     * @param int $retry
     * @param RequestInterface $request
     * @param ResponseInterface|null $response
     * @param Throwable|null $exception
     * @return bool
     */
    public function shouldRetryConnection($retry, $request, $response, $exception): bool
    {
        if ($response !== null) {
            return false;
        }

        return $retry < $this->maxRetries && !empty($exception);
    }
}
