<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Batch_Evaluation_Loader {

  /**
   * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO
   */
  private $dao;

  /**
   * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO
   */

  /**
   * @var Curation_Batch_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @var FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * @param \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO $dao Dao class
   * @param Curation_Batch_Postgres_DAO       $dao_psql                  Dao class
   * @param FeatureTogglesInterface $featureToggles            FeatureTogglesInterface
   */
  public function __construct(
      Curation_Batch_DAO $dao,
      Curation_Batch_Postgres_DAO $dao_psql,
      FeatureTogglesInterface $featureToggles
  ) {
    $this->dao = $dao;
    $this->dao_psql                 = $dao_psql;
    $this->featureToggles            = $featureToggles;
  }

  /**
   * @param array $ids verification items IDs
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Item_Model[] Batch_Evaluation_Item_Model
   */
  public function load_evaluation_items_by_ids(array $ids) : array {
    if (empty($ids)) {
      return [];
    }
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $items = $this->dao_psql->load_evaluation_items_by_ids($ids);
    } else {
      $items = $this->dao->load_evaluation_items_by_ids($ids);
    }

    $evaluation_items = [];
    foreach ($items as $item) {
      $evaluation_items[] = new Batch_Evaluation_Item_Model(
          $item['id'],
          $item['sku'],
          $item['qa_status_id']
      );
    }

    return $evaluation_items;
  }
}
