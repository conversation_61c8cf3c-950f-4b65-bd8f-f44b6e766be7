<?php

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Tests\libs\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use Exception;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use RuntimeException;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_API_Client;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_Api_Service;

class White_Label_Nextgen_Api_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var White_Label_Nextgen_API_Client
     */
    public $client;
    /**
     * @var ObjectProphecy|LoggerInterface
     */
    private $logger;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->client = $this->prophesize(White_Label_Nextgen_API_Client::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->white_label_nextgen_api_service = $this->prophesize(White_Label_Nextgen_Api_Service::class);
    }
    /**
     * Test successful API request.
     */
    public function testPostRequestToWhiteLabelNextgenApiSuccess(): void
    {
        $batchData = ['data' => 'test_batch_data'];
        $responseCode = 200;
        $client = $this->prophesize(White_Label_Nextgen_API_Client::class);
        $client->createBatch($batchData)->shouldBeCalledTimes(1);
        $logger = $this->prophesize(LoggerInterface::class);
        $logger->error(Argument::any())->shouldNotBeCalled();
        $apiService = new White_Label_Nextgen_Api_Service($client->reveal(), $logger->reveal());
        $result = $apiService->post_request_to_white_label_nextgen_api($batchData);
        $this->assertEquals($responseCode, $result);
    }

    /**
     * Test API request with retryable exception.
     */
    public function testPostRequestToWhiteLabelNextgenApiWithRetryableException(): void
    {
        $batchData = ['data' => 'test_batch_data'];
        $responseCode = 200;
        $client = $this->prophesize(White_Label_Nextgen_API_Client::class);
        $client->createBatch($batchData)->willThrow(new Exception('Mocked retryable exception'))
            ->shouldBeCalledTimes(White_Label_Nextgen_Api_Service::MAX_RETRIES);
        $logger = $this->prophesize(LoggerInterface::class);
        $logger->error(Argument::type('string'), Argument::type('array'))->shouldBeCalledTimes(White_Label_Nextgen_Api_Service::MAX_RETRIES);
        $logger->info(Argument::type('string'))->shouldBeCalledOnce();
        $apiService = new White_Label_Nextgen_Api_Service($client->reveal(), $logger->reveal());
        $this->expectException(RuntimeException::class);
        $apiService->post_request_to_white_label_nextgen_api($batchData);
    }
}
