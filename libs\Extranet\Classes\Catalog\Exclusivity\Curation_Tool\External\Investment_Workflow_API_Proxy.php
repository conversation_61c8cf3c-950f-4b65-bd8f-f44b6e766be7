<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Investment_Sku_Metric;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Investment_Sku_Metric_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Investment_Workflow_Storage;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Curation\Api\Api_Predicted_Winner;
use WF\Shared\Curation\Api\Predicted_Winner\Predicted_Winner_Info_SKU_DTO;

class Investment_Workflow_API_Proxy implements Investment_Workflow_Storage {
  use Logging_Trait;

  private Api_Predicted_Winner $api;

  /**
   * Investment_Workflow_API_Proxy constructor.
   *
   * @param Api_Predicted_Winner $predicted_winners_api Predicted Winners API
   * @param LoggerInterface|null  $logger       Logger
   */
  public function __construct(Api_Predicted_Winner $predicted_winners_api, ?LoggerInterface $logger = null) {
    $this->api    = $predicted_winners_api;
    $this->logger = $logger;
  }

  /**
   *
   * @param array $skus SKUs to get investment metrics
   *
   * @return Investment_Sku_Metric_Collection
   * @throws \WF\Shared\Curation\Api\Exceptions\API_Request_Exception
   */
  public function fetch_investment_metrics(array $skus) : Investment_Sku_Metric_Collection {
    try {
      $loaded_skus = $this->api->find_predicted_winners($skus);
    } catch (\Throwable $exception) {
      $this->log_throwable_error($exception, $exception->getMessage(), ['skus' => $skus]);

      throw $exception;
    }

    return $this->map_skus($loaded_skus);
  }

  /**
   * @param array $skus SKU list
   *
   * @return Investment_Sku_Metric_Collection
   */
  private function map_skus(array $skus) : Investment_Sku_Metric_Collection {
    $collection = new Investment_Sku_Metric_Collection();

    /* @var Predicted_Winner_Info_SKU_DTO $sku */
    foreach ($skus as $sku) {
      $sku_code            = $sku->get_sku();
      $is_predicted_winner = $sku->is_predicted_winner();
      $metric              = new Investment_Sku_Metric($sku_code, $is_predicted_winner);

      $collection->add($sku_code, $metric);
    }

    return $collection;
  }
}
