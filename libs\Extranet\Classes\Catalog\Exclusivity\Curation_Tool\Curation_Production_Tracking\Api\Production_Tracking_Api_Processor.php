<?php

declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Api;

use WF\Curation\ProductionTrackingApi\Client as PT_Api_Client;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Exception\Production_Tracking_Status_Not_Updated_Exception;

class Production_Tracking_Api_Processor implements Production_Tracking_Processor{

  /**
   * @var PT_Api_Client
   */
  private $client;

  /**
   * Production_Tracking_Api_Processor constructor.
   *
   * @param PT_Api_Client $client Client
   */
  public function __construct(PT_Api_Client $client) {
    $this->client = $client;
  }

  /**
   * @param int[] $project_ids Project IDs
   *
   * @return void
   */
  public function update_production_tracking_status(array $project_ids) : void {
    $response = $this->client->updateProductionTrackingStatus($project_ids);

    if (!$response->isRequestSuccessful()) {
      throw new Production_Tracking_Status_Not_Updated_Exception();
    }
  }
}
