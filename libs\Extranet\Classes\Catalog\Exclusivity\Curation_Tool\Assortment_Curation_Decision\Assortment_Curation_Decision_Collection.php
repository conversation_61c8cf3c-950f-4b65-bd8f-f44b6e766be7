<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision;

class Assortment_Curation_Decision_Collection {
  /**
   * @var Assortment_Curation_Decision[]
   */
  private $decisions;

  /**
   * @var Assortment_Curation_No_Decision
   */
  private $no_decision;

  /**
   * @param Assortment_Curation_Decision[] $decisions decisions
   */
  public function __construct(array $decisions) {
    $decisions_by_sku = [];

    foreach ($decisions as $decision) {
      $decisions_by_sku[$decision->get_sku()] = $decision;
    }

    $this->decisions = $decisions_by_sku;

    $this->no_decision = new Assortment_Curation_No_Decision();
  }

  /**
   * @param string $sku sku
   *
   * @return Assortment_Curation_Decision
   */
  public function get_for_sku(string $sku) : Assortment_Curation_Decision {
    return $this->decisions[$sku] ?? $this->no_decision;
  }

  /**
   * @return string[]
   */
  public function get_skus_with_decision() : array {
    return array_keys($this->decisions);
  }
}
