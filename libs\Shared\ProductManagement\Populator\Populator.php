<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\ProductManagement\Populator;

class Populator implements Populator_Interface {
  /**
   * @var \WF\Shared\ProductManagement\Populator\Closure_Cache
   */
  private $cache;

  /**
   * @var \WF\Shared\ProductManagement\Populator\Closure_Generator
   */
  private $generator;

  /**
   * Populator constructor.
   *
   * @param \WF\Shared\ProductManagement\Populator\Closure_Cache     $cache     property cache to use
   * @param \WF\Shared\ProductManagement\Populator\Closure_Generator $generator population closure generator
   */
  public function __construct(Closure_Cache $cache, Closure_Generator $generator) {
    $this->cache     = $cache;
    $this->generator = $generator;
  }

  /**
   * @param object $subject population subject
   * @param array  $map     populated properties
   *
   * @return object
   */
  public function populate($subject, $map) {
    $fqcn = get_class($subject);
    $populator = $this->cache->get($fqcn);
    if ($populator === null) {
      $populator = $this->generator->generate($fqcn);
      $this->cache->set($fqcn, $populator);
    }
    $populator($subject, $map);

    return $subject;
  }
}
