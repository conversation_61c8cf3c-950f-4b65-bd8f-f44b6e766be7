<?php
/**
 * Pretends to be regular mailer, but logs result of sending attempt
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

use Psr\Log\LoggerInterface;

class Logging_Mailer implements Mailer_Interface {
  /**
   * @var \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface
   */
  private $mailer;
  /**
   * @var LoggerInterface
   */
  private $logger;
  /**
   * @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message_Log_Formatter
   */
  private $message_formatter;

  /**
   * Logging_Mailer constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface      $mailer            actual mailer implementation
   * @param LoggerInterface                                             $logger            class actions logger
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message_Log_Formatter $message_formatter formatter to format message for log
   */
  public function __construct(
      Mailer_Interface $mailer,
      LoggerInterface $logger,
      Mail_Message_Log_Formatter $message_formatter = null
  ) {
    $this->mailer = $mailer;
    $this->logger = $logger;
    $this->message_formatter = $message_formatter ?? new Mail_Message_Log_Formatter();
  }

  /**
   * Creates new message which is accepted to send by current mailer
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function create_message() {
    return $this->mailer->create_message();
  }

  /**
   * Sends out given mail message
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $message message to send
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Result
   */
  public function send(Mail_Message $message) {
    $result = $this->mailer->send($message);

    $level = 'info';
    if (!$result->is_successful()) {
      $level = 'error';
    }
    $this->logger->log($level, 'Sent out mail message', [
      'send_result'   => $result->is_successful(),
      'error_info'    => $result->get_reason(),
      'message_info'  => $this->message_formatter->format($message)
    ]);

    return $result;
  }
}
