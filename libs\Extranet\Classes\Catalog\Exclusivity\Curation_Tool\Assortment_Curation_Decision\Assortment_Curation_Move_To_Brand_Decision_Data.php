<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision;

class Assortment_Curation_Move_To_Brand_Decision_Data {
  /**
   * @var int
   */
  private $target_manufacturer_id;

  /**
   * @var int
   */
  private $target_style_id;

  /**
   * @var int
   */
  private $target_substyle_id;

  /**
   * @var int
   */
  private $target_price_tier;

  /**
   * @param int $target_manufacturer_id manufacturer id
   * @param int $target_style_id        style id
   * @param int $target_substyle_id     substyle id
   * @param int $target_price_tier      price tier
   */
  public function __construct(int $target_manufacturer_id, int $target_style_id, int $target_substyle_id, int $target_price_tier) {
    $this->target_manufacturer_id = $target_manufacturer_id;
    $this->target_style_id        = $target_style_id;
    $this->target_substyle_id     = $target_substyle_id;
    $this->target_price_tier      = $target_price_tier;
  }

  /**
   * @return int
   */
  public function get_target_manufacturer_id() : int {
    return $this->target_manufacturer_id;
  }

  /**
   * @return int
   */
  public function get_target_style_id() : int {
    return $this->target_style_id;
  }

  /**
   * @return int
   */
  public function get_target_substyle_id() : int {
    return $this->target_substyle_id;
  }

  /**
   * @return int
   */
  public function get_target_price_tier() : int {
    return $this->target_price_tier;
  }

}
