<?php

namespace App\Application\EventListener;

use App\Application\Exception\NotAuthorizedException;
use App\Application\Service\ViewRenderer;
use App\Application\View\NotAuthorizedView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class NotAuthorizedExceptionListener
{
    private ViewRenderer $renderingService;

    public function __construct(ViewRenderer $renderingService)
    {
        $this->renderingService = $renderingService;
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $request = $event->getRequest();
        $exception = $event->getThrowable();

        if ($exception instanceof NotAuthorizedException
            && $event->isMainRequest()
            && !$request->isXmlHttpRequest()) {
            $event->setResponse(
                new Response(
                    $this->renderingService->render(
                        new NotAuthorizedView($exception->getMessage()),
                        $request->query->get('webpack_public_path_root') ?? '/',
                        $request->server->get('DOCUMENT_ROOT')
                    ),
                    Response::HTTP_UNAUTHORIZED
                )
            );
        }
    }
}
