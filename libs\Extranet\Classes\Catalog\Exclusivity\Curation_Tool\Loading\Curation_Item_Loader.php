<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Application\Logger\LoggerTrait;
use App\Domain\Service\Loading\CurationItemFactory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Curation_Item_Loader implements Curation_Item_Loader_Interface {
    use LoggerTrait;
  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  protected $dao;

  private CurationItemFactory $curationItemFactory;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  protected $postgres_dao;

  /**
   * Curation_Loader constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                       $dao                Curation_Tool_DAO
   * @param \App\Domain\Service\Loading\CurationItemFactory                                             $curationItemFactory
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                          $featureToggles     Feature toggle
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO $postgres_dao       Curation_Tool_Postgres_DAO
   */
  public function __construct(Curation_Tool_DAO $dao, CurationItemFactory $curationItemFactory, FeatureTogglesInterface $featureToggles, Curation_Tool_Postgres_DAO $postgres_dao) {
    $this->dao                 = $dao;
    $this->curationItemFactory = $curationItemFactory;
    $this->featureToggles      = $featureToggles;
    $this->postgres_dao        = $postgres_dao;
  }

  /**
   * @param int $batchId BatchID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  public function load(int $batchId, int $qaStatus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $rows = $this->postgres_dao->get_batch_items($batchId, $qaStatus);
    } else {
      $rows = $this->dao->get_batch_items($batchId);
    }

    // load items from verification item
    $curationItems = [];
    foreach ($rows as $row) {
      $item = $this->curationItemFactory->create();
      $item->set_sku($row['sku']);
      $item->set_context_xn_id($row['context_xn_id']);
      if(isset($row['qa_status'])) {
          $item->set_qa_status(Curation_QA_Status::create($row['qa_status']));
      }
      $curationItems[$row['sku']] = $item;
    }
    return $curationItems;
  }

    public function loadWithCollection(int $batchId, int $contextXnId, int $limit, int $offset, int $qaStatus) : array {
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            if ( !$limit == 0  ) {
                $rows = $this->postgres_dao->get_batch_items_with_xn_id($batchId, $contextXnId, $limit, $offset);
            } else {
                $rows = $this->postgres_dao->get_batch_items($batchId, $qaStatus);
            }
        } else {
            $rows = $this->dao->get_batch_items($batchId);
        }

        // load items from verification item
        $curationItems = [];
        foreach ($rows as $row) {
            $item = $this->curationItemFactory->create();
            $item->set_sku($row['sku']);
            $item->set_context_xn_id($row['context_xn_id']);
            if(isset($row['qa_status'])) {
                $item->set_qa_status(Curation_QA_Status::create($row['qa_status']));
            }
            $curationItems[$row['sku']] = $item;
        }
        return $curationItems;
    }
}