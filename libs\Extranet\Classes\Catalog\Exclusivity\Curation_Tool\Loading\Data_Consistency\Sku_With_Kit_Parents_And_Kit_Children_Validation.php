<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use App\Application\Translation\TranslatorInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;

class Sku_With_Kit_Parents_And_Kit_Children_Validation implements Curation_Data_Consistency_Validation {
  /**
   * @var TranslatorInterface
   */
  private TranslatorInterface $translate;

  /**
   * @var Curation_Data_Consistency_Validation_Storage
   */
  private Curation_Data_Consistency_Validation_Storage $dao;

  /**
   * @param TranslatorInterface                          $translate Translator
   * @param Curation_Data_Consistency_Validation_Storage $dao       DAO
   */
  public function __construct(TranslatorInterface $translate, Curation_Data_Consistency_Validation_Storage $dao) {
    $this->translate = $translate;
    $this->dao       = $dao;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return Curation_Data_Consistency_Validation_Result
   */
  public function validate(int $batch_id, array $sections): Curation_Data_Consistency_Validation_Result {
    $result = new Curation_Data_Consistency_Validation_Result();
    $skus   = $this->extract_skus_from_sections($sections);

    if (count($skus) === 0) {
      return $result;
    }

    $rows = $this->dao->get_skus_with_kit_parents_and_kit_children_data($skus);

    foreach ($rows as $row) {
      $error = $this->get_error($row['sku'], $row['child_sku'], $row['parent_sku']);
      $result->add_error($row['sku'], $error);
    }

    return $result;
  }

  /**
   * @param string $sku        SKU
   * @param string $child_sku  Child SKU
   * @param string $parent_sku Parent SKU
   *
   * @return string
   */
  private function get_error(string $sku, string $child_sku, string $parent_sku): string {
    return $this->translate->trans(
        'Validation.CurationSkuHasBothKitChildrenAndKitParentsValidation',
        [
            '{sku}'        => $sku,
            '{child_sku}'  => $child_sku,
            '{parent_sku}' => $parent_sku,
        ],
    );
  }

  /**
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  private function extract_skus_from_sections(array $sections): array {
    $skus = [];

    $kit_types = [Curation_Item_Type::kit_parent(), Curation_Item_Type::shared()];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        if (!in_array($item->get_type(), $kit_types)) {
          continue;
        }

        $skus[] = $item->get_sku();
      }
    }

    return $skus;
  }
}
