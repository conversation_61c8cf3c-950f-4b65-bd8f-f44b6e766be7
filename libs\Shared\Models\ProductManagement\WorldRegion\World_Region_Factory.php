<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WorldRegion;

class World_Region_Factory {

  /**
   * @return \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface[]
   */
  public function create_all_regions() : array {
    return [
        $this->create(World_Region_Null::REGION_ID),
        $this->create(World_Region_US::REGION_ID),
        $this->create(World_Region_EU::REGION_ID),
    ];
  }

  /**
   * @return \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface[]
   */
  public function get_existing_regions() : array {
    return [
      $this->create(World_Region_US::REGION_ID),
      $this->create(World_Region_EU::REGION_ID),
    ];
  }

  /**
   * @param int $region_id Region ID
   *
   * @return \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface
   */
  public function create(int $region_id) : World_Region_Interface {
    switch ($region_id) {
      case World_Region_US::REGION_ID:
        $region = new World_Region_US();
        break;
      case World_Region_EU::REGION_ID:
        $region = new World_Region_EU();
        break;
      case World_Region_Null::REGION_ID:
        $region = new World_Region_Null();
        break;
      case World_Region_EU_Nearshore::REGION_ID:
        $region = new World_Region_EU_Nearshore();
        break;
      default:
        throw new \InvalidArgumentException('Wrong argument for World Region Factory Create: ' . $region_id);
    }

    return $region;
  }

}
