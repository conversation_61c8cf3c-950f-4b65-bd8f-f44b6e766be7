<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi;

use WF\Curation\ProductionTrackingApi\Api\ApiClient;
use WF\Curation\ProductionTrackingApi\Auth\AuthClient;
use WF\Curation\ProductionTrackingApi\DTO\QueryBuilder;
use WF\Curation\ProductionTrackingApi\DTO\Response;
use WF\Curation\ProductionTrackingApi\DTO\ResponseBuilder;

class Client
{
    /**
     * @var ClientConfig
     */
    private $config;

    /**
     * @param ClientConfig $config Client Config
     *
     * @throws \Exception
     */
    public function __construct(ClientConfig $config)
    {
        $this->config = $config;
    }

    /**
     * @param int[] $projectIds Project IDs
     *
     * @return Response
     */
    public function updateProductionTrackingStatus(array $projectIds): Response
    {
        $authentication = new AuthClient($this->config);
        $accessToken = $authentication->retrieveToken();

        if (!empty($accessToken)) {
            $apiClient = new ApiClient($this->config);
            $query = QueryBuilder::buildUpdateProductionTrackingStatusQuery($projectIds);

            $apiResponse = $apiClient->sendRequest($accessToken, $query);

            if (!empty($apiResponse)) {
                return ResponseBuilder::buildResponseDTO($apiResponse);
            }
        }

        return new Response(false);
    }
}
