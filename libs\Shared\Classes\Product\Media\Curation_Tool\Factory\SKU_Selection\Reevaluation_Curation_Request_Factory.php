<?php
/**
 * Curation Request Factory used for Sku Reevaluation - Automatic Exclusion - before sending downstream
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection;

use Exception;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container;

class Reevaluation_Curation_Request_Factory extends Curation_Request_Factory {
  /**
   * @param int $type_id type id
   *
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container
   * @throws \Exception
   */
  public function create_requirement_container(int $type_id) : Requirement_Container {
    throw new Exception('Use the reevaluation requirement container factory instead');
  }
}
