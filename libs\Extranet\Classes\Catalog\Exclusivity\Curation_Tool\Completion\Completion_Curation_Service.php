<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use Psr\Log\LoggerInterface;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Shared\Traits\Logging_Trait;

class Completion_Curation_Service {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
   */
  private $batchDataService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
   */
  private $batchStatusUpdater;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker
   */
  private $curationBatchChecker;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service     $batchDataService     Completion_Batch_Data_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater   $batchStatusUpdater   Completion_Batch_Status_Updater
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker $curationBatchChecker Curation Batch Checker
   * @param LoggerInterface|null                                                                      $logger               logger

   */
  public function __construct(
      Completion_Batch_Data_Service $batchDataService,
      Completion_Batch_Status_Updater $batchStatusUpdater,
      Completion_Curation_Batch_Checker $curationBatchChecker,
      ?LoggerInterface $logger = null
  ) {
    $this->batchDataService     = $batchDataService;
    $this->batchStatusUpdater   = $batchStatusUpdater;
    $this->curationBatchChecker = $curationBatchChecker;
    $this->logger               = $logger;
  }

  /**
   * @param int $batchId    Batch Id
   * @param int $employeeId Employee Id
   *
   * @return array
   */
  public function complete(int $batchId, int $employeeId) : array {
    if (!$this->curationBatchChecker->isCurationCompleted($batchId)) {
      $this->log_info(
          sprintf('Curation is not completed, BatchID "%s"', $batchId),
          ['batchId' => $batchId, 'employeeId' => $employeeId]
      );
      return ['status' => false, 'reason' => 'Curation is not completed'];
    }

    if (!$this->isAssignedBatchInProgress($batchId)) {
      $this->log_info(
          sprintf('Curation batch is not "in progress" or employee is not assigned for batchId "%s"', $batchId),
          ['batchId' => $batchId, 'employeeId' => $employeeId]
      );
      return ['status' => false, 'reason' => 'Curation batch is not "in progress" or employee is not assigned'];
    }
    $this->batchStatusUpdater->changeStatus($batchId, Batch::STATUS_MANUAL_CURATION_COMPLETE, $employeeId);
    $this->log_info(
        sprintf('Batch with BatchId "%s" marked as completed', $batchId),
        ['batchId' => $batchId, 'employeeId' => $employeeId]
    );
    return ['status' => true];
  }

  /**
   * @param int $batchId Batch id
   *
   * @return bool
   */
  private function isAssignedBatchInProgress(int $batchId) : bool {
    $batchData = $this->batchDataService->get($batchId);

    return $batchData->getStatus() === Batch::STATUS_MANUAL_IN_PROGRESS && !empty($batchData->getAssignedEmployeeId());
  }
}
