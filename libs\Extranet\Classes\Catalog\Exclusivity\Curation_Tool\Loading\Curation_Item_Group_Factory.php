<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use WF\Shared\Classes\ProductManagement\Utils\Merger;

class Curation_Item_Group_Factory {

  private const SECTION_CURATION_ITEM_COUNT_LIMIT = 1000;

  private Merger $merger;
  private bool $isEnabledCollisionLoadingLimitation;
  private CurationItemFactory $curationItemFactory;

  /**
   * Curation_Item_Group_Factory constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Utils\Merger $merger Merger
   * @param CurationItemFactory                               $curationItemFactory FT value for mtbw_enable_curation_collision_loading_limitation)
   * @param bool                                              $isEnabledCollisionLoadingLimitation               FT value for mtbw_pa_wl_automation_curation_post_qa_ui
   */
  public function __construct(Merger $merger, CurationItemFactory $curationItemFactory, bool $isEnabledCollisionLoadingLimitation) {
    $this->merger = $merger;
    $this->isEnabledCollisionLoadingLimitation = $isEnabledCollisionLoadingLimitation;
    $this->curationItemFactory = $curationItemFactory;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curationItems Curation Items
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group[]
   */
  public function createFromCurationItems(array $curationItems): array {
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group[]
     */
    $groups = [];

    $simpleSkusGroup = new Curation_Item_Group();

    foreach ($curationItems as $curationItem) {
      // if it is a simple sku, add it to the simple skus group
      if (empty($curationItem->get_context_xn_id()) && empty($curationItem->get_kit_parents())) {
        $simpleSkusGroup->add($curationItem);
        continue;
      }

      $collection = new Curation_Item_Group();
      $collection->add($curationItem);

      $groups[] = $collection;
    }

    // merge dependent groups together
    /** @var Curation_Item_Group[] $results */
    $results = $this->merger->merge($groups);

    // remove curation items if too many
    // @todo a better algorithm to decide items to remove: https://projecthub.service.csnzoo.com/browse/MTBW-746
    if ($this->isEnabledCollisionLoadingLimitation) {
      foreach ($results as $curationItemGroup) {
        $curationItemGroupCount = count($curationItemGroup->getSkus());
        while ($curationItemGroup->isCollectionGroup()
               && $curationItemGroupCount > Curation_Item_Group_Factory::SECTION_CURATION_ITEM_COUNT_LIMIT
        ) {
          $curationItemGroup->remove($curationItemGroup->getSkus()[$curationItemGroupCount - 1]);
          $curationItemGroupCount = count($curationItemGroup->getSkus());
        }
      }
    }

    // add simple skus group to the results
    if (!empty($simpleSkusGroup->getItems())) {
      $results[] = $simpleSkusGroup;
    }

    return $results;
  }

  /**
   * @param array $data Data
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group
   */
  public function createFromArray(array $data): Curation_Item_Group {
    $group = new Curation_Item_Group();

    foreach ($data as $row) {
      $item = $this->curationItemFactory->create();
      $item->set_sku($row['sku']);
      $item->set_kit_parents($row['kit_parents']);
      $item->set_kitsco(filter_var($row['is_kitsco'], FILTER_VALIDATE_BOOLEAN));

      $group->add($item);
    }

    return $group;
  }
}
