<?php

namespace App\Infrastructure\Connection\Driver;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Exception;
use WF\Shared\Traits\Logging_Trait;
use function count;
use function mb_strtoupper;
use function rtrim;
use function sprintf;

class PDOConnectionPgql implements PDOProxyInterface
{
    use Logging_Trait;

    private const T_SQL_ARRAY_STR_POST_FIX = '_array';
    private ?Connection $dbalConnection = null;


    private ConnectionPsqlProperties $ConnectionPsqlProperties;

    /**
     * PDOConnectionPgql constructor.
     *
     * @param ConnectionPsqlProperties $ConnectionPsqlProperties
     */
    public function __construct(
        ConnectionPsqlProperties $ConnectionPsqlProperties
    ) {
        $this->ConnectionPsqlProperties = $ConnectionPsqlProperties;
    }

    /**
     * Lazy connection initializing
     *
     * @return Connection
     */
    private function getConnection(): Connection
    {
        if (!$this->dbalConnection) {
            $ConnectionProperties = [
                'driver' => $this->ConnectionPsqlProperties->toArray()['driver'],
                'host' => 'brand-workflows-curation-tool-cloudsql',
                'port' => '5432',
                'user' => $this->ConnectionPsqlProperties->toArray()['user'],
                'password' => $this->ConnectionPsqlProperties->toArray()['password'],
                'dbname' => 'btbb-bc-curation'
            ];
            $this->dbalConnection = DriverManager::getConnection($ConnectionProperties);
        }
        return $this->dbalConnection;
    }
    /**
         * @param string $statement
         * @param array  $driver_options
         *
         * @return StatementProxy
         *
         * @throws Exception
         */
    public function prepare(string $statement, array $driver_options = []): StatementProxy
    {
        $statementProxy = $this->getConnection()->prepare($statement);
        return new StatementProxy($statementProxy);
    }

    /**
   * @param string $sql
   * @param array  $params
   *
   * @return array
   */
    public function fetchOne(string $sql, array $params = []): array
    {
        $connection = $this->getConnection();
        $statement = $connection->prepare($sql);

        if ($statement->execute($params)) {
            $row = $statement->fetch();
            return $row ?: [];
        }

        return [];
    }

    /**
     * @param string $sql
     * @param array  $params
     *
     * @return array
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        $result = [];
        $connection = $this->getConnection();
        $statement = $connection->prepare($sql);
        if ($statement->execute($params)) {
            $result = $statement->fetchAll();
        }

        return $result;
    }

    /**
     * @param string $sql
     * @param array  $params
     *
     * @return mixed|false
     */
    public function fetchColumn(string $sql, array $params = [])
    {
        $connection = $this->getConnection();
        $statement = $connection->prepare($sql);
        if ($statement->execute($params)) {
            return $statement->fetchColumn();
        }

        return false;
    }

    /**
     * @param string $sql
     * @param array  $params
     *
     * @return void
     */
    public function executeQuery(string $sql, array $params = []): void
    {
        $connection = $this->getConnection();
        $statement = $connection->prepare($sql);
        $statement->execute($params);
    }

    /**
     * @param int    $count
     * @param string $prefix
     * @param string $dataType
     *
     * @return string
     */
    public function paramsForList(int $count, string $prefix = '?', string $dataType): string
    {
        $prefix = $prefix === '?' ? 'json' : $prefix;
        $arrayStr = $prefix . self::T_SQL_ARRAY_STR_POST_FIX;

        $upperDataType = mb_strtoupper($dataType);

        return sprintf('(SELECT CAST(value as %s) AS value FROM OPENJSON(:%s))', $upperDataType, $arrayStr);
    }


    /**
     * @param array    $data
     * @param string $prefix
     *
     * @return string
     */
    public function paramsForLists(array $data): string
    {
        $prefix = count($data) > 1 ? ',' : '';
        $str = '';
        foreach ($data as $value) {
            $str .= "'" . $value . "'" . $prefix;
        }
        return rtrim($str, ',');
    }
}
