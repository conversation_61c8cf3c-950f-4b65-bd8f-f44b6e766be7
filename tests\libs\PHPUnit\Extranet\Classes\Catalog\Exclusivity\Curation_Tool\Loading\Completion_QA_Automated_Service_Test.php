<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage\Curation_Only_Batches_Postgres_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api\Partial_White_Label_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream as Downstream;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_SKU_Service;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO;

class Completion_QA_Automated_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Completion_QA_Automated_Service
     */
    private $qa_service;

    /**
     * @var Completion_Batch_Data_Service
     */
    private $batch_data_service;
    /**
     * @var App\Application\DTO\QARequest
     */
    private $request;
    /**
     * @var Partial_White_Label_Api_Processor
     */
    private $api_partial_wl_processor;
    /**
     * @var Downstream\Batch_Data_Loader
     */
    private $batch_loader;
    /**
     * @var Completion_Verification_Item_Service
     */
    private $vi_service;
    /**
     * @var Completion_Batch_Status_Updater
     */
    private $batch_status_updated;
    /**
     * @var Object
     */
    public $Object;

    /**
     * @var Curation_Batch_DAO
     */
    private $dao;

    /**
     * @var Batch_SKU_Service
     */
    private $batch_sku_service;

    /**
     * @var Curation_Request_Factory
     */
    private $curation_request_factory;

    /**
     * @var Batch_Factory
     */
    private $batch_factory;

    /**
     * @var Completion_QA_Automated_Batch_Checker
     */
    private $qa_batch_checker;

    /**
     * @var Completion_QA_Notify_Curation_Rejected
     */
    private $notify_curation_rejected;

    /**
     * @var Curation_Automation_Item_Storage
     */
    private $automation_item_storage;

    /**
     * @var batchId
     */
    private $batchId;

    /**
     * @var employeeId
     */
    private $employeeId;

    /**
     * @var brandCatalogId
     */
    private $brandCatalogId;

    /**
     * @var storage
     */
    private $storage;


    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO Curation_Automation_Item_PostgreSQL_DAO
     */
    private $curation_automation_item_postgres_dao;


    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->batchId = 1;
        $this->employeeId = 1;
        $this->brandCatalogId = 1;
        $this->batch_data_service = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->vi_service = $this->prophesize(Completion_Verification_Item_Service::class);
        $this->batch_status_updated = $this->prophesize(Completion_Batch_Status_Updater::class);
        $this->qa_batch_checker = $this->prophesize(Completion_QA_Automated_Batch_Checker::class);
        $this->notify_curation_rejected = $this->prophesize(Completion_QA_Notify_Curation_Rejected::class);
        $this->automation_item_storage = $this->prophesize(Curation_Automation_Item_Storage::class);
        $this->storage = $this->prophesize(Curation_Only_Batch_Service_Storage::class);
        $this->storage_psql = $this->prophesize(Curation_Only_Batches_Postgres_DAO::class);
        $this->curation_automation_item_postgres_dao = $this->prophesize(Curation_Automation_Item_PostgreSQL_DAO::class);
        $this->featureToggles = $this->createMock(FeatureTogglesInterface::class);

        $batch = $this->prophesize(Curation_Automation_Unwhitelabel::class);
        $this->featureToggles->method('isEnabled')->willReturn(false);

        $batch->get_batch_id()->willReturn(1);
        $batch->get_skus()->willReturn([]);
        $batch->get_unwhitelabel()->willReturn(true);
        $this->batch_data_service->get_qa_batch_skus_with_exclude_reason($this->batchId)->willReturn($batch);
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->set_verification_id_for_skus(1, 1);
        $this->storage->mark_batch_as_sent(1, 1, true);


        $this->qa_service = new Completion_QA_Automated_Service(
            $this->batch_data_service->reveal(),
            $this->vi_service->reveal(),
            $this->batch_status_updated->reveal(),
            $this->qa_batch_checker->reveal(),
            $this->notify_curation_rejected->reveal(),
            $this->automation_item_storage->reveal(),
            $this->storage->reveal(),
            $this->storage_psql->reveal(),
            $this->curation_automation_item_postgres_dao->reveal(),
            $this->featureToggles
        );
    }
    /**
     * @test
     *
     * @return void
     */
    public function unwhitelabelExcludedSkus_test_with_feature_toggle_false()
    {
        $curation_automation_unwhitelabel = new Curation_Automation_Unwhitelabel(101, ['SKU1', 'SKU2']);
        $skus_with_excluded_reason = $this->batch_data_service
          ->get_qa_batch_skus_with_exclude_reason(Argument::exact(101))
          ->willReturn($curation_automation_unwhitelabel);
        $this->curation_automation_item_postgres_dao->unwhitelabel($skus_with_excluded_reason);
        $actual_result = $this->qa_service->unwhitelabelExcludedSkus(101);
        $this->assertEquals(true, $actual_result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function unwhitelabelExcludedSkus_test_with_feature_toggle_true()
    {
        $curation_automation_unwhitelabel = new Curation_Automation_Unwhitelabel(102, ['SKU1', 'SKU2']);
        $skus_with_excluded_reason = $this->batch_data_service
          ->get_qa_batch_skus_with_exclude_reason(Argument::exact(102))
          ->willReturn($curation_automation_unwhitelabel);
        $this->curation_automation_item_postgres_dao->unwhitelabel($skus_with_excluded_reason);
        $this->featureToggles->method('isEnabled')->willReturn(true);
        $actual_result = $this->qa_service->unwhitelabelExcludedSkus(102);
        $this->assertEquals(true, $actual_result);
    }
}
