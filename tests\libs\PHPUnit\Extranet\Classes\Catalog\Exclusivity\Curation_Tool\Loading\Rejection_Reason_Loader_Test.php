<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Rejection_Reason_Loader_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
     */
    private $postgres_DAO;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->postgresql_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->logger = $this->prophesize(LoggerInterface::class);


        $this->subject = new Rejection_Reason_Loader(
            $this->dao->reveal(),
            $this->postgresql_dao->reveal(),
            $this->featureToggles->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_rejection_reasons_call_feature_toggle_on()
    {
        $regionId = 1;
        $this->dao->get_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getRejectionReasons($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_rejection_reasons_feature_toggle_off()
    {
        $this->dao->get_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getRejectionReasons();
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_suggested_style_rejection_reasons_call_feature_toggle_on()
    {
        $this->dao->get_suggested_style_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_suggested_style_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getSuggestedStyleRejectionResons();
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_suggested_style_rejection_reasons_feature_toggle_off()
    {
        $this->dao->get_suggested_style_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_suggested_style_rejection_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getSuggestedStyleRejectionResons();
    }
}
