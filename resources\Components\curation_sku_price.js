/**
 * Show sku price and price options
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Button, Loading, Modal, Grid, Column} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {getPriceOptionsService} from './curation_tool_services';

const PRICE_OPTIONS_MAX_NUMBER_OF_ROWS = 20;

class CurationSkuPrice extends React.PureComponent {
  static propTypes = {
    sku: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    priceOptionsCount: PropTypes.number.isRequired,
  };

  state = {
    open: false,
    priceOptions: [],
  };

  openModal = () => {
    this.setState({open: true, priceOptions: []});

    getPriceOptionsService(this.props.sku)
      .then(response => {
        this.setState({
          open: true,
          priceOptions: response.price_options,
        });
      })
      .catch(() => {});
  };

  closeModal = () => this.setState({open: false});

  getColumnNames = () => {
    if (this.state.priceOptions.length === 0) {
      return [];
    }

    return Object.getOwnPropertyNames(this.state.priceOptions[0]);
  };

  getGridRowKey = priceOption => {
    return Object.values(priceOption).join('-');
  };

  getPriceOptions = () => {
    return this.state.priceOptions.slice(0, PRICE_OPTIONS_MAX_NUMBER_OF_ROWS);
  };

  arePriceOptionsLoaded = () => {
    return this.state.priceOptions.length > 0;
  };

  isNumberOfPriceOptionsExceeded = () => {
    return this.state.priceOptions.length > PRICE_OPTIONS_MAX_NUMBER_OF_ROWS;
  };

  render() {
    return (
      <div className="text_center">
        <Translation
          msgid="CurationTool.CurationSkuPriceValue"
          params={{thispropsprice: this.props.price}}
        />
        {this.props.priceOptionsCount > 1 && (
          <div>
            <Button text onClick={this.openModal}>
              <Translation msgid="CurationTool.PriceOptions" />
            </Button>
            <Modal
              overlayClose
              scrollLock
              isOpen={this.state.open}
              onRequestClose={this.closeModal}
              onSecondaryClose={this.closeModal}
            >
              {!this.arePriceOptionsLoaded() && <Loading size="large" />}
              {this.arePriceOptionsLoaded() && (
                <div>
                  <Grid>
                    {this.getColumnNames().map(columnName => (
                      <Column size={3} key={columnName}>
                        {columnName}
                      </Column>
                    ))}
                  </Grid>
                  {this.getPriceOptions().map(priceOption => (
                    <Grid key={this.getGridRowKey(priceOption)}>
                      {Object.values(priceOption).map(value => (
                        <Column size={3} key={value}>
                          {value}
                        </Column>
                      ))}
                    </Grid>
                  ))}
                  {this.isNumberOfPriceOptionsExceeded() && (
                    <p>
                      <Translation msgid="CurationTool.NumberOfPriceOptionsExceededMessage" />
                    </p>
                  )}
                </div>
              )}
            </Modal>
          </div>
        )}
      </div>
    );
  }
}

export default CurationSkuPrice;
