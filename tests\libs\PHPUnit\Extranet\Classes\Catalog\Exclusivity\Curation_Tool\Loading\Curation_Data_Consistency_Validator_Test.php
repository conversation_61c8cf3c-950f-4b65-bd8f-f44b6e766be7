<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Application\Translation\TranslatorInterface;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Result;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Saved_Kit_Parents_Validation;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_Postgres_DAO;

class Curation_Data_Consistency_Validator_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var iterable<Curation_Data_Consistency_Validation>|\Prophecy\Prophecy\ObjectProphecy
     */
    private $validations;

    /**
     * @var Curation_Data_Consistency_Validator_Storage
     */
    private $dao;


    /**
     * @var Curation_Data_Consistency_Validation_Postgres_DAO
     */
    private $dao_postgres;

    /**
     * @var Curation_Data_Consistency_Validation_Storage
     */
    private $dao_postgres_storage;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var Saved_Kit_Parents_Validation
     */
    private $savedKitValidation;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Data_Consistency_Validator_Storage::class);
        $this->dao_postgres = $this->prophesize(Curation_Data_Consistency_Validation_Postgres_DAO::class);
        $this->dao_postgres_storage = $this->prophesize(Curation_Data_Consistency_Validation_Storage::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->validations = $this->prophesize(Curation_Data_Consistency_Validation::class);
        $this->translator = $this->prophesize(TranslatorInterface::class);



        $this->subject = new Curation_Data_Consistency_Validator(
            $this->dao->reveal(),
            $this->dao_postgres->reveal(),
            $this->featureToggles->reveal(),
            [$this->validations->reveal()],
            $this->logger->reveal()
        );

        $this->savedKitValidation = new Saved_Kit_Parents_Validation($this->translator->reveal(), $this->dao_postgres_storage->reveal(), $this->dao_postgres->reveal(), $this->featureToggles->reveal());
    }

    /**
     * @test
     *
     * @return void
     */
    public function run_validations_feature_toggle_off()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $validation = new Curation_Data_Consistency_Validation_Result();
        $validation->add_error('SKU', 'Testing');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->validations->validate(Argument::cetera(), Argument::cetera())->willReturn($validation);
        $this->dao_postgres_storage->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao->save_results(Argument::cetera())->willReturn([]);
        $this->dao_postgres->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao_postgres->save_results(Argument::cetera())->willReturn([]);
        $this->subject->run_validations($batchId, $sections);
    }

    /**
     * @test
     *
     * @return void
     */
    public function run_validations_feature_toggle_on()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $validation = new Curation_Data_Consistency_Validation_Result();
        $validation->add_error('SKU', 'Testing');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->validations->validate(Argument::cetera(), Argument::cetera())->willReturn($validation);
        $this->dao_postgres_storage->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao->save_results(Argument::cetera())->willReturn([]);
        $this->dao_postgres->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao_postgres->save_results(Argument::cetera())->willReturn([]);
        $this->subject->run_validations($batchId, $sections);
    }

    /**
     * @test
     *
     * @return void
     */
    public function savedKit_feature_toggle_off()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $this->dao_postgres_storage->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao->save_results(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->dao_postgres->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao_postgres->save_results(Argument::cetera())->willReturn([]);
        $this->savedKitValidation->validate($batchId, $sections);
    }

    /**
     * @test
     *
     * @return void
     */
    public function savedKit_feature_toggle_on()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $this->dao_postgres_storage->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao->save_results(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->dao_postgres->get_saved_kit_parents(Argument::cetera())->willReturn([]);
        $this->dao_postgres->save_results(Argument::cetera())->willReturn([]);
        $this->savedKitValidation->validate($batchId, $sections);
    }
}
