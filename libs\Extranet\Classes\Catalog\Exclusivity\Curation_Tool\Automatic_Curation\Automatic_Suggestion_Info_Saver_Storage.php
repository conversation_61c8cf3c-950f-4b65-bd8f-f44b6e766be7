<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

interface Automatic_Suggestion_Info_Saver_Storage {
  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_style_info(int $batch_id, array $skus);

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_substyle_info(int $batch_id, array $skus);

  /**
   * @param int      $batch_id         Batch ID
   * @param string[] $skus             SKUs
   * @param array    $suggested_styles Suggested Styles
   * @param int      $style_id         Style ID
   * @param string   $saved_at         Saved At
   * @param string   $notes            Notes
   * @param int|null $reason           Suggested style rejection reason
   *
   * @return void
   */
  public function save_suggested_style_info(int $batch_id, array $skus, array $suggested_styles, int $style_id, string $saved_at, string $notes, ?int $reason);

  /**
   * @param int      $batch_id            Batch ID
   * @param string[] $skus                SKUs
   * @param array    $suggested_substyles Suggested Styles
   * @param int      $style_id            Style ID
   * @param int      $substyle_id         Substyle ID
   * @param string   $saved_at            Saved At
   * @param string   $notes               Notes
   * @param int|null $reason              Suggested style rejection reason
   *
   * @return void
   */
  public function save_suggested_substyle_info(int $batch_id, array $skus, array $suggested_substyles, int $style_id, int $substyle_id, string $saved_at, string $notes, ?int $reason);
}
