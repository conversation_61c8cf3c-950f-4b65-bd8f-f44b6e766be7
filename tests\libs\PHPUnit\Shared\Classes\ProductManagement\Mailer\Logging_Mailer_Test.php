<?php
/**
 * Logging Mailer Test
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Mailer;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Mailer\Logging_Mailer;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message_Log_Formatter;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Result;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface;
use WF\Shared\Logging\Logger;

class Logging_Mailer_Test extends TestCase
{
    use ProphecyTrait;
    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface|\Prophecy\Prophecy\ObjectProphecy */
    private $mailer;
    /** @var \WF\Shared\Logging\Logger|\Prophecy\Prophecy\ObjectProphecy */
    private $logger;
    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message_Log_Formatter|\Prophecy\Prophecy\ObjectProphecy */
    private $message_formatter;
    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message|\Prophecy\Prophecy\ObjectProphecy */
    private $message;

    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Logging_Mailer */
    private $subject;

    /**
     * Set up test environment
     *
     * @return void
     */
    protected function setUp(): void
    {
        $this->message = $this->prophesize(Mail_Message::class);
        $this->mailer = $this->prophesize(Mailer_Interface::class);
        $this->logger = $this->prophesize(Logger::class);
        $this->message_formatter = $this->prophesize(Mail_Message_Log_Formatter::class);

        $this->subject = new Logging_Mailer(
            $this->mailer->reveal(),
            $this->logger->reveal(),
            $this->message_formatter->reveal()
        );
    }

    /**
     * @test
     * @return void
     */
    public function it_is_mailer()
    {
        $this->assertInstanceOf(Mailer_Interface::class, $this->subject);
    }

    /**
     * @test
     * @return void
     */
    public function it_creates_message_with_internal_mailer()
    {
        $message = $this->prophesize(Mail_Message::class);
        $this->mailer->create_message()->willReturn($message);

        $result = $this->subject->create_message();

        $this->assertEquals($message->reveal(), $result);
    }

    /**
     * @test
     * @dataProvider internal_send_results
     *
     * @param string $expectation log level expectation
     * @param mixed  $result      result of sending
     *
     * @return void
     */
    public function it_chooses_log_level_depending_on_seding_result($expectation, $result)
    {
        $this->mailer->send($this->message)->willReturn($result);

        $this->subject->send($this->message->reveal());

        $this->logger->log($expectation, Argument::cetera())->shouldHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_logs_message_details()
    {
        $result = $this->prophesize(Mail_Result::class);
        $result->is_successful()->willReturn(false);
        $result->get_reason()->willReturn('Some failure reason');
        $this->mailer->send($this->message)->willReturn($result);

        $this->subject->send($this->message->reveal());

        $this->logger->log('error', 'Sent out mail message', Argument::any())->shouldHaveBeenCalled();
        $this->logger->log('error', Argument::any(), Argument::withEntry('send_result', false))->shouldHaveBeenCalled();
        $this->logger->log('error', Argument::any(), Argument::withEntry('error_info', 'Some failure reason'))->shouldHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_uses_formatter_to_log_raw_info()
    {
        $result = $this->prophesize(Mail_Result::class);
        $result->is_successful()->willReturn(true);
        $result->get_reason()->willReturn('');
        $this->mailer->send($this->message)->willReturn($result);
        $this->message_formatter->format($this->message)->willReturn(['formatted', 'array']);

        $this->subject->send($this->message->reveal());

        $this->logger->log('info', Argument::any(), Argument::withEntry('message_info', ['formatted', 'array']))->shouldHaveBeenCalled();
    }

    /**
     * Represents different sending results, which could be returned by internal mailer
     *
     * @return array
     */
    public function internal_send_results()
    {
        $result1 = $this->prophesize(Mail_Result::class);
        $result1->is_successful()->willReturn(true);
        $result1->get_reason()->willReturn('');

        $result2 = $this->prophesize(Mail_Result::class);
        $result2->is_successful()->willReturn(false);
        $result2->get_reason()->willReturn('Some failure reason');

        return [
            'Internal mailer succeeded' => ['info', $result1],
            'Internal mailer failed' => ['error', $result2]
        ];
    }
}
