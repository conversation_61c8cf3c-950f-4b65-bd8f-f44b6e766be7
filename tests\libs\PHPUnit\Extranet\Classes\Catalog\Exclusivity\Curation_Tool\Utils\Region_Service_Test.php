<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Logging\Logger;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_US;

class Region_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @test
     *
     * @return void
     */
    public function get_region()
    {
        $batch_id = 1;
        $brand_catalog_id = 1;
        $expectedRegion = new World_Region_US();

        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_brand_catalog($batch_id)->willReturn($brand_catalog_id)->shouldNotBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $curationToolPostgresDao->get_batch_brand_catalog($batch_id)->willReturn($brand_catalog_id)->shouldBeCalled();

        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_brand_catalog($batch_id)->willReturn($brand_catalog_id)->shouldNotBeCalled();

        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();

        $logger = $this->prophesize(Logger::class);

        $regionFactoryMock = $this->prophesize(World_Region_Factory::class);
        $regionFactoryMock->create(Argument::any())->willReturn($expectedRegion)->shouldNotBeCalled();

        // $factory = $this->prophesize(World_Region_Factory::class);
        // $factory->create(Argument::any())->shouldNotBeCalled();

        $region_service = new Region_Service(
            $curationToolDao->reveal(),
            new World_Region_Factory(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal(),
            $logger->reveal()
        );
        $result = $region_service->get_region($batch_id);

        $this->assertEquals($expectedRegion, $result);
    }
}
