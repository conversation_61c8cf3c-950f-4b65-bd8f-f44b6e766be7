<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities;

class Completion_Batch_Data
{
  private int $batchId;

  private ?int $assignedEmployeeId;

  private int $status;

  private int $brandCatalogId;

  private ?int $processTypeID;

  private ?string $processTypeName;

  private ?\DateTime $createdAt;

  private ?int $approverEmployeeId;

  private ?\DateTime $approvedAt;

  /**
   * @param int $batchId Batch ID
   * @param int $status Status
   * @param int $brandCatalogId Brand Catalog ID
   * @param int|null $assignedEmployeeId Assigned Employee ID
   * @param int|null $processTypeID Process Type ID
   * @param string|null $processTypeName Process Type ID
   * @param \DateTime|null $createdAt Created At
   * @param int|null $approverEmployeeId Assigner Employee ID
   * @param \DateTime|null $approvedAt Approved At
   */
  public function __construct(
      int $batchId,
      int $status,
      int $brandCatalogId,
      ?int $assignedEmployeeId,
      ?int $processTypeID,
      ?string $processTypeName,
      ?\DateTime $createdAt,
      ?int $approverEmployeeId,
      ?\DateTime $approvedAt
    ) {
    $this->batchId            = $batchId;
    $this->status             = $status;
    $this->assignedEmployeeId = $assignedEmployeeId;
    $this->brandCatalogId     = $brandCatalogId;
    $this->processTypeID      = $processTypeID;
    $this->processTypeName    = $processTypeName;
    $this->createdAt          = $createdAt;
    $this->approverEmployeeId = $approverEmployeeId;
    $this->approvedAt         = $approvedAt;
  }


  /**
   * @return int
   */
  public function getBatchId(): int
  {
    return $this->batchId;
  }

  /**
   * @return int|null
   */
  public function getAssignedEmployeeId() : ?int {
    return $this->assignedEmployeeId;
  }

  /**
   * @return int
   */
  public function getStatus() : int {
    return $this->status;
  }

  /**
   * @return int
   */
  public function getBrandCatalogId() : int {
    return $this->brandCatalogId;
  }

  /**
   * @return int|null
   */
  public function getProcessTypeID() : ?int {
    return $this->processTypeID;
  }

  /**
   * @return string
   */
  public function getProcessTypeName() : string {
    return $this->processTypeName ?? 'Not Specified';
  }

  /**
   * @return \DateTime|null
   */
  public function getCreatedAt(): ?\DateTime {
      return $this->createdAt;
  }

  /**
   * @return int|null
   */
  public function getApproverEmployeeId() : ?int {
    return $this->approverEmployeeId;
  }

  /**
   * @return \DateTime|null
   */
  public function getApprovedAt(): ?\DateTime {
      return $this->approvedAt;
  }
}
