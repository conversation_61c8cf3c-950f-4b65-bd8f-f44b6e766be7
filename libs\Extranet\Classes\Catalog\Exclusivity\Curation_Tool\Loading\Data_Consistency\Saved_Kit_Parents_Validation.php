<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use App\Application\Translation\TranslatorInterface;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_Postgres_DAO;

class Saved_Kit_Parents_Validation implements Curation_Data_Consistency_Validation {
  /**
   * @var TranslatorInterface
   */
  private TranslatorInterface $translate;

  /**
   * @var Curation_Data_Consistency_Validation_Storage
   */
  private $dao;


  /**
   * @var Curation_Data_Consistency_Validation_Postgres_DAO
   */
  private Curation_Data_Consistency_Validation_Postgres_DAO $dao_postgres;


  /**
   * @var FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * @param TranslatorInterface                          $translate Translator
   * @param Curation_Data_Consistency_Validation_Storage $dao       DAO
   * @param Curation_Data_Consistency_Validation_Postgres_DAO $dao_postgres       DAO Postgres
   * @param FeatureTogglesInterface         $featureToggles     FeatureTogglesInterface
   */
  public function __construct(TranslatorInterface $translate, Curation_Data_Consistency_Validation_Storage $dao, Curation_Data_Consistency_Validation_Postgres_DAO $dao_postgres, FeatureTogglesInterface $featureToggles) {
    $this->translate = $translate;
    $this->dao       = $dao;
    $this->dao_postgres       = $dao_postgres;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return Curation_Data_Consistency_Validation_Result
   */
  public function validate(int $batch_id, array $sections): Curation_Data_Consistency_Validation_Result {


    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $skus = $this->dao_postgres->get_saved_kit_parents($batch_id);
    } else {
      $skus   = $this->dao->get_saved_kit_parents($batch_id);
    }
    $result = new Curation_Data_Consistency_Validation_Result();

    foreach ($skus as $sku) {
      $result->add_error($sku, $this->translate->trans('Validation.CurationSavedKitParentsValidation', ['{sku}' => $sku]));
    }

    return $result;
  }
}
