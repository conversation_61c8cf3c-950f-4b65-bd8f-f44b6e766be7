<?php

namespace WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching;


use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Curation\Api\Curation\TalentApi;
use PDO;
class Curation_Batch_Postgres_DAO {

  const CURATION_REQUEST_SKU_IMPORT_STATUS_COMPLETED = 2;

  const CURATION_DOWNSTREAMED_STATUS_ID = 6;

  private const MANUAL_CURATION_ONLY_TYPE = 7;

  private ProductConnection $pdo;

  private PostgresConnection $pdo_psql;

  private TalentApi $talentApiObj;

  /**
   * Batch_SKU_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   * @param PostgresConnection $pdo_psql PDO_SQL
   * @param TalentApi $talentApiObj talentApiObj
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql, TalentApi $talentApiObj) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
    $this->talentApiObj = $talentApiObj;
  }

  /**
   * @param int $batch_id the batcn id
   *
   * @return array
   */
  public function get_batch_info(int $batch_id) : array {
    $sql = '
      SELECT
          cb."ID" AS id,
          cb."SuID" AS supplier_id,
          cb."BrandCatalogID" AS brand_catalog_id,
          cb."CurationRequestTypeID" AS curation_request_type
      FROM "tblCurationBatch" cb
      WHERE cb."ID" = :batch_id;
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batch info ');
    }

    return $statement->fetch();
  }

  /**
   * Returns not sent to downstream batches pending PR
   *
   * @return array
   */
  public function get_not_sent_batches() : array {
    $sent_batches = array();
    $batch_data =  $this->get_not_sent_batches_internal();
    if (count($batch_data) == 0) {
      return $sent_batches;
    }
    $brand_catalog_ids = array_unique(array_column($batch_data, "brand_catalog_id"));
    $assigned_employee_ids =array_unique(array_column($batch_data, "emid"));
    $brand_catalog =  $this->get_not_sent_batches_external($brand_catalog_ids);
    $employee_data = $this->talentApiObj->getEmployeeList($assigned_employee_ids);
    $employee_arr = $this->get_employee_data_filter($employee_data);
    foreach ($batch_data as $batch_data_row) {
      $batches_row = array();
      $brand_catalog_id = $batch_data_row['brand_catalog_id'];
      $emp_id = $batch_data_row['emid'];
      if($brand_catalog_id != null){
        $batches_row = array_merge($batch_data_row, $brand_catalog[$brand_catalog_id]);
      }else{
        $batches_row = array_merge($batch_data_row, array('brand_catalog_short_name' => ''));
      }
      if(key_exists($emp_id,$employee_arr)){
        $batches_row = array_merge($batches_row, array('assigned_employee' => $employee_arr[$emp_id]));
      }else{
        $batches_row = array_merge($batches_row, array('emid' => '', 'assigned_employee' => ''));
      }
      array_push($sent_batches, $batches_row);
    }

    return $sent_batches;

  }

  /**
   * Returns not sent to downstream batches
   *
   * @param int $sent_downstream_max_days_ago Sent Downstream Max Days Ago
   *
   * @return array
   */

  public function get_sent_downstream_ready_for_whitelabel_batches(int $sent_downstream_max_days_ago) : array {
    $query_separation=true;
    $result=[];
      $curation_batches=$this->get_curation_batches($sent_downstream_max_days_ago);
      $brand_catalog_ids=array_unique(array_column($curation_batches,'BrandCatalogID'));
      $emp_ids=array_unique(array_column($curation_batches,'AssignedEmID'));
      $batch_ids=array_unique(array_column($curation_batches,'id'));

      $brand_catalogs= $this->get_brand_catalog($brand_catalog_ids);
      $employees=$this->get_employees($emp_ids);
      $white_label_batches=$this->get_white_label_batches($batch_ids);
      $whiteLabelBatchIds = array();
      if (count($white_label_batches) > 0 ){
        foreach ($white_label_batches as $white_label_batch){
          $whiteLabelBatchIds[] = $white_label_batch['ID'];
        }
      }
    foreach ($curation_batches as $row){
      if (in_array($row['id'], $whiteLabelBatchIds)) {
        continue;
      }
        $row['brand_catalog_short_name']=null;
        $row['assigned_employee']=null;
        foreach ($brand_catalogs as $brand_catalog){
          if($brand_catalog['BclgID']==$row['BrandCatalogID']){
            $row['brand_catalog_short_name']=$brand_catalog['brand_catalog_short_name'];
            break;
          }
        }

        foreach ($employees as $employee){
          if($employee['EmID']==$row['AssignedEmID']){
            $row['assigned_employee']=$employee['assigned_employee'];
            break;
          }
        }
        $result[]=$row;
      }
    return $result;
  }

  /**
   * Returns not sent to downstream batches
   *
   * @param int $sent_downstream_max_days_ago Sent Downstream Max Days Ago
   *
   * @return array
   */
  public function get_curation_batches(int $sent_downstream_max_days_ago) : array {
    $sql = "
          SELECT 
              cb.\"ID\" AS id,
              cb.\"SuID\" AS supplier_id,
              cb.\"BrandCatalogID\" AS brand_catalog_id,
              cb.\"CurationRequestTypeID\" AS curation_request_type,
              cb.\"BrandCatalogID\",
              cb.\"AssignedEmID\"
          FROM \"tblCurationBatch\" cb
          WHERE cb.\"VerificationID\" IS NOT NULL
              AND NOT EXISTS (
                  SELECT 1
                  FROM \"tblVerificationItem\" vi
                  JOIN \"tblCurationRequestSKU\" crs ON vi.\"ViID\" = crs.\"VerificationItemID\"
                  JOIN \"tblCurationRequest\" cr ON crs.\"RequestID\" = cr.\"ID\"
                  WHERE vi.\"ViBatchID\" = cb.\"ID\"
                  AND cr.\"ManualTypeID\" = :curation_only
              )
              AND cb.\"SentWLAt\" > CURRENT_DATE - INTERVAL '".$sent_downstream_max_days_ago." days'";
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':curation_only', self::MANUAL_CURATION_ONLY_TYPE, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batches ready for white label');
    }
    return $statement->fetchAll();
  }
  /**
   * Returns set of brand catalog records
   *
   * @param array $brand_catalog_ids
   *
   * @return array
   */
  public function get_brand_catalog(array $brand_catalog_ids) : array {
    $sql = '
     SELECT  bc.BclgID,
             bc.BclgShortName AS brand_catalog_short_name
     FROM csn_product.dbo.tblBrandCatalog bc WITH(NOLOCK) 
     WHERE bc.BclgID IN ' . $this->pdo->paramsForList(count($brand_catalog_ids), 'ids', \App\Infrastructure\Helper\SQL::int);

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('ids', $brand_catalog_ids, SQL::int);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batches ready for white label');
    }
    return $statement->fetchAll();
  }

  /**
   * Returns employee records with id and name.
   *
   * @param array $emp_ids
   *
   * @return array
   */
  public function get_employees(array $emp_ids): array {
    $sql = '
     SELECT
         e.EmID,
         e.EmFirstName + \' \' + e.EmLastName AS assigned_employee
     FROM csn_hr.dbo.tblEmployee e WITH(NOLOCK) 
     WHERE e.EmID IN ' . $this->pdo->paramsForList(count($emp_ids), 'ids', \App\Infrastructure\Helper\SQL::int);

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('ids', $emp_ids, SQL::int);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batches ready for white label');
    }
    return $statement->fetchAll();
  }


  /**
   * Returns get list of white label batches.
   *
   * @param array $ids
   *
   * @return array
   */

  public function get_white_label_batches(array $ids): array {

    $sql ='SELECT wb.ID FROM	
            (SELECT ID,
	          ROW_NUMBER() OVER (PARTITION BY ID ORDER BY ID DESC) AS RowNumber 
            FROM csn_product.dbo.tblWhiteLabelBatch WITH (NOLOCK) 
            WHERE ID IN ' . $this->pdo->paramsForList(count($ids), 'id', SQL::int).') wb
            WHERE wb.RowNumber=1';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('id', $ids, SQL::int);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch white label batches');
    }
    return $statement->fetchAll();
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch object
   *
   * @return array
   */
  public function get_batch_curation_requests(Batch $batch) : array {
    $sql = '
      SELECT DISTINCT cr."ID" AS id, cr."QuickformID" AS quickform_id
      FROM "tblCurationRequest" cr
      JOIN "tblCurationRequestSKU" crs ON crs."RequestID" = cr."ID"
      JOIN "tblVerificationItem" vi ON vi."ViSKU" = crs."SKU"
      WHERE vi."ViBatchID" = :batch_id AND crs."ImportStatusID" = :import_status;
      ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch->get_id(), PDO::PARAM_INT);
    $statement->bindValue(':import_status', static::CURATION_REQUEST_SKU_IMPORT_STATUS_COMPLETED, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batch curation requests');
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $ids A list of IDs to retrieve
   *
   * @return array
   */
  public function load_evaluation_items_by_ids($ids) {
    //get skus from veritfication table
    $vi_skus= $this->get_vi_qa_skus_by_vids($ids);
    if (empty( $vi_skus)) {
      return [];
    }
    $vi_skus1= array();
    $vi_skus1 =array_column($vi_skus, "sku");
    //get skus from product table
    $pr_skus = $this->get_prskus_by_vi_skus($vi_skus1);
    foreach ($vi_skus as $sku =>$viskusrow)
    {
      //match  if skus returned from verificationItem table exists in product table otherwise remove it
      if(!array_key_exists($viskusrow['sku'],$pr_skus))
      {
        unset($vi_skus[$sku]);
      }

    }
    return $vi_skus;
  }
  /**
   * @param array $ids A list of IDs to retrieve
   *
   * @return array
   */
  public function get_prskus_by_vi_skus($skus) {
    $sql = '
        SELECT  pr.PrSKU  AS sku,
                pr.PrStatus AS status
        FROM csn_product.dbo.tblProduct pr WITH (NOLOCK)
        WHERE pr.PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));
    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to load evaluation items for IDs');
    }
    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
  }

  /**
   * @param array $ids A list of IDs to retrieve
   *
   * @return array
   */
  public function get_vi_qa_skus_by_vids($ids): array
  {
    $id_str = $this->pdo_psql->paramsForLists($ids);
    if($id_str === ""){return array();}
    $sql = "
            SELECT 
                vi.\"ViID\" AS id,
                vi.\"ViSKU\" AS sku,
                vi.\"ViQAStatusID\" AS qa_status_id
            FROM \"tblVerificationItem\" vi
            WHERE vi.\"ViID\" IN (". $id_str .")";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to load evaluation items for IDs');
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   *
   * @return void
   */
  public function send_curation_only_batch_downstream(int $batch_id, int $verification_id) : void {
    $sql = '
      UPDATE "tblCurationBatch"
      SET "VerificationID" = :verification_id,
          "StatusID" = :status_id
      WHERE "ID" = :batch_id;
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':status_id', self::CURATION_DOWNSTREAMED_STATUS_ID, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot set verficiation ID on Batch ID: ' . $batch_id);
    }
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return int|null
   */
  public function get_manual_curation_type_for_batch(int $batch_id) : ?int {

    $sql = '
        SELECT cr."ManualTypeID"
        FROM "tblVerificationItem" vi
        JOIN "tblCurationRequestSKU" crs ON vi."ViID" = crs."VerificationItemID"
        JOIN "tblCurationRequest" cr ON crs."RequestID" = cr."ID"
        WHERE vi."ViBatchID" = :batch_id;
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Could not load curation manual type id for batch ID: ' . $batch_id);
    }

    return $statement->fetchColumn(0);
  }

  /**
   * This method will fetch data from external table.
   * @param array $BclgIDs the BclgIDs
   * @return array
   */

  public function get_not_sent_batches_external(array $BclgIDs): array {
    $sql = '
      SELECT  
        bc.BclgID,
        bc.BclgShortName AS brand_catalog_short_name
      FROM csn_product.dbo.tblBrandCatalog bc WITH(NOLOCK)
       WHERE bc.BclgID IN ' . $this->pdo->paramsForList(count($BclgIDs), 'BclgIDs', SQL::nvarchar(8));
    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('BclgIDs', $BclgIDs, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch not sent batches');
    }

    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
  }

  /**
   * This method will have all the tables and joins for the internal table.
   *
   * @return array
   */

  public function get_not_sent_batches_internal(): array {
    $sql = '
      SELECT 
          cb."ID" AS id,
          cb."SuID" AS supplier_id,
          cb."BrandCatalogID" AS brand_catalog_id,
          cb."CurationRequestTypeID" AS curation_request_type,
          cb."AssignedEmID" AS EmID,
          cr."ManualTypeID" AS curation_manual_request_type
      FROM 
          "tblCurationBatch" cb
      LEFT JOIN LATERAL (
          SELECT tcr."ManualTypeID"
          FROM "tblVerificationItem" vi
          JOIN "tblCurationRequestSKU" crs ON vi."ViID" = crs."VerificationItemID"
          JOIN "tblCurationRequest" tcr ON tcr."ID" = crs."RequestID"
          WHERE vi."ViBatchID" = cb."ID"
          LIMIT 1
      ) cr ON true
      WHERE cb."VerificationID" IS NULL
      ';

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch not sent batches');
    }

    return $statement->fetchAll();
  }

  /**
   * Returns employee data in id as key format
   * * @param object $employee_data the employee_data
   * @return array
   */
  public function get_employee_data_filter(object $employee_data) : array {
    $emp_arr = array ();
    foreach ($employee_data->content as $employee_ids_row) {
      $emp_arr[$employee_ids_row->id] = $employee_ids_row->fullName;
    }
    return  $emp_arr;

  }
}
