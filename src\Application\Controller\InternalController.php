<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Curation\ExclusivityAssortment\Infrastructure\DAO\ExclusivityAssortmentMetricsLoaderDAO;
use WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolCandidatesDbLoader;
use WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolCandidatesWithPricesFromApiLoader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Shared\Merchandising\SKU_Collisions\Storage\SKU_Collider_DAO;

/**
 * This controller is used for the liveness and readiness probes (see k8s.yaml).
 * All paths that start with `/internal/` are accessible only from within the datacenter network and the engineering VLAN.
 *
 * @Route(path="/internal")
 */
class InternalController
{
    /**
     * @Route(path="/ping", name="ping")
     */
    public function ping(): Response
    {
        return new Response('pong');
    }

    /**
     * @Route(path="/db-check", name="db-check")
     *
     * @deprecated
     * @todo should be removed after testing in PROD
     */
    public function dbCheck(
        Curation_Tool_DAO $curationToolDAO,
        Curator_Loader_DAO $curatorLoaderDAO,
        AssortmentToolCandidatesDbLoader $assortmentToolCandidatesDbLoader,
        ExclusivityAssortmentMetricsLoaderDAO $exclusivityAssortmentMetricsLoaderDAO,
        Curation_Production_Tracking_DAO $curationProductionTrackingDAO,
        AssortmentToolCandidatesWithPricesFromApiLoader $assortmentToolCandidatesWithPricesFromApiLoader,
        SKU_Collider_DAO $skuColliderDAO
    ): Response {
        $response = [];

        // checking csn_hr at SQLINTERNALTOOLS
        $error = null;
        try {
            $curatorLoaderDAO->get_curator_legacy(55);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['InternalToolsConnection']['csn_hr'] = $error ?? 'success';

        // checking csn_hr at SQLPRODUCT
        $error = null;
        try {
            $curatorLoaderDAO->get_curator(55);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection']['csn_hr'] = $error ?? 'success';

        // checking csn_hr & csn_product at SQLPRODUCT
        $error = null;
        try {
            $curationToolDAO->get_sku_data(2, ['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection1']['csn_product'] = $error ?? 'success';
        $response['ProductConnection1']['csn_hr'] = $error ?? 'success';

        // checking csn_pricing_tech & csn_product at SQLPRODUCT
        $error = null;
        try {
            $curationToolDAO->get_skus_wsc_cost_with_margin(['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection2']['csn_product'] = $error ?? 'success';
        $response['ProductConnection2']['csn_pricing_tech'] = $error ?? 'success';

        // checking csn_hr & csn_product at SQLPRODUCT
        $error = null;
        try {
            $curationToolDAO->get_sku_data(2, ['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection3']['csn_product'] = $error ?? 'success';
        $response['ProductConnection3']['csn_hr'] = $error ?? 'success';

        // checking csn_merch_tool & csn_product at SQLPRODUCT
        $error = null;
        try {
            $curationProductionTrackingDAO->get_project_ids(2);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection4']['csn_product'] = $error ?? 'success';
        $response['ProductConnection4']['csn_merch_tool'] = $error ?? 'success';

        // checking csn_product_global & csn_product at SQLPRODUCT
        $error = null;
        try {
            $skuColliderDAO->getCollectionCollisions(2, ['SKU00001']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['ProductConnection4']['csn_product'] = $error ?? 'success';
        $response['ProductConnection4']['csn_product_global'] = $error ?? 'success';

        // checking csn_pricing_tech & csn_hr & csn_product & csn_merch_tool at SQLPRODUCTCATALOG
        $error = null;
        try {
            $assortmentToolCandidatesDbLoader->load_assortment_tool_candidates(['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['MerchConnection1']['csn_product'] = $error ?? 'success';
        $response['MerchConnection1']['csn_merch_tool'] = $error ?? 'success';
        $response['MerchConnection1']['csn_hr'] = $error ?? 'success';
        $response['MerchConnection1']['csn_pricing_tech'] = $error ?? 'success';

        // checking csn_hr & csn_product & csn_merch_tool at SQLPRODUCTCATALOG
        $error = null;
        try {
            $assortmentToolCandidatesWithPricesFromApiLoader->load_assortment_tool_candidates(['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['MerchConnection2']['csn_product'] = $error ?? 'success';
        $response['MerchConnection2']['csn_merch_tool'] = $error ?? 'success';
        $response['MerchConnection2']['csn_hr'] = $error ?? 'success';

        // checking csn_bi_export & csn_product & csn_merch_tool at SQLPRODUCTCATALOG
        $error = null;
        try {
            $exclusivityAssortmentMetricsLoaderDAO->getMetricsBySku(['HCD1361']);
        } catch (Throwable $exception) {
            $error = $exception->getMessage();
        }
        $response['MerchConnection3']['csn_product'] = $error ?? 'success';
        $response['MerchConnection3']['csn_merch_tool'] = $error ?? 'success';
        $response['MerchConnection3']['csn_bi_export'] = $error ?? 'success';

        return new JsonResponse($response);
    }
}
