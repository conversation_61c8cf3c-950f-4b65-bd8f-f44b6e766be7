<?php

namespace App\Tests\Integration\WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External;

use App\Infrastructure\Connection\Driver\StatementProxy;
use App\Infrastructure\Connection\ProductConnection;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Shared\Curation\Api\Cost\CostApiService;
use WF\Shared\Logging\Logger;
use function array_push;

class Get_Skus_Cost_With_Margin_Test extends TestCase
{
    use ProphecyTrait;

    private $curationToolDao;

    /**
     * @var App\Infrastructure\Connection\ProductConnection|\Prophecy\Prophecy\ObjectProphecy
     */
    private $pod;
    /**
     * @var WF\Shared\Curation\Api\Cost\CostApiService|\Prophecy\Prophecy\ObjectProphecy
     */
    private $costAPiclient;
    /**
     * @var WF\Shared\Logging\Logger|\Prophecy\Prophecy\ObjectProphecy
     */
    private $mockLogger;
    /**
     * @var App\Infrastructure\Connection\Driver\StatementProxy|\Prophecy\Prophecy\ObjectProphecy
     */
    private $stat;

    protected function setUp(): void
    {
        $this->stat = $this->prophesize(StatementProxy::class);
        $this->pod = $this->prophesize(ProductConnection::class);
        $this->mockLogger = $this->prophesize(Logger::class);
        $this->costAPiclient = $this->prophesize(CostApiService::class);
        //$this->costAPiclient = new Cost_Api_Client('dev', $this->mockLogger->reveal());
        $this->curationToolDao = new Curation_Tool_DAO($this->pod->reveal(), $this->mockLogger->reveal(), false, $this->costAPiclient->reveal());
    }

    /**
     * @test
     * @return void
     */
    public function getSKusAverageCostTest()
    {
        $this->costAPiclient->get_part_cost(Argument::any())->willReturn(9999.00);
        $this->pod->paramsForList(Argument::any(), Argument::any(), Argument::any())->willReturn('VCY3119');
        $this->pod->prepare(Argument::any())->willReturn($this->stat->reveal());
        $rows = ['ID' => '1231231'];
        $rows = array_push($rows, ['sku' => 'VCY3119']);
        $this->stat->execute()->willReturn($rows);
        $this->stat->bindValuesList(Argument::any(), Argument::any(), Argument::any())->shouldNotHaveBeenCalled();
        $this->stat->fetchAll(Argument::any(), Argument::any(), Argument::any())->willReturn([['sku' => 'VCY3119', 'ID' => '1231231', 'margin' => '0.5']]);
        $finalResp = $this->curationToolDao->get_skus_wsc_cost_with_margin(['VCY3119']);
        $finalResp['VCY3119'] = '9999';
        $this->assertEquals('9999', $finalResp['VCY3119']);
    }
}
