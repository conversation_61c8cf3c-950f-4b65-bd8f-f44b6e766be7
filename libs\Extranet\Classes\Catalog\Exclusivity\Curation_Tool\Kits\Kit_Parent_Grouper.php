<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group;
use WF\Shared\Classes\ProductManagement\Utils\Merger;

class Kit_Parent_Grouper {
  /**
   * @var \WF\Shared\Classes\ProductManagement\Utils\Merger
   */
  private $merger;

  /**
   * Kit_Parent_Grouper constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Utils\Merger $merger Merger
   */
  public function __construct(Merger $merger) {
    $this->merger = $merger;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group $curationItemGroup Curation_Item_Group
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent[]
   */
  public function createKitParents(Curation_Item_Group $curationItemGroup) : array {
    $parents = [];

    foreach ($curationItemGroup->getItems() as $item) {
      foreach ($item->get_kit_parents() as $parentSku) {
        if (empty($parents[$parentSku])) {
          $parent          = new Kit_Parent();
          $parent->sku     = $parentSku;
          $parent->related = [$parent];

          $parents[$parentSku] = $parent;
        }

        // add all eligible children as dependencies
        if (!$item->is_kitsco()) {
          $parents[$parentSku]->children[] = $item->get_sku();
        }
      }
    }

    ksort($parents);

    // create groups for all parents
    $groups = [];
    foreach ($parents as $parentSku => $parent) {
      $group = new Kit_Parent_Group();
      $group->addItem($parent);

      $groups[] = $group;
    }

    $mergedGroups = $this->merger->merge($groups);

    // update kit parents depending on the merged groups
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Group $group
     */
    foreach ($mergedGroups as $group) {
      $related = [];

      foreach ($group->getItems() as $item) {
        $related[]     = $item->sku;
        $item->related = [];
      }

      sort($related);

      $parents[$related[0]]->related = $related;
    }

    return $parents;
  }
}
