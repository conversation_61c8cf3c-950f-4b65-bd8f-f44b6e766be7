<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;

class Completion_Verification_Item_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage
     */
    private $dao;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service
     */
    private $subject;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Completion_Verification_Item_Storage::class);
        $this->dao_psql = $this->prophesize(Batch_Management_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Completion_Verification_Item_Service(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_true_when_locked_information_updated_feature_toggle_off()
    {
        $batchId = 1;
        $employeeId = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->update_locked_data_if_null($batchId, $employeeId)->willReturn(true)->shouldBeCalled();

        $actualResult = $this->subject->update_locked_data_if_null($batchId, $employeeId);

        $this->assertEquals(true, $actualResult);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_true_when_locked_information_updated_feature_toggle_on()
    {
        $batchId = 1;
        $employeeId = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->update_locked_data_if_null($batchId, $employeeId)->willReturn(true)->shouldBeCalled();

        $actualResult = $this->subject->update_locked_data_if_null($batchId, $employeeId);

        $this->assertEquals(true, $actualResult);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_false_when_locked_information_not_updated()
    {
        $batchId = 1;
        $employeeId = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->update_locked_data_if_null($batchId, $employeeId)->willReturn(false)->shouldBeCalled();

        $actualResult = $this->subject->update_locked_data_if_null($batchId, $employeeId);

        $this->assertEquals(false, $actualResult);
    }
}
