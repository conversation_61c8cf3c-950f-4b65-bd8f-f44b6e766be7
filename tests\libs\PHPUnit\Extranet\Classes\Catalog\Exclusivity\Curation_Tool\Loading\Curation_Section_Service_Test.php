<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Curation_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Logging\Logger;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU;

class Curation_Section_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_loads_with_automatic_savers()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $employeeID = 1;
        $affectedRows = 1;

        /** @var Section_Loader_Interface $sectionLoa2der */
        $sectionLoader = $this->prophesize(Section_Loader_Interface::class);
        $sectionLoader->getSections($batchId)->willReturn($sections);

        $sectionAdditionalLoader = $this->prophesize(Section_Additional_Data_Loader::class);

        /** @var Automatic_Curation_Saver $automaticCurationSaver1 */
        $automaticCurationSaver1 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver1->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows)->shouldBeCalled();

        /** @var Automatic_Curation_Saver $automaticCurationSaver2 */
        $automaticCurationSaver2 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver2->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows)->shouldBeCalled();

        $savers = [$automaticCurationSaver1->reveal(), $automaticCurationSaver2->reveal()];

        $subject = new Curation_Section_Service($sectionLoader->reveal(), $sectionAdditionalLoader->reveal(), $savers);
        $subject->loadWithAutomaticCurationSaving($batchId, $employeeID);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_loads_sections_once_if_no_rows_affected()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $employeeID = 1;
        $affectedRows = 0;

        /** @var Section_Loader_Interface $sectionLoa2der */
        $sectionLoader = $this->prophesize(Section_Loader_Interface::class);

        $sectionLoader->getSections($batchId)->willReturn($sections)->shouldBeCalledTimes(1);

        $sectionAdditionalLoader = $this->prophesize(Section_Additional_Data_Loader::class);

        /** @var Automatic_Curation_Saver $automaticCurationSaver1 */
        $automaticCurationSaver1 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver1->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        /** @var Automatic_Curation_Saver $automaticCurationSaver2 */
        $automaticCurationSaver2 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver2->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        $savers = [$automaticCurationSaver1->reveal(), $automaticCurationSaver2->reveal()];

        $subject = new Curation_Section_Service($sectionLoader->reveal(), $sectionAdditionalLoader->reveal(), $savers);
        $subject->loadWithAutomaticCurationSaving($batchId, $employeeID);

        $groupProphet = $this->prophesize(Curation_Item_Group::class);
        $group = $groupProphet->reveal();

        $collections = [];

        $curation_item1 = new Curation_Item();
        $curation_item1->set_sku('SVV11134');
        $curation_item1->set_kit_parents(['SVV11137']);

        $curation_item2 = new Curation_Item();
        $curation_item2->set_sku('SVV11135');
        $curation_item2->set_kit_parents(['SVV11137']);

        $curation_item3 = new Curation_Item();
        $curation_item3->set_sku('SVV11136');
        $curation_item3->set_kit_parents(['SVV11137']);

        $curation_item4 = new Curation_Item();
        $curation_item4->set_sku('SVV11137');
        $curation_item4->set_kit_parents([]);
        $curation_item4->set_related_kits([]);

        $collections[] = $curation_item1;
        $collections[] = $curation_item2;
        $collections[] = $curation_item3;
        $collections[] = $curation_item4;

        $logger = $this->prophesize(Logger::class);
        $kit_parent_grouper = $this->prophesize(Kit_Parent_Grouper::class);

        $sectionLoader3 = new Section_Loader(
            $this->prophesize(Curation_Tool_DAO::class)->reveal(),
            $this->prophesize(Context_Data_Service::class)->reveal(),
            $this->prophesize(Assortment_Curation_Decision_Loader::class)->reveal(),
            $this->prophesize(Curation_Image_Loader::class)->reveal(),
            $this->prophesize(Curation_Item_Group_Loader_Interface::class)->reveal(),
            $kit_parent_grouper->reveal(),
            $this->prophesize(World_Region_EU::class)->reveal(),
            $this->prophesize(Curation_Price_Loader::class)->reveal(),
            new CurationItemFactory(),
            $this->prophesize(FeatureTogglesInterface::class)->reveal(),
            $this->prophesize(Curation_Tool_Postgres_DAO::class)->reveal(),
            $logger->reveal(),
            $this->prophesize(Section_Additional_Data_Loader::class)->reveal()
        );

        $section = new Section();
        $section->set_title('Kits & Composites 1');
        $section->set_curation_items([$curation_item4]);

        $parents = [];
        $parent = new Kit_Parent();
        $parent->sku = 'SVV11137';
        $parent->related = [];

        $parents[] = $parent;

        $groupProphet->getItems()->willReturn($collections);
        $kit_parent_grouper->createKitParents($group)->willReturn($parents);

        $sectionLoader3->createKitGroupingSection('Kits & Composites 1', $group);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_reloads_sections_if_rows_affected()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $employeeID = 1;
        $affectedRows = 1;

        /** @var Section_Loader_Interface $sectionLoa2der */
        $sectionLoader = $this->prophesize(Section_Loader_Interface::class);

        $sectionAdditionalLoader = $this->prophesize(Section_Additional_Data_Loader::class);

        $sectionLoader->getSections($batchId)->willReturn($sections)->shouldBeCalledTimes(3);

        /** @var Automatic_Curation_Saver $automaticCurationSaver1 */
        $automaticCurationSaver1 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver1->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        /** @var Automatic_Curation_Saver $automaticCurationSaver2 */
        $automaticCurationSaver2 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver2->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        $savers = [$automaticCurationSaver1->reveal(), $automaticCurationSaver2->reveal()];

        $subject = new Curation_Section_Service($sectionLoader->reveal(), $sectionAdditionalLoader->reveal(), $savers);
        $subject->loadWithAutomaticCurationSaving($batchId, $employeeID);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_reloads_sections_if_rows_affected_return_blank()
    {
        $batchId = 0;
        $employeeID = 1;

        /** @var Section_Loader_Interface $sectionLoa2der */
        $sectionLoader = $this->prophesize(Section_Loader_Interface::class);

        $sectionAdditionalLoader = $this->prophesize(Section_Additional_Data_Loader::class);

        $savers = [];

        $subject = new Curation_Section_Service($sectionLoader->reveal(), $sectionAdditionalLoader->reveal(), $savers);
        $result = $subject->loadWithAutomaticCurationSaving($batchId, $employeeID);
        $this->assertEquals([], $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function load_call()
    {
        $batchId = 1;
        $sections = [
            $this->prophesize(Section::class)->reveal()
        ];
        $employeeID = 1;
        $affectedRows = 1;

        /** @var Section_Loader_Interface $sectionLoa2der */
        $sectionLoader = $this->prophesize(Section_Loader_Interface::class);

        $sectionAdditionalLoader = $this->prophesize(Section_Additional_Data_Loader::class);

        $sectionLoader->getSections($batchId)->willReturn($sections)->shouldBeCalledTimes(1);

        /** @var Automatic_Curation_Saver $automaticCurationSaver1 */
        $automaticCurationSaver1 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver1->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        /** @var Automatic_Curation_Saver $automaticCurationSaver2 */
        $automaticCurationSaver2 = $this->prophesize(Automatic_Curation_Saver::class);
        $automaticCurationSaver2->execute($batchId, Argument::any(), $employeeID)->willReturn($affectedRows);

        $savers = [$automaticCurationSaver1->reveal(), $automaticCurationSaver2->reveal()];

        $subject = new Curation_Section_Service($sectionLoader->reveal(), $sectionAdditionalLoader->reveal(), $savers);
        $subject->load($batchId);
    }
}
