<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\DTO;

class ValidationDetail
{
    /**
     * @var int
     */
    private $batchId;

    /**
     * @var string[]
     */
    private $errors = [];

    /**
     * @var string[]
     */
    private $skus = [];

    /**
     * ValidationDetail constructor.
     *
     * @param int      $batchId
     * @param string[] $errors
     * @param string[] $skus
     */
    public function __construct(int $batchId, array $errors, array $skus)
    {
        $this->batchId = $batchId;
        $this->errors = $errors;
        $this->skus = $skus;
    }

    /**
     * @return int
     */
    public function getBatchId(): int
    {
        return $this->batchId;
    }

    /**
     * @return string[]
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @return string[]
     */
    public function getSkus(): array
    {
        return $this->skus;
    }
}
