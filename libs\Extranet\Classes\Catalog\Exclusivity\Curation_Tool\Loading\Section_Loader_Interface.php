<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

interface Section_Loader_Interface {
  /**
   * @param int $batchId Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
   */
  public function getSections(int $batchId) : array;

    /**
     * @param int $batchId Batch ID
     * @param boolean $count count
     * @param string $sectionName sectionName
     * @param int $pageNum pagenum
     * @param int $limit limit
     *
     *
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    public function getSectionsCountAndData(int $batchId,  bool $count, string $sectionName, int $pageNum, int $limit, string $supplier, string $countBase, string $pageType, int $xnId) : array;


    public function createKitGroupingSection(string $name, Curation_Item_Group $group) : Section;

}