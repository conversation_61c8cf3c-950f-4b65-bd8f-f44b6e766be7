<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Contract;

interface UrlBuilderInterface
{
    /**
     * @param string $service Service name
     * @param string $resource Resource name
     *
     * @return string URL to PuREST resource
     * @throws \WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTUrlBuilderException
     */
    public function buildUrl(string $service, string $resource): string;
}
