<?php
/**
 * PHP version 8
 *
 * <AUTHOR> Tymoshy<PERSON> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use App\Application\Translation\TranslatorInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;

class Brand_Catalog_Validation implements Curation_Data_Consistency_Validation {
  /**
   * @var TranslatorInterface
   */
  private TranslatorInterface $translate;

  /**
   * @var Curation_Data_Consistency_Validation_Storage
   */
  private Curation_Data_Consistency_Validation_Storage $dao;

  /**
   * @param TranslatorInterface                          $translate Translator
   * @param Curation_Data_Consistency_Validation_Storage $dao       DAO
   */
  public function __construct(TranslatorInterface $translate, Curation_Data_Consistency_Validation_Storage $dao) {
    $this->translate = $translate;
    $this->dao       = $dao;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return Curation_Data_Consistency_Validation_Result
   */
  public function validate(int $batch_id, array $sections): Curation_Data_Consistency_Validation_Result {
    $result = new Curation_Data_Consistency_Validation_Result();
    $skus   = $this->extract_skus_from_sections($sections);

    if (count($skus) === 0) {
      return $result;
    }

    $skus_invalid_brand_catalog = $this->dao->get_skus_invalid_brand_catalog($skus);

    foreach ($skus_invalid_brand_catalog as $sku) {
      $result->add_error($sku, $this->translate->trans('Validation.CurationSkuInvalidBrandCatalog', ['{sku}' => $sku]));
    }

    return $result;
  }


  /**
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  private function extract_skus_from_sections(array $sections): array {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        $skus[] = $item->get_sku();
      }
    }

    return $skus;
  }
}
