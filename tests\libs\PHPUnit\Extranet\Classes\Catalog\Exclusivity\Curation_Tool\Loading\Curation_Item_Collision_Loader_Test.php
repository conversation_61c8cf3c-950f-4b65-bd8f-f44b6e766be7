<?php

declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Exception;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Collision_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_<PERSON>g<PERSON>_DAO;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Kit_Collider;
use WF\Shared\Curation\Api\Api_Curation;
use WF\Shared\Curation\Api\ApiProductContextCollectionNextgen;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Logging\Logger;
use WF\Shared\SLO\NullSLOReceiver;

use WF\Shared\SLO\SLOFactory;
use function array_map;
use function array_merge;
use function mb_strlen;
use function mb_strtoupper;
use function rand;

class Curation_Item_Collision_Loader_Test extends TestCase
{
    use ProphecyTrait;

    private $logger;

    /**
     * @param int   $batchId                        ID of batch
     * @param array $dbSkusRecords                  List of SKU received from DB
     * @param array $kitCollisions                  List of SKU loaded as Kit collision
     * @param bool  $featureCollisionsOptimization  Does feature enabled
     * @param array $collectionCollisions           List of SKU loaded as Collection collision
     * @param bool  $featureUseEligibilityFilter    Do we need to use API to check eligibility
     * @param array $eligibilitySKUs                List of SKU -> eligible data
     * @param array $expectedSKUs                   List of expected SKU received after business logic
     * @param bool  $featureUseContextCollectionApi Should we use an API for context collection SKUs or no
     *
     * @return void
     * @test
     * @dataProvider loadSkusDataProvider
     */
    public function should_load_skus(
        int $batchId,
        array $dbSkusRecords,
        array $kitCollisions,
        bool $featureCollisionsOptimization,
        array $collectionCollisions,
        bool $featureUseEligibilityFilter,
        array $eligibilitySKUs,
        array $expectedSKUs,
        bool $featureUseContextCollectionApi = false
    ) {
        // given
        /** @var Curation_Tool_DAO $curationToolDao */
        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);

        /** @var Curation_Kit_Collider $kitCollider */
        $kitCollider = $this->prophesize(Curation_Kit_Collider::class);

        $dbSkus = array_map(
            static fn (array $item) => $item['sku'],
            $dbSkusRecords
        );
        $kitCollider->getCollisionsForSKUS($dbSkus)->willReturn($kitCollisions)->shouldBeCalled();

        //    $this->mock_feature_toggle(
        //        Curation_Item_Collision_Loader::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION,
        //        $featureCollisionsOptimization
        //    );
        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::ENABLE_CONTEXT_COLLECTION_API_FOR_COLLISIONS,
        //        $featureUseContextCollectionApi
        //    );

        /** @var ApiProductContextCollectionNextgen $pccNextgenApi */
        $pccNextgenApi = $this->prophesize(ApiProductContextCollectionNextgen::class);

        /** @var Curation_Collection_Collider $curationCollectionCollider */
        $curationCollectionCollider = $this->prophesize(Curation_Collection_Collider::class);
        if ($featureCollisionsOptimization) {
            //    $curationCollectionCollider->get_all_collisions_collections($contextXnIDs, $skus)->willReturn($sections)->shouldBeCalled();
            // array where key - SKU, value - collection id (weird naming ~ $contextXnID)
            //      $curationCollectionCollider->get_all_collisions_collections(Argument::any())->shouldNotBeCalled();

            throw new Exception('not implemented yet');
        } elseif ($featureUseContextCollectionApi) {
            $curationCollectionCollider->get_all_collisions_collections(Argument::any())
                ->shouldNotBeCalled();
            $curationCollectionCollider->get_collisions_for_collection(Argument::any())
                ->shouldNotBeCalled();

            $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();
            $pccNextgenApi->findSkuCollisions($dbSkus)
                ->willReturn($collectionCollisions)
                ->shouldBeCalledOnce();
        } else {
            $pccNextgenApi->findSkuCollisions(Argument::any())->shouldNotBeCalled();
            $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();

            $curationCollectionCollider->get_all_collisions_collections(Argument::any())
                ->shouldNotBeCalled();

            foreach ($collectionCollisions as $contextXnId => $skus) {
                // TODO implement the multiple iteration case (in such case we'll need to increase exclude SKUs array)
                $curationCollectionCollider->get_collisions_for_collection(
                    $contextXnId,
                    $dbSkus
                )->willReturn($skus)->shouldBeCalled();
            }
        }

        $sloFactory = $this->prophesize(SLOFactory::class);

        $sloReceiver = new NullSLOReceiver();
        $sloFactory->start(Argument::any())->willReturn($sloReceiver);
        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalled();
        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::MERCH_CURATION_USE_ELIGIBILITY_FILTER_COLLISION,
        //        $featureUseEligibilityFilter
        //    );

        /** @var Api_Curation $curationApi */
        $curationApi = $this->prophesize(Api_Curation::class);
        if ($featureUseEligibilityFilter && $eligibilitySKUs) {
            $curationApi->is_eligible_batch(Argument::any())->willReturn($eligibilitySKUs)->shouldBeCalled();
        } else {
            $curationApi->is_eligible_batch(Argument::any())->shouldNotBeCalled();
        }

        //$logger = $this->prophesize(Logger::class);
        $this->logger = $this->prophesize(Logger::class);

        $subject = new Curation_Item_Collision_Loader(
            $curationToolDao->reveal(),
            new CurationItemFactory(),
            $curationCollectionCollider->reveal(),
            $kitCollider->reveal(),
            $curationApi->reveal(),
            $sloFactory->reveal(),
            $this->logger->reveal(),
            $featureUseContextCollectionApi,
            $featureCollisionsOptimization,
            $featureUseEligibilityFilter,
            $pccNextgenApi->reveal(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal()
        );

        // when
        $newItems = $subject->load($batchId, -1);

        // then
        $this->assertEquals($expectedSKUs, $newItems, 'List of SKUs is wrong.');
    }

    /**
     * @return array
     */
    public function loadSkusDataProvider(): array
    {
        $dataToProvide = [];

        // single SKU without collision, without filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $collectionCollisions = [
            $contextXnId => [],
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId]);

        $dataToProvide['single_sku_without_collision_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // single SKU without collision, with API collision feature, without filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $featureUseContextCollectionApi = true;
        $collectionCollisions = [
            $sku => $contextXnId,
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId]);

        $dataToProvide['single_sku_without_collision_with_collection_api_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
            $featureUseContextCollectionApi,
        ];

        // single SKU without collision, with filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $collectionCollisions = [
            $contextXnId => [],
        ];
        $featureUseEligibilityFilter = true;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId]);

        $dataToProvide['single_sku_without_collision_with_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // single SKU with collection collision, without filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId, $collisionSku => $contextXnId]);

        $dataToProvide['single_sku_with_collection_collision_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // single SKU with collection collision, with api feature, without filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $featureUseContextCollectionApi = true;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $collisionSku => $contextXnId,
            $sku => $contextXnId,
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId, $collisionSku => $contextXnId]);

        $dataToProvide['single_sku_with_collection_collision_with_api_feature_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
            $featureUseContextCollectionApi
        ];

        // single SKU with collection collision, with positive filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];
        $featureUseEligibilityFilter = true;
        $eligibilitySKUs = [$collisionSku => true];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId, $collisionSku => $contextXnId]);

        $dataToProvide['single_sku_with_collection_collision_with_positive_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // single SKU with collection collision, with negative filtration
        $batchId = $this->randomInt();
        $sku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $sku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionsSkus = [];
        $featureCollisionsOptimization = false;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];
        $featureUseEligibilityFilter = true;
        $eligibilitySKUs = [$collisionSku => false];

        $expectedSKUs = $this->buildExpectedSkus([$sku => $contextXnId]);

        $dataToProvide['single_sku_with_collection_collision_with_negative_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // SKUs with collection & kit collision, without filtration
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $kitSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => $contextXnId,
            ],
            [
                'sku' => $kitSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionSku1 = mb_strtoupper($this->randomString(8));
        $kitCollisionSku2 = mb_strtoupper($this->randomString(8));
        $kitCollisionsSkus = [$kitCollisionSku1, $kitCollisionSku2];
        $featureCollisionsOptimization = false;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => $contextXnId,
                $kitSku => $contextXnId,
                $collisionSku => $contextXnId,
                $kitCollisionSku1 => null,
                $kitCollisionSku2 => null,
            ]
        );

        $dataToProvide['skus_with_collection_and_kit_collision_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        // SKUs with collection & kit collision, with api collection feature, without filtration
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $kitSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => $contextXnId,
            ],
            [
                'sku' => $kitSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionSku1 = mb_strtoupper($this->randomString(8));
        $kitCollisionSku2 = mb_strtoupper($this->randomString(8));
        $kitCollisionsSkus = [$kitCollisionSku1, $kitCollisionSku2];
        $featureCollisionsOptimization = false;
        $featureUseContextCollectionApi = true;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $singleSku => $contextXnId,
            $kitSku => $contextXnId,
            $collisionSku => $contextXnId,
        ];
        $featureUseEligibilityFilter = false;
        $eligibilitySKUs = [];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => $contextXnId,
                $kitSku => $contextXnId,
                $collisionSku => $contextXnId,
                $kitCollisionSku1 => null,
                $kitCollisionSku2 => null,
            ]
        );

        $dataToProvide['skus_with_collection_and_kit_collision_with_collection_api_without_filtration'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
            $featureUseContextCollectionApi
        ];

        // SKUs with collection & kit collision, ignore filtration for kit
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $kitSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => $contextXnId,
            ],
            [
                'sku' => $kitSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionSku1 = mb_strtoupper($this->randomString(8));
        $kitCollisionSku2 = mb_strtoupper($this->randomString(8));
        $kitCollisionsSkus = [$kitCollisionSku1, $kitCollisionSku2];
        $featureCollisionsOptimization = false;
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];
        $featureUseEligibilityFilter = true;
        $eligibilitySKUs = [
            $collisionSku => true,
            $kitCollisionSku1 => false,
            $kitCollisionSku2 => false,
        ];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => $contextXnId,
                $kitSku => $contextXnId,
                $collisionSku => $contextXnId,
                $kitCollisionSku1 => null,
                $kitCollisionSku2 => null,
            ]
        );

        $dataToProvide['skus_with_collection_and_kit_collision_ignore_filtration_for_kit'] = [
            $batchId,
            $dbSkusRecords,
            $kitCollisionsSkus,
            $featureCollisionsOptimization,
            $collectionCollisions,
            $featureUseEligibilityFilter,
            $eligibilitySKUs,
            $expectedSKUs,
        ];

        return $dataToProvide;
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_fallback_to_db_on_exception()
    {
        // given
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $collectionSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => null,
            ],
            [
                'sku' => $collectionSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => null,
                $collectionSku => $contextXnId,
                $collisionSku => $contextXnId,
            ]
        );

        /** @var Curation_Tool_DAO $curationToolDao */
        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldNotBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $curationToolPostgresDao->get_batch_items($batchId, -1)->willReturn($dbSkusRecords)->shouldBeCalled();


        /** @var Curation_Kit_Collider $kitCollider */
        $kitCollider = $this->prophesize(Curation_Kit_Collider::class);

        $dbSkus = array_map(
            static fn (array $item) => $item['sku'],
            $dbSkusRecords
        );
        $kitCollider->getCollisionsForSKUS($dbSkus)->willReturn([])->shouldBeCalledOnce();

        //    $this->mock_feature_toggle(
        //        Curation_Item_Collision_Loader::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION,
        //        false
        //    );
        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::ENABLE_CONTEXT_COLLECTION_API_FOR_COLLISIONS,
        //        true
        //    );

        /** @var Curation_Collection_Collider $curationCollectionCollider */
        $curationCollectionCollider = $this->prophesize(Curation_Collection_Collider::class);

        /** @var ApiProductContextCollectionNextgen $pccNextgenApi */
        $pccNextgenApi = $this->prophesize(ApiProductContextCollectionNextgen::class);
        $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();
        $pccNextgenApi->findSkuCollisions($dbSkus)
            ->willThrow(new API_Request_Exception())
            ->shouldBeCalledOnce();

        $curationCollectionCollider->get_all_collisions_collections(Argument::any())
            ->shouldNotBeCalled();

        foreach ($collectionCollisions as $contextXnId => $skus) {
            // TODO implement the multiple iteration case (in such case we'll need to increase exclude SKUs array)
            $curationCollectionCollider->get_collisions_for_collection(
                $contextXnId,
                $dbSkus
            )->willReturn($skus)->shouldBeCalled();
        }

        $sloFactory = $this->prophesize(SLOFactory::class);

        $sloReceiver = new NullSLOReceiver();
        $sloFactory->start(Argument::any())->willReturn($sloReceiver);

        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::MERCH_CURATION_USE_ELIGIBILITY_FILTER_COLLISION,
        //        false
        //    );

        /** @var Api_Curation $curationApi */
        $curationApi = $this->prophesize(Api_Curation::class);
        $curationApi->is_eligible(Argument::any())->shouldNotBeCalled();

        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalled();

        $logger = $this->prophesize(Logger::class);

        $subject = new Curation_Item_Collision_Loader(
            $curationToolDao->reveal(),
            new CurationItemFactory(),
            $curationCollectionCollider->reveal(),
            $kitCollider->reveal(),
            $curationApi->reveal(),
            $sloFactory->reveal(),
            $logger->reveal(),
            true,
            false,
            false,
            $pccNextgenApi->reveal(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal()
        );

        // when
        $newItems = $subject->load($batchId, -1);

        // then
        $this->assertEquals($expectedSKUs, $newItems, 'List of SKUs is wrong.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_fallback_to_db_from_api_on_pa_flow()
    {
        // given
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $collectionSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => null,
            ],
            [
                'sku' => $collectionSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $collisionSku = mb_strtoupper($this->randomString(8));
        $apiCollisions = [
            $singleSku => null,
            $collectionSku => null,
        ];
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => null,
                $collectionSku => $contextXnId,
                $collisionSku => $contextXnId,
            ]
        );

        /** @var Curation_Tool_DAO $curationToolDao */
        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldBeCalled();

        /** @var Curation_Kit_Collider $kitCollider */
        $kitCollider = $this->prophesize(Curation_Kit_Collider::class);

        $dbSkus = array_map(
            static fn (array $item) => $item['sku'],
            $dbSkusRecords
        );
        $kitCollider->getCollisionsForSKUS($dbSkus)->willReturn([])->shouldBeCalledOnce();

        //    $this->mock_feature_toggle(
        //        Curation_Item_Collision_Loader::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION,
        //        false
        //    );
        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::ENABLE_CONTEXT_COLLECTION_API_FOR_COLLISIONS,
        //        true
        //    );

        /** @var ApiProductContextCollectionNextgen $pccNextgenApi */
        $pccNextgenApi = $this->prophesize(ApiProductContextCollectionNextgen::class);

        /** @var Curation_Collection_Collider $curationCollectionCollider */
        $curationCollectionCollider = $this->prophesize(Curation_Collection_Collider::class);

        $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();
        $pccNextgenApi->findSkuCollisions($dbSkus)
            ->willReturn($apiCollisions)
            ->shouldBeCalledOnce();



        $curationCollectionCollider->get_all_collisions_collections(Argument::any())
            ->shouldNotBeCalled();

        foreach ($collectionCollisions as $contextXnId => $skus) {
            // TODO implement the multiple iteration case (in such case we'll need to increase exclude SKUs array)
            $curationCollectionCollider->get_collisions_for_collection(
                $contextXnId,
                $dbSkus
            )->willReturn($skus)->shouldBeCalled();
        }

        $sloFactory = $this->prophesize(SLOFactory::class);

        $sloReceiver = new NullSLOReceiver();
        $sloFactory->start(Argument::any())->willReturn($sloReceiver);
        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalled();

        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::MERCH_CURATION_USE_ELIGIBILITY_FILTER_COLLISION,
        //        false
        //    );

        /** @var Api_Curation $curationApi */
        $curationApi = $this->prophesize(Api_Curation::class);
        $curationApi->is_eligible(Argument::any())->shouldNotBeCalled();

        $logger = $this->prophesize(Logger::class);
        $this->logger = $this->prophesize(Logger::class);

        $subject = new Curation_Item_Collision_Loader(
            $curationToolDao->reveal(),
            new CurationItemFactory(),
            $curationCollectionCollider->reveal(),
            $kitCollider->reveal(),
            $curationApi->reveal(),
            $sloFactory->reveal(),
            $logger->reveal(),
            true,
            false,
            false,
            $pccNextgenApi->reveal(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal()
        );

        // when
        $newItems = $subject->load($batchId, -1);

        // then
        $this->assertEquals($expectedSKUs, $newItems, 'List of SKUs is wrong.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_combine_api_and_db_on_complex_pa_flow()
    {
        // given
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $collection1Sku = mb_strtoupper($this->randomString(8));
        $collection2Sku = mb_strtoupper($this->randomString(8));
        $contextXnId1 = $this->randomInt();
        $contextXnId2 = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => null,
            ],
            [
                'sku' => $collection1Sku,
                'context_xn_id' => $contextXnId1,
            ],
            [
                'sku' => $collection2Sku,
                'context_xn_id' => $contextXnId2,
            ],
        ];
        $collision1Sku = mb_strtoupper($this->randomString(8));
        $collision2Sku = mb_strtoupper($this->randomString(8));
        $apiCollisions = [
            $singleSku => null,
            $collection1Sku => $contextXnId1,
            $collection2Sku => null,
            $collision1Sku => $contextXnId1,
        ];
        $collectionCollisions = [
            $contextXnId2 => [$collision2Sku],
        ];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => null,
                $collection1Sku => $contextXnId1,
                $collision1Sku => $contextXnId1,
                $collection2Sku => $contextXnId2,
                $collision2Sku => $contextXnId2,
            ]
        );

        /** @var Curation_Tool_DAO $curationToolDao */
        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $curationToolPostgresDao->get_batch_items($batchId, -1)->willReturn($dbSkusRecords)->shouldNotBeCalled();
        /** @var Curation_Kit_Collider $kitCollider */
        $kitCollider = $this->prophesize(Curation_Kit_Collider::class);

        $dbSkus = array_map(
            static fn (array $item) => $item['sku'],
            $dbSkusRecords
        );
        $kitCollider->getCollisionsForSKUS($dbSkus)->willReturn([])->shouldBeCalledOnce();

        //    $this->mock_feature_toggle(
        //        Curation_Item_Collision_Loader::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION,
        //        false
        //    );
        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::ENABLE_CONTEXT_COLLECTION_API_FOR_COLLISIONS,
        //        true
        //    );


        /** @var ApiProductContextCollectionNextgen $pccNextgenApi */
        $pccNextgenApi = $this->prophesize(ApiProductContextCollectionNextgen::class);

        /** @var Curation_Collection_Collider $curationCollectionCollider */
        $curationCollectionCollider = $this->prophesize(Curation_Collection_Collider::class);

        $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();
        $pccNextgenApi->findSkuCollisions($dbSkus)
            ->willReturn($apiCollisions)
            ->shouldBeCalledOnce();

        $curationCollectionCollider->get_all_collisions_collections(Argument::any())
            ->shouldNotBeCalled();

        foreach ($collectionCollisions as $contextXnId => $skus) {
            // TODO implement the multiple iteration case (in such case we'll need to increase exclude SKUs array)
            $curationCollectionCollider->get_collisions_for_collection(
                $contextXnId,
                array_merge($dbSkus, [$collision1Sku])
            )->willReturn($skus)->shouldBeCalled();
        }

        $sloFactory = $this->prophesize(SLOFactory::class);

        $sloReceiver = new NullSLOReceiver();
        $sloFactory->start(Argument::any())->willReturn($sloReceiver);

        //    $this->mock_feature_toggle(
        //        Feature_Toggle_Names::MERCH_CURATION_USE_ELIGIBILITY_FILTER_COLLISION,
        //        false
        //    );

        /** @var Api_Curation $curationApi */
        $curationApi = $this->prophesize(Api_Curation::class);
        $curationApi->is_eligible(Argument::any())->shouldNotBeCalled();

        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalled();
        $logger = $this->prophesize(Logger::class);
        $this->logger = $this->prophesize(Logger::class);


        $subject = new Curation_Item_Collision_Loader(
            $curationToolDao->reveal(),
            new CurationItemFactory(),
            $curationCollectionCollider->reveal(),
            $kitCollider->reveal(),
            $curationApi->reveal(),
            $sloFactory->reveal(),
            $logger->reveal(),
            true,
            false,
            false,
            $pccNextgenApi->reveal(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal()
        );

        // when
        $newItems = $subject->load($batchId, -1);

        // then
        $this->assertEquals($expectedSKUs, $newItems, 'List of SKUs is wrong.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_use_default_eligible_value_when_api_not_available()
    {
        // given
        $batchId = $this->randomInt();
        $singleSku = mb_strtoupper($this->randomString(8));
        $kitSku = mb_strtoupper($this->randomString(8));
        $contextXnId = $this->randomInt();
        $dbSkusRecords = [
            [
                'sku' => $singleSku,
                'context_xn_id' => $contextXnId,
            ],
            [
                'sku' => $kitSku,
                'context_xn_id' => $contextXnId,
            ],
        ];
        $kitCollisionSku1 = mb_strtoupper($this->randomString(8));
        $kitCollisionSku2 = mb_strtoupper($this->randomString(8));
        $kitCollisionsSkus = [$kitCollisionSku1, $kitCollisionSku2];
        $collisionSku = mb_strtoupper($this->randomString(8));
        $collectionCollisions = [
            $contextXnId => [$collisionSku],
        ];

        $expectedSKUs = $this->buildExpectedSkus(
            [
                $singleSku => $contextXnId,
                $kitSku => $contextXnId,
                $collisionSku => $contextXnId,
                $kitCollisionSku1 => null,
                $kitCollisionSku2 => null,
            ]
        );

        /** @var Curation_Tool_DAO $curationToolDao */
        $curationToolDao = $this->prophesize(Curation_Tool_DAO::class);
        $curationToolDao->get_batch_items($batchId)->willReturn($dbSkusRecords)->shouldNotBeCalled();

        $curationToolPostgresDao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $curationToolPostgresDao->get_batch_items($batchId, -1)->willReturn($dbSkusRecords)->shouldBeCalled();

        /** @var Curation_Kit_Collider $kitCollider */
        $kitCollider = $this->prophesize(Curation_Kit_Collider::class);
        $dbSkus = array_map(
            static fn (array $item) => $item['sku'],
            $dbSkusRecords
        );
        $kitCollider->getCollisionsForSKUS($dbSkus)->willReturn($kitCollisionsSkus)->shouldBeCalled();

        /** @var ApiProductContextCollectionNextgen $pccNextgenApi */
        $pccNextgenApi = $this->prophesize(ApiProductContextCollectionNextgen::class);

        /** @var Curation_Collection_Collider $curationCollectionCollider */
        $curationCollectionCollider = $this->prophesize(Curation_Collection_Collider::class);
        $pccNextgenApi->findSkuCollisions(Argument::any())->shouldNotBeCalled();
        $pccNextgenApi->findProductContextCollection(Argument::any())->shouldNotBeCalled();
        $curationCollectionCollider->get_all_collisions_collections(Argument::any())
            ->shouldNotBeCalled();
        foreach ($collectionCollisions as $contextXnId => $skus) {
            $curationCollectionCollider->get_collisions_for_collection(
                $contextXnId,
                $dbSkus
            )->willReturn($skus)->shouldBeCalled();
        }

        $sloFactory = $this->prophesize(SLOFactory::class);
        $sloReceiver = new NullSLOReceiver();
        $sloFactory->start(Argument::any())->willReturn($sloReceiver);

        /** @var Api_Curation $curationApi */
        $curationApi = $this->prophesize(Api_Curation::class);
        $curationApi->is_eligible_batch(Argument::any())
            ->willThrow(new API_Request_Exception())->shouldBeCalledOnce();

        $featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalled();
        $logger = $this->prophesize(Logger::class);
        $this->logger = $this->prophesize(Logger::class);

        $subject = new Curation_Item_Collision_Loader(
            $curationToolDao->reveal(),
            new CurationItemFactory(),
            $curationCollectionCollider->reveal(),
            $kitCollider->reveal(),
            $curationApi->reveal(),
            $sloFactory->reveal(),
            $logger->reveal(),
            false,
            false,
            true,
            $pccNextgenApi->reveal(),
            $featureToggles->reveal(),
            $curationToolPostgresDao->reveal()
        );

        // when
        $newItems = $subject->load($batchId, -1);

        // then
        $this->assertEquals($expectedSKUs, $newItems, 'List of SKUs is wrong.');
    }

    /**
     * @param array $skuToContext List of SKU - Context ID to build list of result SKU
     *
     * @return array
     */
    private function buildExpectedSkus(array $skuToContext): array
    {
        $expectedSKUs = [];
        foreach ($skuToContext as $sku => $contextXnId) {
            $item = new Curation_Item();
            $item->set_sku($sku);
            if ($contextXnId) {
                $item->set_context_xn_id($contextXnId);
            }

            $expectedSKUs[$sku] = $item;
        }

        return $expectedSKUs;
    }

    // TODO move methods below to the trait ?..

    /**
     * @param int $len Length of result int
     *
     * @return int
     */
    private function randomInt(int $len = 6): int
    {
        return (int) $this->randomDigitString($len);
    }

    /**
     * @param int $len Length of result digit
     *
     * @return string
     */
    private function randomDigitString(int $len = 6): string
    {
        $chars = '0123456789';

        return $this->randomChars($len, $chars);
    }

    /**
     * @param int $len Length of result string
     *
     * @return string
     */
    private function randomString(int $len = 10): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

        return $this->randomChars($len, $chars);
    }

    /**
     * @param int    $len     Length of result string
     * @param string $charset Chars to be used in result string
     *
     * @return string
     */
    private function randomChars(int $len, string $charset): string
    {
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            $index = rand(0, mb_strlen($charset) - 1);
            $str .= $charset[$index];
        }

        return $str;
    }
}
