<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Curation_Item_Brand_Type extends AbstractEnumeration {

  /**
   * @var array
   */
  private static $values = [
      'Non EB'      => 0,
      'EB Tail'     => 1,
      'EB Flagship' => 2
  ];

  /**
   * @return Curation_Item_Brand_Type
   */
  public static function non_eb() : self {
    return self::get_value_for_option(self::$values['Non EB']); /** @phpstan-ignore-line */
  }

  /**
   * @return Curation_Item_Brand_Type
   */
  public static function eb_flagship() : self {
    return self::get_value_for_option(self::$values['EB Flagship']); /** @phpstan-ignore-line */
  }

  /**
   * @return Curation_Item_Brand_Type
   */
  public static function eb_tail() : self {
    return self::get_value_for_option(self::$values['EB Tail']); /** @phpstan-ignore-line */
  }
}
