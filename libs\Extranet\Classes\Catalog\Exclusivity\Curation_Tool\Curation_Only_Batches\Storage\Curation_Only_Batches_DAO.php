<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use PDO;

class Curation_Only_Batches_DAO implements Curation_Only_Batch_Service_Storage {
  use Logging_Trait;

  private ProductConnection $pdo_ptrw;

  /**
   * @param ProductConnection         $pdo_ptrw C6
   * @param LoggerInterface|null $logger   Logger
   */
  public function __construct(ProductConnection $pdo_ptrw, ?LoggerInterface $logger = null) {
    $this->pdo_ptrw = $pdo_ptrw;
    $this->logger = $logger;
  }

  /**
   * @return int
   */
  public function create_verification_id() : int {
    $sql = '
    INSERT INTO csn_product.dbo.tblVerification (VerifiedDate, CloningPrtID)
    OUTPUT INSERTED.VerificationID
    VALUES (GETDATE(), :ticket_id)
    ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue(':ticket_id', 0, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot create Verification ID');
    }

    return $statement->fetch(PDO::FETCH_COLUMN);
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   * @param bool $fromPWL from partial white label
   *
   * @return void
   */
  public function mark_batch_as_sent(int $batch_id, int $verification_id, bool $fromPWL = false) : void {
    $statusUpd = "StatusId = :status_id,";
    if($fromPWL)
    {
      $statusUpd = '';
    }
    $sql = '
    UPDATE csn_product.dbo.tblCurationBatch
    SET SentWLAt = GETDATE(),
    '.$statusUpd.'
    VerificationID = :verification_id
    WHERE ID = :batch_id
    ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    if(!$fromPWL) {
     $statement->bindValue(':status_id', Batch::DOWNSTREAMED_STATUS, PDO::PARAM_INT);
    }

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot mark batch as sent');
    }
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verficiation ID
   *
   * @return void
   */
  public function set_verification_id_for_skus(int $batch_id, int $verification_id) : void {
    $sql = '
    UPDATE csn_product.dbo.tblVerificationItem
    SET ViVerificationID = :verification_id
    WHERE ViBatchID = :batch_id
    ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot set verification id for skus');
    }
  }
}
