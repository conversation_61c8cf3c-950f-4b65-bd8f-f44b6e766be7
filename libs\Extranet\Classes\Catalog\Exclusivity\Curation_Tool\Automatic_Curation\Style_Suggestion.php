<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Style_Suggestion {

  /**
   * @var int
   */
  private $style_id;

  /**
   * @var string
   */
  private $style_name;

  /**
   * @var float
   */
  private $probability;

  /**
   * @var int
   */
  private $rank;

  /**
   * Style_Suggestion constructor.
   *
   * @param int    $style_id    Style Id
   * @param string $style_name  Style Name
   * @param float  $probability Probability
   * @param int    $rank        Rank
   */
  public function __construct(int $style_id, string $style_name, float $probability, int $rank) {
    $this->style_id    = $style_id;
    $this->style_name  = $style_name;
    $this->probability = $probability;
    $this->rank        = $rank;
  }

  /**
   * @return int
   */
  public function get_style_id() : int {
    return $this->style_id;
  }

  /**
   * @return string
   */
  public function get_style_name() : string {
    return $this->style_name;
  }

  /**
   * @return float
   */
  public function get_probability() : float {
    return round($this->probability, 2);
  }

  /**
   * @return int
   */
  public function get_rank() : int {
    return $this->rank;
  }
}
