<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date;

class Curation_Item implements \JsonSerializable {
  /**
   * @var string
   */
  private $sku;

  /**
   * @var int|null
   */
  private $context_xn_id;

  /**
   * @var string[]
   */
  private $kit_parents = [];

  /**
   * @var string[]
   */
  private $related_kits = [];

  /**
   * @var int
   */
  private $status;

  /**
   * @var bool
   */
  private $is_kitsco = false;

  /**
   * @var bool
   */
  private $readonly = false;

  /**
   * @var string|null
   */
  private $name;

  /**
   * @var int|null
   */
  private $class_id;

  /**
   * @var string|null
   */
  private $class_name;

  /**
   * @var string|null
   */
  private $manufacturer_name;

  /**
   * @var int|null
   */
  private $manufacturer_brw_id;

  /**
   * @var int
   */
  private $brand_type;

  /**
   * @var string
   */
  private $brand_name;

  /**
   * @var float|null
   */
  private $sale_price;

  /**
   * @var int|null
   */
  private $excluded_reason_id;

  /**
   * @var string
   */
  private $automatic_excluded_reason;

  /**
   * @var int|null
   */
  private $price_tier;

  /**
   * @var int|null
   */
  private $final_style_id;

  /**
   * @var int|null
   */
  private $final_sub_style_id;

  /**
   * @var int|null
   */
  private $final_granular_style_id;

  /**
   * @var int|null
   */
  private $final_brand_id;

  /**
   * @var string|null
   */
  private $brand_catalog_name;

  /**
   * @var bool
   */
  private $is_wrong_continent;

  /**
   * @var string|null
   */
  private $saved_at;

  /**
   * @var string|null
   */
  private $saved_by;

  /**
   * @var int|null
   */
  private $decision_source_id;

  /**
   * @var string|null
   */
  private $rejected_at;

  /**
   * @var string|null
   */
  private $rejected_by;

  /**
   * @var string|null
   */
  private $rejected_note;

  /**
   * @var string|null
   */
  private $style_decision_notes;

  /**
   * @var int|null
   */
  private $price_options_count;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date|null
   */
  private $last_clone_date;

  /**
   * @var int|null
   */
  private $image_resource_id;

  /**
   * @var Curation_QA_Status|null
   */
  private $qa_status;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type|null
   */
  private $type;

  /**
   * @var string|null
   */
  private $url;

  /**
   * @var string|null
   */
  private $image;

  /**
   * @var array|null
   */
  private $suppliers = [];

  /**
   * @var bool
   */
  private $is_canadian_supplier_only = false;

  /**
   * @var int[]
   */
  private $suggested_styles = [];

  /**
   * @var int[]
   */
  private $suggested_substyles = [];

  /**
   * @var bool
   */
  private $should_move_to_header_brand = false;

  /**
   * @var bool
   */
  private $should_move_to_tail_brand = false;

  /**
   * @var bool
   */
  private $is_predicted_winner = false;

  /**
   * @var bool
   */
  private $is_hold_out_manufacturer = false;

  /**
   * @var bool
   */
  private $is_whitelabel_downstream = false;


    /**
   * Curation_Item constructor.
   */
  public function __construct() {
    $this->qa_status = Curation_QA_Status::not_applicable();
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @param string $sku SKU
   *
   * @return void
   */
  public function set_sku(string $sku) {
    $this->sku = $sku;
  }

  /**
   * @return int|null
   */
  public function get_context_xn_id() {
    return $this->context_xn_id;
  }

  /**
   * @param int|null $context_xn_id Context XnID
   *
   * @return void
   */
  public function set_context_xn_id(int $context_xn_id = null) {
    $this->context_xn_id = $context_xn_id;
  }

  /**
   * @return array
   */
  public function get_kit_parents() : array {
    return $this->kit_parents;
  }

  /**
   * @param string[] $kit_parents Kit Parents
   *
   * @return void
   */
  public function set_kit_parents(array $kit_parents) {
    $this->kit_parents = $kit_parents;
  }

  /**
   * @return int
   */
  public function get_status() : int {
    return $this->status;
  }

  /**
   * @param int $status Status
   *
   * @return void
   */
  public function set_status(int $status) {
    $this->status = $status;
  }

  /**
   * @return bool
   */
  public function is_kitsco() : bool {
    return $this->is_kitsco;
  }

  /**
   * @param bool $is_kitsco Is Kitsco
   *
   * @return void
   */
  public function set_kitsco(bool $is_kitsco) {
    $this->is_kitsco = $is_kitsco;
  }

  /**
   * @return bool
   */
  public function is_readonly() : bool {
    return $this->readonly;
  }

  /**
   * @param bool $readonly Readonly
   *
   * @return void
   */
  public function set_readonly(bool $readonly) {
    $this->readonly = $readonly;
  }

  /**
   * @return null|string
   */
  public function get_name() {
    return $this->name;
  }

  /**
   * @param null|string $name Name
   *
   * @return void
   */
  public function set_name(string $name = null) {
    $this->name = $name;
  }

  /**
   * @return int|null
   */
  public function get_class_id() : ?int {
    return $this->class_id;
  }

  /**
   * @param int|null $class_id Class ID
   *
   * @return void
   */
  public function set_class_id(?int $class_id) {
    $this->class_id = $class_id;
  }

  /**
   * @return null|string
   */
  public function get_class_name() {
    return $this->class_name;
  }

  /**
   * @param null|string $class_name class_name
   *
   * @return void
   */
  public function set_class_name(string $class_name = null) {
    $this->class_name = $class_name;
  }

  /**
   * @return null|string
   */
  public function get_manufacturer_name() {
    return $this->manufacturer_name;
  }

  /**
   * @param null|string $manufacturer_name manufacturer_name
   *
   * @return void
   */
  public function set_manufacturer_name(string $manufacturer_name = null) {
    $this->manufacturer_name = $manufacturer_name;
  }

  /**
   * @return int|null
   */
  public function get_manufacturer_brw_id() {
    return $this->manufacturer_brw_id;
  }

  /**
   * @param int|null $manufacturer_brw_id manufacturer_brw_id
   *
   * @return void
   */
  public function set_manufacturer_brw_id(int $manufacturer_brw_id = null) {
    $this->manufacturer_brw_id = $manufacturer_brw_id;
  }

  /**
   * @param int $brand_type brand_type
   *
   * @return void
   */
  public function set_brand_type(int $brand_type = null) {
    $this->brand_type = $brand_type;
  }

  /**
   * @param string $brand_name brand_name
   *
   * @return void
   */
  public function set_brand_name(string $brand_name = null) {
    $this->brand_name = $brand_name;
  }

  /**
   * @return float|null
   */
  public function get_sale_price() {
    return $this->sale_price;
  }

  /**
   * @param float|null $sale_price Sale Price
   *
   * @return void
   */
  public function set_sale_price(float $sale_price = null) {
    $this->sale_price = $sale_price;
  }

  /**
   * @return int|null
   */
  public function get_excluded_reason_id() {
    return $this->excluded_reason_id;
  }

  /**
   * @param int|null $excluded_reason_id Excluded reason id
   *
   * @return void
   */
  public function set_excluded_reason_id(int $excluded_reason_id = null) {
    $this->excluded_reason_id = $excluded_reason_id;
  }

  /**
   * @return int|null
   */
  public function get_price_tier() {
    return $this->price_tier;
  }

  /**
   * @param int|null $price_tier Price Tier
   *
   * @return void
   */
  public function set_price_tier(int $price_tier = null) {
    $this->price_tier = $price_tier;
  }

  /**
   * @return int|null
   */
  public function get_final_style_id() {
    return $this->final_style_id;
  }

  /**
   * @param int|null $final_style_id Final Style ID
   *
   * @return void
   */
  public function set_final_style_id(int $final_style_id = null) {
    $this->final_style_id = $final_style_id;
  }

  /**
   * @return int|null
   */
  public function get_final_sub_style_id() {
    return $this->final_sub_style_id;
  }

  /**
   * @param int|null $final_sub_style_id final_sub_style_id
   *
   * @return void
   */
  public function set_final_sub_style_id(int $final_sub_style_id = null) {
    $this->final_sub_style_id = $final_sub_style_id;
  }

  /**
   * @param int|null $final_granular_style_id final_granular_style_id
   *
   * @return void
   */
  public function set_final_granular_style_id(int $final_granular_style_id = null) {
    $this->final_granular_style_id = $final_granular_style_id;
  }

  /**
   * @return int|null
   */
  public function get_final_brand_id() {
    return $this->final_brand_id;
  }

  /**
   * @param int|null $final_brand_id Final Brand id
   *
   * @return void
   */
  public function set_final_brand_id(int $final_brand_id = null) {
    $this->final_brand_id = $final_brand_id;
  }

  /**
   * @return null|string
   */
  public function get_saved_at() {
    return $this->saved_at;
  }

  /**
   * @return string|null
   */
  public function get_brand_catalog_name() {
    return $this->brand_catalog_name;
  }

  /**
   * @param string $brand_catalog_name Brand catalog Name
   *
   * @return void
   */
  public function set_brand_catalog_name(string $brand_catalog_name) {
    $this->brand_catalog_name = $brand_catalog_name;
  }

  /**
   * @param null|string $saved_at Save At
   *
   * @return void
   */
  public function set_saved_at(string $saved_at = null) {
    $this->saved_at = $saved_at;
  }

  /**
   * @return null|string
   */
  public function get_saved_by() {
    return $this->saved_by;
  }

  /**
   * @param null|string $saved_by Saved by
   *
   * @return void
   */
  public function set_saved_by(string $saved_by = null) {
    $this->saved_by = $saved_by;
  }

  /**
   * @return int|null
   */
  public function get_decision_source_id() {
    return $this->decision_source_id;
  }

  /**
   * @param int|null $decision_source_id Decision Source ID
   *
   * @return void
   */
  public function set_decision_source_id(?int $decision_source_id) {
    $this->decision_source_id = $decision_source_id;
  }

  /**
   * @return null|string
   */
  public function get_rejected_by() {
    return $this->rejected_by;
  }

  /**
   * @param null|string $rejected_by Rejected by
   *
   * @return void
   */
  public function set_rejected_by(string $rejected_by = null) {
    $this->rejected_by = $rejected_by;
  }

  /**
   * @return null|string
   */
  public function get_rejected_at() {
    return $this->rejected_at;
  }

  /**
   * @param null|string $rejected_at Rejected by
   *
   * @return void
   */
  public function set_rejected_at(string $rejected_at = null) {
    $this->rejected_at = $rejected_at;
  }

  /**
   * @return null|string
   */
  public function get_rejected_note() {
    return $this->rejected_note;
  }

  /**
   * @param null|string $rejected_note Rejected note
   *
   * @return void
   */
  public function set_rejected_note(string $rejected_note = null) {
    $this->rejected_note = $rejected_note;
  }

  /**
   * @return null|string
   */
  public function get_style_decision_notes() : ?string {
    return $this->style_decision_notes;
  }

  /**
   * @param null|string $style_decision_notes style_decision_note
   *
   * @return void
   */
  public function set_style_decision_notes(?string $style_decision_notes) {
    $this->style_decision_notes = $style_decision_notes;
  }

  /**
   * @return int|null
   */
  public function get_price_options_count() {
    return $this->price_options_count;
  }

  /**
   * @param int|null $price_options_count Price Options Count
   *
   * @return void
   */
  public function set_price_options_count(int $price_options_count = null) {
    $this->price_options_count = $price_options_count;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date|null
   */
  public function get_last_clone_date() {
    return $this->last_clone_date;
  }

  /**
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date $last_clone_date Last Clone Date
   *
   * @return void
   */
  public function set_last_clone_date(Last_Clone_Date $last_clone_date) {
    $this->last_clone_date = $last_clone_date;
  }

  /**
   * @return int|null
   */
  public function get_image_resource_id() {
    return $this->image_resource_id;
  }

  /**
   * @param int|null $image_resource_id Image Resource ID
   *
   * @return void
   */
  public function set_image_resource_id(int $image_resource_id = null) {
    $this->image_resource_id = $image_resource_id;
  }

  /**
   * @return null|\WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public function get_qa_status(): ?Curation_QA_Status {
    return $this->qa_status;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status $qa_status QA Status ID
   *
   * @return void
   */
  public function set_qa_status(Curation_QA_Status $qa_status) {
    $this->qa_status = $qa_status;
  }

  /**
   * @return null|\WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type
   */
  public function get_type() {
    return $this->type;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type $type Type
   *
   * @return void
   */
  public function set_type(Curation_Item_Type $type = null) {
    $this->type = $type;
  }

  /**
   * @return array
   */
  public function get_related_kits() : array {
    return $this->related_kits;
  }

  /**
   * @param array $related_kits Related kits
   *
   * @return void
   */
  public function set_related_kits(array $related_kits) {
    $this->related_kits = $related_kits;
  }

  /**
   * @return null|string
   */
  public function get_url() {
    return $this->url;
  }

  /**
   * @param string|null $url URL
   *
   * @return void
   */
  public function set_url(string $url = null) {
    $this->url = $url;
  }

  /**
   * @return null|string
   */
  public function get_image() {
    return $this->image;
  }

  /**
   * @param string|null $image Image
   *
   * @return void
   */
  public function set_image(string $image = null) {
    $this->image = $image;
  }

  /**
   * @return string[]
   */
  public function get_suppliers() : array {
    // TODO: This check is necessary because of bad data
    // TODO: Revert after the issue with empty suppliers is solved
    return $this->suppliers ?? [];
  }

  /**
   * @param mixed $suppliers Suppliers
   *
   * @return void
   */
  public function set_suppliers($suppliers) {
    //TODO: Same issue as get_suppliers;
    $this->suppliers = $suppliers ?? [];
  }

  /**
   * @return bool
   */
  public function is_canadian_only_supplier() : bool {
    return $this->is_canadian_supplier_only;
  }

  /**
   * @param bool $is_canadian_supplier_only Is Canadian Supplier
   *
   * @return void
   */
  public function set_canadian_supplier_only(bool $is_canadian_supplier_only) {
    $this->is_canadian_supplier_only = $is_canadian_supplier_only;
  }

  /**
   * @return bool
   */
  public function is_pending_qa() : bool {
    return !in_array($this->qa_status, [Curation_QA_Status::approved(), Curation_QA_Status::updated()]);
  }

  /**
   * @return bool
   */
  public function is_rejected() : bool {
    return $this->qa_status === Curation_QA_Status::rejected();
  }

  /**
   * @return bool
   */
  public function is_approved_qa() : bool {
    return $this->qa_status === Curation_QA_Status::approved();
  }

  /**
   * @return bool
   */
  public function is_rejected_qa() : bool {
    return $this->qa_status === Curation_QA_Status::rejected();
  }

  /**
   * @return bool
   */
  public function is_updated_qa() : bool {
    return $this->qa_status === Curation_QA_Status::updated();
  }

  /**
   * @return null|string
   */
  public function get_saved_at_formatted() {
    if (empty($this->saved_at)) {
      return null;
    }

    return (new \DateTime($this->saved_at))->format('Y-m-d H:i:s');
  }

  /**
   * @return null|string
   */
  public function get_rejected_at_formatted() {
    if (empty($this->rejected_at)) {
      return null;
    }

    return (new \DateTime($this->rejected_at))->format('Y-m-d H:i:s');
  }

  /**
   * @return bool
   */
  public function is_recently_cloned() : bool {
    return $this->last_clone_date !== null ? $this->last_clone_date->is_recently_cloned() : false;
  }

  /**
   * @return null|string
   */
  public function get_last_clone_date_formatted() {
    return $this->last_clone_date !== null ? $this->last_clone_date->get_date_formatted() : null;
  }

  /**
   * @return bool
   */
  public function get_is_wrong_continent() : bool {
    return $this->is_wrong_continent;
  }

  /**
   * @param bool $is_wrong Does this catalog id belong to wrong continent
   *
   * @return void
   */
  public function set_is_wrong_continent(bool $is_wrong) {
    $this->is_wrong_continent = $is_wrong;
  }

  /**
   * @return string|null
   */
  public function get_saved_by_detailed() : ?string {
    if (empty($this->decision_source_id)) {
      return $this->saved_by;
    }

    return sprintf('%s (%s)', $this->saved_by, Curation_Decision_Source::create($this->decision_source_id)->label());
  }

  /**
   * @param int[] $suggested_styles Suggested styles
   *
   * @return void
   */
  public function set_suggested_styles(array $suggested_styles) {
    $this->suggested_styles = $suggested_styles;
  }

  /**
   * @return int[]
   */
  public function get_suggested_styles() : array {
    return $this->suggested_styles;
  }

  /**
   * @param int[] $suggested_substyles Suggested sub styles
   *
   * @return void
   */
  public function set_suggested_substyles(array $suggested_substyles) {
    $this->suggested_substyles = $suggested_substyles;
  }

  /**
   * @return int[]
   */
  public function get_suggested_substyles() : array {
    return $this->suggested_substyles;
  }

  /**
   * @return int|null
   */
  public function get_final_granular_style_id() : ?int {
    return $this->final_granular_style_id;
  }

  /**
   * @param bool $should_move_to_header_brand should_move_to_header_brand
   *
   * @return void
   */
  public function set_should_move_to_header_brand(bool $should_move_to_header_brand) {
    $this->should_move_to_header_brand = $should_move_to_header_brand;
  }

  /**
   * @param bool $should_move_to_tail_brand should_move_to_tail_brand
   *
   * @return void
   */
  public function set_should_move_to_tail_brand(bool $should_move_to_tail_brand) {
    $this->should_move_to_tail_brand = $should_move_to_tail_brand;
  }

  /**
   * @param bool $is_predicted_winner predicted winner flag
   *
   * @return void
   */
  public function set_is_predicted_winner(bool $is_predicted_winner) : void {
    $this->is_predicted_winner = $is_predicted_winner;
  }

  /**
   * @param bool $is_hold_out_manufacturer holdout manufacturer flag
   *
   * @return void
   */
  public function set_is_hold_out_manufacturer(bool $is_hold_out_manufacturer): void {
    $this->is_hold_out_manufacturer = $is_hold_out_manufacturer;
  }

  /**
   * @return bool  Indicate the record was automatically downstream from AMT to Whitelabel
   */
  public function get_is_whitelabel_downstream(): bool {
    return $this->is_whitelabel_downstream;
  }

  /**
   * @param bool $is_whitelabel_downstream Indicate the record was automatically downstream from AMT to Whitelabel.
   *
   * @return void
   */
  public function set_is_whitelabel_downstream(bool $is_whitelabel_downstream): void {
    $this->is_whitelabel_downstream = $is_whitelabel_downstream;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'sku'                => $this->sku,
        'name'               => $this->name,
        'manufacturer'       => $this->manufacturer_name,
        'manufacturerBrwId'  => $this->manufacturer_brw_id,
        'brandType'          => $this->brand_type,
        'brandName'          => $this->brand_name,
        'class'              => $this->class_name,
        'suppliers'          => $this->suppliers,
        'isCanadianSupplier' => $this->is_canadian_supplier_only,
        'price'              => $this->sale_price,
        'priceOptionsCount'  => $this->price_options_count,
        'image'              => $this->image,
        'lastCloneDate'      => $this->get_last_clone_date_formatted(),
        'isRecentlyCloned'   => $this->is_recently_cloned(),
        'savedAt'            => $this->get_saved_at_formatted(),
        'savedBy'            => $this->get_saved_by_detailed(),
        'rejectedAt'         => $this->get_rejected_at_formatted(),
        'rejectedBy'         => $this->rejected_by,
        'rejectedNote'       => $this->rejected_note,
        'url'                => $this->url,
        'type'               => (string)$this->type,
        'isKitsco'           => $this->is_kitsco,
        'kitParents'         => $this->kit_parents,
        'relatedKits'        => $this->related_kits,
        'readonly'           => $this->readonly,
        'qaStatus'           => $this->get_qa_status()->value(),
        'brandCatalogName'   => $this->get_brand_catalog_name(),
        'isWrongContinent'   => $this->is_wrong_continent,
        'decision'           => [
            'exclusionReasonId'       => $this->excluded_reason_id,
            'automaticExcludedReason' => $this->automatic_excluded_reason,
            'priceTier'               => $this->price_tier ?? 1,
            'styleId'                 => $this->final_style_id,
            'substyleId'              => $this->final_sub_style_id,
            'manufacturerId'          => $this->final_brand_id,
            'granularStyleId'         => $this->final_granular_style_id,
            'isSuggestedStyleMatched' => in_array($this->final_style_id, $this->get_suggested_styles()),
            'styleDecisionNotes'      => $this->style_decision_notes
        ],
        'suggestedStyles'    => $this->get_suggested_styles(),
        'suggestedSubstyles' => $this->get_suggested_substyles(),

        'shouldMoveToHeaderBrand' => $this->should_move_to_header_brand,
        'shouldMoveToTailBrand'   => $this->should_move_to_tail_brand,
        'isPredictedWinner'       => $this->is_predicted_winner,
        'isHoldoutManufacturer'   => $this->is_hold_out_manufacturer,
        'isWhitelabelDownstream'  => $this->is_whitelabel_downstream,
    ];
  }

  /**
   * @return null|string
   */
  public function get_automatic_excluded_reason(): ?string {
    return $this->automatic_excluded_reason;
  }

  /**
   * @param null|string $automatic_excluded_reason automatic_excluded_reason
   *
   * @return void
   */
  public function set_automatic_excluded_reason(string $automatic_excluded_reason = null): void {
    $this->automatic_excluded_reason = $automatic_excluded_reason;
  }
}
