<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;

class Curation_Decision {
  const EXCLUSION_REASON_CONTEXT_SKU = 12;

  /**
   * @var int
   */
  private $batch_id;

  /**
   * @var string
   */
  private $sku;

  /**
   * @var int
   */
  private $excluded_reason_id = 0;

  /**
   * @var int|null
   */
  private $price_tier;

  /**
   * @var int|null
   */
  private $style_id;

  /**
   * @var int|null
   */
  private $substyle_id;

  /**
   * @var int|null
   */
  private $granular_style_id;

  /**
   * @var int|null
   */
  private $brand_id;

  /**
   * @var int
   */
  private $locked_em_id;

  /**
   * @var string|null
   */
  private $locked_date;

  /**
   * @var int|null
   */
  private $context_xn_id;

  /**
   * @var bool
   */
  private $kitsco = false;

  /**
   * @var Curation_QA_Status|null
   */
  private $qa_status;

  /**
   * @var Curation_Decision_Source|null
   */
  private $decision_source;

  /**
   * @return int
   */
  public function get_batch_id() : int {
    return $this->batch_id;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return int
   */
  public function get_excluded_reason_id() : int {
    return $this->excluded_reason_id;
  }

  /**
   * @return int|null
   */
  public function get_price_tier() {
    return $this->price_tier;
  }

  /**
   * @return int|null
   */
  public function get_style_id() {
    return $this->style_id;
  }

  /**
   * @return int|null
   */
  public function get_substyle_id() {
    return $this->substyle_id;
  }

  /**
   * @return int|null
   */
  public function get_brand_id() {
    return $this->brand_id;
  }

  /**
   * @return int|null
   */
  public function get_granular_style_id() : ?int {
    return $this->granular_style_id;
  }

  /**
   * @return int
   */
  public function get_locked_em_id() : int {
    return $this->locked_em_id;
  }

  /**
   * @return null|string
   */
  public function get_locked_date() : ?string {
    return $this->locked_date;
  }

  /**
   * @return int|null
   */
  public function get_context_xn_id() {
    return $this->context_xn_id;
  }

  /**
   * @return bool
   */
  public function is_kitsco() : bool {
    return $this->kitsco;
  }

  /**
   * @return Curation_QA_Status|null
   */
  public function get_qa_status() : ?Curation_QA_Status {
    return $this->qa_status;
  }

  /**
   * @return Curation_Decision_Source|null
   */
  public function get_decision_source() : ?Curation_Decision_Source {
    return $this->decision_source;
  }

  /**
   * @param int                                                                                  $batch_id           Batch ID
   * @param string                                                                               $sku                SKU
   * @param int                                                                                  $locked_em_id       Locked employee ID
   * @param string                                                                               $locked_date        Locked Date
   * @param int                                                                                  $excluded_reason_id Excluded Reason ID
   * @param int|null                                                                             $price_tier         Price Tier
   * @param int|null                                                                             $style_id           Style ID
   * @param int|null                                                                             $substyle_id        Substyle ID
   * @param int|null                                                                             $brand_id           Brand ID
   * @param int|null                                                                             $granular_style_id  Granularstyle ID
   * @param int|null                                                                             $context_xn_id      Context XN ID
   * @param bool                                                                                 $kitsco             Kitsco
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status         $qa_status          QA Status ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source $decision_source    Curation Decision Source
   */
  public function __construct(
      int $batch_id,
      string $sku,
      int $locked_em_id,
      string $locked_date,
      int $excluded_reason_id = 0,
      int $price_tier = null,
      int $style_id = null,
      int $substyle_id = null,
      int $brand_id = null,
      int $granular_style_id = null,
      int $context_xn_id = null,
      bool $kitsco = false,
      Curation_QA_Status $qa_status = null,
      Curation_Decision_Source $decision_source = null
  ) {
    $this->batch_id           = $batch_id;
    $this->sku                = $sku;
    $this->locked_em_id       = $locked_em_id;
    $this->locked_date        = $locked_date;
    $this->excluded_reason_id = $excluded_reason_id;
      // set value to 04 (By default) in case of 0 or null
    $this->price_tier        = $price_tier > 0 ? $price_tier : 4;
      // set value to null in case of 0
    $this->style_id          = $style_id > 0 ? $style_id : null;
    $this->substyle_id       = $substyle_id > 0 ? $substyle_id : null;
    $this->brand_id          = $brand_id > 0 ? $brand_id : null;
    $this->granular_style_id = $granular_style_id > 0 ? $granular_style_id : null;
    $this->context_xn_id     = $context_xn_id > 0 ? $context_xn_id : null;
    $this->kitsco            = $kitsco;
    $this->qa_status         = $qa_status;
    $this->decision_source   = $decision_source;
  }
}