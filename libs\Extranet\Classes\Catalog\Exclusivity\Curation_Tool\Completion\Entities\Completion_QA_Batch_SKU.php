<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities;

class Completion_QA_Batch_SKU {
  /**
   * @var string
   */
  private $sku;

  /**
   * @var string
   */
  private $message;

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return string
   */
  public function get_message() : string {
    return $this->message;
  }

  /**
   * @param string $sku     SKU
   * @param string $message Message
   */
  public function __construct(string $sku, string $message) {
    $this->sku     = $sku;
    $this->message = $message;
  }
}
