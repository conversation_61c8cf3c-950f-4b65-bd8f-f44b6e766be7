<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Application\Exception\NotAuthorizedException;
use App\Application\Logger\LoggerTrait;
use App\Application\Service\Security\ToolAvailabilityCheckerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Shared\Classes\ProductManagement\Curation\World_Regions;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU_Nearshore;
use function in_array;

class BatchService implements LoggerAwareInterface
{
    use LoggerAwareTrait;
    use LoggerTrait;

    private Completion_Batch_Data_Service $batchDataService;

    private World_Region_EU_Nearshore $nearshoreRegion;

    private AuthorizationCheckerInterface $authorizationChecker;

    public function __construct(
        Completion_Batch_Data_Service $batchDataService,
        World_Region_EU_Nearshore $nearshoreRegion,
        AuthorizationCheckerInterface $authorizationChecker
    ) {
        $this->batchDataService = $batchDataService;
        $this->nearshoreRegion = $nearshoreRegion;
        $this->authorizationChecker = $authorizationChecker;
    }

    /**
     * Verifies that the current user are able to access given Batch
     *
     * @param int $batchId ID of the batch to check
     * @return void
     */
    public function ensureAccessToBatch(int $batchId): void
    {
        if ($batchId === 0) {
            return;
        }

        try {
            $batch_data = $this->batchDataService->get($batchId);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->error(
                'Batch not found',
                [
                    'batch_id' => $batchId,
                    'exception_message' => $exception->getMessage(),
                    'exception_trace' => $exception->getTraceAsString(),
                ]
            );

            return;
        }

        // See for original security rules https://github.com/wayfair-secure/php/pull/17171
        $isNearShoreEUBatch = in_array(
            $batch_data->getBrandCatalogId(),
            $this->nearshoreRegion->getBrandCatalogIds(),
            true
        );
        $isOffShoreUSBatch = $batch_data->getBrandCatalogId() === World_Regions::REGION_US;

        $isNearShoreEUUser = $this->authorizationChecker->isGranted(
            ToolAvailabilityCheckerInterface::CURATION_BATCH_MANAGEMENT_EU_NEARSHORE_CURATORS
        );
        $isOffShoreUSUser = $this->authorizationChecker->isGranted(
            ToolAvailabilityCheckerInterface::CURATION_US_OFFSHORE
        );

        if ($isNearShoreEUUser === true && $isNearShoreEUBatch === false) {
            $this->info(
                'User is not authorized to access nearshore EU batch',
                ['batch_id' => $batchId]
            );
            throw new NotAuthorizedException('User is not authorized to access nearshore batch');
        }

        if ($isOffShoreUSUser === true && $isOffShoreUSBatch === false) {
            $this->info(
                'User is not authorized to access offshore US batch',
                ['batch_id' => $batchId]
            );
            throw new NotAuthorizedException('User is not authorized to access offshore batch');
        }
    }
}
