<?php


$directory = new \RecursiveDirectoryIterator('./libs');
/** @var string[]|SplFileInfo[]|FilesystemIterator|Iterator $iterator */
$iterator = new \RecursiveIteratorIterator($directory);
$files = array();
foreach ($iterator as $info) {

    if ($info->isFile() && preg_match('/\.php$/', $info->getPathname())) {
        $fullPath = $info->getRealPath();
        $fileContent = file_get_contents($fullPath);
        // remove commented lines
        $fileContent = preg_replace('!/\*.*?\*/!s', '', $fileContent);
        // remove commented lines
        $fileContent = preg_replace('/\n\s*\n/', "\n", $fileContent);

        if(!preg_match('/(?:class|interface|trait)\s+([a-zA-Z0-9_]+)/', $fileContent, $classNameMatches)){
            continue;
        }

        if($classNameMatches[1] !== $info->getBasename('.php')) {
            echo $classNameMatches[1]. "\n";
            if(preg_match('/'.$info->getBasename('.php').'/i', $fileContent)) {
                $newName = preg_replace('/('.$info->getBasename('.php').')\.php$/i', $classNameMatches[1].'.php', $fullPath);

                rename(
                    $fullPath,
                    $newName
                );

                echo(sprintf('"%s" renamed to "%s"', $fullPath, $newName));
                echo "\n";
            }
        }
    }
}
