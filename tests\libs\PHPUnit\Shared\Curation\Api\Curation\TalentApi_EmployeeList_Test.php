<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\libs\Shared\Curation\Api\Curation;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Psr7\Response;
use Mockery;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\Curation\TalentApi;
use WF\Shared\Environment;
use function time;

class TalentApi_EmployeeList_Test extends TestCase
{
    /**
     * @var Client Client.
     */
    private $httpClient;

    private $cache;

    public const CACHE_KEY = 'talent_api_access_token';

    public const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 1800; // 30 minutes



    protected function setUp(): void
    {
        $this->httpClient = Mockery::mock(Client::class);
        $this->cache = Mockery::mock(CacheItemPoolInterface::class);

        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() + 1800]);
        $this->cache->shouldReceive('getItem')->with(TalentApi::CACHE_KEY)->andReturn($cacheItem);
        $responseData = [];
        $this->client = new TalentApi(Environment::DEVELOPMENT, 'testclientid', 'testclientsecret', $this->httpClient, $this->cache);
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "abc"}'));
        $this->httpClient->shouldReceive('request')->andReturn(new Response(200, [], '{"id":1, "fullName": "Test", "email":"<EMAIL>"}'));
        $this->client->client = $this->httpClient;
        parent::setUp();
    }


    /**
     * @test
     * @dataProvider get_employee_data
     * @return void
     */
    public function getEmployeeList_Test(object $employee_data)
    {
        $employeeIds = [1, 2, 3, 4, 5, 6];
        $actual = $this->client->getEmployeeList($employeeIds);
        $this->assertEquals($employee_data, $actual);
    }

    /**
     * @return array
     */
    public function getEmployeeData(): array
    {
        return [
            [ // Test case 1
                'employee' => (object)[
                    'id' => 1,
                    'fullName' => 'Test',
                    'email' => '<EMAIL>'
                ]
            ],
            // Add more test cases as needed
        ];
    }

    // Assuming your data provider returns an array
    public function get_employee_data(): array
    {
        return [
            [(object) [
                'id' => 1,
                'fullName' => 'Test',
                'email' => '<EMAIL>',
            ]],
        ];
    }



    /**
     * @test
     * @return void
     */
    public function testHandleExceptionForClientExceptionWithCode401()
    {
        $employeeIds = [1, 2, 3, 4, 5, 6];
        // Mock the logger
        $logger = Mockery::mock(LoggerInterface::class);

        // Mock the HTTP client to throw a ClientException with code 401
        $this->httpClient->shouldReceive('request')
            ->andThrow(new ClientException('Unauthorized', $this->createMock(RequestInterface::class), new Response(401)));

        // Set the logger
        $this->client->setLogger($logger);

        // Set the employee ID for testing
        $employeeId = 1;

        // Expect the clearAccessToken method to be called
        $this->cache->shouldReceive('deleteItem')->with(TalentApi::CACHE_KEY)->andReturnTrue();
        $this->client->clearAccessToken();

        // Expect the getEmployeeDetails method to be called with the specified employee ID
        $logger->shouldNotReceive('error');
        $this->client->getEmployeeDetails($employeeId);


        // Expect the logger's error method not to be called
        $logger->shouldNotReceive('error');
        // Call the method that contains the try-catch block
        $result = $this->client->getEmployeeList($employeeIds);

        $this->assertEquals(null, $result); // Check if the result is not null
    }
}
