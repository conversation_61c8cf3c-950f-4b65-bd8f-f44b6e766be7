<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Suggestion_Info_Saver_Storage;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;

class Automatic_Suggestion_DAO implements Automatic_Suggestion_Info_Saver_Storage {
  use Logging_Trait;

  private const PK_CONTRAINT_VIOLATION_ERROR = 2627;

  private ProductConnection $pdo;

  /**
   * @var int
   */
  private int $suggestion_limit_per_sku;

  /**
   * @param ProductConnection    $pdo                      SQLPRODUCT
   * @param int                  $suggestion_limit_per_sku suggestion amount per sku
   * @param LoggerInterface|null $logger                   logger
   */
  public function __construct(ProductConnection $pdo, int $suggestion_limit_per_sku, ?LoggerInterface $logger = null) {
    $this->pdo                      = $pdo;
    $this->suggestion_limit_per_sku = $suggestion_limit_per_sku;
    $this->logger                   = $logger;
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_style_info(int $batch_id, array $skus) {
    $this->info('Removing Suggested style info', ['batch_id' => $batch_id, 'skus' => $skus]);

    $sql = 'DELETE FROM csn_product.dbo.tblVerificationAutomaticStyleCurationInfo
            WHERE SKU IN  (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')
            AND BatchID = :batch_id';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot remove suggested styles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sku' => $skus, 'sql' => $sql]
      );

      throw $exception;
    }
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_substyle_info(int $batch_id, array $skus) {
    $this->log_info('Removing Suggested style info', ['batch_id' => $batch_id, 'skus' => $skus]);

    $sql = 'DELETE FROM csn_product.dbo.tblVerificationAutomaticSubStyleCurationInfo
            WHERE SKU IN  (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')
            AND BatchID = :batch_id';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot remove suggested substyles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sku' => $skus, 'sql' => $sql]
      );

      throw $exception;
    }
  }


  /**
   * @param int      $batch_id         Batch ID
   * @param array    $skus             SKUs
   * @param array    $suggested_styles Suggested Styles
   * @param int      $style_id         Style ID
   * @param string   $saved_at         Saved At
   * @param string   $notes            Notes
   * @param int|null $reason           Reason
   *
   * @return void
   * @throws \Exception
   */
  public function save_suggested_style_info(int $batch_id, array $skus, array $suggested_styles, int $style_id, string $saved_at, string $notes, ?int $reason) {
    $columns = [
        'BatchID'  => SQL::int,
        'SKU'      => SQL::nvarchar(8),
        'StyleID'  => SQL::int,
        'SavedAt'  => SQL::datetime,
        'Notes'    => SQL::varchar(500),
        'ReasonID' => SQL::smallint
    ];

    $items = [];
    foreach ($skus as $sku) {
      $items[] = [
          'BatchID'  => $batch_id,
          'SKU'      => $sku,
          'StyleID'  => $style_id,
          'SavedAt'  => $saved_at,
          'Notes'    => $notes,
          'ReasonID' => $reason
      ];
    }

    $style_prediction_columns = [
        'SKU'          => SQL::nvarchar(8),
        'SuggestionID' => SQL::int,
        'Rank'         => SQL::int,
        'Probability'  => SQL::decimal(5, 2),
    ];

    $suggested_style_items = [];

    foreach ($suggested_styles as $suggested_style) {
      $suggested_style_items[] = [
          'SKU'          => $suggested_style['sku'],
          'SuggestionID' => $suggested_style['suggestion_id'],
          'Rank'         => $suggested_style['rank'],
          'Probability'  => $suggested_style['probability']
      ];
    }

    $sql = SQLBulkHelper::getTempTableJsonSql($columns, 'tempStyleUserSuggestions');
    $sql .= SQLBulkHelper::getTempTableJsonSql($style_prediction_columns, 'tempStylePredictions', 'style_predictions_json');
    $sql .= '
      DECLARE @displayed_limit SMALLINT = :displayed_limit
    ';
    // update exists curation info
    $sql .= '
      UPDATE info SET
        StyleID = tmp.StyleID,
        IsMatched = prediction.matched,
        Notes = tmp.Notes,
        ReasonID = IIF(prediction.matched = 0, tmp.ReasonID, NULL),
        SuggestedStylesDetails = (
           SELECT SKU, style.SuggestionID AS StyleID, Rank, Probability
           FROM #tempStylePredictions style
           WHERE style.SKU = tmp.SKU 
           ORDER BY Rank 
           FOR JSON PATH
        ),
        SavedAt = tmp.SavedAt

      FROM #tempStyleUserSuggestions tmp
      JOIN csn_product.dbo.tblVerificationAutomaticStyleCurationInfo info ON info.BatchID = tmp.BatchID AND info.SKU = tmp.SKU
      CROSS APPLY (
        SELECT COALESCE((SELECT TOP 1 1 FROM #tempStylePredictions style
                              WHERE style.SKU = tmp.SKU
                                AND style.SuggestionID = tmp.StyleID), 0) AS matched
      ) AS prediction
    ';
    // insert new curation info
    $sql .= '
      INSERT INTO csn_product.dbo.tblVerificationAutomaticStyleCurationInfo (BatchID, SKU, StyleID, IsMatched, Notes, ReasonID, SuggestedStylesDetails, SavedAt)
      SELECT 
        BatchID, 
        SKU, 
        StyleID,
        prediction.matched,
        Notes,
        IIF(prediction.matched = 0, tmp.ReasonID, NULL),
        (
          SELECT SKU, style.SuggestionID AS StyleID, Rank, Probability
          FROM #tempStylePredictions style
          WHERE style.SKU = tmp.SKU 
          ORDER BY Rank 
          FOR JSON PATH
        ) AS SuggestedStylesDetails,
        tmp.SavedAt

      FROM #tempStyleUserSuggestions tmp
      CROSS APPLY (
        SELECT COALESCE((SELECT TOP 1 1 FROM #tempStylePredictions style
                              WHERE style.SKU = tmp.SKU
                                AND style.SuggestionID = tmp.StyleID), 0) AS matched
      ) AS prediction

      WHERE NOT EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.tblVerificationAutomaticStyleCurationInfo info WHERE info.BatchID = tmp.BatchID AND info.SKU = tmp.SKU);
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, \json_encode($items), PDO::PARAM_STR);
    $statement->bindValue(':style_predictions_json', \json_encode($suggested_style_items), PDO::PARAM_STR);
    $statement->bindValue(':displayed_limit', $this->suggestion_limit_per_sku, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot save suggested styles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sql' => $sql]
      );
      throw $exception;
    }
  }


  /**
   * @param int      $batch_id            Batch ID
   * @param array    $skus                SKUs
   * @param array    $suggested_substyles Suggested substyles
   * @param int      $style_id            Style ID
   * @param int      $substyle_id         Substyle ID
   * @param string   $saved_at            Saved At
   * @param string   $notes               Notes
   * @param int|null $reason              Reason
   *
   * @return void
   * @throws \Exception
   */
  public function save_suggested_substyle_info(int $batch_id, array $skus, array $suggested_substyles, int $style_id, int $substyle_id, string $saved_at, string $notes, ?int $reason) {
    $this->log_info('Saving suggested substyle info', ['batch_id' => $batch_id, 'skus' => $skus]);

    $columns = [
        'BatchID'    => SQL::int,
        'SKU'        => SQL::nvarchar(8),
        'StyleID'    => SQL::int,
        'SubstyleID' => SQL::int,
        'SavedAt'    => SQL::datetime,
        'Notes'      => SQL::varchar(500),
        'ReasonID'   => SQL::smallint
    ];

    $items = [];
    foreach ($skus as $sku) {
      $items[] = [
          'BatchID'    => $batch_id,
          'SKU'        => $sku,
          'StyleID'    => $style_id,
          'SubstyleID' => $substyle_id,
          'SavedAt'    => $saved_at,
          'Notes'      => $notes,
          'ReasonID'   => $reason
      ];
    }

    $substyle_prediction_columns = [
        'SKU'          => SQL::nvarchar(8),
        'SuggestionID' => SQL::int,
        'Rank'         => SQL::int,
        'Probability'  => SQL::decimal(5, 2),
    ];

    $suggested_substyle_items = [];

    foreach ($suggested_substyles as $suggested_substyle) {
      $suggested_substyle_items[] = [
          'SKU'          => $suggested_substyle['sku'],
          'SuggestionID' => $suggested_substyle['suggestion_id'],
          'Rank'         => $suggested_substyle['rank'],
          'Probability'  => $suggested_substyle['probability']
      ];
    }

    $sql = SQLBulkHelper::getTempTableJsonSql($columns, 'tempSubstyleSuggestions');
    $sql .= SQLBulkHelper::getTempTableJsonSql($substyle_prediction_columns, 'tempSubstylePredictions', 'substyle_predictions_json');
    $sql .= '
      DECLARE @displayed_limit SMALLINT = :displayed_limit
    ';
    // update exists curation info
    $sql .= '
      UPDATE info SET
        StyleID = tmp.StyleID,
        SubStyleID = tmp.SubstyleID,
        IsMatched = prediction.matched,
        Notes = tmp.Notes,
        ReasonID = IIF(prediction.matched = 0, tmp.ReasonID, NULL),
        SuggestedSubstylesDetails = (
           SELECT SKU, substyle.SuggestionID AS SubstyleID, Rank, Probability
           FROM #tempSubstylePredictions substyle
           WHERE substyle.SKU = tmp.SKU 
           ORDER BY Rank 
           FOR JSON PATH
        ),
        SavedAt = tmp.SavedAt

      FROM #tempSubstyleSuggestions tmp
      JOIN csn_product.dbo.tblVerificationAutomaticSubstyleCurationInfo info ON info.BatchID = tmp.BatchID AND info.SKU = tmp.SKU
      CROSS APPLY (
        SELECT COALESCE((SELECT TOP 1 1 FROM #tempSubstylePredictions substyle
                              WHERE substyle.SKU = tmp.SKU
                                AND substyle.SuggestionID = tmp.SubstyleID), 0) AS matched
      ) AS prediction
    ';
    // insert new curation info
    $sql .= '
      INSERT INTO csn_product.dbo.tblVerificationAutomaticSubstyleCurationInfo (BatchID, SKU, StyleID, SubstyleID, IsMatched, Notes, ReasonID, SuggestedSubstylesDetails, SavedAt)
      SELECT
        BatchID,
        SKU,
        StyleID,
        SubstyleID,
        prediction.matched AS IsMatched,
        Notes,
        IIF(prediction.matched = 0, tmp.ReasonID, NULL),
        (
          SELECT SKU, substyle.SuggestionID AS SubstyleID, Rank, Probability
          FROM #tempSubstylePredictions substyle
          WHERE substyle.SKU = tmp.SKU
          ORDER BY Rank
          FOR JSON PATH
        ) AS SuggestedSubstylesDetails,
        tmp.SavedAt

      FROM #tempSubstyleSuggestions tmp
      CROSS APPLY (
        SELECT COALESCE((SELECT TOP 1 1 FROM #tempSubstylePredictions substyle
                              WHERE substyle.SKU = tmp.SKU
                                AND substyle.SuggestionID = tmp.SubstyleID), 0) AS matched
      ) AS prediction

      WHERE NOT EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.tblVerificationAutomaticSubstyleCurationInfo info WHERE info.BatchID = tmp.BatchID AND info.SKU = tmp.SKU);
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, \json_encode($items), PDO::PARAM_STR);
    $statement->bindValue(':substyle_predictions_json', \json_encode($suggested_substyle_items), PDO::PARAM_STR);
    $statement->bindValue(':displayed_limit', $this->suggestion_limit_per_sku, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $errorInfo = $statement->errorInfo();

      if (!empty($errorInfo) && $errorInfo[1] === self::PK_CONTRAINT_VIOLATION_ERROR) {
        $this->log_warning("PK Constraint (BatchID, SKU) violation - inserting duplicated data", $errorInfo);

        return;
      }

      $exception = ExecutionException::forStatement($statement, 'Cannot save suggested substyles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sql' => $sql]
      );
      throw $exception;
    }
  }
}
