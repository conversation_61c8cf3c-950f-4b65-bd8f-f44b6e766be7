<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\SKU_Style_Suggestion_DTO;

interface Api_Style_Suggestion {

  /**
   * @param string[] $skus  SKU list
   * @param int      $limit Max number of suggestions to return
   *
   * @return SKU_Style_Suggestion_DTO[]
   * @throws API_Request_Exception
   */
  public function get_style_suggestion(array $skus, int $limit) : array;

  /**
   * @param string[] $skus  SKU list
   * @param int      $limit Max number of suggestions to return
   *
   * @return SKU_Style_Suggestion_DTO[]
   * @throws API_Request_Exception
   */
  public function get_substyle_suggestion(array $skus, int $limit) : array;
}
