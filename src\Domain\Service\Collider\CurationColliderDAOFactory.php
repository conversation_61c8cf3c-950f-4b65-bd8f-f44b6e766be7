<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Domain\Service\Collider;

use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Legacy_DAO;
use WF\Shared\Merchandising\SKU_Collisions\Storage\SKU_Collider_DAO;

class CurationColliderDAOFactory
{
    private Curation_Collider_DAO $curationColliderDAO;
    private Curation_Collider_Legacy_DAO $curationColliderLegacyDAO;

    public function __construct(
        Curation_Collider_DAO $curationColliderDAO,
        Curation_Collider_Legacy_DAO $curationColliderLegacyDAO
    ) {
        $this->curationColliderDAO = $curationColliderDAO;
        $this->curationColliderLegacyDAO = $curationColliderLegacyDAO;
    }

    public function getCurationColliderDAO(bool $useNew): SKU_Collider_DAO
    {
        return $useNew
            ? $this->curationColliderDAO
            : $this->curationColliderLegacyDAO;
    }
}
