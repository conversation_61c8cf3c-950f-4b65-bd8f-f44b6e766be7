<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service;

use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\Context_SKU_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\White_Label_Brand_Picklist;
use WF\Shared\DAOs\Product\Media\Curation_Tool\Context_Sku_DAO;
use WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU;

class Context_SKU_Service implements Context_SKU_Collection_Retrieve_Interface {

  /**
   * @var \WF\Shared\DAOs\Product\Media\Curation_Tool\Context_Sku_DAO
   */
  private $dao;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Factory\Context_SKU_Factory
   */
  private $factory;

  /**
   * @var bool
   */
  private $do_upsert;

  /**
   * Context_SKU_Finder constructor.
   *
   * @param Context_Sku_DAO $context_sku_dao Context SKU DAO
   * @param Context_SKU_Factory $factory Context SKU Factory
   * @param bool $do_upsert Need to save the calculated mapping?
   */
  public function __construct(
      Context_Sku_DAO     $context_sku_dao,
      Context_SKU_Factory $factory,
      bool                $do_upsert = false
  ) {
    $this->dao       = $context_sku_dao;
    $this->factory   = $factory;
    $this->do_upsert = $do_upsert;
  }

  /**
   * @param string $sku SKU
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU
   */
  public function get_context_sku(string $sku): Context_SKU {
    $sku_list  = $this->find_context_sku($sku);
    $first_sku = $sku_list[0] ?? ['PrSKU' => '', 'PrXnID' => null];

    $context_sku = $this->factory->create($sku, (string)$first_sku['PrSKU'], $first_sku['PrXnID']);

    if ($this->do_upsert) {
      $this->dao->upsert_context_collection_sku($sku, $context_sku->get_context_sku(), $context_sku->get_xn_id());
    }

    return $context_sku;
  }

  /**
   * @param string $sku SKU
   *
   * @return int|null
   */
  public function get_context_xnid(string $sku): ?int {
    $original_xn_id = $this->dao->get_xn_id_from_original_data($sku);
    if (!empty($original_xn_id)) {
      return $original_xn_id;
    }

    return $this->get_context_sku($sku)->get_xn_id();
  }

  /**
   * @param string ...$skus SKUs
   *
   * @return array Map of SKU to XN ID (Collection ID)
   */
  public function get_context_xnid_map(string ...$skus): array {
    if (empty($skus)) {
      return [];
    }

    $skus_to_xnid_map = [];
    foreach ($skus as $sku) {
      $skus_to_xnid_map[$sku] = $this->get_context_xnid($sku);
    }

    return $skus_to_xnid_map;
  }

  /**
   * @param string $sku SKU
   *
   * @return array
   */
  private function find_context_sku(string $sku): array {
    $context_sku = $this->dao->get_context_sku($sku, White_Label_Brand_Picklist::STANDARD);

    if (empty($context_sku[0]['PrXnID'])) {
      $context_sku = $this->dao->get_context_sku($sku, White_Label_Brand_Picklist::LEGACY_WHITE_LABEL_BRAND);
    }

    if (empty($context_sku[0]['PrXnID'])) {
      $context_sku = $this->dao->get_sku_data($sku);
    }

    return $context_sku;
  }
}
