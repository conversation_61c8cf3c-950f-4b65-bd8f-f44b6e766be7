<?php

namespace App\Application\Controller\QA;

use App\Application\Controller\AbstractBaseController;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;

use function sprintf;

abstract class AbstractQAControllerAbstract extends AbstractBaseController
{
    /**
     * @param int $batch_id
     * @param int $expected_process_type_id
     * @param Completion_Batch_Data_Service $batch_data_service
     *
     * @return bool FT `mtbw_pa_wl_automation_curation_post_qa_ui`
     */
    protected function is_expected_process_type(
        int $batch_id,
        int $expected_process_type_id,
        Completion_Batch_Data_Service $batch_data_service
    ): bool {
        $this->info(
            'Asserting batch process type',
            [
                'batch_id' => $batch_id,
                'expected_process_type_id' => $expected_process_type_id,
            ]
        );

        try {
            $batch_data = $batch_data_service->get($batch_id);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->error(
                sprintf('Failed to load batch data: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'exception' => $exception]
            );

            return false;
        }

        $process_type_id = $batch_data->getProcessTypeID();

        if ($process_type_id === null) {
            $this->info(
                'Batch does NOT have process type. Exiting batch process type assertion....',
                [
                    'batch_id' => $batch_id,
                    'process_type_id' => $process_type_id,
                    'expected_process_type_id' => $expected_process_type_id,
                ]
            );

            return true;
        }

        $this->info(
            'Batch process type',
            [
                'batch_id' => $batch_id,
                'status' => $batch_data->getStatus(),
                'process_type' => $process_type_id,
                'expected_process_type_id' => $expected_process_type_id,
            ]
        );

        return $process_type_id === $expected_process_type_id;
    }
}
