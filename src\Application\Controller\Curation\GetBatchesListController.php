<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use App\Application\DTO\CurationRequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Batch_Data\Get_Batch_Data_Service;

class GetBatchesListController extends AbstractBaseController
{
    /**
     * @param Get_Batch_Data_Service $batch_service get batch list service
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/batch_list", methods={"GET"})
     */
    public function get_batch_list(
        Get_Batch_Data_Service $batch_service,
        CurationRequest        $curationRequest
    ): JsonResponse {
        $batch_id = $curationRequest->getBatchId();
        $result = $batch_service->get_batch_list_call($this->getEmployeeId(), $batch_id);
        return $this->json($result);
    }

    /**
     * @param Get_Batch_Data_Service $batch_service get batch list service
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/batch_data", methods={"GET"})
     */
    public function get_batch_data(
        Get_Batch_Data_Service $batch_service,
        CurationRequest        $curationRequest
    ): JsonResponse {
        $batchId = $curationRequest->getBatchId();
        $countBase = $curationRequest->getCountBase();
        $result = $batch_service->getSectionsListCount($batchId, $countBase);
        return $this->json($result);
    }

    /**
     * @param Get_Batch_Data_Service $batch_service get batch list service
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/batch_details", methods={"GET"})
     */
    public function get_batch_details(
        Get_Batch_Data_Service $batch_service,
        CurationRequest        $curationRequest
    ): JsonResponse {
        $batchId = $curationRequest->getBatchId();
        $sectionName = $curationRequest->getSectionName();
        $pageNum = $curationRequest->getPageNumber();
        $limit = $curationRequest->getLimit();
        $suppplier = $curationRequest->getSupplier();
        $countBase = $curationRequest->getCountBase();
        $pageType = $curationRequest->getPageType();
        $xnId = $curationRequest->getXnId();
        $result = $batch_service->getSectionsList($batchId, $pageNum, $limit, $sectionName, $suppplier, $countBase, $pageType, $xnId);
        return $this->json($result);
    }
}
