<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use WF\Curation\ProductionTrackingApi\Http\HttpClientFactory;

class ClientConfig
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var int
     */
    private $maxRetries;

    /**
     * @var int
     */
    private $connectionTimeout;

    /**
     * @var string
     */
    private $clientId;

    /**
     * @var string
     */
    private $clientSecret;

    /**
     * @var Client
     */
    private $authHttpClient;

    /**
     * @var Client
     */
    private $apiHttpClient;

    /**
     * ClientConfig constructor.
     *
     * @param LoggerInterface $logger
     * @param int $retriesCount
     * @param int $connectionTimeout
     * @param string $clientId
     * @param string $clientSecret
     */
    public function __construct(
        LoggerInterface $logger,
        int $retriesCount,
        int $connectionTimeout,
        string $clientId,
        string $clientSecret,
        string $authUrl,
        string $apiUrl,
        ?Client $httpClient = null
    ) {
        $this->logger = $logger;
        $this->maxRetries = $retriesCount;
        $this->connectionTimeout = $connectionTimeout;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;

        $this->setAuthHttpClient($authUrl, $httpClient);
        $this->setApiHttpClient($apiUrl, $httpClient);
    }

    /**
     * @return LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * @return Client
     */
    public function getAuthHttpClient(): Client
    {
        return $this->authHttpClient;
    }

    /**
     * @return Client
     */
    public function getApiHttpClient(): Client
    {
        return $this->apiHttpClient;
    }

    /**
     * @param string $url
     * @param Client|null $httpClient
     *
     * @return void
     * @throws \Exception
     */
    public function setAuthHttpClient(string $url, ?Client $httpClient): void
    {
        $this->authHttpClient = $this->setupHttpClient($url, $httpClient);
    }

    /**
     * @param string $url
     * @param Client|null $httpClient
     *
     * @return void
     * @throws \Exception
     */
    public function setApiHttpClient(string $url, ?Client $httpClient): void
    {
        $this->apiHttpClient = $this->setupHttpClient($url, $httpClient);
    }

    /**
     * @param string $url
     * @param Client|null $httpClient
     *
     * @return Client
     * @throws \Exception
     */
    private function setupHttpClient(string $url, ?Client $httpClient): Client
    {
        return HttpClientFactory::create($url, $this->maxRetries, $this->connectionTimeout, $httpClient);
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getClientSecret(): string
    {
        return $this->clientSecret;
    }
}
