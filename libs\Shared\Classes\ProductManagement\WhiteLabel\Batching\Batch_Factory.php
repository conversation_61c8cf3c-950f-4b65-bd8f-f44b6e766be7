<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\ProductManagement\Populator\Populator;

class Batch_Factory {
  /**
   * @var \WF\Shared\ProductManagement\Populator\Populator
   */
  private $populator;

  /**
   * Batch_Factory constructor.
   *
   * @param \WF\Shared\ProductManagement\Populator\Populator                            $populator       Model populator
   */
  public function __construct(
      Populator $populator
  ) {
    $this->populator       = $populator;
  }

  /**
   * @param array $map Model property map
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function create(array $map = []) : Batch {
    $batch = new Batch();

    if (count($map) > 0) {
      return $this->populator->populate($batch, $map);
    }

    return $batch;
  }

  /**
   * @param array $map Model map
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU
   */
  public function create_verified_batch_sku(array $map) : Verified_Batch_SKU {
    return $this->populator->populate(new Verified_Batch_SKU(), $map);
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $original_batch_sku Original sku object
   * @param string                                                                     $sku_value          New sku value
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU
   */
  public function copy_verified_batch_sku(Verified_Batch_SKU $original_batch_sku, string $sku_value) : Verified_Batch_SKU {
    $verified_batch_sku = new Verified_Batch_SKU();

    $verified_batch_sku->set_sku($sku_value);
    $verified_batch_sku->set_target_ma_id($original_batch_sku->get_target_ma_id());
    $verified_batch_sku->set_is_excluded_from_wl($original_batch_sku->get_is_excluded_from_wl());
    $verified_batch_sku->set_status($original_batch_sku->get_status());

    return $verified_batch_sku;
  }
}
