<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item;

class Curation_Automation_Item_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_creates_curation_automation_item()
    {
        $item = new Curation_Automation_Item(
            123,
            'ABC123',
            Curation_Batch_Automation_Type::ds_model(),
            80,
            1,
            2,
            false
        );

        $this->assertInstanceOf(Curation_Automation_Item::class, $item);
        $this->assertEquals(123, $item->get_curation_batch_id());
        $this->assertEquals('ABC123', $item->get_vi_sku());
        $this->assertEquals(1, $item->get_curation_batch_automation_type_id()->value());
        $this->assertEquals(80, $item->get_ds_model_score());
        $this->assertEquals(1, $item->get_vs_predicted_style_id());
        $this->assertEquals(2, $item->get_vss_predicted_substyle_id());
        $this->assertFalse($item->get_unwhitelabel());
    }
}
