## Single-purpose Database Guides

### Resources

- How to request [SPDB](https://docs.csnzoo.com/php/decentralizing/guides/db.html)
- Symfony applications using ORM should follow the [Doctrine integration guide](https://symfony.com/doc/current/doctrine.html).
- Other applications can use [php-core-db](https://github.com/wayfair-secure/php-core-db) or [php-core-db-symfony-bundle](https://github.com/wayfair-secure/php-core-db-symfony-bundle) using the provided environment variables.
- To add database migrations check [db-on-demand](https://github.com/wayfair-shared/db-on-demand)

### How to connect `SPDB` database

- Follow the installation steps to add [DB Bundle](https://github.com/wayfair-secure/php-core-db-symfony-bundle#installation) or [php-core-db](https://github.com/wayfair-secure/php-core-db)
- Make sure you configured `service, bundle and doctrine` configuration files properly.
- For MSSQL SPDBs, change your application `docker-compose.yaml` to add [Microsoft SQL Server](https://github.com/wayfair-shared/dbtech-containers) as dependency to your application, check example below
    ```
    db:
      image: Wayfair/dbtech-mssql2017:1.0.5
      container_name: 'db'
      restart: 'always'
      networks:
        - dbnet
      ports:
        - 1433:1433
      environment:
        - MSSQL_SA_PASSWORD=Password123
        - MSSQL_PID=Developer
        - MSSQL_AGENT_ENABLED=True
        - DATABASES_TO_CREATE=service1
    
    ```
- Add the required [environment variables](https://docs.csnzoo.com/php/decentralizing/guides/environment_variables.html) to the `devbox` service in `docker-compose.yaml`. Replace variable values with your `SPDB` configuration.

    ```
      DB_HOST: db
      DB_PORT: 1433
      DB_NAME: MyDb
      DB_USER: SA
      B_PASSWORD_SECRET_NAME: SA_PASS
    ```

- Modify helm chart configurations to add the new database connection information as environment variables. Check example [here](https://github.com/wayfair-shared/devaccel-pr-analyser/blob/2537f2dc485a59ae951e00666632d299830b3a58/k8s.yaml#L156)



### Need help
- [SPDB help channel](https://wayfair.slack.com/archives/CUUEMA1KP)
- [Doctrine Migration](https://symfony.com/bundles/DoctrineMigrationsBundle/current/index.html)
- [Doctrine Associations / Relations](https://symfony.com/doc/current/doctrine/associations.html)
- [How to Use Doctrine DBAL to execute raw queries](https://symfony.com/doc/current/doctrine/dbal.html)
- [How to Generate Entities from an Existing Database](https://symfony.com/doc/current/doctrine/reverse_engineering.html)

If you still have questions, reach out to `#php-decoupling-forum` on Slack
