<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\DTO;

use WF\Curation\WhiteLabelApi\Entity\Batch;
use WF\Curation\WhiteLabelApi\Entity\SKU;

final class RequestBuilder
{
    private function __construct()
    {
    }

    /**
     * @param  array $batches
     * @return array
     */
    public static function buildJsonRequest(array $batches): array
    {
        return array_map(
            function (Batch $batch) {
                return self::buildBatchJsonRequest($batch);
            },
            $batches
        );
    }

    /**
     * @param  Batch $batch
     * @return array
     */
    private static function buildBatchJsonRequest(Batch $batch): array
    {
        $json = [];
        $json['whiteLabelBatchId'] = $batch->getBatchId();
        $json['brandCatalogId'] = $batch->getBrandCatalogId();
        $json['skus'] = array_map(
            function (SKU $sku) {
                return self::buildSkuJ<PERSON>($sku);
            },
            $batch->getSkus()
        );

        return $json;
    }

    /**
     * @param  SKU $sku
     * @return array
     */
    private static function buildSkuJsonRequest(SKU $sku): array
    {
        $jsonSku = [];
        $jsonSku['sku'] = $sku->getSku();
        $jsonSku['targetManufacturerId'] = self::getTargetManufacturerId($sku);
        $jsonSku['isExcluded'] = $sku->isExcluded();


        return $jsonSku;
    }

    /**
     * @param  SKU $sku
     * @return int|null
     */
    private static function getTargetManufacturerId(SKU $sku): ?int
    {
        return !$sku->isExcluded() ? $sku->getTargetManufacturerId() : null;
    }
}
