<?php

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\PostgresBulkHelper;
use App\Infrastructure\Helper\SQLBulkHelper;
use pdo_sql;
use PDO;
use PDOException;
use Psr\Log\LoggerInterface;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;


class Automatic_Suggestion_Postgres_DAO {
  use Logging_Trait;

  private const PK_CONTRAINT_VIOLATION_ERROR = 2627;

  private $pdo_sqlser;
  private $pdo_psql;
  private $pdo;



  /**
   *  suggestion_limit_per_sku
   */
  private $suggestion_limit_per_sku;

  /**
   * @param ProductConnection    $pdo_sqlser                 SQLPRODUCT
   * @param int                  $suggestion_limit_per_sku suggestion amount per sku
   * @param PostgresConnection   $pdo_psql                POSTGRES
   * @param LoggerInterface|null $logger                  logger
   * @param ProductConnection   $pdo                      PDO connect
   */
  public function __construct(ProductConnection $pdo_sqlser, PostgresConnection $pdo_psql,int $suggestion_limit_per_sku,?LoggerInterface $logger = null,  ProductConnection $pdo) {
    $this->pdo_sqlser                      = $pdo_sqlser;
    $this->suggestion_limit_per_sku      = $suggestion_limit_per_sku;
    $this->pdo_psql                      = $pdo_psql;
    $this->logger                         = $logger;
    $this->pdo                      = $pdo;
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_style_info(int $batch_id, array $skus) {
    $this->info('Removing Suggested style info postgres sql ', ['batch_id' => $batch_id, 'skus' => $skus]);
    $sku_lst  = $this->pdo_psql->paramsForLists($skus);
    $sql = "DELETE FROM \"tblVerificationAutomaticStyleCurationInfo\"
            WHERE \"SKU\" IN ($sku_lst) AND \"BatchID\" = :batch_id";

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot remove suggested styles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sku' => $skus, 'sql' => $sql]
      );

      throw $exception;
    }
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_substyle_info(int $batch_id, array $skus) {
    $this->log_info('Removing Suggested style info', ['batch_id' => $batch_id, 'skus' => $skus]);
    $sku_lst = $this->pdo_psql->paramsForLists($skus);
    $sql = "DELETE FROM \"tblVerificationAutomaticSubStyleCurationInfo\"
            WHERE \"SKU\" IN ($sku_lst) AND \"BatchID\" = :batch_id";

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot remove suggested substyles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sku' => $skus, 'sql' => $sql]
      );

      throw $exception;
    }
  }

  public function save_suggested_substyle_info(int $batch_id, array $skus, array $suggested_substyles, int $style_id, int $substyle_id, string $saved_at, string $notes, ?int $reason) {
    $this->log_info('Saving suggested substyle info', ['batch_id' => $batch_id, 'skus' => $skus]);

    $tblSuggestion = sprintf("tempSubStyleUserSuggestions_%s", $this->createRandomCode());
    $tblPrediction = sprintf("tempSubStylePredictions_%s", $this->createRandomCode());

    $columns = [
        'BatchID'    => SQL::int,
        'SKU'        => SQL::varchar(8),
        'StyleID'    => 'INTEGER NULL',
        'SubstyleID' => 'INTEGER NULL',
        'SavedAt'    => 'TIMESTAMP(4) WITH TIME ZONE NULL',
        'Notes'      => SQL::varchar(500),
        'ReasonID'   => SQL::int
    ];
    $items = [];
    foreach ($skus as $sku) {
      $items[] = [
          'BatchID'    => $batch_id,
          'SKU'        => $sku,
          'StyleID'    => $style_id,
          'SubstyleID' => $substyle_id,
          'SavedAt'    => $saved_at,
          'Notes'      => $notes,
          'ReasonID'   => $reason
      ];
    }

    $substyle_prediction_columns = [
        'SKU'          => SQL::varchar(8),
        'SuggestionID' => SQL::int,
        'Rank'         => SQL::int,
        'Probability'  => SQL::decimal(5, 2),
    ];

    $suggested_substyle_items = [];

    foreach ($suggested_substyles as $suggested_substyle) {
      $suggested_substyle_items[] = [
          'SKU'          => $suggested_substyle['sku'],
          'SuggestionID' => $suggested_substyle['suggestion_id'],
          'Rank'         => $suggested_substyle['rank'],
          'Probability'  => $suggested_substyle['probability']
      ];
    }

    $sql = "
        DO $$ 
        DECLARE 
        BEGIN
          UPDATE \"tblVerificationAutomaticSubStyleCurationInfo\" AS info
          SET
              \"StyleID\" = tmp.\"styleid\",
              \"SubStyleID\" = tmp.\"substyleid\",
              \"IsMatched\" = prediction.\"matched\",
              \"Notes\" = tmp.\"notes\",
              \"ReasonID\" = CASE WHEN prediction.\"matched\" = false THEN tmp.\"reasonid\" ELSE NULL END,
              \"SuggestedSubStylesDetails\" = (
                  SELECT jsonb_agg(jsonb_build_object('SKU', substyle.\"sku\", 'SubstyleID', substyle.\"suggestionid\", 'Rank', substyle.\"rank\", 'Probability', substyle.\"probability\"))
                  FROM \"". $tblPrediction ."\" AS substyle
                  WHERE substyle.\"sku\" = tmp.\"sku\"
                  GROUP BY substyle.\"sku\"
                  ORDER BY MAX(substyle.\"rank\")
              ),
              \"SavedAt\" = tmp.\"savedat\"
          FROM \"". $tblSuggestion ."\" AS tmp
          CROSS JOIN LATERAL (
              SELECT COALESCE((
                  SELECT 1
                  FROM \"". $tblPrediction ."\" AS substyle
                  WHERE substyle.\"sku\" = tmp.\"sku\"
                    AND substyle.\"suggestionid\" = tmp.\"substyleid\"
                  LIMIT 1
              ), 0)::BOOLEAN AS matched
          ) AS prediction
          WHERE info.\"BatchID\" = tmp.\"batchid\" AND info.\"SKU\" = tmp.\"sku\";
          
          INSERT INTO \"tblVerificationAutomaticSubStyleCurationInfo\" (\"BatchID\", \"SKU\", \"StyleID\", \"SubStyleID\", \"IsMatched\", \"Notes\", \"ReasonID\", \"SuggestedSubStylesDetails\", \"SavedAt\")
          SELECT
              \"batchid\", \"sku\", \"styleid\", \"substyleid\",
              CASE WHEN prediction.\"matched\" = true THEN true ELSE false END AS \"IsMatched\",
              \"notes\",
              CASE WHEN prediction.\"matched\" = false THEN tmp.\"reasonid\" ELSE NULL END,
              (
                  SELECT jsonb_agg(jsonb_build_object('SKU', substyle.\"sku\", 'SubstyleID', substyle.\"suggestionid\", 'Rank', substyle.\"rank\", 'Probability', substyle.\"probability\"))
                  FROM \"". $tblPrediction ."\" substyle
                  WHERE substyle.\"sku\" = tmp.\"sku\"
                  GROUP BY substyle.\"sku\"
                  ORDER BY MAX(substyle.\"rank\")
              ) AS suggestedsubstylesdetails,
              tmp.savedat
          FROM \"". $tblSuggestion ."\" AS tmp
          CROSS JOIN LATERAL (
              SELECT COALESCE((
                  SELECT 1 FROM \"". $tblPrediction ."\" AS substyle
                  WHERE substyle.\"sku\" = tmp.\"sku\"
                    AND substyle.\"suggestionid\" = tmp.\"substyleid\"
                  LIMIT 1
              ), 0)::BOOLEAN AS matched
          ) AS prediction
          WHERE NOT EXISTS (
              SELECT 1 FROM \"tblVerificationAutomaticSubStyleCurationInfo\" info
              WHERE info.\"BatchID\" = tmp.\"batchid\"
                AND info.\"SKU\" = tmp.\"sku\"
              LIMIT 1
          );
    
        END $$;
    ";

    try {
      $this->drop_pg_temp_table($tblSuggestion);
      $this->drop_pg_temp_table($tblPrediction);
      $this->create_temp_pg_table($columns, $tblSuggestion);
      $this->create_temp_pg_table($substyle_prediction_columns, $tblPrediction);
      $this->insert_into_pg_temp_table($tblSuggestion,$columns,$items);
      $this->insert_into_pg_temp_table($tblPrediction,$substyle_prediction_columns,$suggested_substyle_items);

      $statement = $this->pdo_psql->prepare($sql);
      $statement->execute();
    } catch (PDOException $e) {
      $errorInfo = $statement->errorInfo();
      if (!empty($errorInfo) && $errorInfo[1] === self::PK_CONTRAINT_VIOLATION_ERROR) {
        $this->log_warning("PK Constraint (BatchID, SKU) violation - inserting duplicated data", $errorInfo);
        return;
      }
      $exception = ExecutionException::forStatement($statement, 'Cannot save suggested substyles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sql' => $sql]
      );
      throw $exception;
    } finally {
      $this->drop_pg_temp_table($tblSuggestion);
      $this->drop_pg_temp_table($tblPrediction);
    }

  }

  /**
   * @param int      $batch_id         Batch ID
   * @param array    $skus             SKUs
   * @param array    $suggested_styles Suggested Styles
   * @param int      $style_id         Style ID
   * @param string   $saved_at         Saved At
   * @param string   $notes            Notes
   * @param int|null $reason           Reason
   *
   * @return void
   * @throws \Exception
   */
  public function save_suggested_style_info(int $batch_id, array $skus, array $suggested_styles, int $style_id, string $saved_at, string $notes, ?int $reason) {
    $this->log_info('Saving suggested style info', ['batch_id' => $batch_id, 'skus' => $skus]);

    $tblSuggestion = sprintf("tempStyleUserSuggestions_%s", $this->createRandomCode());
    $tblPrediction = sprintf("tempStylePredictions_%s", $this->createRandomCode());

    $columns = [
        'BatchID'  => SQL::int,
        'SKU'      => SQL::varchar(8),
        'StyleID'  => 'INTEGER NULL',
        'SavedAt'  => 'TIMESTAMP(4) WITH TIME ZONE NULL',
        'Notes'    => SQL::varchar(500),
        'ReasonID' => 'INTEGER NULL'
    ];

    $items = [];
    foreach ($skus as $sku) {
      $items[] = [
          'BatchID'  => $batch_id,
          'SKU'      => $sku,
          'StyleID'  => $style_id,
          'SavedAt'  => $saved_at,
          'Notes'    => $notes,
          'ReasonID' => $reason
      ];
    }

    $style_prediction_columns = [
        'SKU'          => SQL::varchar(8),
        'SuggestionID' => SQL::int,
        'Rank'         => SQL::int,
        'Probability'  => SQL::decimal(5, 2),
    ];

    $suggested_style_items = [];

    foreach ($suggested_styles as $suggested_style) {
      $suggested_style_items[] = [
          'SKU'          => $suggested_style['sku'],
          'SuggestionID' => $suggested_style['suggestion_id'],
          'Rank'         => $suggested_style['rank'],
          'Probability'  => $suggested_style['probability']
      ];
    }

    $sql = "
        DO $$ 
        DECLARE 
        BEGIN
          UPDATE \"tblVerificationAutomaticStyleCurationInfo\" AS info
          SET
              \"StyleID\" = tmp.\"styleid\",
              \"IsMatched\" = prediction.\"matched\",
              \"Notes\" = tmp.\"notes\",
              \"ReasonID\" = CASE WHEN prediction.\"matched\" = false THEN tmp.\"reasonid\" ELSE NULL END,
              \"SuggestedStylesDetails\" = (
                  SELECT jsonb_agg(jsonb_build_object('SKU', style.\"sku\", 'StyleID', style.\"suggestionid\", 'Rank', style.\"rank\", 'Probability', style.\"probability\"))
                  FROM \"". $tblPrediction ."\" AS style
                  WHERE style.\"sku\" = tmp.\"sku\"
                  GROUP BY style.\"sku\"
                  ORDER BY MAX(style.\"rank\")
              ),
              \"SavedAt\" = tmp.\"savedat\"
          FROM \"". $tblSuggestion ."\" AS tmp
          LEFT JOIN LATERAL (
              SELECT COALESCE((
                  SELECT 1
                  FROM \"". $tblPrediction ."\" AS style
                  WHERE style.\"sku\" = tmp.\"sku\"
                  AND style.\"suggestionid\" = tmp.\"styleid\"
                  LIMIT 1
              ), 0)::BOOLEAN AS \"matched\"
          ) AS prediction ON TRUE
          WHERE info.\"BatchID\" = tmp.\"batchid\" AND info.\"SKU\" = tmp.\"sku\";
          
          INSERT INTO \"tblVerificationAutomaticStyleCurationInfo\" (\"BatchID\", \"SKU\", \"StyleID\", \"IsMatched\", \"Notes\", \"ReasonID\", \"SuggestedStylesDetails\", \"SavedAt\")
          SELECT 
              tmp.\"batchid\",  tmp.\"sku\",  tmp.\"styleid\", prediction.\"matched\", tmp.\"notes\",
              CASE WHEN prediction.\"matched\" = false THEN tmp.\"reasonid\" ELSE NULL END,
              (
                  SELECT jsonb_agg(jsonb_build_object('SKU', style.\"sku\", 'StyleID', style.\"suggestionid\", 'Rank', style.\"rank\", 'Probability', style.\"probability\"))
                  FROM \"". $tblPrediction ."\" AS style
                  WHERE style.\"sku\" = tmp.\"sku\" 
                  GROUP BY style.\"sku\"
                  ORDER BY MAX(style.\"rank\")
              ) AS \"SuggestedStylesDetails\",
              tmp.\"savedat\"
          FROM \"". $tblSuggestion ."\" AS tmp
          CROSS JOIN LATERAL (
              SELECT COALESCE((
                  SELECT 1 FROM \"". $tblPrediction ."\" AS style
                  WHERE style.\"sku\" = tmp.\"sku\"
                  AND style.\"suggestionid\" = tmp.\"styleid\"
                  LIMIT 1
              ), 0)::BOOLEAN AS matched
          ) AS prediction
          WHERE NOT EXISTS (
              SELECT 1 FROM \"tblVerificationAutomaticStyleCurationInfo\" AS info
              WHERE info.\"BatchID\" = tmp.\"batchid\"
              AND info.\"SKU\" = tmp.\"sku\"
              LIMIT 1
          );
    
        END $$;
    ";

    try {
      $this->drop_pg_temp_table($tblSuggestion);
      $this->drop_pg_temp_table($tblPrediction);
      $this->create_temp_pg_table($columns, $tblSuggestion);
      $this->create_temp_pg_table($style_prediction_columns, $tblPrediction);
      $this->insert_into_pg_temp_table($tblSuggestion,$columns,$items);
      $this->insert_into_pg_temp_table($tblPrediction,$style_prediction_columns,$suggested_style_items);

      $statement = $this->pdo_psql->prepare($sql);
      $statement->execute();
    } catch (PDOException $e) {
      $exception = ExecutionException::forStatement($statement, 'Cannot save suggested styles info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sql' => $sql]
      );
      throw $exception;
    } finally {
      $this->drop_pg_temp_table($tblSuggestion);
      $this->drop_pg_temp_table($tblPrediction);
    }
  }

  /**
   * @param string $tblName
   * @param array $columns
   * @param array $items
   * @description Used to insert in $tblName table in postgres db
   * @return array
   */
  private function insert_into_pg_temp_table( string $tblName, array $columns, array $items) : void {

    $sqlValuesClause = $values = PostgresBulkHelper::convertArrayToValuesString($items);
    $sql             = 'INSERT INTO "'.$tblName.'" (' . PostgresBulkHelper::getColumns($columns, PostgresBulkHelper::INSERT_COLUMNS) . ') VALUES ' . $sqlValuesClause . '';
    $statement       = $this->pdo_psql->prepare($sql);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save validation results');
    }
  }
  /**
   *
   * @return array
   */
  public function checktemp_table(): array
  {


    $sql = 'SELECT *
            FROM "tblVerificationAutomaticSubStyleCurationInfo"
            LIMIT 100;';

    $sql1 = 'SELECT *
            FROM "tempStyleUserSuggestions"
            LIMIT 100;';

    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load tblVerificationItemQAStatusHistory');
    }
    return $statement->fetchAll();
  }

  /**
   * @param string $tblName
   * @description Used to drop the temporary table in postgres db
   * @return array
   */
  private function drop_pg_temp_table(string $tblName): void {
    $sql = sprintf('DROP TABLE IF EXISTS "%s"', $tblName);
    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
      $this->log_throwable_error($exception, $exception->getMessage());

      throw $exception;
    }
  }

  /**
   * @param array $columns
   * @param string $tblName
   * @description Used to create temporary table in postgres db
   * @return void
   */
  public function create_temp_pg_table($columns, $tblName)
  {

    $sql = 'CREATE TABLE IF NOT EXISTS "'.$tblName.'" ('. PostgresBulkHelper::getColumns($columns,PostgresBulkHelper::TABLE_COLUMNS).')';
    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot create '.$tblName.' table');

      throw $exception;
    }
  }

  private function createRandomCode(): string
  {
    $chars = "abcdefghijkmnopqrstuvwxyz023456789";
    srand((double) microtime() * 1000000);

    $i = 0; $pass = '';

    while ($i <= 10)
    {
      $num = rand() % 33;
      $tmp = substr($chars, $num, 1);
      $pass = $pass . $tmp;
      $i++;
    }
    return $pass;
  }
}
