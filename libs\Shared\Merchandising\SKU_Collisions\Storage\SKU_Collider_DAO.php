<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\SKU_Collisions\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use WF\Shared\Merchandising\SKU_Collisions\SKU_Collider_Storage;
use PDO;

class SKU_Collider_DAO implements SKU_Collider_Storage {
  private const FLEXGROUP_PERIGOLD_ID = 18226;
  private const STORE_PERIGOLD_ID     = 457;

  private ProductConnection $pdo_ptrw;

  /**
   * @param ProductConnection $pdo_ptrw SQLPRODUCT
   */
  public function __construct(ProductConnection $pdo_ptrw) {
    $this->pdo_ptrw = $pdo_ptrw;
  }

  /**
   * @param string[] $skus calculate kit collisions for the list of given skus
   *
   * @return string[]
   */
  public function getKitCollisions(array $skus) : array {
    $sql = '
      DROP TABLE IF EXISTS #SKUS;
      CREATE TABLE #SKUS (PrSKU NVARCHAR(8) PRIMARY KEY)  
      INSERT INTO #SKUS (PrSKU)
      SELECT DISTINCT value FROM ' . $this->pdo_ptrw->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . ' t

      SELECT DISTINCT child.ChildSKU
      FROM csn_product.dbo.vwExclusivityKitCompositionActive child WITH (NOLOCK)
      WHERE child.ParentSKU IN (
          SELECT kit.ParentSKU
          FROM csn_product.dbo.vwExclusivityKitCompositionActive kit WITH (NOLOCK)
          JOIN #SKUS skus ON skus.PrSKU = kit.ChildSKU
        )
      AND child.ChildSKU NOT IN (
          SELECT kit.ParentSKU
          FROM csn_product.dbo.vwExclusivityKitCompositionActive kit WITH (NOLOCK)
          JOIN #SKUS skus ON skus.PrSKU = kit.ChildSKU
        )
    ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get kit collisions');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param array $collection_ids Collection IDs
   * @param array $skus_excluded  SKUs to be Excluded
   *
   * @return array
   */
  public function getAllCollectionCollisions(array $collection_ids, array $skus_excluded) : array {
    if (empty($skus_excluded)) {
      return [];
    }

    $sql = '
            WITH source_product_collection (SourceSKU, PrXnID) AS (
                SELECT p.PrSKU, p.PrXnID
                FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                WHERE p.PrXnID IN ' . $this->pdo_ptrw->paramsForList(count($collection_ids), 'collection_ids_product', SQL::int) . '
                UNION ALL SELECT gblp_collection.GblPrSKU, gblp_collection.GblPrXnID
                FROM csn_product.dbo.tblGblProduct gblp_collection WITH (NOLOCK)
                WHERE gblp_collection.GblPrXnID IN ' . $this->pdo_ptrw->paramsForList(count($collection_ids), 'collection_ids_gbl_product', SQL::int) . ' 
                  AND gblp_collection.GblBclgID IN (15, 16, 17)
            ),
            best_product (BestSKU, SourceSKU) AS (
                SELECT DISTINCT ISNULL(consolidation.FinalPrSKU, source_product_collection.SourceSKU), source_product_collection.SourceSKU
                FROM source_product_collection
                LEFT JOIN csn_product.dbo.tblProductConsolidationFinal consolidation WITH (NOLOCK)
                       ON consolidation.SourcePrSKU = source_product_collection.SourceSKU
            )

            SELECT DISTINCT product_collision.PrSKU, product_collision.PrXnID 
            FROM source_product_collection
            INNER JOIN best_product ON best_product.SourceSKU = source_product_collection.SourceSKU
            INNER JOIN csn_product.dbo.tblProduct product_collision WITH (NOLOCK) ON product_collision.PrSKU = best_product.BestSKU
            INNER JOIN csn_product.dbo.tblJoinProductChannel channel WITH (NOLOCK)
                    ON product_collision.PrSKU = channel.PrSKU AND channel.PrChannelID = 1
            LEFT JOIN csn_product.dbo.tblProductAdditional prxna WITH (NOLOCK)
                    ON product_collision.PrSKU = prxna.PrSKU            
            WHERE
            (
                product_collision.PrStatus = 4
                OR 
                (
                  product_collision.PrStatus IN ( 1, 2 )
                  AND
                  (
                    NOT EXISTS
                    (
                      SELECT TOP (1) 1 AS PtExists
                      FROM csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                      INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                              ON qfl.QflID = qfb.QfbQflID
                      LEFT JOIN csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                              ON pt2.QuickformID = qfl.QflQfpID
                      WHERE qfb.QfbPrSku = product_collision.PrSKU
                          AND pt2.ExecutionCompleteDate IS NULL
                    )
                  )
                )
            )
            AND product_collision.PrBclgID IN ( 1, 2, 3 )
            -- Exclude SKUs already displayed based on their Best Version
            -- An SKU could have more than one version. All of them theoretically result in the same best version sku
            -- So, if we have something in the exclusion list, the best way to ensure none of its versions would be in the returned result
            -- is to exclude by their best versions
            AND product_collision.PrSKU NOT IN (
                SELECT BestSKU FROM best_product WHERE SourceSKU IN ' . $this->pdo_ptrw->paramsForList(count($skus_excluded), 'exclude_sku', SQL::nvarchar(8)) . '
            )
            AND NOT EXISTS(
              SELECT TOP 1 1 AS is_parent
              FROM csn_product.dbo.vwExclusivityKitComposition kca WITH ( NOLOCK )
              WHERE kca.ParentSKU = product_collision.PrSKU
            )
            ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValuesList(':collection_ids_product', $collection_ids, SQL::int);
    $statement->bindValuesList(':collection_ids_gbl_product', $collection_ids, SQL::int);
    $statement->bindValuesList(':exclude_sku', $skus_excluded, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get collection collisions - ' . implode(';', $statement->errorInfo()));
    }

    // array['sku1'] = array('PrXnID' => 12343)
    $queryResult = $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
    $collisions  = [];
    foreach ($queryResult as $sku => $collision) {
      $collisions[$sku] = $collision['PrXnID'];
    }
    unset($sku);

    if (empty($collisions)) {
      return [];
    }

    // get perigold white label map
    $perigold_wl_sku_map = $this->getPerigoldWhiteLabelSkusWithContextCollection(array_keys($collisions));
    if (empty($perigold_wl_sku_map)) {
      return $collisions;
    }

    $perigold_replaced_with_wl_skus = [];
    foreach ($collisions as $sku => $context_collection_id) {
      // if no replacement was found
      if (empty($perigold_wl_sku_map[$sku])) {
        $perigold_replaced_with_wl_skus[$sku] = $context_collection_id;

        continue;
      }

      $wl_sku = $perigold_wl_sku_map[$sku];

      // if replacement is in the ignored list
      if (in_array($wl_sku, $skus_excluded)) {
        continue;
      }

      $perigold_replaced_with_wl_skus[$wl_sku] = $context_collection_id;
    }

    // array['sku1' => 12334, 'skku2' => 44545,...];
    return $perigold_replaced_with_wl_skus;
  }

  /**
   * @param int   $collection_id Collection ID
   * @param array $skus_excluded SKUs to be Excluded
   *
   * @return string[]
   */
  public function getCollectionCollisions(int $collection_id, array $skus_excluded) : array {
    if (empty($skus_excluded)) {
      return [];
    }

    $sql = '
            DECLARE @collectionID INT = :collection_id

            ;WITH source_product_collection (SourceSKU) AS (
                SELECT p.PrSKU
                FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                WHERE p.PrXnID = @collectionID
                UNION ALL SELECT gblp_collection.GblPrSKU
                FROM csn_product.dbo.tblGblProduct gblp_collection WITH (NOLOCK)
                WHERE gblp_collection.GblPrXnID = @collectionID AND gblp_collection.GblBclgID IN (15, 16, 17)
            ),
            best_product (BestSKU, SourceSKU) AS (
                SELECT DISTINCT ISNULL(consolidation.FinalPrSKU, source_product_collection.SourceSKU), source_product_collection.SourceSKU
                FROM source_product_collection
                LEFT JOIN csn_product.dbo.tblProductConsolidationFinal consolidation WITH (NOLOCK)
                       ON consolidation.SourcePrSKU = source_product_collection.SourceSKU
            )

            SELECT DISTINCT product_collision.PrSKU
            FROM source_product_collection
            INNER JOIN best_product ON best_product.SourceSKU = source_product_collection.SourceSKU
            INNER JOIN csn_product.dbo.tblProduct product_collision WITH (NOLOCK) ON product_collision.PrSKU = best_product.BestSKU
            INNER JOIN csn_product.dbo.tblJoinProductChannel channel WITH (NOLOCK)
                    ON product_collision.PrSKU = channel.PrSKU AND channel.PrChannelID = 1
            LEFT JOIN csn_product.dbo.tblProductAdditional prxna WITH (NOLOCK)
                    ON product_collision.PrSKU = prxna.PrSKU
            WHERE
            (
                product_collision.PrStatus = 4
                OR 
                (
                  product_collision.PrStatus IN ( 1, 2 )
                  AND
                  (
                    NOT EXISTS
                    (
                      SELECT TOP (1) 1 AS PtExists
                      FROM csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                      INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                              ON qfl.QflID = qfb.QfbQflID
                      LEFT JOIN csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                              ON pt2.QuickformID = qfl.QflQfpID
                      WHERE qfb.QfbPrSku = product_collision.PrSKU
                          AND pt2.ExecutionCompleteDate IS NULL
                    )
                  )
                )
            )
            AND product_collision.PrBclgID IN ( 1, 2, 3 )
            -- Exclude SKUs already displayed based on their Best Version
            -- An SKU could have more than one version. All of them theoretically result in the same best version sku
            -- So, if we have something in the exclusion list, the best way to ensure none of its versions would be in the returned result
            -- is to exclude by their best versions
            AND product_collision.PrSKU NOT IN (
                SELECT BestSKU FROM best_product WHERE SourceSKU IN ' . $this->pdo_ptrw->paramsForList(count($skus_excluded), 'exclude_sku', SQL::nvarchar(8)) . '
            )
            AND NOT EXISTS(
              SELECT TOP 1 1 AS is_parent
              FROM csn_product.dbo.vwExclusivityKitComposition kca WITH ( NOLOCK )
              WHERE kca.ParentSKU = product_collision.PrSKU
            )
            ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue(':collection_id', $collection_id, PDO::PARAM_INT);
    $statement->bindValuesList(':exclude_sku', $skus_excluded, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get collection collisions - ' . implode(';', $statement->errorInfo()));
    }

    $collisions = $statement->fetchAll(PDO::FETCH_COLUMN);

    if (empty($collisions)) {
      return $collisions;
    }

    // get perigold white label map
    $perigold_wl_sku_map = $this->getPerigoldWhiteLabelSkus($collisions);
    if (empty($perigold_wl_sku_map)) {
      return $collisions;
    }

    $perigold_replaced_with_wl_skus = [];
    foreach ($collisions as $sku) {
      // if no replacement was found
      if (empty($perigold_wl_sku_map[$sku])) {
        $perigold_replaced_with_wl_skus[] = $sku;

        continue;
      }

      $wl_sku = $perigold_wl_sku_map[$sku];

      // if replacement is in the ignored list
      if (in_array($wl_sku, $skus_excluded)) {
        continue;
      }

      $perigold_replaced_with_wl_skus[] = $wl_sku;
    }

    return $perigold_replaced_with_wl_skus;
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return string[]
   */
  public function getPerigoldWhiteLabelSkus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }

    // find Perigold skus from the initial list for which we compute the replacements
    $perigold_skus = $this->getPerigoldSkus($skus);

    if (empty($perigold_skus)) {
      return [];
    }

    $white_label_skus_data = $this->getPerigoldWhiteLabelSkusData($perigold_skus);

    $perigold_white_label_sku_map = [];

    foreach ($white_label_skus_data as $item) {
      $perigold_sku   = $item['PerigoldSKU'];
      $whitelabel_sku = $item['VersionSKU'];
      $is_valid       = $item['IsValid'];
      $pr_xn_id       = $item['PrXnID'];

      // skip if we already have a candidate foundd
      if (isset($perigold_white_label_sku_map[$perigold_sku])) {
        continue;
      }

      // skip if it is not valid
      if (!$is_valid) {
        continue;
      }

      $perigold_white_label_sku_map[$perigold_sku] = $whitelabel_sku;
    }

    return $perigold_white_label_sku_map;
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array
   */
  public function getPerigoldWhiteLabelSkusWithContextCollection(array $skus) : array {
    if (empty($skus)) {
      return [];
    }

    // find Perigold skus from the initial list for which we compute the replacements
    $perigold_skus = $this->getPerigoldSkus($skus);

    if (empty($perigold_skus)) {
      return [];
    }

    $white_label_skus_data = $this->getPerigoldWhiteLabelSkusData($perigold_skus);

    $perigold_white_label_sku_map = [];

    foreach ($white_label_skus_data as $item) {
      $perigold_sku   = $item['PerigoldSKU'];
      $whitelabel_sku = $item['VersionSKU'];
      $is_valid       = $item['IsValid'];
      $pr_xn_id       = $item['PrXnID'];

      // skip if we already have a candidate foundd
      if (isset($perigold_white_label_sku_map[$perigold_sku])) {
        continue;
      }

      // skip if it is not valid
      if (!$is_valid) {
        continue;
      }

      $perigold_white_label_sku_map[$perigold_sku] = $pr_xn_id;
    }

    return $perigold_white_label_sku_map;
  }

  /**
   * Returns a list of Perigold SKUs from a list of input skus
   *
   * @param string[] $skus SKUs
   *
   * @return array
   */
  public function getPerigoldSkus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }

    $sql = '
            SELECT product.PrSKU AS perigold_sku 
            FROM csn_product.dbo.tblProduct product WITH (NOLOCK)
            LEFT JOIN csn_product.dbo.tbljoinProductFlexGroup fpg WITH (NOLOCK) ON fpg.PrSKU = product.PrSKU AND fpg.fpgid = :flexgroup_perigold_id
            LEFT JOIN csn_product.dbo.tblStoreExclusiveManufacturer em WITH (NOLOCK) ON em.SemMaID = product.PrMaID and em.SemSoID = :store_perigold_id

            WHERE product.PrSKU IN (' . $this->pdo_ptrw->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')
                  AND (em.SemMaID IS NOT NULL OR fpg.PrSKU IS NOT NULL)
            ';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue(':flexgroup_perigold_id', self::FLEXGROUP_PERIGOLD_ID, PDO::PARAM_INT);
    $statement->bindValue(':store_perigold_id', self::STORE_PERIGOLD_ID, PDO::PARAM_INT);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get Perigold skus');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $perigold_skus Perigold SKUs
   *
   * @return array
   */
  public function getPerigoldWhiteLabelSkusData(array $perigold_skus) : array {
    if (empty($perigold_skus)) {
      return [];
    }

    $sql = 'DECLARE @SKUS TABLE (PrSKU NVARCHAR(8) PRIMARY KEY)
            INSERT INTO @SKUS (PrSKU)
            SELECT DISTINCT value FROM ' . $this->pdo_ptrw->paramsForList(count($perigold_skus), 'perigold_sku', SQL::nvarchar(8)) . ' t
            ';

    $sql .= 'SELECT
                t.PerigoldSKU,
                t.VersionSKU,
                t.PrXnID,
                t.IsWhiteLabelManufacturer,
                t.IsValidPrChannel,
                t.IsActivePrStatus,
                t.IsValidBrandCatalog,
                t.IsNotKitParent,
                IIF(t.IsWhiteLabelManufacturer = 1 AND t.IsValidPrChannel = 1 AND t.IsActivePrStatus = 1 AND t.IsValidBrandCatalog = 1 AND t.IsNotKitParent = 1, 1, 0) AS IsValid,
                t.VersionCloneSKU,
                t.VersionPriority,
                t.VersionInfo
            FROM (
                   SELECT productPerigold.PrSKU AS PerigoldSKU,
                   version.PrSKU AS VersionSKU,
                   version.Info AS VersionInfo,
                   version.Priority AS VersionPriority,
                   version.CloneSKU AS VersionCloneSKU,
                   productPerigold.PrXnID AS PrXnID,
                   ' . $this->getPerigoldWhiteLabelSkuSubqueryWhiteLabelManufacturer() . '  AS IsWhiteLabelManufacturer,
                   ' . $this->getPerigoldWhiteLabelSkuSubqueryProductChannel() . ' AS IsValidPrChannel,
                   ' . $this->getPerigoldWhiteLabelSkuSubqueryActiveStatus() . ' AS IsActivePrStatus,
                   ' . $this->getPerigoldWhiteLabelSkuSubqueryBrandCatalog() . ' AS IsValidBrandCatalog,
                   ' . $this->getPerigoldWhiteLabelSkuSubqueryNotKitParent() . ' AS IsNotKitParent

                   FROM csn_product.dbo.tblProduct productPerigold WITH (NOLOCK)
                   JOIN @SKUS skus ON skus.PrSKU = productPerigold.PrSKU
                   CROSS APPLY (' . $this->getPerigoldWhiteLabelSkuSubqueryFindSkuVersions() . ') version
                   JOIN csn_product.dbo.tblProduct productVersion WITH (NOLOCK) ON productVersion.PrSKU = version.PrSKU

                   WHERE productPerigold.PrBclgID = productVersion.PrBclgID
            ) t

            ORDER BY PerigoldSKU, VersionPriority, VersionSKU';

    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValuesList(':perigold_sku', $perigold_skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get Perigold wl skus versions');
    }

    return $statement->fetchAll();
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryBrandCatalog() : string {
    return 'IIF(productVersion.PrBclgID IN (1, 2, 3), 1, 0)';
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryWhiteLabelManufacturer() : string {
    $sql = 'IIF(EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.tblManufacturer manufacturer WITH (NOLOCK) WHERE manufacturer.MaID = productVersion.PrMaID AND manufacturer.MaBrwid = 1), 1, 0)';

    return $sql;
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryNotKitParent() : string {
    $sql = 'IIF(NOT EXISTS(
                SELECT TOP 1 1 AS is_parent
                FROM csn_product.dbo.vwExclusivityKitComposition kca WITH (NOLOCK)
                WHERE kca.ParentSKU = productVersion.PrSKU
           ), 1, 0)';

    return $sql;
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryProductChannel() : string {
    $sql = 'IIF(EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.tblJoinProductChannel channel WITH (NOLOCK) WHERE productVersion.PrSKU = channel.PrSKU AND channel.PrChannelID = 1), 1, 0)';

    return $sql;
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryActiveStatus() : string {
    $sql = 'IIF(productVersion.PrStatus = 4
            OR (
              productVersion.PrStatus IN ( 1, 2 ) AND
              (
                NOT EXISTS
                (
                  SELECT TOP (1) 1 AS PtExists
                  FROM csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                  JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                  ON qfl.QflID = qfb.QfbQflID
                  LEFT JOIN csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                  ON pt2.QuickformID = qfl.QflQfpID
                  WHERE qfb.QfbPrSku = productVersion.PrSKU
                  AND pt2.ExecutionCompleteDate IS NULL
                 )
              )
            )
            , 1, 0)';

    return $sql;
  }

  /**
   * @return string
   */
  private function getPerigoldWhiteLabelSkuSubqueryFindSkuVersions() : string {
    $sql = 'SELECT DISTINCT 
                  ISNULL(consolidation.FinalPrSKU, scl.SclgClonedPrSKU) AS PrSKU, 
                  scl.SclgClonedPrSKU AS CloneSKU, 
                  1 AS Priority,
                  \'Found in Clone Log as original, select Consolidate Final Best Version\' AS Info      
            FROM csn_product.dbo.tblskuclonelog scl WITH (NOLOCK)
            LEFT JOIN csn_product.dbo.tblProductConsolidationFinal consolidation WITH (NOLOCK) ON consolidation.SourcePrSKU = scl.SclgClonedPrSKU
            WHERE scl.SclgOriginalPrSKU = productPerigold.prsku

            UNION

            SELECT DISTINCT 
                  ISNULL(consolidation.FinalPrSKU, scl.SclgOriginalPrSKU) AS PrSKU, 
                  scl.SclgOriginalPrSKU AS CloneSKU, 
                  2 AS Priority,
                  \'Found in Clone Log as target - Look for Cloned + Consolidate Final - Best Version\' AS Info
            FROM csn_product.dbo.tblskuclonelog scl WITH (NOLOCK)
            LEFT JOIN csn_product.dbo.tblProductConsolidationFinal consolidation WITH (NOLOCK) ON consolidation.SourcePrSKU = scl.SclgOriginalPrSKU
            WHERE scl.SclgClonedPrSKU = productPerigold.prsku

            UNION

            SELECT DISTINCT 
                  pco.PcoSourcePrSKU AS PrSKU, 
                  null AS CloneSKU, 
                  3 AS Priority,
                  \'Found in Consolidation as target\' AS Info
            FROM csn_product.dbo.tblProductConsolidation pco WITH (NOLOCK)
            WHERE pco.PcoTargetPrSKU = productPerigold.prsku AND pco.pcotype = \'Duplicate\'
            
            UNION

            SELECT DISTINCT 
                  pco.PcoTargetPrSKU AS PrSKU, 
                  null AS CloneSKU, 
                  4 AS Priority,
                  \'Found in Consolidation as source\' AS Info
            FROM csn_product.dbo.tblProductConsolidation pco WITH (NOLOCK)
            WHERE pco.PcoSourcePrSKU = productPerigold.prsku AND pco.pcotype = \'Duplicate\'';

    return $sql;
  }
}
