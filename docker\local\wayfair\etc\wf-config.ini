[resources]
WF_AB_DEPLOYMENT = 'false'
WF_AEROSPIKE_REST_ENDPOINTS = '{"wayfair-rest":"c1.aerospike-proxy.service.intraiad1.devconsul.csnzoo.com:8080","wayfair":"c1.aerospike-proxy.service.intraiad1.devconsul.csnzoo.com:8080","scribe":"c1.aerospike-proxy.service.intraiad1.devconsul.csnzoo.com:8080","wayfair-xdr":"c1.aerospike-proxy.service.intraiad1.devconsul.csnzoo.com:8080"}'
WF_ALL_PODS = '{"bo1": "BO1","cit01": "BOINT","cit03": "SEINT","dev01": "BODEV","devbo1": "BO1DEV","exp01": "EXPER","ie2": "IRE","san01": "B<PERSON>AN","san03": "SESAN","se1": "SE1","sec01": "BOSEC","sec02": "EPSEC","sec03": "SESEC","sec04": "IRESEC","secbo1": "BO1SEC","secdevbo1": "BO1DEVSEC","secse1": "SE1SEC","secstgbo1": "BO1STGSEC","srv1": "BOCOP","stgbo1": "BO1STG","web01": "BOWEB","web02": "EPWEB","web03": "SEWEB","fra1":"FRA1","devfra1":"DEVFRA1","iad1":"IAD1","deviad1":"DEVIAD1","dsm1":"DSM1","devdsm1":"DEVDSM1","pdx1":"PDX1","devpdx1":"DEVPDX1","grq1":"GRQ1","devgrq1":"DEVGRQ1"}'
WF_ALL_SERVICES_LB = '{"sdegrq1":"leader-purest-haproxy.service.intradsm1.sdeconsul.csnzoo.com:8280","devsdedsm1":"leader-purest-haproxy.service.intradsm1.sdedevconsul.csnzoo.com:8280","bo1": "purest.service.bo1.csnzoo.com:8280", "web01": "boservices.csnzoo.com:8280", "se1": "purest.service.se1.csnzoo.com:8280", "web03": "seservices.csnzoo.com:8280", "dev01": "devservices.csnzoo.com:8280", "devbo1": "devservices.csnzoo.com:8280", "sdedsm1": "leader-purest-haproxy.service.intradsm1.sdeconsul.csnzoo.com:8280", "stg01": "staging.purest.service.bo1.csnzoo.com:8280", "ie2": "10.236.200.200:8280","devfra1": "purest-haproxy.service.intrafra1.devconsul.csnzoo.com:8280","devgrq1": "purest-haproxy.service.intragrq1.devconsul.csnzoo.com:8280","deviad1": "purest-haproxy.service.intraiad1.devconsul.csnzoo.com:8280","devdsm1": "purest-haproxy.service.intradsm1.devconsul.csnzoo.com:8280","devpdx1": "purest-haproxy.service.intrapdx1.devconsul.csnzoo.com:8280","fra1": "purest-haproxy.service.intrafra1.consul.csnzoo.com:8280","grq1": "purest-haproxy.service.intragrq1.consul.csnzoo.com:8280","iad1": "purest-haproxy.service.intraiad1.consul.csnzoo.com:8280","dsm1": "purest-haproxy.service.intradsm1.consul.csnzoo.com:8280","pdx1": "purest-haproxy.service.intrapdx1.consul.csnzoo.com:8280"}'
WF_BDE_ATWOOD_API = 'kube-bde-atwood-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_BLACKHAWK = '216.104.233.10:8443'
WF_BLACKHAWK_BACKUP = '205.138.247.10:8443'
WF_ORDER_HOLDS_API = 'http://kube-order-holds-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_CATALOG_SERVICE_STOREFRONTAPI = 'kube-catalog-service-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_CATALOG_COMPATIBLE_PRODUCTS_SERVICE = 'http://kube-catalog-compatibleitems-api.service.intraiad1.devconsul.csnzoo.com'
WF_CATALOG_UPXSELL_COMPATIBLE_PRODUCTS_GRAPHQL = 'https://kube-catalog-upxsell-relatedproducts-graphql.service.intradsm1.sdeconsul.csnzoo.com'
WF_CATALOG_BEST_OPTION_COMBINATION_API = 'kube-catalog-best-option-combination-api.service.intradsm1.devconsul.csnzoo.com'
WF_CATALOG_UPXSELL_DONATIONS_SERVICE = 'kube-catalog-upxsell-donations.service.intraiad1.devconsul.csnzoo.com'
WF_CLM = '10.22.70.200:8080'
WF_CLUSTERNAME = 'webphp'
WF_CLOUD_REGION = ''
WF_CLOUD_ZONE = ''
WF_CONSUL_BASE = 'intradsm1.sdeconsul.csnzoo.com'
WF_CONSUL_DATACENTER = 'intradsm1'
WF_CONSULLB = 'consul.service.intradsm1.devconsul.csnzoo.com:8500'
WF_CUSTINFOSVCLB = 'kube-custinfosvc.service.intradsm1.sdeconsul.csnzoo.com'
WF_CUSTINFOSVC = 'kube-custinfosvc-java.service.intradsm1.sdeconsul.csnzoo.com'
WF_DB_LOCATION = '{"master":"sqlproduct.dev.db.csnzoo.com","slaves":["sqlinventory.dev.db.csnzoo.com"],"sqlrelay":"sqlrelayc1-pt-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8167"}'
WF_DEFAULT_OPTIONS_API = 'kube-default-options-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_DEPLOY = 'https://artifactory-haproxy.service.intradsm1.consul.csnzoo.com:8099'
WF_DEPLOY_OPCACHE_FLUSH_DELAY = '1'
WF_DESCARTES_EXT = 'https://rtg4bif.services.lnos.com'
WF_DESCARTES_INT = 'http://descartesifc.csnzoo.com'
WF_DMPS_SPLITTER_SERVICE = 'kube-dmps-splitter.service.intraiad1.devconsul.csnzoo.com'
WF_DOCROOT = '/usr/local/www/'
WF_DPN_SERVICE = 'kube-dpn-service.service.intradevbo1.consul.csnzoo.com'
WF_ENV = 'dev'
WF_FANGORN_SERVICE_KUBERNETES_URL = 'kubernetesc1.service.bo1.csnzoo.com:8887'
WF_FANGORN_SERVICE_URL = 'fangorn.service.bo1.csnzoo.com'
WF_FINANCE_SERVICE_URL = 'financec1.service.bo1.csnzoo.com'
WF_FRAUD_SERVICE_KUBERNETES_URL = 'kubernetesc1.service.bo1.csnzoo.com:8480'
WF_FREYJA_ZEROMQ_PUBLISHERS = '{}'
WF_FEATURE_TEST_COMPILED_FEATURES = '1'
WF_FEATURE_FALLBACK_SOURCE = 'db'
WF_FEATURE_FRESH_FEATURES_PATH = 'null'
WF_GCE_PROJECT = 'wf-gcp-us-sds-prod'
WF_GCS_IMG_BUCKET = 'prod.crunched.img.wfrcdn.com'
WF_GCS_IMG_DOCS_BUCKET = 'prod.docs.img.wfrcdn.com'
WF_GCS_IMG_DOCS_ENDPOINT = 'secure.img-fg.wfcdn.com/docs'
WF_GCS_IMG_DOCS_KEYFILE = 'wf-img-proj-a25ddae5fe68.json'
WF_GCS_IMG_GENERAL_BUCKET = 'prod.general.img.wfrcdn.com'
WF_GCS_IMG_GENERAL_KEYFILE = 'wf-img-proj-61fd33918ea8.json'
WF_GCS_IMG_KEYFILE = 'wf-img-proj-6a7f67a9a51b.json'
WF_GELFRECEIVER = 'gelf-proxy.service.intradsm1.sdeconsul.csnzoo.com'
WF_GRAYMATTER_REST = 'kube-graymatter-rest.service.intradsm1.sdeconsul.csnzoo.com'
WF_HOSTNAME = 'gke-dsm1.c.wf-gcp-us-sde-prod.internal'
WF_INVENTORY_CW = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_LS = 'kube-scs-inventoryservice-liteship.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_DEF = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_DEF_BACKUP = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_DEF_K8S = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_DEF_K8S_BACKUP = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_INVENTORY_ISO = 'kube-instockoptions.service.intradsm1.sdeconsul.csnzoo.com'
WF_FULFILLABILITY_SERVICE = 'kube-fopt-fulfillability-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_FOPT_DELIVERYGUARANTEECACHE_API = 'kube-fopt-deliveryguaranteecache-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIP_DEF = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIP_DEF_BACKUP = 'kube-scs-liteshipservice-def.service.intrapdx1.devconsul.csnzoo.com'
WF_LITESHIP_SF = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIP_SF_BACKUP = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIP_SF_K8S = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_FULFILLMENT_PLAN_SERVICE = 'kube-fulfillment-plan-services.service.intradsm1.sdeconsul.csnzoo.com'
WF_IPADDRESS = '***********'
WF_DELIVERY_ORCHESTRATOR_SF = 'http://kube-scs-deliveryexpectationorchestrator.service.intradevbo1.consul.csnzoo.com/DeliveryExpectation'
WF_ISILON_REST = 'rest.filer01.bo1.csnzoo.com'
WF_ISILON_REST_ALL = '{"default":"rest.isilon.se2.csnzoo.com", "bo1":"rest.isilon.bo3.csnzoo.com", "bo3":"rest.isilon.bo3.csnzoo.com", "ie2":"rest.isilon.ie2.csnzoo.com"}'
WF_KAFKA_STREAMING_BROKERS = '[ "c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKA_C7_STREAMING_BROKERS = '[ "c7.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_KAFKAPROXY = 'kube-kafka-proxy.service.intradsm1.sdeconsul.csnzoo.com'
WF_KAFKARESTPROXY = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_LEGALENTITY_SERVICE = 'kube-finance-legal-entity-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_LOCATION = 'dev'
WF_LOCATION_SHORT = 'dev'
WF_OSM_ADSERVICE = 'kube-osm-ad-request.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_RETURNS_ELIGIBILITY_URL = 'http://kube-os-returns-eligibility.service.intradevbo1.consul.csnzoo.com/'
WF_ORDER_SQL = 'SQLORDER.csnzoo.com'
WF_PAYMENTS_API = 'payments-api.service.secdsm1.devconsul.csnzoo.com'
WF_PREEMPTABLE = ''
WF_PRICING_SERVICE_STOREFRONTAPI = 'kube-pricing-aletheia-storefront.service.intraiad1.consul.csnzoo.com'
WF_POD = 'sdedsm1'
WF_POD_NICENAME = 'GCE_US-CENTRAL1-B'
WF_RAW_IMG_ENDPOINT_BO = 'origin.img.wfrcdn.com'
WF_RAW_IMG_ENDPOINT_SE = 'origin-se.img.wfrcdn.com'
WF_RECSSVC_KUBERNETES_INGRESS = 'kube-recommend.service.intradsm1.consul.csnzoo.com:80'
WF_PRODUCT_SEARCH_K8S = 'kube-product-search-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_REDIS_SHARDS_STATIC = '{}'
WF_SAYL = '[]'
WF_SCRIBE_EVENT_REGISTRY = 'kube-scribe-event-registry.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCRIBE_GATEWAY = 'kube-scribe-gateway.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCRIBE_LEGACY_GATEWAY = 'kube-scribe-gateway.service.intradsm1.devconsul.csnzoo.com'
WF_SCRIBE_PRIVATE_GATEWAY = 'kube-scribe-private-gateway.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCRIBE_PRIVATE_GATEWAY_PRICING = 'kube-scribe-private-gateway-pricing.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCRIBE_PRIVATE_GATEWAY_SEARCHRECS = 'kube-scribe-private-gateway-search-recs.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCRIBE_PUBLIC_GATEWAY = 'kube-scribe-public-api.service.intradsm1.devconsul.csnzoo.com'
WF_SECEGRESSPROXY = 'secegressproxy.service.intradsm1.devconsul.csnzoo.com:3128'
WF_DATADOGSQUID = 'http://datadogsquid.service.intradsm1.devconsul.csnzoo.com:3128'
WF_SQUIDPROXY = 'squidproxy.service.intradsm1.devconsul.csnzoo.com:3128'
WF_SECURE_SERVICESLB = 'leader-secpurest-haproxy.service.intradsm1.sdeconsul.csnzoo.com:8243'
WF_SERVICE_ENG_ORDER_DATA_HOST = 'kube-service-engineering-order-data-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_OD_ORDER_DATA_HOST = 'kube-order-data-api.service.intradsm1.devconsul.csnzoo.com'
WF_OD_ORDER_DATA_BO1 = 'kube-order-data-api.service.intrabo1.consul.csnzoo.com'
WF_OD_ACCESS_API = 'kube-order-access-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_OD_ACCESS_API_ISTIO = 'istio-ox-service-mesh.service.intradsm1.devconsul.csnzoo.com'
WF_OX_ORDER_SERVICES = 'istio-ox-service-mesh.service.intradsm1.sdeconsul.csnzoo.com'
WF_OX_ADDRESS_SERVICE = 'kube-ox-address-service.service.intradsm1.devconsul.csnzoo.com'
WF_OD_ELASTICSEARCH_HOST = 'http://ssesc1.service.intraiad1.devconsul.csnzoo.com:9200'
WF_OD_MOD_BRIDGE_SVC = 'kube-modification-bridge-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_ORDER_FINANCIALS_API_URL = 'kube-order-financials-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_ORDER_FULFILLMENT_API_URL = 'https://kube-order-fulfillment-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_FEATURE_RESERVATIONS_API_URL = 'kube-feature-reservations.service.intradsm1.sdeconsul.csnzoo.com'
WF_ORDER_PLATFORM_PHP_SERVICES_URL = 'kube-order-platform-php-services.service.intradsm1.sdeconsul.csnzoo.com'
WF_OD_PLATFORM_API = 'kube-order-platform-api.service.intradevbo1.consul.csnzoo.com'
WF_OS_CHANGE_DELIVERY_METHOD_ELIGIBILITY_URL = 'kube-change-delivery-method-eligibility.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_CHANGE_DELIVERY_METHOD_MODIFICATION_URL = 'kube-change-delivery-method.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_CHANGE_DELIVERY_METHOD_OPTIONS_URL = 'kube-change-delivery-method-options.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_EDIT_REFUND_METHOD_ELG_URL = 'kube-nodeport-edit-refund-method-elg.service.intradevbo1.consul.csnzoo.com'
WF_OS_EDIT_REFUND_METHOD_MOD_URL = 'kube-nodeport-edit-refund-method-mod.service.intradevbo1.consul.csnzoo.com'
WF_OS_RETURNS_URL = 'kube-returns.service.intradevbo1.consul.csnzoo.com'
WF_OS_RETURNS_PLATFORM_URL = 'kube-returns-platform.service.intraiad1.devconsul.csnzoo.com'
WF_OX_ORDER_RETURNS_URL = 'kube-order-returns.service.intradsm1.sdeconsul.csnzoo.com'
WF_OX_ORDER_RESOLUTIONS_URL = 'kube-order-resolutions.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_SHIP_SPEED_METHOD_URL = 'kube-ship-speed-method.service.intradevbo1.consul.csnzoo.com'
WF_OS_MODIFY_DELIVERY_INSTRUCTIONS_URL = 'kube-modify-delivery-instructions.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_RETURN_ELIGIBILITY_URL = 'kube-return-eligibility-api.service.intradevbo1.consul.csnzoo.com'
WF_OS_DELIVERY_INSTRUCTIONS_MODIFICATION_ELIGIBILITY_URL = 'kube-update-delivery-instructions.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_CANCELLATION_SERVICE_URL = 'kube-cancellation-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_RETURN_CANCELLATION_URL = 'kube-return-cancellation.service.intradevbo1.consul.csnzoo.com'
WF_EDIT_RETURN_PICKUP_ELIGIBILITY_URL = 'kube-edit-return-pickup-eligibility.service.intradevbo1.consul.csnzoo.com'
WF_SMTP_HOST = 'smtp.csnzoo.com'
WF_SQLRELAY_SOCKET_PATH_PREFIX = '/wayfair/tmp/sqlrelay/tmp/'
WF_STATSD = '[ "statsdreplicator.statsdreplicator-internal-us-central1-forwarder.il4.us-central1.lb.wf-gcp-us-lm-dev.internal" ]'
WF_STATSDRELAY = '127.0.0.1'
WF_TELEGRAF_PORT = '8094'
WF_TESTGROUP = 'G'
WF_VIRTUAL = 'gce'
WF_ZONE = 'intra'
WF_SPONSORED_PRODUCTS_WSPSVC_API = 'kube-wspsvc.service.intradevbo1.consul.csnzoo.com'
WF_GELFRECEIVER_PORT = '12201'
WF_SOLR_ZOOKEEPER = '{ "bo1": ["zookeeperc1n1.dev.bo1.csnzoo.com:2181", "zookeeperc1n2.dev.bo1.csnzoo.com:2181", "zookeeperc1n3.dev.bo1.csnzoo.com:2181"], "iad1": ["zookeeper-iad1-g1-1.c.wf-gcp-us-support-dev.internal:2181", "zookeeper-iad1-g2-1.c.wf-gcp-us-support-dev.internal:2181", "zookeeper-iad1-g3-1.c.wf-gcp-us-support-dev.internal:2181"] }'
WF_AUTH_SERVICE = 'https://kube-auth-identity.service.intradsm1.sdeconsul.csnzoo.com'
WF_AUTH_OPENID_CONFIGURATION = 'https://kube-auth-identity.service.intradsm1.sdeconsul.csnzoo.com/.well-known/openid-configuration'
WF_AUTH_JWKS = 'https://kube-auth-identity.service.intradsm1.sdeconsul.csnzoo.com/.well-known/openid-configuration/jwks'
WF_RETURNS_ROUTING_SERVICE = 'https://kube-returns-routing.service.intradevbo1.consul.csnzoo.com'
WF_RETURNS_ROUTING_SERVICE_CD = 'https://kube-returns-routing-service.service.intradevbo1.consul.csnzoo.com'
WF_CORE_CUSTOMER_DATA_SERVICE = 'kube-core-customer-data.service.intradsm1.sdeconsul.csnzoo.com'
WF_SWITCHBOARD_HOST = 'http://kube-switchboard.service.intradsm1.sdeconsul.csnzoo.com'
WF_SWITCHBOARD_BASE_PATH = 'api/v1'
WF_WARRANTY_SERVICE_URL = 'http://kube-home-service-warranty.service.intradsm1.sdeconsul.csnzoo.com/'
WF_HOME_SERVICES_POST_ORDER_BOOKING_API = 'kube-hs-postorder-booking.service.intradsm1.devconsul.csnzoo.com'
WF_DD_EGRESSPROXY = ''
WF_DOGSTATSDPROXY = 'dogstatsdproxy.query.sdeconsul.csnzoo.com'
WF_DOGSTATSD_PORT = '31600'
WF_SCS_SCHEDULE_DELIVERY_SERVICE = 'http://kube-scs-schedule-delivery-service.service.intradevbo1.consul.csnzoo.com'
WF_DYNAMIC_DATA_DISTRIBUTOR = 'kube-dynamic-data-distributor.service.intradsm1.sdeconsul.csnzoo.com'
WF_DYNAMIC_SERVICES_STOREFRONT_API = 'kube-dynamic-services-storefront-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_DYNAMIC_SELECTOR = 'kube-dynamic-selector.service.intradsm1.sdeconsul.csnzoo.com'
WF_LIST_SERVICE = 'kube-list-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAYFIN_SERVICE = 'kube-wayfin-service.service.intradsm1.sdeconsul.csnzoo.com:30085'
WF_WAYFIN_DATA_SERVICE = 'kube-wayfin-data-service.service.intraiad1.devconsul.csnzoo.com'
WF_CUST_TARG_SVC = 'kube-customer-targeting-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_COMM_API = 'kube-sales-customer-communication-api.service.intraiad1.devconsul.csnzoo.com'
WF_NEL_ENDPOINTS = '[{"url":"https://nel.wayfair.io/nel"}]'
WF_SF_ISTIO_TESTBENCH = 'kube-sf-istio-testbench.service.intradevbo1.consul.csnzoo.com'
WF_SF_ISTIO_TESTBENCH_CONTROL = 'kube-sf-istio-testbench-control.service.intradevbo1.consul.csnzoo.com'
WF_DYNAMIC_CONTENT_SERVICE = 'kube-dynamic-content.service.intraiad1.devconsul.csnzoo.com'
WF_PROMOCODE_SERVICE = 'kube-sf-promos-promocodes.service.intradevbo1.consul.csnzoo.com'
WF_VIEW_RETURN_INSTRUCTIONS_URL = 'kube-view-return-instructions.service.intradevbo1.consul.csnzoo.com'
WF_ORDER_PLACEMENT_API_URL = 'kube-order-placement-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_REPLACEMENT_APP = 'kube-replacement-app.service.intradsm1.sdeconsul.csnzoo.com'
WF_REPORT_A_PROBLEM_API = 'kube-report-a-problem.service.intradsm1.sdeconsul.csnzoo.com'
WF_ORDER_MEMO_SERVICE_URL = 'kube-order-memo-service.service.intraiad1.devconsul.csnzoo.com'
WF_CANCEL_WORKFLOW_API = 'kube-cancellation.service.intradsm1.sdeconsul.csnzoo.com'
WF_HOME_SERVICES_ADDON_SERVICES_API = 'kube-addonservices-service.service.intraiad1.devconsul.csnzoo.com'
WF_HOME_SERVICES_SERVICE_LOADER = 'kube-homeservices-service-loader.service.intradsm1.sdeconsul.csnzoo.com'
WF_CL_ENTITY_SERVICES_URL = 'kube-sdc-services.service.intraiad1.devconsul.csnzoo.com'
WF_SF_EVENTS_API_K8S = 'kube-sf-events-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_SF_REVIEWS_PROVIDER_K8S = 'kube-sf-reviews-provider.service.intradsm1.sdeconsul.csnzoo.com'
WF_SF_REVIEWS_INGESTION_K8S = 'kube-sf-reviews-ingestion.service.intradsm1.sdeconsul.csnzoo.com'
WF_CUSTOMER_PURCHASE_ORDER_API = 'kube-customer-purchase-order.service.intradsm1.sdeconsul.csnzoo.com'
WF_WFCC_STATUS_SERVICE = 'kube-wfcc-status.service.intraiad1.devconsul.csnzoo.com'
WF_LOYALTY_REWARDS_SERVICE = 'kube-loyalty-rewards.service.intraiad1.devconsul.csnzoo.com'
WF_WFCC_ACCOUNT_PAYMENT_SERVICE = 'kube-wfcc-account-payment.service.intraiad1.devconsul.csnzoo.com:31750'
WF_GIFT_CARDS_SERVICE = 'kube-giftcards-service.service.intradsm1.devconsul.csnzoo.com'
WF_PAYMENTS_STORES_PROCESSING = 'kube-payments-stores-processing.service.intradsm1.devconsul.csnzoo.com:31677'
WF_PAYMENTS_RETAIL_BFF = 'kube-payments-retail-bff.service.intradsm1.devconsul.csnzoo.com'
WF_B2B_CUSTOMER_PRO_DATA_SERVICE = 'kube-b2b-customer-pro-data-service.service.intradsm1.devconsul.csnzoo.com'
WF_B2B_ACCOUNT_SERVICE = 'kube-b2b-account-service.service.intraiad1.devconsul.csnzoo.com'
WF_BUSINESS_ACCOUNT_VERIFICATION_SERVICES = 'kube-business-account-verification-services.service.intradsm1.devconsul.csnzoo.com'
WF_NCT_ORDERS_SERVICE_URL = 'https://kube-nct-orders-api.service.intradsm1.sdeconsul.csnzoo.com/graphql'
WF_USER_PROFILE_SERVICE = 'kube-ph-user-profile.service.intradsm1.sdeconsul.csnzoo.com'
WF_FULFILLMENT_ATTRIBUTES_SERVICE = 'https://kube-fulfillment-attributes.service.intraiad1.devconsul.csnzoo.com'
WF_RETAIL_BARCODE_SERVICE = 'kube-retail-barcode.service.intradsm1.sdeconsul.csnzoo.com'
WF_ESL_GATEWAY_SERVICE = 'kube-retail-esl-gateway.service.intradsm1.sdeconsul.csnzoo.com'
WF_RETAIL_STORE_METADATA_SERVICE = 'kube-retail-store-metadata-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_RETAIL_CATALOG_TRANSLATION_API = 'kube-retail-catalog-translation-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_RETAIL_TRANSLATION_SERVICE = 'http://kube-retail-catalog-translation-api.service.intradsm1.devconsul.csnzoo.com'
WF_SUPPLIER_ACCOUNT_CREATION_SERVICE = 'kube-ph-sup-creation-svc.service.intradsm1.sdeconsul.csnzoo.com'
WF_SUPPLIER_PROFILE_SERVICE = 'kube-ph-sup-profile-svc.service.intradsm1.sdeconsul.csnzoo.com'
WF_PARTNER_ACCOUNT_SERVICE = 'kube-partner-account-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_SUPPLIER_SYNCHRONISER_SERVICE = 'kube-supplier-synchroniser-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_COI_SERVICE = 'kube-supplier-certificate-of-insurance.service.intradsm1.sdeconsul.csnzoo.com'
WF_MSSQL_QUERY_PARSER_SERVICE = 'kube-mssql-query-parser.service.intradsm1.sdeconsul.csnzoo.com'
WF_TRANSLATIONS_PLATFORM_URL = 'https://kube-translations-platform.service.intraiad1.devconsul.csnzoo.com'
WF_BRAND_CATALOG_RESOLVER_URL = 'https://kube-localization-brand-catalog-resolver.service.intraiad1.devconsul.csnzoo.com'
WF_LANGUAGE_DETECTOR_API_URL = 'https://kube-language-detector.service.intraiad1.devconsul.csnzoo.com'
WF_CL_GENERAL_ELECTRIC_MICROSERVICE = '{"default":"kube-general-electric-micro.service.intraiad1.consul.csnzoo.com", "deviad1":"kube-general-electric-micro.service.intraiad1.devconsul.csnzoo.com", "iad1":"http-service-dep.general-electric.svc", "dsm1":"http-service-dep.general-electric.svc", "pdx1":"http-service-dep.general-electric.svc"}'
WF_CL_SAMSUNG_MICROSERVICE = '{"default":"kube-samsung-micro.service.intraiad1.consul.csnzoo.com", "bo1":"samsung-integration-microservices-service.samsung.svc", "deviad1":"samsung-integration-microservices-service.samsung.svc", "iad1":"samsung-integration-microservices-service.samsung.svc", "dsm1":"samsung-integration-microservices-service.samsung.svc", "pdx1":"samsung-integration-microservices-service.samsung.svc"}'
WF_REVIEWS_SEARCH_K8S = 'kube-reviews-search-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_REGIONAL_VCD_VENDING_SERVICE = 'https://kube-regional-vcd-vending-service.service.intradsm1.devconsul.csnzoo.com'
WF_DYNAMIC_PHONE_NUMBER_SERVICE = 'https://kube-phone-number-generation.service.intradsm1.sdeconsul.csnzoo.com'
WF_SEARCH_AND_RECS_GATEWAY = 'kube-search-and-recs.service.intradsm1.devconsul.csnzoo.com'
WF_DRE_API = 'http://kube-se-routing-gql.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAYHOME_ORDER_SERVICE = 'kube-wayhome-order-service.service.intradsm1.devconsul.csnzoo.com'
WF_WAYHOME_JOB_SERVICE = 'kube-wayhome-job-service-java.service.intradsm1.sdeconsul.csnzoo.com'
WF_SALES_DISTRIBUTION_ROUTING_SERVICE = 'https://kube-sales360-distribution-routing-service.service.intradsm1.devconsul.csnzoo.com'
WF_CONSOLIDATED_DELIVERY_AGENT_API = 'kube-consolidated-delivery-agent-api.service.intraiad1.devconsul.csnzoo.com'
WF_PROFIT_SERVICE = 'http://kube-profit-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_DDT_ORCHESTRATOR_SERVICE = 'kube-ddt-orchestrator.service.intradsm1.sdeconsul.csnzoo.com'
WF_NCT_QUOTES_SERVICE_URL = 'https://kube-nct-quotes.service.intradsm1.sdeconsul.csnzoo.com/graphql'
WF_WFCC_ACCOUNT_SERVICE_URL = 'https://kube-wfcc-account.service.intradsm1.devconsul.csnzoo.com'
WF_WFCC_ACQUISITION_SERVICE_URL = 'https://istio-wfcc-acquisition.service.intradsm1.devconsul.csnzoo.com:32443'
WF_WFCC_CAPITAL_ONE_COMMUNICATION_SERVICE_URL = 'https://istio-wfcc-capitalone-communication-service.service.intradsm1.devconsul.csnzoo.com:32443'
WF_WFCC_PAYMENTS_EXECUTOR_SERVICE_URL = 'https://istio-payments-closed-loop-credit-executor.service.intradsm1.devconsul.csnzoo.com:32443'
WF_WFCC_STATUS_SERVICE_URL = 'https://kube-wfcc-status.service.intradsm1.devconsul.csnzoo.com'
WF_WFCC_LOYALTY_REWARDS_SERVICE_URL = 'https://kube-loyalty-rewards.service.intradsm1.devconsul.csnzoo.com'
WF_AR_VENDOR_SERVICE_URL = 'https://kube-finance-ar-vendor-service.service.intradsm1.sdeconsul.csnzoo.com/graphql'
WF_FINANCE_UNLEASH_TOGGLES_URL = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com'
WF_INCOMM_CRT_URL = '/wayfair/etc/priv/certs/spil.incomm.com.crt'
WF_NGX_PERIMETERX = 'http://127.0.10.1:8089'
WF_STAGING_FEATURE_TOGGLES = 'http://kube-nodeport-staging-feature-toggles.service.intraiad1.consul.csnzoo.com:30902/api/'
WF_INFRAPLATS_FEATURE_TOGGLES = 'http://kube-nodeport-infraplats-feature-toggles.service.intraiad1.consul.csnzoo.com:30904/api/'
WF_GENERAL_FEATURE_TOGGLES = 'http://kube-nodeport-general-feature-toggles.service.intraiad1.consul.csnzoo.com:30903/api/'
WF_SALES_QUOTE_API = 'https://kube-sales-quotes-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCAM_FRICTION_API = 'kube-scamwise-friction-service.service.intradsm1.devconsul.csnzoo.com'
WF_SCAM_BEATRIX_API = 'kube-scamwise-beatrix-decision-service.service.intradsm1.devconsul.csnzoo.com'
WF_EMAIL_RECSSVCLB = 'haproxy-recommendations.service.intradsm1.devconsul.csnzoo.com:8888'
WF_EMAILSEARCH = 'kube-email-service-search.service.intradsm1.sdeconsul.csnzoo.com'
WF_EMAILSEARCH_V2 = 'kube-email-service-search.service.intradsm1.sdeconsul.csnzoo.com'
WF_HONEYCOMB_LB = 'honeycombgpu.service.intradsm1.devconsul.csnzoo.com:8000'
WF_KAFKA_C1_BROKERS = 'c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C1_BROKERS_IAD1 = 'c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C1_BROKERS_SDEPROD = 'c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C3_BROKERS = 'c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C13_BROKERS_SDEPROD = 'c13.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C14_BROKERS_SDEPROD = 'c14.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_AGGREGATE_BROKERS = '[ "c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKA_STREAMING_BROKERS_NEW = '[ "c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKARESTPROXYC1 = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC3 = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC13 = 'c13.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKA_C2_BROKERS = 'c22.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_GENERAL_LOCAL_BROKERS = '[ "c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKA_GENERAL_AGG_BROKERS = '[ "c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_MODEL_DIVERSITY_SORT = 'diversity-sort.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_ELVIS = 'elvis.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_ELVIS_B2B_NON_TRADE = 'elvis-b2b-non-trade.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_ELVIS_UK = 'elvis-uk.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_ELVIS_DE = 'elvis-de.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_ELVIS_CA = 'elvis-ca.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_RECNETV2_PERIGOLD = 'recnetv2-perigold.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_SOFA = 'sofa.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_SOFA_V4PT3 = 'sofa-v4pt3.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_SOFA_PDP = 'sofa-pdp.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_SOFA_V5 = 'sofa-v5.model.service.intradsm1.devconsul.csnzoo.com'
WF_MODEL_VISUAL_SEARCH_REC = 'visual-search-rec.model.service.intradsm1.devconsul.csnzoo.com'
WF_PROWEB_QAS_SERVICE = 'https://proweb-haproxy.service.intraiad1.devconsul.csnzoo.com:9456/proweb.wsdl'
WF_RABBITMQ = 'rabbitmqc1-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQ_RMQ_3_6 = 'rabbitmqc2-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQHT = 'rabbitmqc1-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQHT_RMQ_3_6 = 'rabbitmqc2-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQLB = 'rabbitmqc1-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQLB_RMQ_3_6 = 'rabbitmqc2-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:5672'
WF_RABBITMQMGMT = 'rabbitmqc1-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:15672'
WF_RABBITMQMGMT_RMQ_3_6 = 'rabbitmqc2-amqp-haproxy.service.intradsm1.devconsul.csnzoo.com:15672'
WF_RECOMMENDATION_SERVICE = 'kube-recommendation-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_RECSSVCLB = 'kube-recommend.service.intradsm1.consul.csnzoo.com:80'
WF_RECSSVC_ELASTIC = '{"ingress": "esingest-recsrv.service.intraiad1.devconsul.csnzoo.com:9200","client": "esclient-recsrv.service.intraiad1.devconsul.csnzoo.com:9200"}'
WF_SERVICESLB = 'leader-purest-haproxy.service.intradsm1.sdeconsul.csnzoo.com:8280'
WF_SCHEMA_REPO = 'haproxy-schema-repo.service.intradevbo1.consul.csnzoo.com:2876/schema-repo'
WF_SCHEMA_REGISTRY = 'kube-kafka-schema-c1.service.intradsm1.sdeconsul.csnzoo.com:80'
WF_SISLB = 'kube-search-info-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_SEARCH_INTERP_SVC_LB = 'kube-search-interpretation.service.intradsm1.sdeconsul.csnzoo.com'
WF_SOURCE_SQL = '[ "***********" ]'
WF_SPCS_HOST = 'https://kube-cost-service-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_SRC_RATES_API = 'https://kube-finance-ar-src-rates-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_TELEGRAF_SERVICE = 'general.metricslb.service.intradsm1.sdeconsul.csnzoo.com'
WF_TELEGRAF_SERVICE_STOREFRONT = 'storefront.metricslb.service.intradsm1.sdeconsul.csnzoo.com'
WF_TELEGRAF_SERVICE_MONITORING = 'monitoring.metricslb.service.intradsm1.sdeconsul.csnzoo.com'
WF_VISUALSEARCH = 'visual-search.service.intradsm1.devconsul.csnzoo.com:8811'
WF_WHISPERLB_KUBERNETES = 'kube-cerebro.service.intradsm1.sdeconsul.csnzoo.com'
WF_WHISPERLB = 'kube-cerebro.service.intradsm1.sdeconsul.csnzoo.com'
WF_ZOOKEEPER = '{ "server1": "zookeeperc1-dsm1-g1-1.c.wf-gcp-us-sde-support-prod.internal:2181", "server2": "zookeeperc1-dsm1-g2-1.c.wf-gcp-us-sde-support-prod.internal:2181", "server3": "zookeeperc1-dsm1-g3-1.c.wf-gcp-us-sde-support-prod.internal:2181" }'
WF_ZOOKEEPER_IAD1 = '{ "server1": "zookeeper-iad1-g1-1.c.wf-gcp-us-support-dev.internal:2181", "server2": "zookeeper-iad1-g2-1.c.wf-gcp-us-support-dev.internal:2181", "server3": "zookeeper-iad1-g3-1.c.wf-gcp-us-support-dev.internal:2181" }'
WF_ZOOKEEPER_BO1 = '{ "server1": "zookeeperc1n1.dev.bo1.csnzoo.com:2181", "server2": "zookeeperc1n2.dev.bo1.csnzoo.com:2181", "server3": "zookeeperc1n3.dev.bo1.csnzoo.com:2181" }'
WF_ATLAS = 'kube-atlas.service.intradsm1.devconsul.csnzoo.com'
WF_KRUSE = 'kube-kruse.service.intradsm1.sdeconsul.csnzoo.com'
WF_KRUSE_DISPLAY = 'kube-kruse-display.service.intradsm1.sdeconsul.csnzoo.com'
WF_KRUSE_GRPC = 'kube-kruse-grpc.service.intradsm1.sdeconsul.csnzoo.com'
WF_SEO_JOBS = 'kube-seo-jobs.service.intradsm1.sdeconsul.csnzoo.com'
WF_SEO_METADATA_OVERRIDE_SERVICE = 'kube-seo-override-svc.service.intradevbo1.consul.csnzoo.com'
WF_SEO_MADLIBS_SERVICE = 'kube-seo-madlibs-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_SEO_PRERENDER_SERVICE = 'kube-seo-pre-render-svc.service.intradsm1.devconsul.csnzoo.com'
WF_SEO_KEYWORD_QUERY_MODEL = 'kube-seo-keyword-query-model.service.intradsm1.sdeconsul.csnzoo.com'
WF_WSPCLICKSVC = 'kube-wspclicksvc.service.intraiad1.devconsul.csnzoo.com'
WF_WSPSVC = 'kube-wspsvc.service.intraiad1.devconsul.csnzoo.com'
WF_CUSTSEGSVC = 'kube-segmentation.service.intradsm1.sdeconsul.csnzoo.com'
WF_YAMATO_REST = 'kube-yamato-rest.service.intraiad1.devconsul.csnzoo.com'
WF_YAMATO_FLEX_SUBS = 'kube-yamato-flex.service.intraiad1.devconsul.csnzoo.com'
WF_INTEL_GOVERNANCE = 'kube-intel-governance-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_EMAIL_RECSSVCLB_NEW = '10.228.228.200:8888'
WF_INVENTORY_INT = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_EMAILNOTIF = '10.228.136.201:8085'
WF_AEROSPIKE_ENTRY_NODES = '["aerospikec1.service.intradsm1.sdeconsul.csnzoo.com"]'
WF_KAFKA_SCE_GCP_BROKERS_IAD1_C13 = '[ "c13.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_LIBRA = 'kube-libra.service.intraiad1.devconsul.csnzoo.com'
WF_NETAPPSLB = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_PUSHNOTIFIER = 'kube-push-service-rest.service.intraiad1.devconsul.csnzoo.com:80'
WF_PUSHNOTIFIER_V2 = 'kube-push-service-rest.service.intraiad1.devconsul.csnzoo.com:80'
WF_PUSHSERVICE = 'kube-push-service-rest.service.intraiad1.devconsul.csnzoo.com:80'
WF_RAW_IMG_ENDPOINT_BO_INT = 'origin-raw.img.wfrcdn.com'
WF_RECSSVCLB_NEW = 'haproxy-recommendations.service.intradevbo1.consul.csnzoo.com:8888'
WF_SOLR_ORDERPRODUCT = 'devc1.solr.service.bo1.csnzoo.com:8180'
WF_HALO_SERVICEHANDLER_URL = 'https://kube-haloai-servicehandler.service.intradsm1.sdeconsul.csnzoo.com'
WF_HALO_CONVERSATION_MANAGER_URL = 'https://kube-haloai-conversation-manager.service.intradsm1.sdeconsul.csnzoo.com'
WF_PUSH_SERVICE = 'kube-push-service-rest.service.intraiad1.devconsul.csnzoo.com'
WF_SOLRLB = '10.228.130.201:8180'
WF_SOLRLB_MKT = '10.228.130.201:8180'
WF_CHATBOT = 'devchatbot.csnzoo.com'
WF_CATALOG_SERVICE_MERCHAPI = 'kube-catalog-service-merch-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_CATALOG_SERVICE_PRODUCTCACHEAPI = 'kube-catalog-service-product-cache-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_PEGASUS_SERVICE_URL = 'http://kube-pegasus.service.intraiad1.devconsul.csnzoo.com'
WF_WMS_INVENTORY_API_URL = 'kube-wms-inventory-api.service.intradevbo1.consul.csnzoo.com'
WF_FINANCE_SERVICE_K8_URL = 'https://kube-finance-python.service.intradsm1.sdeconsul.csnzoo.com'
WF_RETURNS_CREATION_API_URL = 'http://kube-returns-creation-api.service.intradevbo1.consul.csnzoo.com/'
WF_OS_REQUEST_CANCELLATION_URL = 'kube-request-cancellation.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_SHIP_ADDRESS_CHANGE_URL = 'kube-ship-address-change-eligibility.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_CANCELLATION_ELIGIBILITY_URL = 'kube-cancellation-eligibility.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_DELIVERY_MODIFICATION_SERVICE_URL = 'kube-delivery-modification-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_UPDATE_SHIPPING_ADDRESS_URL = 'kube-update-shipping-address.service.intradsm1.sdeconsul.csnzoo.com'
WF_OS_RETURNS_DATA_HOOVER_URL = 'kube-return-replacement-data-hoover.service.intradevbo1.consul.csnzoo.com'
WF_OS_REROUTES_URL = 'kube-os-reroutes.service.intradsm1.sdeconsul.csnzoo.com'
WF_OPP_PRICING_GUID = 'ce355e82-e9ca-46d6-b151-cbbaa62778c7'
WF_USER_AUTHORIZATION_SERVICE = 'https://kube-user-authorization-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_GCS_IMG_RAW_BUCKET = 'test.raw.img.wfrcdn.com'
WF_SQLRELAY_REMOTE_POOLS = '{"bt1,csn_storesitesid": "sqlrelayc1-bt1-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8110", "bt2,csn_storesitesid": "sqlrelayc1-bt2-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8111", "bt3,csn_storesitesid": "sqlrelayc1-bt3-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8112", "bt4,csn_storesitesid": "sqlrelayc1-bt4-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8113", "bt5,csn_storesitesid": "sqlrelayc1-bt5-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8114", "bt6,csn_storesitesid": "sqlrelayc1-bt6-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8115", "bt7,csn_storesitesid": "sqlrelayc1-bt7-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8116", "bt8,csn_storesitesid": "sqlrelayc1-bt8-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8117", "bt9,csn_storesitesid": "sqlrelayc1-bt9-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8118", "crgws_ro,csn_admin": "sqlrelayc1-crgws-ro-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:1433", "crgws_ro,csn_storesitesid": "sqlrelayc1-crgws-ro-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:1433", "ct1,csn_storesitesid": "sqlrelayc1-ct1-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8120", "ct2,csn_storesitesid": "sqlrelayc1-ct2-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8121", "ct3,csn_storesitesid": "sqlrelayc1-ct3-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8122", "ct4,csn_storesitesid": "sqlrelayc1-ct4-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8123", "ct5,csn_storesitesid": "sqlrelayc1-ct5-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8124", "ct6,csn_storesitesid": "sqlrelayc1-ct6-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8125", "ct7,csn_storesitesid": "sqlrelayc1-ct7-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8130", "ct8,csn_storesitesid": "sqlrelayc1-ct8-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8127", "ct9,csn_storesitesid": "sqlrelayc1-ct9-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8128", "hj,csn_admin": "sqlrelayc1-hj-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8190", "hj,csn_storesitesid": "sqlrelayc1-hj-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8191", "hjro,csn_admin": "sqlrelayc1-hjro-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8192", "hjro,csn_storesitesid": "sqlrelayc1-hjro-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8193", "ot,csn_admin_castlegate": "sqlrelayc1-ot-csn-admin-castlegate.service.intradsm1.sdeconsul.csnzoo.com:8134", "ot,csn_admin_finance": "sqlrelayc1-ot-csn-admin-finance.service.intradsm1.sdeconsul.csnzoo.com:8135", "ot,csn_admin_intranet": "sqlrelayc1-ot-csn-admin-intranet.service.intradsm1.sdeconsul.csnzoo.com:8136", "ot,csn_admin_order_management": "sqlrelayc1-ot-csn-admin-order-management.service.intradsm1.sdeconsul.csnzoo.com:8137", "ot,csn_admin_order_processing": "sqlrelayc1-ot-csn-admin-order-processing.service.intradsm1.sdeconsul.csnzoo.com:8138", "ot,csn_admin_partners": "sqlrelayc1-ot-csn-admin-partners.service.intradsm1.sdeconsul.csnzoo.com:8139", "ot,csn_admin_product_availability": "sqlrelayc1-ot-csn-admin-product-availability.service.intradsm1.sdeconsul.csnzoo.com:8140", "ot,csn_admin": "sqlrelayc1-ot-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8133", "ot,csn_admin_transportation_agent_extranet": "sqlrelayc1-ot-csn-admin-transportation-agent-extranet.service.intradsm1.sdeconsul.csnzoo.com:8142", "ot,csn_admin_transportation_jobs": "sqlrelayc1-ot-csn-admin-transportation-jobs.service.intradsm1.sdeconsul.csnzoo.com:8143", "ot,csn_admin_transportation": "sqlrelayc1-ot-csn-admin-transportation.service.intradsm1.sdeconsul.csnzoo.com:8141", "ot,csn_admin_warehouse": "sqlrelayc1-ot-csn-admin-warehouse.service.intradsm1.sdeconsul.csnzoo.com:8145", "ot,csn_my_account": "sqlrelayc1-ot-csn-my-account.service.intradsm1.sdeconsul.csnzoo.com:8146", "ot,csn_sales_engineering": "sqlrelayc1-ot-csn-sales-engineering.service.intradsm1.sdeconsul.csnzoo.com:8147", "ot,csn_storesitesid": "sqlrelayc1-ot-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8148", "ot,csn_supplier_extranet": "sqlrelayc1-ot-csn-supplier-extranet.service.intradsm1.sdeconsul.csnzoo.com:8149", "otro,csn_admin_intranet": "sqlrelayc1-otro-csn-admin-intranet.service.intradsm1.sdeconsul.csnzoo.com:8153", "otro,csn_admin_castlegate": "sqlrelayc1-otro-csn-admin-castlegate.service.intradsm1.sdeconsul.csnzoo.com:8151", "otro,csn_admin_finance": "sqlrelayc1-otro-csn-admin-finance.service.intradsm1.sdeconsul.csnzoo.com:8152", "otro,csn_admin_order_management": "sqlrelayc1-otro-csn-admin-order-management.service.intradsm1.sdeconsul.csnzoo.com:8154", "otro,csn_admin_order_processing": "sqlrelayc1-otro-csn-admin-order-processing.service.intradsm1.sdeconsul.csnzoo.com:8155", "otro,csn_admin_partners": "sqlrelayc1-otro-csn-admin-partners.service.intradsm1.sdeconsul.csnzoo.com:8156", "otro,csn_admin_transportation_agent_extranet": "sqlrelayc1-otro-csn-admin-transportation-agent-extranet.service.intradsm1.sdeconsul.csnzoo.com:8159", "otro,csn_admin_transportation_jobs": "sqlrelayc1-otro-csn-admin-transportation-jobs.service.intradsm1.sdeconsul.csnzoo.com:8160", "otro,csn_admin": "sqlrelayc1-otro-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8150", "otro,csn_admin_transportation": "sqlrelayc1-otro-csn-admin-transportation.service.intradsm1.sdeconsul.csnzoo.com:8158", "otro,csn_admin_warehouse": "sqlrelayc1-otro-csn-admin-warehouse.service.intradsm1.sdeconsul.csnzoo.com:8162", "otro,csn_my_account": "sqlrelayc1-otro-csn-my-account.service.intradsm1.sdeconsul.csnzoo.com:8163", "otro,csn_sales_engineering": "sqlrelayc1-otro-csn-sales-engineering.service.intradsm1.sdeconsul.csnzoo.com:8164", "otro,csn_storesitesid": "sqlrelayc1-otro-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8165", "otro,csn_supplier_extranet": "sqlrelayc1-otro-csn-supplier-extranet.service.intradsm1.sdeconsul.csnzoo.com:8166", "pt,csn_storesitesid": "sqlrelayc1-pt-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8167", "ptrw,csn_storesitesid": "sqlrelayc1-ptrw-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8170", "ptrw,csn_admin": "sqlrelayc1-ptrw-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8168", "ptrw,emailwsid": "sqlrelayc1-ptrw-emailwsid.service.intradsm1.sdeconsul.csnzoo.com:8171", "ptrw,csn_extranet": "sqlrelayc1-ptrw-csn-extranet.service.intradsm1.sdeconsul.csnzoo.com:8169", "tkn1,csn_storesitesid": "sqlrelayc1-tkn1-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8172", "tkn2,csn_storesitesid": "sqlrelayc1-tkn2-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8173", "tkn3,csn_storesitesid": "sqlrelayc1-tkn3-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8174", "tkn4,csn_storesitesid": "sqlrelayc1-tkn4-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8175", "tkn5,csn_storesitesid": "sqlrelayc1-tkn5-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8176", "tkn6,csn_storesitesid": "sqlrelayc1-tkn6-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8177", "tkn7,csn_storesitesid": "sqlrelayc1-tkn7-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8178", "tkn8,csn_storesitesid": "sqlrelayc1-tkn8-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8179", "tkn9,csn_storesitesid": "sqlrelayc1-tkn9-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8180", "wm,csn_admin": "sqlrelayc1-wm-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8194", "wm,csn_storesitesid": "sqlrelayc1-wm-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8195", "wmsro,csn_admin": "sqlrelayc1-wmsro-csn-admin.service.intradsm1.sdeconsul.csnzoo.com:8196", "wmsro,csn_storesitesid": "sqlrelayc1-wmsro-csn-storesitesid.service.intradsm1.sdeconsul.csnzoo.com:8197", "extranet,csn_extranet": "sqlrelayc1-extranet-csn-extranet.service.intradsm1.sdeconsul.csnzoo.com:8198"}'
WF_SQL_SERVER_EXTRANET = 'SQLEXTRANET.db.csnzoo.com'
WF_SUPPLIER_NOTIFICATION_SERVICE_HOST = 'kube-supplier-notification.service.intradsm1.sdeconsul.csnzoo.com'
WF_SUPPLIER_NOTIFICATION_GATEWAY_SERVICE_HOST = 'kube-supplier-notification-gateway.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAM_CLASSIFY_METADATA_URL = 'kube-mats.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAM_CLASSIFY_DISTRIBUTE_API_URL = 'kube-cmedia-distribute-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAM_INGEST_ASSET_DETAILS_API = 'kube-cmedia-ingestion-asset-details-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAM_INGEST_UPLOAD_API = 'kube-cmedia-ingestion-upload-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_WAM_INGEST_MEDIA_INGESTION_RETRIEVAL = 'kube-media-ingestion-retrieval.service.intradsm1.sdeconsul.csnzoo.com'
WF_DYNAMIC_IMAGE_SERVICE = 'kube-tc-dynamic-image-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCAM_FRICTIONS = 'kube-scamwise-friction-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_SSDP_REST_SERVICE = 'kube-ssdp-rest-service.service.intradsm1.sdeconsul.csnzoo.com'
WF_GAMBIT_PRIVATE_CONFIG_ENDPOINT = 'https://kube-gambit.service.intradsm1.sdeconsul.csnzoo.com'
WF_GAMBIT_API_ENDPOINT = 'https://kube-gambit-rest-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_DATADOG_SERVICE_METADATA_URL = 'kube-datadog-metadata-helper.service.intradsm1.sdeconsul.csnzoo.com'
WF_PRODUCT_CARTON_SERVICE = 'https://kube-product-carton.service.intradsm1.sdeconsul.csnzoo.com'
WF_PRODUCT_TYPE_SERVICE = 'https://kube-product-type.service.intraiad1.devconsul.csnzoo.com'
WF_WCP_TRACKING_HISTORY_API = 'kube-wgcp-tracking-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_BIDSIFTSVCLB = 'haproxy-bidsift.service.intradevbo1.consul.csnzoo.com:8094'
WF_CATALOG_SERVICE_INTEGRATIONAPI = 'kube-catalog-service-integration.service.intradsm1.sdeconsul.csnzoo.com'
WF_COST_SERVICE_URL = 'https://kube-cost-service-api.service.intraiad1.devconsul.csnzoo.com'
WF_DISPLAY_ADS_BID_GUIDANCE = 'kube-advertising-bid-guidance.service.intradsm1.sdeconsul.csnzoo.com'
WF_EMAILNOTIF_V2 = 'kube-email-service-rest.service.intradsm1.sdeconsul.csnzoo.com:80'
WF_ERP_VALIDATION_HOST = 'https://kube-erp-integration-validation.service.intradevbo1.consul.csnzoo.com'
WF_FINANCE_API_URL = 'https://kube-finance-api.service.intradsm1.sdeconsul.csnzoo.com'
WF_HALOAI_CONVERSATION_MANAGER_URL = 'https://kube-haloai-conversation-manager.service.intradsm1.sdeconsul.csnzoo.com'
WF_HALOAI_INTENT_SERVICE_URL = 'http://kube-haloai-intentservice.service.intradevbo1.consul.csnzoo.com'
WF_INVENTORYLB = 'kube-scs-inventoryservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_KAFKA_C13_BROKERS = 'c13.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C13_STREAMING_BROKERS = '[ "c13.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKA_C16_BROKERS = 'c16.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C6_BROKERS = 'c6.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C7_BROKERS = 'c7.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C8_BROKERS = 'c8.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C14_BROKERS = 'c14.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C14_LOCAL_BROKERS = 'c14.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C15_AGG_BROKERS = 'c14.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_FRAUD_PRODUCER_CLUSTER_ID = '13'
WF_KAFKA_REPLICATION_BROKERS = '[ "c22.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKA_SUPPLY_CHAIN_BROKERS = '[ "c13.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092" ]'
WF_KAFKARESTPROXYC13_V6 = 'c131.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC2 = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKA_C22_BROKERS = 'c22.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C13_BROKERS_IAD1 = 'c13.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C13_STREAMING_BROKERS_IAD1 = '[ "c13.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_KAFKA_C16_BROKERS_IAD1 = 'c16.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C3_BROKERS_IAD1 = 'c1.kafka-broker.service.intradsm1.sdeconsul.csnzoo.com:9092'
WF_KAFKA_C6_BROKERS_IAD1 = 'c6.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C7_BROKERS_IAD1 = 'c7.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_C8_BROKERS_IAD1 = 'c8.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092'
WF_KAFKA_GENERAL_AGG_BROKERS_IAD1 = '[ "c14.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_KAFKA_GENERAL_LOCAL_BROKERS_IAD1 = '[ "c14.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_KAFKA_SUPPLY_CHAIN_BROKERS_IAD1 = '[ "c13.kafka-broker.service.intraiad1.devconsul.csnzoo.com:9092" ]'
WF_KAFKARESTPROXY_IAD1 = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC13_IAD1 = 'c13.kafkarest-proxy.service.intraiad1.devconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC13_V6_IAD1 = 'c131.kafkarest-proxy-haproxy.service.intraiad1.devconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC1_IAD1 = 'c1.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC14 = 'c14.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC14_V6 = 'c141.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC16 = 'c16.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_KAFKARESTPROXYC16_V6 = 'c161.kafkarest-proxy.service.intradsm1.sdeconsul.csnzoo.com:8082'
WF_LEAD_TIME_SERVICE = 'http://kube-scs-sde.service.intraiad1.devconsul.csnzoo.com'
WF_LITESHIP_INT = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIP_POST = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_LITESHIPLB = 'kube-scs-liteshipservice.service.intradsm1.sdeconsul.csnzoo.com'
WF_OIP_URL = 'http://kube-oip-core.service.intradevbo1.consul.csnzoo.com'
WF_ORDER_PRODUCT_KAFKA_REPLICATION_BROKERS = '[ "c2.kafka-broker.service.intradevbo1.consul.csnzoo.com:9092" ]'
WF_ORDER_PRODUCT_KAFKA_STREAMING_BROKERS = '[ "c2.kafka-broker.service.intradevbo1.consul.csnzoo.com:9092" ]'
WF_OS_CONFIRM_CANCELLATION_URL = 'kube-confirm-cancellation.service.intradevbo1.consul.csnzoo.com'
WF_OS_HOLDS_URL = 'kube-os-holds.service.intradevbo1.consul.csnzoo.com'
WF_OS_RETURN_SCAM_HOLDS_URL = 'kube-returns-holds.service.intraiad1.devconsul.csnzoo.com'
WF_PRICING_ALETHEIA_INTERNAL_SERVICE = 'kube-pricing-aletheia-internal.service.intradsm1.sdeconsul.csnzoo.com'
WF_PRICING_ALETHEIA_STOREFRONT_SERVICE = 'kube-pricing-aletheia-storefront.service.intradsm1.sdeconsul.csnzoo.com'
WF_PRICING_SERVICE_INTERNALAPI = 'kube-pricing-aletheia-internal.service.intraiad1.consul.csnzoo.com'
WF_SCRIBEWEB_SSL = 'kube-scribe-websocket.service.intradsm1.sdeconsul.csnzoo.com'
WF_SCS_PRODUCT_ENTITY_SERVICE_URL = 'http://kube-cl-product-services.service.intraiad1.devconsul.csnzoo.com'
WF_SOLR_CORE = 'skyloft_us'
WF_SOLR_EXTRANET_ZNODE = '{"c9": "/solr/solrcloudc9.dev.bo1", "c10": "/solr/solrcloudc10.dev.bo1", "c19": "/solr/solrcloudc19.dev.bo1", "c20": "/solr/solrcloudc20.dev.bo1"}'
WF_SOLR_ORDERPRODUCT_POOL = '{"c7": "127.0.10.7:8180", "c8": "127.0.10.8:8180"}'
WF_SOLR_ORDERPRODUCT_ZNODE = '{"c7": "/solr/solrcloudc7.dev.bo1", "c8": "/solr/solrcloudc8.dev.bo1", "c1": "/solr/solrcloudc1.dev.bo1", "c2": "/solr/solrcloudc2.dev.bo1", "gcpc7-iad1": "/solr/orderproductc7.devgcp.iad1", "gcpc8-iad1": "/solr/orderproductc8.devgcp.iad1", "gcpc1-iad1": "/solr/orderproductc1.devgcp.iad1", "gcpc2-iad1": "/solr/orderproductc2.devgcp.iad1"}'
WF_SOLR_PRODUCTSEARCH_POOL = '{"c3": "127.0.10.3:8180", "c4": "127.0.10.4:8180"}'
WF_SOLR_PRODUCTSEARCH_ZNODE = '{"c3": "/solr/solrcloudc3.dev.bo1", "c4": "/solr/solrcloudc4.dev.bo1", "c17": "/solr/solrcloudc17.dev.bo1", "c18": "/solr/solrcloudc18.dev.bo1", "gcp-iad1": "/solr/productsearch.devgcp.iad1"}'
WF_SOLR_SHOPYOURCATALOG_ZNODE = '{"c13": "/solr/solrcloudc13.dev.bo1", "c14": "/solr/solrcloudc14.dev.bo1"}'
WF_SOLR_SOLRZOOKEEPER = '{"bo1":["zookeeperc3n1.dev.bo1.csnzoo.com:2181","zookeeperc3n2.dev.bo1.csnzoo.com:2181","zookeeperc3n3.dev.bo1.csnzoo.com:2181"],"iad1":["zookeeperc3-iad1-g1-1.c.wf-gcp-us-support-dev.internal:2181","zookeeperc3-iad1-g2-1.c.wf-gcp-us-support-dev.internal:2181","zookeeperc3-iad1-g3-1.c.wf-gcp-us-support-dev.internal:2181"]}'
WF_SOLR_SQL = 'DEVSQLPRODUCTSEARCH.db.csnzoo.com'
WF_SOLR_SUPPLIERDAM_ZNODE = '{"c27": "/solr/solrcloudc27.dev.bo1", "c28": "/solr/solrcloudc28.dev.bo1"}'
WF_SOLR_TALENT_ZNODE = '{"c15": "/solr/solrcloudc15.dev.bo1", "c16": "/solr/solrcloudc16.dev.bo1"}'
WF_SOLR_TICKET_ZNODE = '{"c11": "/solr/solrcloudc11.dev.bo1", "c12": "/solr/solrcloudc12.dev.bo1"}'
WF_SOLRADMIN_SLAVE = 'c1.solr.service.intradevbo1.consul.csnzoo.com:8180'
WF_VERTICAPROXY = 'haproxy-vertica-proxy.service.intradevbo1.consul.csnzoo.com:8088'
WF_VIDEO_RAW_BUCKET = 'wf-prodmed-vidraw-dev'
WF_VIDEO_TRANSCODE_BUCKET = 'wf-prodmed-vidtrans-dev'
WF_VISUALSEARCH_GCP = 'visual-search.service.intraiad1.devconsul.csnzoo.com:8811'
WF_WHISPERLB_ROOM7 = 'kube-cerebro.service.intradsm1.sdeconsul.csnzoo.com'
WF_WMS_AUTOMATION_CONVEYABILITY_API = 'kube-wms-automation-conveyability.service.intradevbo1.consul.csnzoo.com'
WF_WMS_AUTOMATION_PLATFORM_API = 'kube-wms-automation-platform.service.intradevbo1.consul.csnzoo.com'
WF_WMS_FULFILLMENT_CENTER_CAPACITY_API = 'kube-wms-fulfillment-center-capacity-api.service.intradevbo1.consul.csnzoo.com'
WF_WMS_PRODUCT_RETRIEVAL_SERVICE = 'kube-wms-product-retrieval.service.intradevbo1.consul.csnzoo.com'
WF_WMS_ROUTING_SERVICE_API = 'kube-wms-routing-service.service.intradevbo1.consul.csnzoo.com'
WF_NGX_PAYMETRIC = 'http://127.0.10.1:8090'
