/**
 * Save component for shared sku
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {Button, IconV2 as Icon, Grid, Column} from '@wayfair/homebase-extranet';
import CurationSkuSavedInfoTooltip from './curation_sku_saved_info_tooltip';
import CurationSkuRejectedInfoTooltip from './curation_sku_rejected_info_tooltip';
import {faCircle, faCircleNotch} from '@fortawesome/free-solid-svg-icons';

const isSaved = savedAt => savedAt !== null;
const isRejected = rejectedAt => rejectedAt !== null;

const CurationSharedSkuSave = ({
  isKitsco,
  savedAt,
  savedBy,
  rejectedAt,
  rejectedBy,
  rejectedNote,
  onSave,
}) => {
  return (
    <div className="text_center">
      <Grid>
        <Column size={3}>
          <p>
            <Icon
              icon={isK<PERSON><PERSON> ? faCircle : faCircleNotch}
              className="text_large"
            />
            <Translation msgid="CurationTool.Kitsco" />
          </p>
        </Column>
        <Column size={4}>
          <div>
            <Button onClick={onSave} text>
              {isKitsco ? (
                <Translation msgid="CurationTool.RemoveFromKitsco" />
              ) : (
                <Translation msgid="CurationTool.SaveAsKitsco" />
              )}
            </Button>
            {isSaved(savedAt) && (
              <CurationSkuSavedInfoTooltip
                savedAt={savedAt}
                savedBy={savedBy}
              />
            )}
            {isRejected(rejectedAt) && (
              <CurationSkuRejectedInfoTooltip
                rejectedAt={rejectedAt}
                rejectedBy={rejectedBy}
                rejectedNote={rejectedNote}
              />
            )}
          </div>
        </Column>
      </Grid>
    </div>
  );
};

CurationSharedSkuSave.propTypes = {
  isKitsco: PropTypes.bool.isRequired,
  savedBy: PropTypes.string,
  savedAt: PropTypes.string,
  rejectedBy: PropTypes.string,
  rejectedAt: PropTypes.string,
  rejectedNote: PropTypes.string,
  onSave: PropTypes.func.isRequired,
};

CurationSharedSkuSave.defaultProps = {
  savedBy: null,
  savedAt: null,
  rejectedBy: null,
  rejectedAt: null,
  rejectedNote: null,
};

export default CurationSharedSkuSave;
