<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

class Reevaluation_Sku_Grouper {
  /**
   * Creates an array of related groups for reevaluation
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[]
   * @throws \InvalidArgumentException
   */
  public function create_groups(Batch $batch) : array {
    return $this->create_groups_kits_collections($batch);
  }

  /**
   * Creates a group for every sku
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[]
   */
  private function create_groups_simple_skus(Batch $batch) : array {
    $groups = [];
    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $sku
     */
    foreach ($batch->get_skus() as $sku) {
      $group = new Reevaluation_Sku_Group();
      $group->add_sku($sku);
      $groups[] = $group;
    }

    return $groups;
  }

  /**
   * Creates a list of connected groups (kits can have common components)
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[]
   */
  private function create_groups_kits(Batch $batch) : array {
    $groups = $this->create_groups_simple_skus($batch);

    // merge related groups (with kit components reused)
    return $this->merge_related_groups($groups);
  }

  /**
   * Creates a list of connected groups
   * if group A is related to B, it will merge B into A => AB
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[] $groups Reevaluate_Batch_Sku_Group[]
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[]
   */
  private function merge_related_groups(array $groups) : array {
    $result = [];

    foreach ($groups as $group) {
      $new_result = [];
      foreach ($result as $existing) {
        if ($this->are_kits_related($group, $existing)) {
          $this->merge_group($group, $existing);
        } else {
          $new_result[] = $existing;
        }
      }
      $new_result[] = $group;
      $result       = $new_result;
    }

    return $result;
  }

  /**
   * Puts all the skus from one group to the target group
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $result       Reevaluate_Batch_Sku_Group
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $group_to_add Reevaluate_Batch_Sku_Group to merge on result
   *
   * @return void
   */
  private function merge_group(Reevaluation_Sku_Group $result, Reevaluation_Sku_Group $group_to_add) {
    foreach ($group_to_add->get_skus() as $sku) {
      $result->add_sku($sku);
    }
  }

  /**
   * Check if kits are related (they have connected kits)
   * if a kit component belongs to two kits, they are connected (they have a common parent)
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $group1 Reevaluate_Batch_Sku_Group 1
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $group2 Reevaluate_Batch_Sku_Group 2
   *
   * @return bool
   */
  private function are_kits_related(Reevaluation_Sku_Group $group1, Reevaluation_Sku_Group $group2) : bool {
    return count(array_intersect($group1->get_parents(), $group2->get_parents())) > 0;
  }

  /**
   * Creates groups of related kits that belong to the same collection
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group[]
   */
  private function create_groups_kits_collections(Batch $batch) : array {
    $groups                        = $this->create_groups_kits($batch);
    $groups_eligible_for_exclusion = [];

    // exclude the ones that don't have the same collection across group
    foreach ($groups as $group) {
      $last_collection_id    = null;
      $different_collections = false;
      foreach ($group->get_skus() as $sku) {
        if (!empty($last_collection_id) && $sku->get_collection_id() !== $last_collection_id) {
          $different_collections = true;

          break;
        }

        $last_collection_id = $sku->get_collection_id();
      }

      if (!$different_collections) {
        $groups_eligible_for_exclusion[] = $group;
      }
    }

    return $groups_eligible_for_exclusion;
  }
}
