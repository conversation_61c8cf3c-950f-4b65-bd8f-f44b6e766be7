<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;

class Curation_Automation_Item_DAO implements Curation_Automation_Item_Storage {
  use Logging_Trait;

  /**
   * @var ProductConnection
   */
  private ProductConnection $pdo;

  /**
   * @param ProductConnection              $pdo    the PDO
   * @param \WF\Shared\Logging\Logger|null $logger Logger
   */
  public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null) {
    $this->pdo    = $pdo;
    $this->logger = $logger;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item $item Curation Automation Item
   *
   * @return void
   */
  public function create(Curation_Automation_Item $item): void {
    $this->info(
        sprintf('Saving curation automation item for batchId "%s"', $item->get_curation_batch_id()),
        [
            'curation_batch_id'    => $item->get_curation_batch_id(),
            'vi_sku'    => $item->get_vi_sku(),
            'curation_batch_automation_type_id'    => $item->get_curation_batch_automation_type_id()->value(),
            'ds_model_score'    => $item->get_ds_model_score(),
            'vs_predicted_style_id'    => $item->get_vs_predicted_style_id(),
            'vss_predicted_substyle_id'    => $item->get_vss_predicted_substyle_id(),
            'unwhitelabel'    => $item->get_unwhitelabel(),
        ]
    );

    $sql = '
        INSERT INTO csn_product.dbo.tblCurationAutomationItem
        (
            CurationBatchID,
            ViSKU,
            CurationBatchAutomationTypeID,
            DSModelScore,
            VsPredictedStyleID,
            VssPredictedSubStyleID,
            Unwhitelabel
        )
        VALUES
        (
            :curation_batch_id,
            :vi_sku,
            :curation_batch_automation_type_id,
            :ds_model_score,
            :vs_predicted_style_id,
            :vss_predicted_substyle_id,
            :unwhitelabel
        )
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('curation_batch_id', $item->get_curation_batch_id(), PDO::PARAM_INT);
    $statement->bindValue('vi_sku', $item->get_vi_sku(), PDO::PARAM_STR);
    $statement->bindValue('curation_batch_automation_type_id', $item->get_curation_batch_automation_type_id()->value(), PDO::PARAM_INT);
    $statement->bindValue('ds_model_score', $item->get_ds_model_score(), PDO::PARAM_INT);
    $statement->bindValue('vs_predicted_style_id', $item->get_vs_predicted_style_id(), PDO::PARAM_INT);
    $statement->bindValue('vss_predicted_substyle_id', $item->get_vss_predicted_substyle_id(), PDO::PARAM_INT);
    $statement->bindValue('unwhitelabel', $item->get_unwhitelabel(), PDO::PARAM_BOOL);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation automation item');
      $this->error(
          sprintf('Failed to save curation decision for batchId "%s"', $item->get_curation_batch_id()),
          [
            'curation_batch_id'    => $item->get_curation_batch_id(),
            'vi_sku'    => $item->get_vi_sku(),
            'curation_batch_automation_type_id'    => $item->get_curation_batch_automation_type_id(),
            'ds_model_score'    => $item->get_ds_model_score(),
            'vs_predicted_style_id'    => $item->get_vs_predicted_style_id(),
            'vss_predicted_substyle_id'    => $item->get_vss_predicted_substyle_id(),
            'unwhitelabel'    => $item->get_unwhitelabel(),
            'exception' => $exception,
          ]
      );
      throw $exception;
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel $item Curation Automation Item
   *
   * @return void
   */
  public function unwhitelabel(Curation_Automation_Unwhitelabel $item): void {
    $this->info(
        sprintf('Saving curation automation item for batchId "%s"', $item->get_batch_id()),
        [
          'curation_batch_id'    => $item->get_batch_id(),
          'unwhitelabel'    => $item->get_unwhitelabel(),
        ]
    );

    $skus = $item->get_skus();

    $sql = '
        UPDATE csn_product.dbo.tblCurationAutomationItem
        SET Unwhitelabel = :unwhitelabel
        WHERE
          CurationBatchID = :curation_batch_id
          AND ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('curation_batch_id', $item->get_batch_id(), PDO::PARAM_INT);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
    $statement->bindValue('unwhitelabel', $item->get_unwhitelabel(), PDO::PARAM_BOOL);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation automation item');
      $this->error(
          sprintf('Failed to save curation decision for batchId "%s"', $item->get_batch_id()),
          [
            'curation_batch_id'    => $item->get_batch_id(),
            'unwhitelabel'    => $item->get_unwhitelabel(),
            'exception' => $exception,
          ]
      );
      throw $exception;
    }
  }
}
