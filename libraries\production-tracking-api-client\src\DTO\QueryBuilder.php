<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\DTO;

use WF\Shared\Models\Production_Tracking\V2\Stage_Model;

final class QueryBuilder
{

    private const UPDATE_PRODUCT_TRACKING_STATUS_QUERY = '
    mutation update{
        productTracking {
          update(
            projectEntry: [%s]
          )
        }
      }
  ';

    private const UPDATE_PRODUCT_TRACKING_STATUS_VARIABLES = '
    {
      projectId: %d,
      stage: "%s"
    }
  ';

    private function __construct()
    {
    }

    /**
     * @param int[] $projectIds Project IDs
     *
     * @return string
     */
    public static function buildUpdateProductionTrackingStatusQuery(array $projectIds): string
    {
        $variables = [];

        foreach ($projectIds as $project_id) {
            $variables[] = sprintf(
                self::UPDATE_PRODUCT_TRACKING_STATUS_VARIABLES,
                $project_id,
                Stage_Model::E5_Extranet_White_Label
            );
        }

        return sprintf(self::UPDATE_PRODUCT_TRACKING_STATUS_QUERY, implode(',', $variables));
    }
}
