<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Employee;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer;

class Completion_QA_Notify_Curation_Updated_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\ProductManagement\Mailer\Mailer
     */
    private $mailer;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service
     */
    private $employeeService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service
     */
    private $qaBatchSkuService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated
     */
    private $subject;

    /**
     * @var string
     */
    private const BASE_URL = 'https://xyz.com';

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->mailer = $this->prophesize(Mailer::class);
        $this->employeeService = $this->prophesize(Completion_Employee_Service::class);
        $this->qaBatchSkuService = $this->prophesize(Completion_QA_Batch_SKU_Service::class);

        $this->subject = new Completion_QA_Notify_Curation_Updated(
            $this->mailer->reveal(),
            $this->employeeService->reveal(),
            $this->qaBatchSkuService->reveal(),
            self::BASE_URL,
        );
    }


    /**
     * @test
     *
     * @return void
     */
    public function it_sends_email_to_assigned_employee()
    {
        $batchId = 1;
        $assignedEmployeeID = 2;
        $assignedEmployeeEmail = '<EMAIL>';
        $assignedEmployeeName = 'Assigned Employee';

        $qaEmployeeID = 3;
        $qaEmployeeEmail = '<EMAIL>';
        $qaEmployeeName = 'QA Employee';

        $assignedEmployee = $this->get_completion_employee($assignedEmployeeEmail, $assignedEmployeeName);
        $qaEmployee = $this->get_completion_employee($qaEmployeeEmail, $qaEmployeeName);

        $this->employeeService->get($qaEmployeeID)->willReturn($qaEmployee);
        $this->employeeService->get($assignedEmployeeID)->willReturn($assignedEmployee)->shouldBeCalled();

        $this->qaBatchSkuService->getRejectedSkus($batchId)->willReturn([]);

        /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $mailMessage Mail Message */
        $mailMessage = $this->prophesize(Mail_Message::class);
        $this->mailer->create_message()->willReturn($mailMessage->reveal());

        $mailMessage->set_subject(Argument::any())->willReturn($mailMessage);
        $mailMessage->set_sender(Argument::cetera())->willReturn($mailMessage);
        $mailMessage->set_html_body(Argument::any())->willReturn($mailMessage);

        // recepient is the assigned employee
        $mailMessage->add_recipient($assignedEmployeeEmail, $assignedEmployeeName)->willReturn($mailMessage);
        if (true) {
            $result = $this->subject->sendMailForBulkData($batchId, [], [], [], [], $assignedEmployeeID, $qaEmployeeID);
            $this->assertNull($result);
        } else {
            $result = $this->subject->send($batchId, [], [], [], [], 'ADML2956', $assignedEmployeeID, $qaEmployeeID);
            $this->assertNull($result);
        }
    }

    /**
     * @param string $email Email
     * @param string $name  Name
     *
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Employee
     */
    private function get_completion_employee(string $email, string $name): Completion_Employee
    {
        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Employee $employee */
        $employee = $this->prophesize(Completion_Employee::class);
        $employee->getEmail()->willReturn($email);
        $employee->getName()->willReturn($name);

        return $employee->reveal();
    }
}
