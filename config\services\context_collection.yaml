parameters:

services:
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    WF\Shared\Curation\Api\Product_Context_Collection\Product_Context_Collection_Api_Client:
        arguments:
            - '@WF\Shared\Logging\Logger'
            - '%wf_env%'

    WF\Shared\Curation\Api\Api_Product_Context_Collection:  '@WF\Shared\Curation\Api\Product_Context_Collection\Product_Context_Collection_Api_Client'
