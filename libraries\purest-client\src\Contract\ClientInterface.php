<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Contract;

use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

interface ClientInterface
{
    /**
     * @param RequestInterface $request See WF\BrandWorkflows\PuREST\Infrastructure\RequestFactory
     *
     * @return ResponseInterface
     *
     * @throws \WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTException
     */
    public function request(RequestInterface $request): ResponseInterface;
}
