<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Predicted_Winner;

use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\Api_Predicted_Winner;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Environment;

class Predicted_Winner_Api_Client implements Api_Predicted_Winner {

  private const BASE_URL_DEV  = 'kube-predicted-winners.service.intradsm1.sdeconsul.csnzoo.com/predicted-winners';
  private const BASE_URL_PROD = 'kube-predicted-winners.service.intraiad1.consul.csnzoo.com/predicted-winners';

  private const REQUEST_BATCH_SIZE = 1000;
  /**
   * @var string
   */
  private $base_url;

  /**
   * @var LoggerInterface
   */
  private LoggerInterface $logger;

  /**
   * Predicted_Winner_Api_Client constructor.
   *
   * @param LoggerInterface $logger      Logger
   * @param string          $environment Environment
   */
  public function __construct(LoggerInterface $logger, string $environment) {
    $this->base_url = $environment === Environment::DEVELOPMENT ? self::BASE_URL_DEV : self::BASE_URL_PROD;
    $this->logger   = $logger;
  }

  /**
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  public function get_predicted_winners() : array {
    return $this->make_request('/');
  }

  /**
   * @param array $skus list of SKUS to check if they are predicted winners or not
   *
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  public function find_predicted_winners(array $skus) : array {
    $chunks = array_chunk($skus, self::REQUEST_BATCH_SIZE);
    $result = [];
    foreach ($chunks as $chunk) {
      $winners = $this->make_request('/find', ['skus' => array_values($chunk)]);
      $result = array_merge($result, $winners);
    }
    return $result;
  }

  // TODO refactor methods below to be shared across all (or most) of api clients

  /**
   * @param string $path    The request endpoint
   * @param array  $payload Optional payload to send
   *
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  private function make_request(string $path, ?array $payload = null) : array {
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
    ];

    $curl_resource = curl_init();

    if ($curl_resource === false) { /** @phpstan-ignore-line */
      $this->logger->error('Failed curl initialization');
      throw new API_Request_Exception('Failed curl initialization');
    }

    $endpoint = $this->base_url . $path;
    curl_setopt($curl_resource, CURLOPT_URL, $endpoint);
    curl_setopt($curl_resource, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl_resource, CURLOPT_HEADER, 0);
    curl_setopt($curl_resource, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl_resource, CURLOPT_FOLLOWLOCATION, 1);

    $json_payload = null;
    if ($payload !== null) {
      $json_payload = \json_encode($payload);
      curl_setopt($curl_resource, CURLOPT_POSTFIELDS, $json_payload);
    }

    $this->logger->info(sprintf('GET "%s"', $endpoint));
    if (!empty($json_payload)) {
      $this->logger->info(sprintf('BODY: %s', $json_payload));
    }
    $response = curl_exec($curl_resource);
    $this->logger->info(sprintf('RESPONSE: %s', $response));
    curl_close($curl_resource);

    if (\is_string($response)) {
        return $this->decode_response($response);
    } else {
        $error_message = sprintf('Error: %s', curl_error($curl_resource));
        $this->logger->error($error_message);
        throw new API_Request_Exception($error_message);
    }
  }

  /**
   * @param string $response The HTTP response body
   *
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  private function decode_response(string $response) : array {
    $predicted_winners = [];

    $decoded_response = \json_decode($response);
    if (is_null($decoded_response)) {
      $this->logger->error('Response data is an invalid JSON: ' . $response);

      return $predicted_winners;
    }

    if (isset($decoded_response->error)) {
      $error_message = sprintf('Response error: %s - %s', $decoded_response->error, $decoded_response->message ?? '');
      $this->logger->error($error_message);
      throw new API_Request_Exception($error_message);
    }

    foreach ($decoded_response as $item) {
      $predicted_winners[$item->sku] = new Predicted_Winner_Info_SKU_DTO($item->sku, $item->isPredictedWinner, $item->totalOrders);
    }

    return $predicted_winners;
  }
}
