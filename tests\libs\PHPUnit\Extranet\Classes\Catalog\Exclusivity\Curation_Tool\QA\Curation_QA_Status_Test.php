<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Domain\Enum\AbstractEnumeration;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;

class Curation_QA_Status_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_final()
    {
        $rc = new ReflectionClass(Curation_QA_Status::class);

        $this->assertTrue($rc->isFinal());
    }

    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_enumeration()
    {
        $rc = new ReflectionClass(Curation_QA_Status::class);

        $this->assertTrue($rc->isSubclassOf(AbstractEnumeration::class));
    }

    /**
     * @test
     * @return void
     */
    public function it_creates_non_applicable_from_empty_value()
    {
        $status = Curation_QA_Status::create(null);
        $this->assertTrue($status->equals(Curation_QA_Status::not_applicable()));

        $status = Curation_QA_Status::create('');
        $this->assertTrue($status->equals(Curation_QA_Status::not_applicable()));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_does_not_allow_creation_with_invalid_value()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Value 999 is not supported QA Status');
        Curation_QA_Status::create(999);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_wraps_values_from_db_picklist()
    {
        $not_applicable = Curation_QA_Status::not_applicable()->value();
        $this->assertEquals(0, $not_applicable, 'Not applicable == 0');

        $pending = Curation_QA_Status::pending()->value();
        $this->assertEquals(1, $pending, 'Pending == 1');

        $approved = Curation_QA_Status::approved()->value();
        $this->assertEquals(2, $approved, 'Pending == 2');

        $rejected = Curation_QA_Status::rejected()->value();
        $this->assertEquals(3, $rejected, 'Rejected == 3');

        $mixed = Curation_QA_Status::mixed()->value();
        $this->assertEquals(4, $mixed, 'Mixed == 4');
    }
}
