<?php

declare (strict_types=1);

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool;

class Substyle_Data implements \JsonSerializable {

  /**
   * @var int|null
   */
  private $id;

  /**
   * @var int|null
   */
  private $styleId;

  /**
   * @var string
   */
  private $name;

  /**
   * Substyle_Data constructor.
   *
   * @param int    $id      ID
   * @param string $name    Name
   * @param int    $styleId Style ID
   */
  public function __construct(int $id = null, string $name = '', int $styleId = null) {
    $this->id      = $id;
    $this->name    = $name;
    $this->styleId = $styleId;
  }

  /**
   * @return int|null
   */
  public function value() {
    return $this->id;
  }

  /**
   * @return string
   */
  public function text() : string {
    return $this->name;
  }

  /**
   * @return int|null
   */
  public function styleId() {
    return $this->styleId;
  }

  public function j<PERSON>Serialize()
  {
    return [
      'value' => $this->value(),
      'text' => $this->text(),
      'styleId' => $this->styleId(),
    ];
  }
}
