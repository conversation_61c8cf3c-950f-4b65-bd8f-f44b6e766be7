<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\Infrastructure\Connection\Graph;

use App\Infrastructure\Connection\Graphql\GraphException;
use PHPUnit\Framework\TestCase;
use function implode;

class Curation_Graph_Exception_Test extends TestCase
{
    public function testGraphExceptionWithErrors()
    {
        $errors = ['Error 1', 'Error 2'];

        $exception = new GraphException($errors);

        $this->assertSame($errors, $exception->errors);
        $this->assertSame(implode(', ', $errors), $exception->getMessage());
    }

    public function testGraphExceptionWithoutErrors()
    {
        $exception = new GraphException();

        $this->assertSame([], $exception->errors);
        $this->assertSame('', $exception->getMessage());
    }
}
