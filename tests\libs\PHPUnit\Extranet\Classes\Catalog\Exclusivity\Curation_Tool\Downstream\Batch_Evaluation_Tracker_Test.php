<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Batch_Evaluation_Tracker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage\Batch_Evaluation_Tracker_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage\Batch_Evaluation_Tracker_Postgres_DAO;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result;

class Batch_Evaluation_Tracker_Test extends TestCase
{
    use ProphecyTrait;

    private const DATE_FORMAT = 'Y-m-d H:i:s';
    /**
     * @var Batch_Evaluation_Tracker_DAO
     */
    private $dao;

    /**
     * @var Batch_Evaluation_Tracker_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Batch_Evaluation_Tracker_DAO::class);
        $this->dao_psql = $this->prophesize(Batch_Evaluation_Tracker_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Batch_Evaluation_Tracker(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_save_batch_evaluation_result_update_feature_toggle_off(): void
    {
        $dataTime = $this->get_date_time();
        $db_data = $this->get_evaluation_records();
        $data = $this->get_batch_evaluation_results('SKU1');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_evaluation_records(['SKU1'])->willReturn($db_data);
        $this->dao->update(1, $dataTime->format(self::DATE_FORMAT), null)->shouldBeCalled();
        $this->subject->save_batch_evaluation_result($data);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_save_batch_evaluation_result_insert_feature_toggle_off(): void
    {
        $dataTime = $this->get_date_time();
        $data = $this->get_batch_evaluation_results('SKU0');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_evaluation_records(['SKU0'])->willReturn([]);
        $this->dao->insert('SKU0', $dataTime->format(self::DATE_FORMAT), $dataTime->format(self::DATE_FORMAT), null)->shouldBeCalled();
        $this->subject->save_batch_evaluation_result($data);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_save_batch_evaluation_result_update_feature_toggle_on(): void
    {
        $dataTime = $this->get_date_time();
        $db_data = $this->get_evaluation_records();
        $data = $this->get_batch_evaluation_results('SKU1');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_evaluation_records(['SKU1'])->willReturn($db_data);
        $this->dao_psql->update(1, $dataTime->format(self::DATE_FORMAT), null)->shouldBeCalled();
        $this->subject->save_batch_evaluation_result($data);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_save_batch_evaluation_result_insert_feature_toggle_on(): void
    {
        $dataTime = $this->get_date_time();
        $data = $this->get_batch_evaluation_results('SKU0');
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_evaluation_records(['SKU0'])->willReturn([]);
        $this->dao_psql->insert('SKU0', $dataTime->format(self::DATE_FORMAT), $dataTime->format(self::DATE_FORMAT), null)->shouldBeCalled();
        $this->subject->save_batch_evaluation_result($data);
    }

    private function get_batch_evaluation_results(string $sku): Batch_Evaluation_Result
    {
        return new Batch_Evaluation_Result(
            [
                new Batched_SKU_Evaluation_Result(
                    $sku,
                    Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED,
                    1,
                    new DateTime()
                )
            ],
            Batch_Evaluation_Status::partial()
        );
    }

    private function get_evaluation_records(): array
    {
        $dataTime = $this->get_date_time();
        return [
            [
                'ID' => 1,
                'SKU' => 'SKU1',
                'FirstCheckAt' => $dataTime,
                'LastCheckAt' => $dataTime,
                'ReadyDateAt' => $dataTime
            ]
        ];
    }

    private function get_date_time(): DateTime
    {
        return new DateTime();
    }
}
