<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\WhiteLabel;

use App\Domain\Enum\AbstractEnumeration;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Shared\Classes\ProductManagement\WhiteLabel\White_Label_Batch_State;

class White_Label_Batch_State_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_final()
    {
        $rc = new ReflectionClass(White_Label_Batch_State::class);

        $this->assertTrue($rc->isFinal());
    }

    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_enumeration()
    {
        $rc = new ReflectionClass(White_Label_Batch_State::class);

        $this->assertTrue($rc->isSubclassOf(AbstractEnumeration::class));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_throws_with_impossible_values()
    {
        $this->expectException(InvalidArgumentException::class);

        White_Label_Batch_State::create(99);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_wraps_values_from_db_picklist()
    {
        $items = [
            1 => White_Label_Batch_State::created(),
            2 => White_Label_Batch_State::validation_errors(),
            3 => White_Label_Batch_State::collision_errors(),
            4 => White_Label_Batch_State::ready_for_wl(),
            5 => White_Label_Batch_State::sku_creation(),
            6 => White_Label_Batch_State::sku_creation_complete(),
            7 => White_Label_Batch_State::completed(),
        ];

        foreach ($items as $db => $option) {
            $this->assertEquals($db, $option->value(), $option->label() . ' == ' . $db);
        }
    }
}
