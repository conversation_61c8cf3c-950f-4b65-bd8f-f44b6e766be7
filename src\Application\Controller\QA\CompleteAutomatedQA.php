<?php

namespace App\Application\Controller\QA;

use App\Application\DTO\QARequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream as Downstream;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function count;
use function sprintf;

class CompleteAutomatedQA extends AbstractQAControllerAbstract
{
    /**
     * @param Completion_QA_Automated_Service $qa_service Completion service
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param QARequest $request
     * @param Downstream\Api\Partial_White_Label_Api_Processor $api_partial_wl_processor
     * @param Downstream\Batch_Data_Loader $batch_loader
     * @return JsonResponse
     * @throws \Throwable
     * @Route(path="/complete_automated_qa", methods={"GET"})
     */
    public function __invoke(
        Completion_QA_Automated_Service $qa_service,
        Completion_Batch_Data_Service $batch_data_service,
        QARequest $request,
        Downstream\Api\Partial_White_Label_Api_Processor $api_partial_wl_processor,
        Downstream\Batch_Data_Loader $batch_loader
    ): JsonResponse {
        $batch_id = $request->getBatchId();
        if ($batch_id === 0) {
            $this->error(
                'Failed to complete QA for batch, missing batch_id',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId()
                ]
            );

            return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
        try {
            // safety check
            if (!$this->is_expected_process_type(
                $batch_id,
                Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
                $batch_data_service
            )
            ) {
                $this->warning(
                    sprintf(
                        'Failed to complete QA for batch_id=%d. Unexpected batch process type',
                        $batch_id
                    ),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'Bad request! Cannot complete manual batch!'], JsonResponse::HTTP_BAD_REQUEST);
            }


            if (!$this->isExpectedBatchStatus($batch_id, [Batch::STATUS_AUTOMATED_QA_IN_PROGRESS, Batch::STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS], $batch_data_service)) {
                $this->warning(
                    sprintf(
                        'Failed to complete QA for batch_id=%d. Unexpected batch status',
                        $batch_id
                    ),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'Unexpected batch status! Try update page to see latest batch status!'], JsonResponse::HTTP_BAD_REQUEST);
            }

            $result = $qa_service->complete($batch_id, $this->getEmployeeId());

            $qa_service->unwhitelabelExcludedSkus($batch_id);

            if ($qa_service->isAllSkuApproved($batch_id)) {
                $this->info(
                    'All SKUs confirmed on QA step',
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId()
                    ]
                );

                if ($qa_service->hasSkusWithUpdated($batch_id)) {
                    $batch = $batch_loader->load_data($batch_id);
                    if (count($batch->get_skus()) == 0) {
                        $this->error(
                            sprintf('Downstream failed for BatchId Reason : Sku list can not be empty "%s"', $batch_id),
                            ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                        );
                    }
                    $result_1 = $api_partial_wl_processor->processPartialWhiteLabel($batch);
                    if ($result = true && $result_1 === 200) {
                        $result = true;
                    } else {
                        $result = false;
                    }
                    if ($result) {
                        $qa_service->markPartialWhiteLabelSentWLAt($batch_id);
                        $this->info(
                            'Partial white label request created',
                            [
                                'batch_id' => $batch_id,
                                'employee_id' => $this->getEmployeeId()
                            ]
                        );
                    }
                    $this->info(
                        "SKUs with 'updated' require manual intervention",
                        [
                            'batch_id' => $batch_id,
                            'employee_id' => $this->getEmployeeId()
                        ]
                    );
                }
            }

            return $this->json(['result' => $result]);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('An error occurred during QA complete step: %s', $exception->getMessage()),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception,
                ]
            );
            throw $exception;
        }
    }
}
