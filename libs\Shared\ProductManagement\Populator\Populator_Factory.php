<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\ProductManagement\Populator;

class Populator_Factory {
  /**
   * @var \WF\Shared\ProductManagement\Populator\Closure_Generator
   */
  private $generator;

  /**
   * @param \WF\Shared\ProductManagement\Populator\Closure_Generator $generator generator
   */
  public function __construct(Closure_Generator $generator) {
    $this->generator = $generator;
  }

  /**
   * @param string $fqcn fully qualified class name to generate a populator for
   *
   * @return \WF\Shared\ProductManagement\Populator\Populator_Interface
   */
  public function get_populator(string $fqcn) : Populator_Interface {
    $populator_closure = $this->generator->generate($fqcn);

    return new Closure_Populator($populator_closure);
  }
}
