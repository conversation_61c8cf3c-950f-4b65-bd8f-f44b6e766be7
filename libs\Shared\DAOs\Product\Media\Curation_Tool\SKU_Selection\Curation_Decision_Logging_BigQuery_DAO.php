<?php

namespace WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection;

use Google\Cloud\BigQuery\BigQueryClient;
use Exception;
use WF\Shared\Environment;
use Psr\Log\LoggerInterface;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Decision_Logging_Model;


class Curation_Decision_Logging_BigQuery_DAO
{
    private $bigQueryClient;
    private $datasetId;
    private $tableId;

    /**
     * @var string
     */
    private string $projectId;

      /**
       * @var LoggerInterface
       */
      private $logger;


    private const CLOUD_GBQ_PROJECT_ID_DEV= 'wf-gcp-us-btbb-bc-dev';
    private const CLOUD_GBQ_PROJECT_ID_PROD= 'wf-gcp-us-btbb-bc-prod';

    /**
    * Curation_Decision_Logging_BigQuery_DAO constructor
    *@param string $environment Environment
    *@param LoggerInterface $logger Logger
    */
    public function __construct(string $environment,
    LoggerInterface $logger)
    {
        $this->projectId = $environment === Environment::DEVELOPMENT ? self::CLOUD_GBQ_PROJECT_ID_DEV : self::CLOUD_GBQ_PROJECT_ID_PROD;
        $this->bigQueryClient = new BigQueryClient([
            'projectId' => $this->projectId,
        ]);
        $this->datasetId = 'curation';
        $this->tableId = 'tbl_sku_curation_process_eligibility';
        $this->logger = $logger;
    }

    public function insertDataBigQuery(): string
    {
        try{
            //Get the Dataset
            $dataset = $this->bigQueryClient->dataset($this->datasetId);
            //Get the Table
            $table = $dataset->table($this->tableId);

            //Data to be inserted

            $data=['SKU'=>'TESTDEVCURTOOL',
            'IsEligible'=>'1',
            'curationDomainApplication'=>'Brand-Workflows-Curation-Tool'];

            //Convert the data to be inserted to a row to json format
            $row = ['data' => $data];

            //Insert the data into the table
            $insertResponse = $table->insertRows([$row]);

            if($insertResponse->isSuccessful()){
                return "Data Inserted Successfully";
            }else{
                return "Data Insertion Failed";
            }
        }
        catch(Exception $e){
            return $e->getMessage();
        }
        return "Data Insertion Failed";

    }

    /**
     * Insert all SKUs to BigQuery table
     *
     * @param Curation_Decision_Logging_Model[] $curationDecisionLoggingModelList
     * @return void
     */
   public function insertAllSkusToBigQueryTable(array $curationDecisionLoggingModelList): void
    {try{
                if(empty($curationDecisionLoggingModelList)){
                    $this->logger->error('Curation Decision Logging Model is empty');
                    return;
                }
                else
                {
                    $this->logger->info('Inserting Curation Decision Logging Model into BigQuery');
                    $rows = [];
                    foreach ($curationDecisionLoggingModelList as $curationDecisionLoggingModel) {
                        $curationDecisionLoggingModel->setMetaData('Curation Decision Logging From Brand-Workflows-Curation-Tool');
                        $curationDecisionLoggingModel->setCreatedDate();
                        $rows[] = ['data' => $curationDecisionLoggingModel->toArray()];
                    }


                    //Get the Dataset
                    $dataset = $this->bigQueryClient->dataset($this->datasetId);
                    //Get the Table
                    $table = $dataset->table($this->tableId);

                    $this->logger->info('Data to be inserted into BigQuery: ' . json_encode($rows));
                    $chunks = array_chunk($rows, 999);
                    foreach ($chunks as $chunk => $batch) {
                        $insertResponse=$table->insertRows($batch);

                        if ($insertResponse->isSuccessful()) {
                            $this->logger->info('Data Inserted Successfully');
                        } else {
                            foreach ($insertResponse->failedRows() as $failedRow)
                            {
                                $this->logger->error('Data Insertion Failed: ' . json_encode($failedRow['errors']));
                            }

                        }
                    }
                }
    }catch(Exception $e){
            $this->logger->error('Error in insertAllSkusToBigQueryTable: ' . $e->getMessage());
            return;
        }


    }

}

