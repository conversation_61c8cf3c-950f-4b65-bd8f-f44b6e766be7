<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Curation\ExclusivityAssortment\Infrastructure\Helper\SQLBulkHelper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader_Storage;
use WF\Shared\Helpers\SQL;
use PDO;

class Assortment_Curation_Decision_Loader_DAO implements Assortment_Curation_Decision_Loader_Storage {
  private ProductConnection $pdo;

  /**
   * @param ProductConnection $pdo PDO
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

    /**
     * @param string[] $skus skus
     *
     * @return int[]
     */
  public function get_curation_price_tiers_for_skus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }
    // create temp table to keep same $skus order
    $column_map    = [
        'SKU' => SQL::nvarchar(8),
    ];
    $inserted_data = [];
    foreach ($skus as $sku) {
      $inserted_data[] = [
          'SKU' => $sku,
      ];
    }

    $sql = SQLBulkHelper::get_temp_table_json_sql($column_map, 'tmpCurationSkus');

    $sql .= '
                IF OBJECT_ID(\'tempdb.dbo.#tmpChildSKUs\') IS NOT NULL
                    DROP TABLE #tmpChildSKUs
                CREATE TABLE #tmpChildSKUs (
                    ChildSKU nvarchar(8)
                )
                
                INSERT INTO #tmpChildSKUs(ChildSKU)
                SELECT kitComposition.ChildSku as ChildSku
                FROM #tmpCurationSkus tmp
                     JOIN csn_product.dbo.vwExclusivityKitCompositionActive kitComposition WITH (NOLOCK)
                          ON kitComposition.ParentSKU = tmp.SKU
              
                SELECT
                    p.PrSKU AS sku,
                    pt.CptPriceTier AS price_tier
                FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                 INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
                 INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
                 CROSS APPLY (
                    SELECT ISNULL(CASE MAX(CptPriceTier) WHEN 4 THEN 4 ELSE MAX(CptPriceTier) + 1 END, 1) AS CptPriceTier
                    FROM csn_product.dbo.tblClassPriceTier cpt WITH (NOLOCK)
                    WHERE CptPriceTierCeiling < p.PrSalePrice AND CptClID = cl.ClID
                ) pt
                WHERE
                    EXISTS (SELECT 1 FROM #tmpCurationSkus WHERE SKU = p.PrSKU)
                    OR EXISTS (SELECT 1 FROM #tmpChildSKUs tmp WHERE p.PrSKU = tmp.ChildSku)';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($inserted_data), PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to load context data for skus');
    }

    return $statement->fetchAll(PDO::FETCH_KEY_PAIR);
  }

  /**
   * @param array $skus the SKUs to look data for
   *
   * @return array
   */
  public function get_child_skus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }

    $sql = 'SELECT DISTINCT ParentSku, ChildSku 
            FROM csn_product.dbo.vwExclusivityKitCompositionActive WITH (NOLOCK)
            WHERE ParentSku IN (' . $this->pdo->paramsForList(count($skus), 'parent_sku', SQL::nvarchar(8)) . ')';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValuesList(':parent_sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get products kit data');
    }

    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_COLUMN);
  }
}
