#!/bin/bash

# Credit to list-service team
# See https://github.com/wayfair-shared/list-service
# Run this as: sh ./local_setup.sh <username> <dev box url>

devurl="$1@$2"
# Copy over credentials to the project for the app to find

# Required to decrypt all secrets from folder `credentials` which have extension `aes128`
scp "${devurl}:/wayfair/etc/priv/dek/credentials.aes128"          "../docker/local/wayfair/etc/priv/dek/"

# Required
scp "${devurl}:/wayfair/etc/priv/credentials/csn_extranet.aes128"   "../docker/local/wayfair/etc/priv/credentials/"

# Used for API connection to NextGen WL API
scp "${devurl}:/wayfair/etc/priv/credentials/curation_tool_client_for_white_label_nextgen_api.aes128" "../docker/local/wayfair/etc/priv/credentials/"

# Used for API connection to NextGen WL API
scp "${devurl}:/wayfair/etc/priv/credentials/curation_tool_client_id_for_white_label_nextgen_api.aes128" "../docker/local/wayfair/etc/priv/credentials/"

# Used for API connection to TALENT API
scp "${devurl}:/wayfair/etc/priv/credentials/ah-brand-workflows-curation-tool-talent-reader.aes128" "../docker/local/wayfair/etc/priv/credentials/"

# Hardcoded secret to retrive in wayfair/php-core-db
scp "${devurl}:/wayfair/etc/priv/credentials/DBP_ST4.aes128"        "../docker/local/wayfair/etc/priv/credentials/"
