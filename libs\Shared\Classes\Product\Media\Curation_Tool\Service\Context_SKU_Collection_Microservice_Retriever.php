<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service;

use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\Api_Product_Context_Collection;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;

use function array_diff;
use function array_keys;
use function array_merge;
use function sprintf;

class Context_SKU_Collection_Microservice_Retriever implements Context_SKU_Collection_Retrieve_Interface {

  private Context_SKU_Service $context_sku_service;

  private Api_Product_Context_Collection $pcc_api;

  private LoggerInterface $logger;

  private bool $fetchContextXnIdFromMicroservice;

  /**
   * Context_SKU_Collection_Microservice_Retriever constructor.
   *
   * @param Context_SKU_Service $context_sku_service Context_SKU_Service
   * @param Api_Product_Context_Collection $pcc_api Api_Product_Context_Collection
   * @param LoggerInterface $logger Logger
   * @param bool $fetchContextXnIdFromMicroservice FT `curation_fetch_context_xn_id_from_microservice`
   */
  public function __construct(
    Context_SKU_Service $context_sku_service,
    Api_Product_Context_Collection $pcc_api,
    LoggerInterface $logger,
    bool $fetchContextXnIdFromMicroservice
  ) {
    $this->context_sku_service = $context_sku_service;
    $this->pcc_api = $pcc_api;
    $this->logger = $logger;
    $this->fetchContextXnIdFromMicroservice = $fetchContextXnIdFromMicroservice;
  }

  /**
   * @param string $sku SKU
   *
   * @return int|null
   */
  public function get_context_xnid(string $sku): ?int {
    if (empty($sku)) {
      $this->logger->warning('Empty SKU for context collection (XN) ID received.');

      return null;
    }

    return $this->get_context_xnid_map($sku)[$sku] ?? null;
  }

  /**
   * @param string ...$skus SKUs
   *
   * @return array Map of SKU to XN ID (Collection ID)
   */
  public function get_context_xnid_map(string ...$skus): array {
    if (empty($skus)) {
      $this->logger->warning('Empty SKUs list for context collection (XN) IDs received.');

      return [];
    }

    try {
      if ($this->fetchContextXnIdFromMicroservice) {
        $this->logger->info(
            sprintf(
                'Retrieving context collection (XN) IDs from PCC API'
            ),
            ['skus' => $skus]
        );

        $result = $this->pcc_api->find_product_context_collection(...$skus);
        $result_skus = array_keys($result);
        $diff_skus = array_diff($skus, $result_skus);
        if (empty($diff_skus)) {
          return $result;
        }

        $this->logger->warning(
            'Some SKUs are missing in PCC API, so retrieving context collection (XN) IDs from DB',
            [
                'skus'        => $skus,
                'missed_skus' => $diff_skus,
            ]
        );

        $missing_result = $this->context_sku_service->get_context_xnid_map(...$diff_skus);

        return array_merge($result, $missing_result);
      }
    } catch (API_Request_Exception $exception) {
      $this->logger->error(
          sprintf(
              'An Exception (%s) thrown during PCC API call, retrieving context collection (XN) IDs from DB',
              $exception->getMessage()
          ),
          [
              'skus' => $skus,
              'exception_message' => $exception->getMessage(),
              'exception_trace' => $exception->getTraceAsString(),
          ]
      );
    }

    $this->logger->info(
        sprintf(
            'Retrieving context collection (XN) IDs from DB'
        ),
        ['skus' => $skus]
    );

    return $this->context_sku_service->get_context_xnid_map(...$skus);
  }
}
