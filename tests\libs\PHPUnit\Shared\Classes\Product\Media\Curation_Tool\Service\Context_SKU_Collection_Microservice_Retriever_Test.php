<?php

declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\Product\Media\Curation_Tool\Service;

use Exception;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Microservice_Retriever;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Service;
use WF\Shared\Curation\Api\Api_Product_Context_Collection;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Logging\Logger;

class Context_SKU_Collection_Microservice_Retriever_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Service
     */
    private $context_sku_service;

    /**
     * @var \WF\Shared\Curation\Api\Api_Product_Context_Collection
     */
    private $pcc_api;

    /**
     * @var \WF\Shared\Logging\Logger
     */
    private $logger;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Microservice_Retriever
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->context_sku_service = $this->prophesize(Context_SKU_Service::class);
        $this->pcc_api = $this->prophesize(Api_Product_Context_Collection::class);
        $this->logger = $this->prophesize(Logger::class);
        $fetchContextXnIdFromMicroservice = true;

        $this->subject = new Context_SKU_Collection_Microservice_Retriever(
            $this->context_sku_service->reveal(),
            $this->pcc_api->reveal(),
            $this->logger->reveal(),
            $fetchContextXnIdFromMicroservice
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_skip_empty_list()
    {
        // given
        $skus = [];

        // when
        $result = $this->subject->get_context_xnid_map(...$skus);

        // then
        $this->assertIsArray($result, 'Result should be an array');
        $this->assertEmpty($result, 'Result should be an empty array');

        $this->logger->warning(Argument::type('string'))->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid_map(Argument::any())->shouldNotHaveBeenCalled();
        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_product_context_collection(Argument::any())->shouldNotHaveBeenCalled();
        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_skip_empty_item()
    {
        // given
        $sku = '';

        // when
        $result = $this->subject->get_context_xnid($sku);

        // then
        $this->assertNull($result, 'Result should be NULL');

        $this->logger->warning(Argument::type('string'))->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid_map(Argument::any())->shouldNotHaveBeenCalled();
        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_product_context_collection(Argument::any())->shouldNotHaveBeenCalled();
        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_handle_microservice_call()
    {
        // given
        $skus = ['ABC123', 'XYZ987'];
        $expected_result = ['ABC123' => 123, 'XYZ987' => 987];

        $this->pcc_api->find_product_context_collection(...$skus)
          ->willReturn($expected_result)
          ->shouldBeCalledOnce();

        // when
        $result = $this->subject->get_context_xnid_map(...$skus);

        // then
        $this->assertEquals($expected_result, $result, 'Result should be expected one');

        $this->logger->info(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid_map(Argument::any())->shouldNotHaveBeenCalled();
        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_handle_legacy_call()
    {
        // given
        $skus = ['ABC123', 'XYZ987'];
        $expected_result = ['ABC123' => 123, 'XYZ987' => 987];

        $fetchContextXnIdFromMicroservice = false;
        $this->subject = new Context_SKU_Collection_Microservice_Retriever(
            $this->context_sku_service->reveal(),
            $this->pcc_api->reveal(),
            $this->logger->reveal(),
            $fetchContextXnIdFromMicroservice
        );

        $this->context_sku_service->get_context_xnid_map(...$skus)
            ->willReturn($expected_result)
            ->shouldBeCalledOnce();

        // when
        $result = $this->subject->get_context_xnid_map(...$skus);

        // then
        $this->assertEquals($expected_result, $result, 'Result should be expected one');

        $this->logger->info(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_product_context_collection(Argument::any())->shouldNotHaveBeenCalled();
        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_fallback_to_legacy_on_api_exception()
    {
        // given
        $skus = ['ABC123', 'XYZ987'];
        $expected_result = ['ABC123' => 123, 'XYZ987' => 987];

        $this->pcc_api->find_product_context_collection(...$skus)
            ->willThrow(new API_Request_Exception())
            ->shouldBeCalledOnce();
        $this->context_sku_service->get_context_xnid_map(...$skus)
            ->willReturn($expected_result)
            ->shouldBeCalledOnce();

        // when
        $result = $this->subject->get_context_xnid_map(...$skus);

        // then
        $this->assertEquals($expected_result, $result, 'Result should be expected one');

        $this->logger->info(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledTimes(2);
        $this->logger->error(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function should_throw_exception_on_unexpected_exception()
    {
        // given
        $skus = ['ABC123', 'XYZ987'];
        $expected_result = ['ABC123' => 123, 'XYZ987' => 987];

        $this->pcc_api->find_product_context_collection(...$skus)
            ->willThrow(new Exception())
            ->shouldBeCalledOnce();
        $this->expectException(Exception::class);

        // when
        $this->subject->get_context_xnid_map(...$skus);

        // then
    }

    /**
     * In some cases (e.g. PA flow, where PrStatus = 1 (Being Added)) we'll not have records in PCC API
     *
     * @test
     *
     * @return void
     */
    public function should_fullfill_missed_skus_via_legacy_approach()
    {
        // given
        $skus = ['ABC123', 'XYZ987', 'LPN456'];
        $api_result = ['ABC123' => 123, 'XYZ987' => 987];
        $missed_sku = 'LPN456';
        $db_result = ['LPN456' => 456];
        $expected_result = ['ABC123' => 123, 'XYZ987' => 987, 'LPN456' => 456];

        $this->pcc_api->find_product_context_collection(...$skus)
            ->willReturn($api_result)
            ->shouldBeCalledOnce();
        $this->context_sku_service->get_context_xnid_map($missed_sku)
            ->willReturn($db_result)
            ->shouldBeCalledOnce();

        // when
        $result = $this->subject->get_context_xnid_map(...$skus);

        // then
        $this->assertEquals($expected_result, $result, 'Result should be expected one');

        $this->logger->info(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledOnce();
        $this->logger->warning(Argument::type('string'), Argument::type('array'))
            ->shouldHaveBeenCalledOnce();

        $this->context_sku_service->get_context_xnid(Argument::any())->shouldNotHaveBeenCalled();

        $this->pcc_api->find_sku_collisions(Argument::any())->shouldNotHaveBeenCalled();
    }
}
