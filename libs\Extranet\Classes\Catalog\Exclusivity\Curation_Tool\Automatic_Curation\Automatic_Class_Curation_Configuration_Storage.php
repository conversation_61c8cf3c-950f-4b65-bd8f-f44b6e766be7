<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

interface Automatic_Class_Curation_Configuration_Storage {
  /**
   * @param int $region_id Region ID
   *
   * @return Automatic_Class_Curation_Configuration_Item[]
   */
  public function get_configuration_items(int $region_id) : array;

  /**
   * @param string[] $skus             skus
   * @param int      $brand_catalog_id brand_catalog_id
   *
   * @return Automatic_Class_Curation_Item[]
   */
  public function get_class_by_skus(array $skus, int $brand_catalog_id) : array;
}
