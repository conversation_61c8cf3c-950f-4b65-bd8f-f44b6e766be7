<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Batch_Downstream;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Item_Model;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Loader;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO;

class Batch_Evaluation_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Curation_Batch_DAO
     */
    private $dao;

    /**
     * @var Curation_Batch_Postgres_DAO
     */

    /**
     * @var Curation_Batch_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;


    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->storage = $this->prophesize(Curation_Batch_DAO::class);
        $this->postgresql_dao = $this->prophesize(Curation_Batch_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);


        $this->subject = new Batch_Evaluation_Loader(
            $this->storage->reveal(),
            $this->postgresql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }
    /**
     * @test
     *
     * @return void
     */
    public function load_evaluation_items_by_ids_feature_toggle_on()
    {
        $daoResult = [
            ['id' => 1, 'sku' => 'ABC', 'qa_status_id' => 2],
            ['id' => 2, 'sku' => 'ABC1', 'qa_status_id' => 2],
            ['id' => 3, 'sku' => 'ABC2', 'qa_status_id' => 2]
        ];
        $ids = [1, 2, 3, 4];
        $this->storage->load_evaluation_items_by_ids($ids);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->load_evaluation_items_by_ids($ids)->willReturn($daoResult);
        $result = $this->subject->load_evaluation_items_by_ids($ids);
        $this->assertIsArray($result);
        $this->assertContainsOnlyInstancesOf(Batch_Evaluation_Item_Model::class, $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function load_evaluation_items_by_ids_feature_toggle_off()
    {
        $daoResult = [
            ['id' => 1, 'sku' => 'ABC', 'qa_status_id' => 2],
            ['id' => 2, 'sku' => 'ABC1', 'qa_status_id' => 2],
            ['id' => 3, 'sku' => 'ABC2', 'qa_status_id' => 2]
        ];
        $ids = [1, 2, 3, 4];
        $this->storage->load_evaluation_items_by_ids($ids)->willReturn($daoResult);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->load_evaluation_items_by_ids($ids);
        $result = $this->subject->load_evaluation_items_by_ids($ids);
        $this->assertIsArray($result);
        $this->assertContainsOnlyInstancesOf(Batch_Evaluation_Item_Model::class, $result);
    }
}
