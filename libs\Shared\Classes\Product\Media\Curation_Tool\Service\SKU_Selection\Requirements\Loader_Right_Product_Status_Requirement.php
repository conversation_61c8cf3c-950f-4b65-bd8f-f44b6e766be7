<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\Product_Model;

class Loader_Right_Product_Status_Requirement implements Sku_Right_Product_Status_Requirement_Interface {
  /**
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   *
   * @return bool
   */
  public function is_right_product_status(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return $request_sku->get_product_status() !== Product_Model::STATUS_BEING_ADDED;
  }
}
