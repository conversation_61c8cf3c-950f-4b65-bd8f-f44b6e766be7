<?php
/**
 * Returns an array of all registered bundles and with which environment each is registered
 *
 * For more information about Symfony bundles, please check
 * https://symfony.com/doc/current/bundles.html
 */

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    WF\EnvConfigSymfonyBundle\WayfairEnvConfigBundle::class => ['all' => true],
    WF\LoggingSymfonyBundle\WayfairLoggingBundle::class => ['all' => true],
    Wayfair\SecretsSymfonyBundle\WayfairSecretsBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    WF\DbSymfonyBundle\WayfairDbBundle::class => ['all' => true],
    Sensio\Bundle\FrameworkExtraBundle\SensioFrameworkExtraBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    WF\PartnerHome\User\Authentication\SymfonyBundle\UserToServiceAuthBundle::class => ['all' => true],
];
