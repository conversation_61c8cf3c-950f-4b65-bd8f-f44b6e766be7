<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WhiteLabel\Batching;

interface Downstreamable_Batch extends Batch_With_Id, \Countable, \ArrayAccess, \IteratorAggregate {

  /**
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_SKU[]
   */
  public function get_skus() : array;

  /**
   * @return int
   */
  public function get_brand_catalog_id();

  /**
   * @return null|int
   */
  public function get_pt_ticket_id();
}
