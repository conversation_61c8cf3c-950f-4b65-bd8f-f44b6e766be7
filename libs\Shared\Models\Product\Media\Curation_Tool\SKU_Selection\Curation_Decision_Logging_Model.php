<?php

namespace WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Curation_Decision_Logging_Model
{
    /**
     * @var string|null
     */
    private $sku;

    /**
     * @var int|null
     */
    private $prStatus;

    /**
     * @var string|null
     */
    private $createdDate;

    /**
     * @var int|null
     */
    private $isEligible;

    /**
     * @var int|null
     */
    private $isMasterClass;

    /**
     * @var int|null
     */
    private $isCoreClass;

    /**
     * @var int|null
     */
    private $isMasterCoreClass;

    /**
     * @var int|null
     */
    private $isHoldoutManufacturer;

    /**
     * @var int|null
     */
    private $isWayfairChannel;

    /**
     * @var int|null
     */
    private $isStandardBrand;

    /**
     * @var int|null
     */
    private $isActiveJoinSupplier;

    /**
     * @var int|null
     */
    private $isRightPrStatus;

    /**
     * @var int|null
     */
    private $hasImages;

    /**
     * @var int|null
     */
    private $isKitPrSku;

    /**
     * @var int|null
     */
    private $isRightAssignedSupplierMethod;

    /**
     * @var int|null
     */
    private $isPerigoldOnly;

    /**
     * @var int|null
     */
    private $hasHoldoutManufacturerPart;

    /**
     * @var int|null
     */
    private $isEu;

    /**
     * @var int|null
     */
    private $isExceedingPriceCeiling;

    /**
     * @var int|null
     */
    private $supplierId;

    /**
     * @var string|null
     */
    private $supplierName;

    /**
     * @var int|null
     */
    private $manufacturerId;

    /**
     * @var string|null
     */
    private $manufacturerName;

    /**
     * @var string|null
     */
    private $curationDomainApplication;

    /**
     * @var string|null
     */
    private $metaData;

    const CURATION_DOMAIN_APPLICATION_NAME = 'Brand-Workflows-Curation-Tool';

    private Curation_Request_SKU_Base_Model $curation_request_sku_base_model;

    /**
     * Curation_Decision_Logging_Model Constructor
     *@param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $curation_request_sku_base_model
     *@param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku
     *@param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch
     */

    public function __construct(
    Curation_Request_SKU_Base_Model $curation_request_sku_base_model , Verified_Batch_SKU $verified_batch_sku, Batch $batch)
    {
        $this->sku = $curation_request_sku_base_model->get_sku();
        $this->prStatus = $curation_request_sku_base_model->get_product_status();
        $this->isEligible = $this->booleanToInt($curation_request_sku_base_model->is_eligible());
        $this->isMasterCoreClass = $this->booleanToInt($curation_request_sku_base_model->is_master_core_class());
        $this->isHoldoutManufacturer = $this->booleanToInt($curation_request_sku_base_model->is_holdout_manufacturer());
        $this->isWayfairChannel = $this->booleanToInt($curation_request_sku_base_model->is_wayfair_channel());
        $this->isStandardBrand = $this->booleanToInt($curation_request_sku_base_model->is_standard_brand());
        $this->isActiveJoinSupplier = $this->booleanToInt($curation_request_sku_base_model->is_active_join_supplier());
        $this->isRightPrStatus = $this->booleanToInt($curation_request_sku_base_model->is_right_product_status());
        $this->hasImages = $this->booleanToInt($curation_request_sku_base_model->has_images()) ;
        $this->isKitPrSku = $this->booleanToInt($curation_request_sku_base_model->is_kit_pr_sku());
        $this->isRightAssignedSupplierMethod = $this->booleanToInt($curation_request_sku_base_model->is_right_assigned_supplier_method());
        $this->isPerigoldOnly = $this->booleanToInt($curation_request_sku_base_model->is_perigold_only());
        $this->hasHoldoutManufacturerPart = $this->booleanToInt($curation_request_sku_base_model->has_holdout_manufacturer_part());
        $this->isEu = $this->booleanToInt($curation_request_sku_base_model->is_eu());
        $this->isExceedingPriceCeiling = $this->booleanToInt($curation_request_sku_base_model->is_exceeding_price_ceiling());
        $this->curationDomainApplication = self::CURATION_DOMAIN_APPLICATION_NAME;
        $this->supplierId = $batch->get_supplier_id();
        $this->manufacturerId = $verified_batch_sku->get_target_ma_id();
    }

    public function booleanToInt($value): ?int
    {
        if( $value === null)
        {
            return null;
        }
        else
        {
            return $value ? 1 : 0;
        }
    }

    public function setMetaData($description): void
    {
        if (empty($description)) {
            throw new \Exception('Meta data cannot be empty');
        }
        $metaDataJson=[
        'description' => $description
        ];
        $this->metaData = $this->convertToJson($metaDataJson);
    }

    public function convertToJson($data): string
    {
        return json_encode($data, JSON_UNESCAPED_SLASHES);
    }

    public function setCreatedDate()
    {
        $this->createdDate = date('Y-m-d H:i:s');
    }

    public function toArray(): array {
        return get_object_vars($this);
    }

    public function setSupplierId(int $supplierId): void
    {
        $this->supplierId = $supplierId;
    }
    public function setSupplierName(string $supplierName): void
    {
        $this->supplierName = $supplierName;
    }


}
