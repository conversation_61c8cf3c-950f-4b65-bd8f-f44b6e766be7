<?php

declare(strict_types=1);

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use App\Application\DTO\SaveKitscoCurationDecision;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Save_Info_Factory;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function array_map;
use function array_values;
use function date;
use function sprintf;

class SaveKitscoController extends AbstractBaseController
{
    private Curation_Save_Info_Factory $save_info_factory;

    /**
     * @param Curation_Save_Info_Factory $save_info_factory the save info factory
     */
    public function __construct(Curation_Save_Info_Factory $save_info_factory)
    {
        $this->save_info_factory = $save_info_factory;
    }

    /**
     * @param Kit_Parent_Grouper $kit_parent_grouper the kit parent grouper
     * @param Curation_Item_Group_Factory $factory the model factory
     * @param Curation_Decision_Service $curation_decision_manager the curation decision manager
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param SaveKitscoCurationDecision $kitscoCurationDecision
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/save_kitsco", methods={"POST"})
     */
    public function __invoke(
        Kit_Parent_Grouper $kit_parent_grouper,
        Curation_Item_Group_Factory $factory,
        Curation_Decision_Service $curation_decision_manager,
        Completion_Batch_Data_Service $batch_data_service,
        SaveKitscoCurationDecision $kitscoCurationDecision
    ): JsonResponse {
        $is_kitsco = $kitscoCurationDecision->isKitsco();
        $sku = $kitscoCurationDecision->getSku();
        $batch_id = $kitscoCurationDecision->getBatchId();

        $this->info(
            'Saving kitsco',
            ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'is_kitsco' => $is_kitsco]
        );

        try {
            // safety check
            if (!$this->isExpectedBatchStatus(
                $batch_id,
                [Batch::STATUS_MANUAL_UNASSIGNED, Batch::STATUS_MANUAL_ASSIGNED, Batch::STATUS_MANUAL_IN_PROGRESS],
                $batch_data_service
            )) {
                $this->warning(
                    sprintf('Failed to save kitsco for batch_id=%d. Unexpected batch status.', $batch_id),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json([], Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            // Calculates the new kit parent groups
            /**
             *  \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper $kit_parent_grouper
             */
            $item_group = $factory->createFromArray($kitscoCurationDecision->getSharedComponents());
            $kit_parents = $kit_parent_grouper->createKitParents($item_group);

            // Save kitsco decision
            $saved_at = date('Y-m-d H:i:s');
            if ($is_kitsco) {
                $this->info('Saving as kitsco', ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]);
                $curation_decision_manager->save_as_kitsco(
                    $batch_id,
                    $sku,
                    $saved_at,
                    $this->getEmployeeId(),
                    Curation_Decision_Source::manual()
                );
            } else {
                $kit_parent_skus = array_map(
                    static function (Kit_Parent $parent) {
                        return $parent->sku;
                    },
                    $kit_parents
                );

                $this->info(
                    'Removing kitsco',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'sku' => $sku]
                );
                $curation_decision_manager->remove_kitsco(
                    $batch_id,
                    $sku,
                    $saved_at,
                    $this->getEmployeeId(),
                    $kit_parent_skus,
                    Curation_Decision_Source::manual()
                );
            }

            $employee = $this->getUser();

            $employee_name = sprintf('%s %s', $employee->firstName(), $employee->lastName());

            $result = $this->save_info_factory->getCurationSaveInfo(
                $saved_at,
                $employee_name,
                Curation_Decision_Source::manual()
            );

            return $this->json([
                'saveInfo' => $result,
                'kitParentGroups' => array_values($kit_parents)
            ]);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('Failed to Save Kitsco: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );
            throw $exception;
        }
    }
}
