<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <j<PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\WhiteLabel;

use App\Infrastructure\Connection\DatabaseConstantsInterface;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use WF\Shared\Helpers\SQL;

class Kit_Collision_SKU_DAO {

  private ProductConnection $pdo;

  /**
   * The constructor
   *
   * @param ProductConnection $pdo The PDO object to access the DB
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * Returns array of SKUs in a kit with the current SKU
   * This is the list of PrStatus to use to filter the results:
   * 1 - Being Added
   * 2 - Kit Component
   * 4 - Live Product
   * 3 - Admin Can Sell
   * 4 - Live Product
   * For status 3 the only valid ACS reason ID is:
   * 1- Missing Image
   * The requested brand catalogs are:
   * 1 - Wayfair US
   * 2 - Wayfair UK
   * 3 - Wayfair Germany
   * The requested Channel is 1 (Wayfair)
   *
   * @param string $sku              The potential kit component
   * @param bool   $include_kit_skus Include parent SKUs in the result set
   *
   * @return string[] String array of collision sku identifiers. This is not typed because we need only the SKU at the moment
   */
  public function find_by_kit_component($sku, $include_kit_skus = true) {
    $result = [];
    $sql    = '
             DECLARE @searched NVARCHAR(8) = :sku

             SELECT DISTINCT ISNULL(kpcf.FinalPrSKU, pk.KitPrSKU) AS KitPrSKU,
                             ISNULL(kccf.FinalPrSKU, pk.ChildPrSKU) AS ChildPrSKU
             FROM (
               SELECT pk.KitPrSKU,
                      CASE
                        WHEN pa.PrAcsID IN (1, 2, 4, 7, 12) THEN 3
                        ELSE NULL
                      END AS PrAcsIDFlag,
                     CASE
                        WHEN pa.PrDrID IN (22, 23) THEN 7
                        ELSE NULL
                      END AS PrDrIDFlag
               FROM csn_product.dbo.tblProductKit pk WITH (NOLOCK)
               LEFT JOIN csn_product.dbo.tblProductAdditional pa WITH (NOLOCK)
                 ON pa.PrSKU = pk.KitPrSKU
               WHERE pk.KitPrSKU = @searched
               UNION ' .
        // Grab kit sku when uploaded SKU is a child
        // or when uploaded SKU has a duplicate in tblJoinProductSupplier that is a child
        'SELECT KitPrSKU,
                      CASE
                        WHEN pa.PrAcsID IN (1, 2, 4, 7, 12) THEN 3
                        ELSE NULL
                      END AS PrAcsIDFlag,
                      CASE
                        WHEN pa.PrDrID IN (22, 23) THEN 7
                        ELSE NULL
                      END AS PrDrIDFlag
               FROM csn_product.dbo.vwProductSupplierManufacturerPart jps1 WITH (NOLOCK)
               INNER JOIN csn_product.dbo.vwProductSupplierManufacturerPart jps2 WITH (NOLOCK)
                 ON jps1.SupplierID = jps2.SupplierID
                 AND jps1.SupplierPartNumber = jps2.SupplierPartNumber
               INNER JOIN csn_product.dbo.tblProductKit pk WITH (NOLOCK)
                 ON pk.ChildPrSKU = jps2.PrSKU
               LEFT JOIN csn_product.dbo.tblProductAdditional pa WITH (NOLOCK)
                 ON pa.PrSKU = jps2.PrSKU
               WHERE jps1.PrSKU = @searched ' .
        // Because sometimes there are 200k rows with an empty RefProdID from one supplier
        'AND LEN(ISNULL(jps1.SupplierPartNumber, \'\')) > 0
             ) KitSKUs ' .
        // Inner join on tblProduct twice, once for parents and once for children, to do the PrStatus checking.
        'INNER JOIN csn_product.dbo.tblProduct Parent WITH (NOLOCK)
               ON KitSKUs.KitPrSKU = parent.PrSKU
             INNER JOIN csn_product.dbo.tblProductKit pk WITH (NOLOCK)
               ON KitSKUs.KitPrSKU = pk.KitPrSKU
             INNER JOIN csn_product.dbo.tblProduct Child WITH (NOLOCK)
               ON pk.ChildPrSKU = Child.PrSKU
             LEFT JOIN csn_product.dbo.tblProductAdditional pa WITH (NOLOCK)
               ON child.PrSKU = pa.PrSKU
             LEFT JOIN csn_product.dbo.tblProductConsolidationFinal kpcf WITH (NOLOCK)
               ON kpcf.SourcePrSKU = pk.KitPrSKU
             LEFT JOIN csn_product.dbo.tblProductConsolidationFinal kccf WITH (NOLOCK)
               ON kccf.SourcePrSKU = pk.ChildPrSKU
             WHERE (   parent.PrStatus IN (4, 6, 13, 18)
                    OR parent.PrStatus = KitSKUs.PrAcsIDFlag
                    OR parent.PrStatus = KitSKUs.PrDrIDFlag
                    OR (
                          parent.PrStatus IN (1, 2)
                            AND NOT EXISTS (
                                SELECT TOP 1 1
                                FROM csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                                INNER JOIN csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                                ON pt2.QuickformID = qfl.QflQfpID
                                INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                                ON qfl.QflID = qfb.QfbQflID
                                WHERE qfb.QfbPrSKU = parent.PrSKU
                                  AND 1 = IIF(pt2.ExecutionCompleteDate IS NOT NULL, 0, 1)
                              )
                        )
                   )
             AND   (    Child.PrStatus IN (4, 6, 13, 18)
                    OR (Child.PrStatus = 3 AND pa.PrAcsID IN (1, 2, 4, 7, 12))
                    OR (Child.PrStatus = 7 AND pa.PrDrID IN (22, 23))
                    OR (
                        Child.PrStatus IN (1, 2)
                        AND NOT EXISTS (
                                SELECT TOP 1 1
                                FROM csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                                INNER JOIN csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                                ON pt2.QuickformID = qfl.QflQfpID
                                INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                                ON qfl.QflID = qfb.QfbQflID
                                WHERE qfb.QfbPrSKU = Child.PrSKU
                                  AND 1 = IIF(pt2.ExecutionCompleteDate IS NOT NULL, 0, 1)
                              )
                        )
                   )
            AND parent.PrBclgID in (1,2,3)
            AND Child.PrBclgID in (1,2,3)
            AND (
              (
                kpcf.FinalPrSKU IS NULL
                AND EXISTS (
                  SELECT TOP 1 1
                  FROM csn_product.dbo.tbljoinProductChannel kppch WITH (NOLOCK)
                  WHERE kppch.PrSKU       = pk.KitPrSKU
                  AND   kppch.PrChannelID = 1
                )
              ) OR (
                kpcf.FinalPrSKU IS NOT NULL
                AND EXISTS (
                  SELECT TOP 1 1
                  FROM csn_product.dbo.tbljoinProductChannel kppch WITH (NOLOCK)
                  WHERE kppch.PrSKU       = kpcf.FinalPrSKU
                  AND   kppch.PrChannelID = 1
                )
              )
            )
            AND (
              (
                kccf.FinalPrSKU IS NULL
                AND EXISTS (
                  SELECT TOP 1 1
                  FROM csn_product.dbo.tbljoinProductChannel kppch WITH (NOLOCK)
                  WHERE     kppch.PrSKU = pk.ChildPrSKU
                  AND kppch.PrChannelID = 1
                )
              ) OR (
                kccf.FinalPrSKU IS NOT NULL
                AND EXISTS (
                  SELECT TOP 1 1
                  FROM csn_product.dbo.tbljoinProductChannel kppch WITH (NOLOCK)
                  WHERE     kppch.PrSKU = kccf.FinalPrSKU
                  AND kppch.PrChannelID = 1
                )
              )
            )';

    $hints = [
        'sku' => SQL::nvarchar(8)
    ];

    $statement = $this->pdo->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);
    $statement->bindValue('sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw new ExecutionException('Find Kit Collisions - ' . implode(';', $statement->errorInfo()));
    }

    $rows = $statement->fetchAll();

    foreach ($rows as $row) {
      if ($include_kit_skus) {
        $result = $this->add_to_result($sku, $result, $row['KitPrSKU']);
      }

      $result = $this->add_to_result($sku, $result, $row['ChildPrSKU']);
    }


    return $result;
  }

  /**
   * Adds a new SKU to the result array
   *
   * @param string   $current_sku The SKU subject of the collision search
   * @param string[] $result      The result array
   * @param string   $new_sku     The potential collision SKU
   *
   * @return string[]
   */
  private function add_to_result($current_sku, $result, $new_sku) {
    if ($new_sku != $current_sku && !in_array($new_sku, $result)) {
      $result[] = $new_sku;
    }

    return $result;
  }

  /**
   * Get the other SKUs in kits with this SKU
   *
   * @param string $sku The SKU
   *
   * @return array of other SKUs in the same kit
   */
  public function get_kit_components($sku) {
    // Get any SKUs that share a Kit ID with this one.
    $sql = 'DECLARE @searched NVARCHAR(8) = :sku

            SELECT ParentSKU AS PrSKU
            FROM csn_product.dbo.vwExclusivityKitComposition  WITH (NOLOCK)
            WHERE ChildSKU = @searched OR ParentSKU = @searched

            UNION

            SELECT DISTINCT ChildSKU AS PrSKU
            FROM csn_product.dbo.vwExclusivityKitComposition  WITH (NOLOCK)
            WHERE ChildSKU = @searched OR ParentSKU = @searched';

    $hints = [
        'sku' => SQL::nvarchar(8)
    ];

    $statement = $this->pdo->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);
    $statement->bindValue('sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw new ExecutionException('Get Kit Components - ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }
}
