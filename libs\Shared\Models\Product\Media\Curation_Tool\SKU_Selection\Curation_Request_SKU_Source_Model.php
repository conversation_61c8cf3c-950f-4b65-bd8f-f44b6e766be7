<?php
/**
 * Model representing a Curation Request SKU Source Model
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container;

class Curation_Request_SKU_Source_Model extends Curation_Request_SKU_Base_Model {

  /**
   * Curation_Request_SKU_Source_Model constructor.
   *
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container $requirement_container Requirements Container
   * @param string                                                                                                  $sku                   SKU
   * @param array                                                                                                   $map                   SKU Data
   *
   * @throws \Exception
   */
  public function __construct(
      Requirement_Container $requirement_container,
      string $sku,
      array $map
  ) {
    if (empty($sku)) {
      throw new \Exception('SKU cannot be empty');
    }
    $this->sku                   = $sku;
    $this->requirement_container = $requirement_container;

    $this->id                                = $map[self::COLUMN_ID] ?? null;
    $this->is_master_core_class              = $map[self::COLUMN_IS_MASTER_CORE_CLASS] ?? null;
    $this->is_holdout_manufacturer           = $map[self::COLUMN_IS_HOLDOUT_MANUFACTURER] ?? null;
    $this->is_wayfair_channel                = (bool)($map[self::COLUMN_IS_WAYFAIR_CHANNEL] ?? false);
    $this->is_active_join_supplier           = (bool)($map[self::COLUMN_IS_ACTIVE_JOIN_SUPPLIER] ?? false);
    $this->is_kit_pr_sku                     = (bool)($map[self::COLUMN_PR_KIT] ?? false);
    $this->is_right_assigned_supplier_method = (bool)($map[self::COLUMN_IS_RIGHT_ASSIGNED_SUPPLIER_METHOD] ?? false);
    $this->is_exceeding_price_ceiling        = (bool)($map[self::COLUMN_IS_EXCEEDING_PRICE_CEILING] ?? false);
    $this->is_perigold_only                  = $map[self::COLUMN_IS_PERIGOLD_ONLY] ?? null;
    $this->product_status                    = $map[self::COLUMN_PRODUCT_STATUS] ?? 0;
    $this->verification_item_id              = $map[self::COLUMN_VERIFICATION_ITEM_ID] ?? null;
    $this->has_holdout_manufacturer_part     = $map[self::COLUMN_HAS_HOLDOUT_MANUFACTURER_PART] ?? null;
    $this->is_eu                             = $map[self::COLUMN_IS_EU] ?? null;
    $this->brand_catalog_id                  = $map[self::COLUMN_BRAND_CATALOG_ID] ?? null;
    $this->class_id                          = $map[self::COLUMN_CLASS_ID] ?? null;

    $this->is_right_product_status = $this->calculate_right_product_status();
    $this->has_images              = $this->calculate_has_images($map[self::COLUMN_IRP_ACTIVE] ?? 0, $map[self::COLUMN_IRO_ACTIVE] ?? 0, $map[self::COLUMN_PRODUCT_STATUS] ?? 0);
    $this->is_standard_brand       = $this->calculate_standard_brand($map[self::COLUMN_BRAND_TYPE] ?? 0);
    $this->price_missing_count     = $this->calculate_price_missing_count($map['sale_price'] ?? 0);

    $this->eligibility_status    = $this->calculate_eligible();
    $this->is_eligible           = $this->eligibility_status !== Curation_Request_SKU_Base_Model::NOT_ELIGIBLE;
    $this->ready_for_curation_id = $this->calculate_ready_for_curation_id();
    $this->import_status_id      = $this->calculate_import_status_id();
  }
}
