<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Automatic_Substyle_Suggestion_Collection {
  /**
   * @var SKU_Substyle_Suggestion[]
   */
  private $sku_substyles = [];

  /**
   * @param array $sku_substyles Sku styles map, ordered by rank
   */
  public function __construct(array $sku_substyles) {
    $this->sku_substyles = $sku_substyles;
  }

  /**
   * @param string $sku SKU
   *
   * @return int[]
   */
  public function get_substyles_ids_for_sku(string $sku) : array {
    if (empty($this->sku_substyles[$sku])) {
      return [];
    }

    return array_map(
        function (Substyle_Suggestion $substyle_suggestion) {
          return $substyle_suggestion->get_substyle_id();
        },
        $this->sku_substyles[$sku]->get_suggestions()
    );
  }

  /**
   * @param string $sku SKU
   *
   * @return float|null
   */
  public function get_threshold_for_sku(string $sku) : ?float {
    if (empty($this->sku_substyles[$sku])) {
      return null;
    }

    return $this->sku_substyles[$sku]->get_threshold();
  }

  /**
   * @param string $sku SKU
   *
   * @return Substyle_Suggestion[]
   */
  public function get_substyles_for_sku(string $sku) : array {
    if (empty($this->sku_substyles[$sku])) {
      return [];
    }

    return $this->sku_substyles[$sku]->get_suggestions();
  }

  /**
   * @return string[]
   */
  public function get_skus_with_substyles(): array {
    return array_map(
        function (SKU_Substyle_Suggestion $sku_substyle_suggestion) {
          return $sku_substyle_suggestion->get_sku();
        },
        array_filter(
            $this->sku_substyles,
            function (SKU_Substyle_Suggestion $sku_substyle_suggestion) {
              return !empty($sku_substyle_suggestion->get_suggestions());
            }
        )
    );
  }

  /**
   * @return array
   */
  public function to_array(): array {
    $sku_substyles = [];

    foreach ($this->sku_substyles as $sku_substyle) {
      $sku       = $sku_substyle->get_sku();
      $threshold = $sku_substyle->get_threshold();

      foreach ($sku_substyle->get_suggestions() as $substyle_suggestion) {
        $sku_substyles [] = [
            'sku'             => $sku,
            'threshold'       => $threshold,
            'suggestion_id'   => $substyle_suggestion->get_substyle_id(),
            'suggestion_name' => $substyle_suggestion->get_substyle_name(),
            'rank'            => $substyle_suggestion->get_rank(),
            'probability'     => $substyle_suggestion->get_probability()
        ];
      }
    }

    return $sku_substyles;
  }
}
