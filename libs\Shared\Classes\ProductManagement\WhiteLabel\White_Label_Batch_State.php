<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel;

use App\Domain\Enum\AbstractEnumeration;

final class White_Label_Batch_State extends AbstractEnumeration implements \JsonSerializable {
  /**
   * csn_product.dbo.tblplWhiteLabelBatchState
   */

  public const BATCH_IS_NEW_STAGING_PROCESS = 0;

  /**
   * @var array
   */
  private static $values = [
      'Created'                       => 1,
      'Validation Errors'             => 2,
      'Collision Errors'              => 3,
      'Ready for WL'                  => 4,
      'Staging SKU Creation'          => 5,
      'Staging SKU Creation Complete' => 6,
      'Completed'                     => 7,
      'Merging in Progress'           => 8,
      'Merging Failed'                => 9,
      'Automated Scrubbing'           => 10,
      'Manual Scrubbing'              => 11,
      'Perigold Check'                => 12,
      'Scrubbing Complete'            => 13,
  ];

  /**
   * @var string|null
   */
  private $label;

  /**
   * @return self
   */
  public static function created() : self {
    return self::get_value_for_option(self::$values['Created']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function validation_errors() : self {
    return self::get_value_for_option(self::$values['Validation Errors']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function collision_errors() : self {
    return self::get_value_for_option(self::$values['Collision Errors']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function ready_for_wl() : self {
    return self::get_value_for_option(self::$values['Ready for WL']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function sku_creation() : self {
    return self::get_value_for_option(self::$values['Staging SKU Creation']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function sku_creation_complete() : self {
    return self::get_value_for_option(self::$values['Staging SKU Creation Complete']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function merging_in_progress() : self {
    return self::get_value_for_option(self::$values['Merging in Progress']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function merging_failed() : self {
    return self::get_value_for_option(self::$values['Merging Failed']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function automated_scrubbing() : self {
    return self::get_value_for_option(self::$values['Automated Scrubbing']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function manual_scrubbing() : self {
    return self::get_value_for_option(self::$values['Manual Scrubbing']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function perigold_check() : self {
    return self::get_value_for_option(self::$values['Perigold Check']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function scrubbing_complete() : self {
    return self::get_value_for_option(self::$values['Scrubbing Complete']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function completed() : self {
    return self::get_value_for_option(self::$values['Completed']); /** @phpstan-ignore-line */
  }

  /**
   * @param int $option the option from the database
   *
   * @return self
   */
  public static function create(int $option) : self {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Unknown batch state: ' . $option);
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function label() : string {
    // if the label is requested for the first time, compute it and save the result
    if ($this->label === null) {
      $this->label = array_search((int)$this->value(), self::$values, true);
    }

    return $this->label;
  }

  /**
   * @return array
   */
  public function jsonSerialize() : array {
    return [
        'label' => $this->label(),
        'value' => $this->value()
    ];
  }
}
