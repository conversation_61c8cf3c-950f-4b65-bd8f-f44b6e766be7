<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\libs\Shared\Curation\Api\Curation;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Mockery;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use WF\Shared\Curation\Api\Curation\TalentApi;
use WF\Shared\Environment;

use function json_encode;
use function time;

class TalentApi_Test extends TestCase
{
    /**
     * @var Client Client.
     */
    private $httpClient;

    private $cache;

    public const CACHE_KEY = 'talent_api_access_token';

    public const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 1800; // 30 minutes



    protected function setUp(): void
    {
        $this->httpClient = Mockery::mock(Client::class);
        $this->cache = Mockery::mock(CacheItemPoolInterface::class);

        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() + 1800]);
        $this->cache->shouldReceive('getItem')->with(TalentApi::CACHE_KEY)->andReturn($cacheItem);
        $responseData = [];
        $this->client = new TalentApi(Environment::DEVELOPMENT, 'testclientid', 'testclientsecret', $this->httpClient, $this->cache);
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "abc"}'));
        $this->httpClient->shouldReceive('request')->andReturn(new Response(200, [], '{"id":1, "fullName": "Test", "email":"<EMAIL>"}'));
        $this->client->client = $this->httpClient;
        parent::setUp();
    }
    /**
     * @test
     * @return void
     */
    public function getAccessToken_Test()
    {
        $empId = 1;
        $actual = $this->client->getAccessToken($empId);
        self::assertEquals('abc', $actual);
    }

    /**
     * @test
     * @return void
     */
    public function getAccessToken_ExpiredCache_Test()
    {
        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() - 3600]);
        $this->cache->shouldReceive('getItem')->with(TalentApi::CACHE_KEY)->andReturn($cacheItem);
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "def"}'));
        $this->cache->shouldReceive('save')->with($cacheItem)->andReturnTrue();

        $actual = $this->client->getAccessToken();

        self::assertEquals('abc', $actual);
    }


    /**
     * @test
     * @return void
     */
    public function getEmployeeDetails_Exception_Test()
    {
        $empId = 1;
        $accessToken = 'abc';

        $this->httpClient->shouldReceive('request')->andThrow(new Exception('Network Error'));

        // Simulate an expired access token to trigger token refresh
        $this->cache->shouldReceive('deleteItem')->with(TalentApi::CACHE_KEY)->andReturnNull();

        // Mock the refreshed access token request
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "xyz"}'));
        $this->cache->shouldReceive('save')->andReturnTrue();

        // Mock the successful employee details request with the refreshed access token
        $this->httpClient->shouldReceive('request')->andReturn(new Response(200, [], '{"id":1, "fullName": "Test", "email":"<EMAIL>"}'));

        $actual = $this->client->getEmployeeDetails($empId);

        self::assertEquals('Test', $actual->fullName);
        self::assertEquals('<EMAIL>', $actual->email);
    }

    /**
     * @test
     * @return void
     */
    public function getAccessToken_SuccessfulRequest_Test()
    {
        $request = [
            'form_params' => [
                'client_id' => $this->client->clientID,
                'client_secret' => $this->client->clientSecret,
                'grant_type' => 'client_credentials',
                'scope' => 'roles'
            ]
        ];
        $url = $this->client->tokenEndpoint . '/openid-connect/token';

        $responseBody = json_encode(['access_token' => 'abc']);
        $this->httpClient->shouldReceive('post')
            ->with($url, $request)
            ->andReturn(new Response(200, [], $responseBody));

        $this->client->cache->shouldReceive('save')->andReturnTrue();

        $actual = $this->client->getAccessToken();

        self::assertEquals('abc', $actual);
    }



    /**
     * @test
     * @return void
     */
    public function getEmployeeDetails_Test()
    {
        $empId = 1;
        $actual = $this->client->getEmployeeDetails($empId);
        self::assertEquals('Test', $actual->fullName);
        self::assertEquals('<EMAIL>', $actual->email);
    }

    /**
     * @test
     * @return void
     */
    public function getAccessToken_Test_Exception()
    {
        $empId = 1;
        $httpClient = Mockery::mock(Client::class);
        $errorResponse = new Exception('Not Authorized!');
        $httpClient->shouldReceive('post')->andThrow($errorResponse);
        $client = new TalentApi(Environment::DEVELOPMENT, 'testclientid', 'testclientsecret', $this->httpClient, $this->cache);
        $client->client = $httpClient;
        $actual = $client->getAccessToken($empId);
        $this->assertEquals(true, $actual);
    }

    /**
     * @test
     * @return void
     */
    public function getCachedAccessToken_NotHit_Test()
    {
        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(false);
        $cacheItem->shouldReceive('get')->andReturn(null); // Change the return value to null
        $this->cache->shouldReceive('getItem')->with(TalentApi::CACHE_KEY)->andReturn($cacheItem);

        $actual = $this->client->getCachedAccessToken();

        self::assertNull(null, $actual);
    }

    /**
     * @test
     * @return void
     */
    public function getCachedAccessToken_ExpiredToken_Test()
    {
        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() - 3600]);
        $this->cache->shouldReceive('getItem')->with(TalentApi::CACHE_KEY)->andReturn($cacheItem);

        $actual = $this->client->getCachedAccessToken();

        self::assertNull(null, $actual);
    }

    /**
     * @test
     * @return void
     */
    public function cacheAccessToken_Test()
    {
        $accessToken = 'xyz';
        $expiresAt = time() + self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS;

        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('expiresAfter')
            ->with(self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS)
            ->andReturnUsing(function ($value) use ($expiresAt, $cacheItem) {
                $this->assertEquals(self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS, $value);
                return $cacheItem;
            });
        $cacheItem->shouldReceive('set')
            ->with(['token' => $accessToken, 'expires_at' => $expiresAt])
            ->andReturnUsing(function ($value) use ($accessToken, $expiresAt, $cacheItem) {
                $this->assertEquals(['token' => $accessToken, 'expires_at' => $expiresAt], $value);
                return $cacheItem;
            });

        $cacheItemPool = Mockery::mock(CacheItemPoolInterface::class);
        $cacheItemPool->shouldReceive('getItem')->with(self::CACHE_KEY)->andReturn($cacheItem);
        $cacheItemPool->shouldReceive('save')->with($cacheItem)->andReturnTrue();

        $this->client->cache = $cacheItemPool;
        $this->client->cacheAccessToken($accessToken);
    }
    /**
     * @test
     * @return void
     */
    public function clearAccessToken_Test()
    {
        $this->cache->shouldReceive('deleteItem')->with(TalentApi::CACHE_KEY)->andReturnTrue();

        $this->client->clearAccessToken();

        $this->assertTrue(true);
    }
}
