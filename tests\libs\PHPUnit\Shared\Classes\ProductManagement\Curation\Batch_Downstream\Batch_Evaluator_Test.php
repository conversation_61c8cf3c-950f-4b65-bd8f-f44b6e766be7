<?php
/**
 * Test for the \WF\Shared\Classes\ProductManagement\Curation\Ticket_Creation\Batch_Evaluator
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Item_Model;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Loader;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluator;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluator_Factory;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result;
use WF\Shared\Logging\Logger;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_SKU;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Batch_Evaluator_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluator_Factory
     */
    private $ticket_creation_factory;

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
     */
    private $mock_batch;

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_SKU
     */
    private $mock_batch_sku;

    /**
     * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Loader
     */
    private $mock_batch_evaluation_loader;


    /**
     * @var \WF\Shared\Logging\Logger
     */
    private $logger;

    /**
     * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluator
     */
    private $subject;

    /**
     * The Setup function of this test
     *
     * @return void
     */
    protected function setUp(): void
    {
        $this->mock_batch_evaluation_loader = $this->prophesize(Batch_Evaluation_Loader::class);
        $this->ticket_creation_factory = $this->prophesize(Batch_Evaluator_Factory::class);
        $verified_batch_sku = $this->prophesize(Verified_Batch_SKU::class);
        $verified_batch_sku->get_sku()->willReturn('ABC');
        $verified_batch_sku->is_kit_parent()->willReturn(false);

        $this->mock_batch = $this->prophesize(Batch::class);
        $this->mock_batch->get_id()->willReturn(1);
        $this->mock_batch->get_sku(Argument::cetera())->willReturn($verified_batch_sku);

        $this->mock_batch_evaluation_loader->load_evaluation_items_by_ids(Argument::cetera())->willReturn([]);

        $this->mock_batch_sku = $this->prophesize(Batch_SKU::class);
        $this->logger = $this->prophesize(Logger::class);

        $this->logger->info(Argument::any());

        $this->subject = new Batch_Evaluator(
            $this->mock_batch_evaluation_loader->reveal(),
            $this->ticket_creation_factory->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * Test for the evaluate_batch()
     *
     * @return void
     */
    public function test_evaluate_batch()
    {
        $actual = $this->subject->evaluate_batch($this->mock_batch->reveal());

        $this->assertInstanceOf(Batch_Evaluation_Result::class, $actual, 'evaluate_batch should return instance of the class Batch_Evaluation_Result');
        $this->logger->info(Argument::any())->shouldBeCalled();
    }

    /**
     * Tests evaluate_sku()
     *
     * @param array  $expected expected value
     * @param string $message  error message
     *
     * @dataProvider evaluate_sku_data_provider
     *
     * @return void
     */
    public function test_evaluate_sku($expected, $message)
    {
        $this->ticket_creation_factory->create_batched_sku_evaluation_result(Argument::cetera())
            ->willReturn(new Batched_SKU_Evaluation_Result($expected['sku'], $expected['reason'], $expected['qa_status'], new DateTime()));

        /** @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku */
        $verified_batch_sku = $this->prophesize(Verified_Batch_SKU::class);
        $verified_batch_sku->is_kit_parent()->willReturn($expected['reason'] === Batched_SKU_Evaluation_Result::REASON_SKU_PARENT);
        $verified_batch_sku->get_id()->willReturn($expected['id']);

        // Adds skus to the batch
        $models[$expected['sku']] = $verified_batch_sku->reveal();
        $this->mock_batch->models = $models;

        $batch_evaluation_item_model = $this->prophesize(Batch_Evaluation_Item_Model::class);
        $batch_evaluation_item_model->get_id()->willReturn($expected['id']);
        $batch_evaluation_item_model->get_sku()->willReturn($expected['sku']);
        $batch_evaluation_item_model->get_qa_status_id()->willReturn($expected['qa_status']);

        $this->mock_batch_evaluation_loader->load_evaluation_items_by_ids(Argument::cetera())
            ->willReturn([$batch_evaluation_item_model]);

        $evaluation_result = $this->subject->evaluate_batch($this->mock_batch->reveal());

        $evaluation_results_of_skus = $evaluation_result->get_batched_skus_evaluation_result();

        /**
         * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result[] $evaluation_results_of_skus
         */
        foreach ($evaluation_results_of_skus as $result) {
            $this->assertEquals($expected['sku'], $result->get_sku(), $message);
            $this->assertEquals($expected['reason'], $result->get_reason(), $message);
            $this->assertEquals($expected['qa_status'], $result->get_status(), $message);
        }
    }

    /**
     * @return array
     */
    public function evaluate_sku_data_provider()
    {
        return
            [
                [
                    [
                        'id' => 1,
                        'sku' => 'ABC123',
                        'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED,
                        'qa_status' => QA_Status_Object::APPROVED,
                    ],
                    'When QA status is ' . QA_Status_Object::APPROVED . ' the reason should be: ' . Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED,
                ],
                [
                    [
                        'id' => 2,
                        'sku' => 'ABC123',
                        'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PARENT,
                        'qa_status' => QA_Status_Object::NOT_APPLICABLE,
                    ],
                    'When QA status is ' . QA_Status_Object::NOT_APPLICABLE . ' and it is parent SKU the reason should be: ' . Batched_SKU_Evaluation_Result::REASON_SKU_PARENT,
                ],
                [
                    [
                        'id' => 3,
                        'sku' => 'ABC123',
                        'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING,
                        'qa_status' => QA_Status_Object::PENDING_APPROVAL,
                    ],
                    'When QA status is ' . QA_Status_Object::PENDING_APPROVAL . ' the reason should be: ' . Batched_SKU_Evaluation_Result::REASON_SKU_PENDING,
                ],
                [
                    [
                        'id' => 4,
                        'sku' => 'ABC123',
                        'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_REJECTED,
                        'qa_status' => QA_Status_Object::REJECTED,
                    ],
                    'When QA status is ' . QA_Status_Object::REJECTED . ' the reason should be: ' . Batched_SKU_Evaluation_Result::REASON_SKU_REJECTED,
                ],
                [
                    [
                        'id' => 5,
                        'sku' => 'ABC123',
                        'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED,
                        'qa_status' => QA_Status_Object::NOT_APPLICABLE,
                    ],
                    'When QA status is ' . QA_Status_Object::NOT_APPLICABLE . ' the reason should be: ' . Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED,
                ],
            ];
    }

    /**
     * Tests calculate_batch_status()
     *
     * @param array  $verification_items verification_items
     * @param array  $expected           expected value
     * @param string $message            error message
     *
     * @dataProvider calculate_batch_status_data_provider
     *
     * @return void
     */
    public function test_calculate_batch_status($verification_items, $expected, $message)
    {
        $batch_models = [];


        $vi_ids = [];
        $batch_evaluation_item_models = [];
        foreach ($verification_items as $vi_id => $item) {
            $this->ticket_creation_factory
                ->create_batched_sku_evaluation_result($item['sku'], Argument::cetera(), $item['status'])
                ->willReturn(new Batched_SKU_Evaluation_Result($item['sku'], $item['reason'], $item['status'], new DateTime()));

            $batch_evaluation_item_model = $this->prophesize(Batch_Evaluation_Item_Model::class);
            $batch_evaluation_item_model->get_id()->willReturn($vi_id);
            $batch_evaluation_item_model->get_sku()->willReturn($item['sku']);
            $batch_evaluation_item_model->get_qa_status_id()->willReturn($item['status']);
            $vi_ids[] = $vi_id;
            $batch_evaluation_item_models[] = $batch_evaluation_item_model;
            /** @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku */
            $verified_batch_sku = $this->prophesize(Verified_Batch_SKU::class);
            $verified_batch_sku->is_kit_parent()->willReturn($item['reason'] === Batched_SKU_Evaluation_Result::REASON_SKU_PARENT);
            $verified_batch_sku->get_id()->willReturn($vi_id);
            $verified_batch_sku->get_sku()->willReturn($item['sku']);

            $batch_models[$item['sku']] = $verified_batch_sku->reveal();

            $this->mock_batch->get_sku($item['sku'])->willReturn($verified_batch_sku->reveal());
        }

        $this->mock_batch_evaluation_loader->load_evaluation_items_by_ids($vi_ids)
            ->willReturn($batch_evaluation_item_models);

        $this->mock_batch->models = $batch_models;

        $item = $this->subject->evaluate_batch($this->mock_batch->reveal());

        $this->assertSame($expected, $item->get_status(), $message);
    }

    /**
     * @return array
     */
    public function calculate_batch_status_data_provider()
    {
        return
            [
                [
                    [
                        1 => ['sku' => 'ABC123', 'status' => QA_Status_Object::APPROVED, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED],
                        2 => ['sku' => 'ABC456', 'status' => QA_Status_Object::NOT_APPLICABLE, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PARENT],
                    ],
                    Batch_Evaluation_Status::ready(),
                    'If all SKUs have been approved the batch evaluation should return ready',
                ],
                [
                    [
                        1 => ['sku' => 'ABC123', 'status' => QA_Status_Object::APPROVED, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED],
                        2 => ['sku' => 'ABC456', 'status' => QA_Status_Object::PENDING_APPROVAL, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING],
                        3 => ['sku' => 'ABC789', 'status' => QA_Status_Object::NOT_APPLICABLE, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED],
                    ],
                    Batch_Evaluation_Status::partial(),
                    'If some SKUs have been rejected the batch evaluation should return partial',
                ],
                [
                    [
                        1 => ['sku' => 'ABC123', 'status' => QA_Status_Object::PENDING_APPROVAL, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING],
                        2 => ['sku' => 'ABC456', 'status' => QA_Status_Object::NOT_APPLICABLE, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED],
                    ],
                    Batch_Evaluation_Status::not_ready(),
                    'If all SKUs have been rejected the batch evaluation should return not ready',
                ],
                [
                    [
                        1 => ['sku' => 'ABC123', 'status' => QA_Status_Object::REJECTED, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_REJECTED],
                        2 => ['sku' => 'ABC456', 'status' => QA_Status_Object::NOT_APPLICABLE, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED],
                    ],
                    Batch_Evaluation_Status::has_rejected(),
                    'If all SKUs have been rejected the batch evaluation should return not ready',
                ],
            ];
    }
}
