<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use WF\Shared\Helpers\Feature\Toggle;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use WF\Shared\Traits\Logging_Trait;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Curation_Production_Tracking_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_Batch_Service_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\White_Label_Batch_Already_Exists_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Exception\Production_Tracking_Status_Not_Updated_Exception;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Shared\SLO\SLOFactory;

final class Downstream_QA_Service {
  use Logging_Trait;

  const SLO_SERVICE_NAME = 'brand-workflows-curation-tool.downstream-qa-service.';

  /**
   * @var Downstream_Batch_Service_Interface
   */
  private $downstream_batch_service;

  /**
   * @var Curation_Only_Batch_Service
   */
  private $curation_only_batch_service;

  /**
   * @var Curation_Production_Tracking_Service
   */
  private $production_tracking_service;


  /**
   * @var Completion_Batch_Data_Service
   */
  private $batch_data_service;
  private SLOFactory $slo;


  /**
   * Downstream_QA_Service constructor.
   *
   * @param Downstream_Batch_Service_Interface   $downstream_batch_service    Downstream Batch Service Interface
   * @param Curation_Only_Batch_Service          $curation_only_batch_service Curation_Only Batch Service
   * @param Curation_Production_Tracking_Service $production_tracking_service Curation Production Tracking Service
   * @param Completion_Batch_Data_Service        $batch_data_service          Completion Batch Data Service
   */
  public function __construct(
      Downstream_Batch_Service_Interface   $downstream_batch_service,
      Curation_Only_Batch_Service          $curation_only_batch_service,
      Curation_Production_Tracking_Service $production_tracking_service,
      Completion_Batch_Data_Service        $batch_data_service
  ) {
    $this->downstream_batch_service    = $downstream_batch_service;
    $this->curation_only_batch_service = $curation_only_batch_service;
    $this->production_tracking_service = $production_tracking_service;
    $this->batch_data_service          = $batch_data_service;
    $this->slo                         = new SLOFactory();
  }

  /**
   * @param int $batch_id
   * @param int $batch_id
   * @return array
   */
  public function downstream_manual_batch(int $batch_id, int $emp_id): array {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    $slo->withTags(['batch_id' => $batch_id, 'employee_id' => $emp_id]);
    if ($batch_id === 0) {
      $this->error(
          'Failed to downstream batch, missing batch_id',
          ['batch_id' => $batch_id,
           'employee_id' => $emp_id
          ]
      );

      return [JsonResponse::HTTP_INTERNAL_SERVER_ERROR];
    }


    try {
      $this->info(
          'Loading batch data',
          [
              'batch_id'    => $batch_id,
              'employee_id' => $emp_id
          ]
      );
      $batch_data = $this->batch_data_service->get($batch_id);
    } catch (Throwable $exception) {
      $this->error(
          sprintf('Failed to load batch data: %s', $exception->getMessage()),
          [
              'batch_id'    => $batch_id,
              'exception'   => $exception
          ]
      );

      return [
          'result' => false,
          'errorCode' => $exception->getCode(),
          'errorMessage' => $exception->getMessage()
      ];
    }
    if(!$this->isExpectedBatchStatusTrue($batch_data->getStatus(),$batch_id,$emp_id)){
      return ['result' => false];
    }

    $slo->stop();
    return $this->downstreanbatchID($batch_data->getStatus(),$batch_id,$emp_id);
  }

  /**
   * downstreanbatchID
   * @param int $batch_status
   * @param int $batch_id
   * @param int $emp_id
   * @return array
   */
public function downstreanbatchID(int $batch_status , int $batch_id, int $emp_id): array {

  $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
  $slo->withTags(['batch_id' => $batch_id, 'employee_id' => $emp_id]);

  try {

    if ($batch_status === Batch::STATUS_MANUAL_QA_COMPLETE) {
      $this->downstream_batch_service->downstream_batch($batch_id);
      $this->info(
          sprintf('Batch batch_id=%s has been downstreamed', $batch_id),
          [
              'batch_id'    => $batch_id,
              'employee_id' => $emp_id,
          ]
      );

      $this->curation_only_batch_service->mark_as_set_downstream($batch_id);

      $this->info(
          sprintf('Batch batch_id=%s marked as downstreamed', $batch_id),
          [
              'batch_id'    => $batch_id,
              'employee_id' => $emp_id,
          ]
      );
    }

    $this->production_tracking_service->downstream_projects($batch_id);

    $this->info(
        sprintf('Batch batch_id=%s downstreamed in Product Tracking', $batch_id),
        [
            'batch_id'    => $batch_id,
            'employee_id' => $emp_id,
        ]
    );

    $slo->stop();
    return ['result' => true];
  } catch (White_Label_Batch_Already_Exists_Exception $exception) {
    $slo->stop();
    return  $this->callaftercatchwhilelabelException( $exception,$batch_id,$emp_id);

  } catch (Throwable $exception) {

 return $this->callaftercatchtrowException( $exception,$batch_id,$emp_id);

}}

  /**
   *
   * @param Throwable   $exception
   * @param int $batch_id
   * @param int $emp_id
   * @return array
   */
  public function callaftercatchtrowException(Throwable $exception , int $batch_id, int $emp_id): array {

    $this->error(
        sprintf(
            'An error occurred during Batch downstream with batch_id=%s: %s',
            $batch_id,
            $exception->getMessage()
        ),
        [
            'batch_id'    => $batch_id,
            'employee_id' => $emp_id,
            'exception'   => $exception,
        ]
    );
    return ['result' => false];
  }

  /**
   * callaftercatchwhilelabelException
   * @param White_Label_Batch_Already_Exists_Exception $exception
   * @param int $batch_id
   * @param int $emp_id
   * @return array
   */
    public function callaftercatchwhilelabelException(White_Label_Batch_Already_Exists_Exception $exception , int $batch_id, int $emp_id): array {

      $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
      $slo->withTags(['batch_id' => $batch_id, 'employee_id' => $emp_id]);

  $this->error(
      sprintf(
          'Can not downstream batch. Batch already exists in White Labeling: %s',
          $exception->getMessage()
      ),
      [
          'batch_id'    => $batch_id,
          'employee_id' => $emp_id,
          'exception'   => $exception,
      ]
  );

  $this->curation_only_batch_service->mark_as_set_downstream($batch_id);
  $this->info(
      sprintf('Batch batch_id=%s marked as downstreamed', $batch_id),
      [
          'batch_id'    => $batch_id,
          'employee_id' => $emp_id,
      ]
  );
  $this->production_tracking_service->downstream_projects($batch_id);
  $this->info(
      sprintf('Batch batch_id=%s has been downstreamed', $batch_id),
      [
          'batch_id'    => $batch_id,
          'employee_id' => $emp_id,
      ]
  );

      $slo->stop();
      return ['result' => true];
}

  /**
   * isExpectedBatchStatusTrue
   * @param int $batch_status
   * @param int $batch_id
   * @param int $emp_id
   * @return bool
   */
  public function isExpectedBatchStatusTrue(int $batch_status , int $batch_id, int $emp_id): bool {
    if (!in_array($batch_status, [Batch::STATUS_MANUAL_QA_COMPLETE, Batch::STATUS_MANUAL_DOWNSTREAMED], true)) {
      $this->info(
          sprintf('Downstream failed for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      $this->log_warning(
          'Failed to downstream batch batch_id='.$batch_id.' Unexpected batch status',
          [
              'batch_id'        => $batch_id,
              'employee_id'     => $emp_id,
              'actual_status'   => $batch_status,
              'expected_status' => [Batch::STATUS_MANUAL_QA_COMPLETE, Batch::STATUS_MANUAL_DOWNSTREAMED]
          ]
      );
      return false;
    }
    return  true;
}
}
