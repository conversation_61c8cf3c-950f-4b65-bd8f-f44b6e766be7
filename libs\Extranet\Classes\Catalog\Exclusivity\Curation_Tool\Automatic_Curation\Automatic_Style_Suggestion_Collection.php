<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Automatic_Style_Suggestion_Collection {
  /**
   * @var SKU_Style_Suggestion[]
   */
  private $sku_styles = [];

  /**
   * @param array $sku_styles Sku styles map, ordered by rank
   */
  public function __construct(array $sku_styles) {
    $this->sku_styles = $sku_styles;
  }

  /**
   * @param string $sku SKU
   *
   * @return int[]
   */
  public function get_styles_ids_for_sku(string $sku) : array {
    if (empty($this->sku_styles[$sku])) {
      return [];
    }

    return array_map(
        function (Style_Suggestion $style_suggestion) {
          return $style_suggestion->get_style_id();
        }, $this->sku_styles[$sku]->get_suggestions()
    );
  }

  /**
   * @param string $sku SKU
   *
   * @return float|null
   */
  public function get_threshold_for_sku(string $sku) : ?float {
    if (empty($this->sku_styles[$sku])) {
      return null;
    }

    return $this->sku_styles[$sku]->get_threshold();
  }

  /**
   * @param string $sku SKU
   *
   * @return Style_Suggestion[]
   */
  public function get_styles_for_sku(string $sku) : array {
    if (empty($this->sku_styles[$sku])) {
      return [];
    }

    return $this->sku_styles[$sku]->get_suggestions();
  }

  /**
   * @return string[]
   */
  public function get_skus_with_styles(): array {
    return array_map(
        function (SKU_Style_Suggestion $sku_style_suggestion) {
          return $sku_style_suggestion->get_sku();
        },
        array_filter(
            $this->sku_styles,
            function (SKU_Style_Suggestion $sku_style_suggestion) {
              return !empty($sku_style_suggestion->get_suggestions());
            }
        )
    );
  }

  /**
   * @return array
   */
  public function to_array(): array {
    $sku_styles = [];

    foreach ($this->sku_styles as $sku_style) {
      $sku       = $sku_style->get_sku();
      $threshold = $sku_style->get_threshold();

      foreach ($sku_style->get_suggestions() as $style_suggestion) {
        $sku_styles [] = [
            'sku'             => $sku,
            'threshold'       => $threshold,
            'suggestion_id'   => $style_suggestion->get_style_id(),
            'suggestion_name' => $style_suggestion->get_style_name(),
            'rank'            => $style_suggestion->get_rank(),
            'probability'     => $style_suggestion->get_probability()
        ];
      }
    }

    return $sku_styles;
  }
}
