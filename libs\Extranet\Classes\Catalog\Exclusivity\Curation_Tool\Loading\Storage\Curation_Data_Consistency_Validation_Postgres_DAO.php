<?php
/**
 * <AUTHOR>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use App\Infrastructure\Helper\PostgresBulkHelper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Result;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Storage;
use WF\Shared\Helpers\SQL;
use PDO;
class Curation_Data_Consistency_Validation_Postgres_DAO {


  /**
   * @param ProductConnection $pdo PDO
   */

  private ProductConnection $pdo;
  
  /**
   * @param PostgresConnection $pdo_psql PDO Postgres
   */

  private PostgresConnection $pdo_psql;

  /**
   * @param PostgresConnection $pdo_psql PDO Postgres
   */
  public function __construct(PostgresConnection $pdo_psql,ProductConnection $pdo){
    $this->pdo_psql = $pdo_psql;
    $this->pdo = $pdo;

  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  public function get_saved_kit_parents(int $batch_id) : array {
    $Vsku= $this->get_saved_kit_parents_internal($batch_id);
    return $this->get_saved_kit_parents_external($Vsku);
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  public function get_saved_kit_parents_internal(int $batch_id) : array {
    $sql = 'SELECT "ViSKU" AS SKU
                FROM "tblVerificationItem"
                WHERE "ViBatchID" = :batch_id
                ORDER BY "ViSKU"';
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get saved kit parents');
    }
    return $statement->fetchAll();
  }

  /**
   * @param array $Vsku Vsku
   *
   * @return string[]
   */
  public function get_saved_kit_parents_external(array $Vsku) : array {

    $sql = 'SELECT ParentSKU
                FROM csn_product.dbo.vwExclusivityKitCompositionActive
                where ParentSKU IN  ' . $this->pdo->paramsForList(count($Vsku), 'sku', SQL::nvarchar(8)) . '';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $Vsku, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get saved kit parents');
    }
    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }


  /**
   * @param int                                           $batch_id Batch ID
   * @param Curation_Data_Consistency_Validation_Result[] $results  Validation Results
   *
   * @return void
   */
  public function save_results(int $batch_id, array $results) {
    $columns = [
        '"BatchID"' => SQL::int,
        '"SKU"'     => SQL::varchar(8),
        '"Error"'   => SQL::varchar(100)
    ];

    $items = [];

    foreach ($results as $result) {
      foreach ($result->get_error_sku_map() as $sku => $error) {
        $items[] = [
            '"BatchID"' => $batch_id,
            '"SKU"'     => $sku,
            '"Error"'   => $error
        ];
      }
    }
    if(empty($items)){
      return;
    }

    // Output the result
    $sqlValuesClause =    $values = PostgresBulkHelper::convertArrayToValuesString($items);
    $sql = 'INSERT INTO "tblVerificationItemDataConsistencyLog" ('. PostgresBulkHelper::getColumns($columns,PostgresBulkHelper::INSERT_COLUMNS).') VALUES '.$sqlValuesClause.'';
    $statement = $this->pdo_psql->prepare($sql);
    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save validation results');
    }
  }

  /**
   * @param array                                           $columns columns
   * @return void
   */
  public function createtblVerificationItemDataConsistencyLog($columns)
  {

    $sql = 'CREATE TABLE IF NOT EXISTS  "tblVerificationItemDataConsistencyLog" ('. PostgresBulkHelper::getColumns($columns,PostgresBulkHelper::TABLE_COLUMNS).')';
    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot create tblVerificationItemQAStatusHistory');
    }
  }

  /**
   *
   * @return void
   */
  public function droptblVerificationItemDataConsistencyLog()
  {

    $sql = 'DROP TABLE IF EXISTS "tblVerificationItemDataConsistencyLog"';
    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot drop tblVerificationItemQAStatusHistory');
    }
  }
  /**
   *
   * @return array
   */
  public function gettblVerificationItemDataConsistencyLog(): array
  {


    $sql = 'SELECT *
            FROM "tblVerificationItemDataConsistencyLog"
            LIMIT 100;';
    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load tblVerificationItemQAStatusHistory');
    }
    return $statement->fetchAll();
  }



  /**
   * @param array $skus
   * @description Used to get skus available in tblVerificationItem table in postgres db
   * @return array
   */
  public function get_data_available_from_verification_item_table(array $skus) : array {
    $in  = str_repeat('?,', count($skus) - 1) . '?';
    $sql = "SELECT * FROM \"tblVerificationItem\" WHERE \"ViSKU\" IN ($in)";
    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute($skus)) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
      $this->log_throwable_error($exception, $exception->getMessage());

      throw $exception;
    }

    return $statement->fetchAll();
  }

}