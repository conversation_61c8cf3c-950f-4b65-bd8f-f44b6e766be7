<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch;

use App\Infrastructure\Connection\DatabaseConstantsInterface;
use App\Infrastructure\Connection\Driver\PDOConnection;
use App\Infrastructure\Connection\InternalToolsConnection;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Curation\Api\Curation\TalentApi;
use PDO;
use WF\Shared\Helpers\SQL;

class Curator_Loader_DAO {

  private InternalToolsConnection $pdo_internal_tools;
  private TalentApi $talentApi;

  /**
   * Curator_Loader_DAO constructor.
   *
   * @param InternalToolsConnection $pdo_internal_tools PDO
   * @param TalentApi $talentApi
   */
  public function __construct(InternalToolsConnection $pdo_internal_tools, TalentApi $talentApi) {
    $this->pdo_internal_tools = $pdo_internal_tools;
    $this->talentApi = $talentApi;
  }

  /**
   * @deprecated Most likely not used
   *
   * @param string $curator_list_ara Value of Curator list ARA
   *
   * @return array
   */
  public function get_curators(string $curator_list_ara) : array {
    $sql = '
      DROP TABLE IF EXISTS #ara_employees
      CREATE TABLE #ara_employees (
        Employee_EmID INT,
        Employee_EmFirstName NVARCHAR(30),
        Employee_EmLastName NVARCHAR(30),
        EmWgID INT,
        WgName VARCHAR(32),
        TltRnkID SMALLINT,
        RnkLevel SMALLINT,
        EtpID TINYINT, -- Employee type
        EtpName VARCHAR(50),
        Manager_EmID INT,
        Manager_EmFirstName NVARCHAR(30),
        Manager_EmLastName NVARCHAR(30),
        IsOverride TINYINT,
        ResourceHasGroup TINYINT,
        ResourceHasLevel TINYINT,
        ResourceHasType TINYINT
      );

      INSERT INTO #ara_employees
          EXEC csn_hr.dbo.spARAResourceAccess @resource_name = :curators_ara;

      SELECT Employee_EmID,
             Employee_EmFirstName,
             Employee_EmLastName,
             WgName, Manager_EmFirstName,
             Manager_EmLastName,
             EmWgID,
             TltRnkID,
             RnkLevel,
             EtpID,
             EtpName,
             Manager_EmID,
             ResourceHasGroup,
             ResourceHasLevel,
             ResourceHasType,
             MAX(IsOverride)
      FROM #ara_employees
      GROUP BY Employee_EmID,
               Employee_EmFirstName,
               Employee_EmLastName,
               WgName,
               Manager_EmFirstName,
               Manager_EmLastName,
               EmWgID,
               TltRnkID,
               RnkLevel,
               EtpID,
               EtpName,
               Manager_EmID,
               ResourceHasGroup,
               ResourceHasLevel,
               ResourceHasType;
    ';

    $statement = $this->pdo_internal_tools->prepare($sql);
    $statement->bindValue(':curators_ara', $curator_list_ara, PDO::PARAM_STR);

    if ($statement->execute() === false) {
      throw new ExecutionException(sprintf('Cannot load curator list: %s', implode(',', $statement->errorInfo())));
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $em_id Employee ID
   *
   * @return array
   */
  public function get_curator(int $em_id) : array {
    return $this->get_curator_logic($em_id);
  }

  /**
   * @deprecated this is original method, but InternalTools connection was deprecated for this app
   * should be removed after successful PROD testing
   *
   * @param int $em_id Employee ID
   *
   * @return array
   */
  public function get_curator_legacy(int $em_id) : array {
    return $this->get_curator_logic($em_id);
  }

  /**
   * @param int $em_id Employee ID
   *
   * @return array
   * @throws \Exception
   */
  private function get_curator_logic(int $em_id) : array {
    try  {
      $result = [];
      $data = $this->talentApi->getEmployeeDetails($em_id);
      $result['employee_id'] = $data->id;
      $result['first_name'] = $data->firstName;
      $result['last_name'] = $data->lastName;
      $result['email_address'] = $data->email;
      return $result;
    } catch (\Exception $exception) {
      throw new ExecutionException(sprintf('Cannot load curator list: %s', $exception->getMessage()));
    }

  }

}
