<?php
/**
 * Shared file between api entry end point file(public/index.php) and commands (bin/console)
 * that execute common bootstrap tasks
 */

require dirname(__DIR__) . '/vendor/autoload.php';

$_SERVER              += $_ENV;
$_SERVER['APP_ENV']   = $_ENV['APP_ENV'] = ($_SERVER['APP_ENV'] ?? $_ENV['APP_ENV'] ?? null) ?? 'prod';
$_SERVER['APP_DEBUG'] = $_SERVER['APP_DEBUG'] ?? $_ENV['APP_DEBUG'] ?? 'prod' !== $_SERVER['APP_ENV'];
$_SERVER['APP_DEBUG'] = $_ENV['APP_DEBUG'] = ((int)$_SERVER['APP_DEBUG'] === 1) || filter_var($_SERVER['APP_DEBUG'], FILTER_VALIDATE_BOOLEAN) ? '1' : '0';
