<?php

declare(strict_types=1);
/**
 * Static Helper to take an array of data (array or models) and generate SQL insert/select statements
 *
 * The reason for building was to generate an XML string that could be bound for insert statements along with the XML parsing nodes
 * From the XML a user could generate a temp table, insert directly into a table, select the data, or return a CTE
 *
 * Knowing we want to move to JSON in the future it was named SQL_Bulk_Helper
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Helper;

use Exception;

use function array_map;
use function array_slice;
use function explode;
use function filter_var;
use function htmlspecialchars;
use function implode;
use function is_array;
use function is_bool;
use function is_null;
use function is_string;
use function preg_match;

use function sprintf;
use const FILTER_DEFAULT;
use const PHP_EOL;

class SQLBulkHelper
{
    public const DEFAULT_XML_PARAM = 'xml';
    public const DEFAULT_JSON_PARAM = 'json';
    public const DEFAULT_CTE_NAME = 'cteXML';
    public const DEFAULT_JSON_NAME = 'cteJSON';
    public const XML_COLUMNS = 1;
    public const SELECT_COLUMNS = 2;
    public const INSERT_COLUMNS = 3;
    public const TABLE_COLUMNS = 4;
    public const JSON_COLUMNS = 5;
    public const JSON_SELECT_COLUMNS = 6;

    /**
     * This will generate the SQL necessary to select back from an OPENJSON query. It requires a flat json object
     * Hopefully this will be used with ODBC so placeholders will get translated at that level
     * I do know others like to optimize a query so the SQL Returned uses the PDO :
     *
     * @param string[] $columnMap the map of column name to type
     * @param string $jsonPlaceholderName the json param name (:json)
     *
     * @return string
     * @throws \Exception
     */
    protected static function getSelectJsonSql(
        array $columnMap,
        string $jsonPlaceholderName = self::DEFAULT_JSON_PARAM
    ): string {
        self::validateParamName($jsonPlaceholderName);
        $tableAlias = 't';
        $selectColumns = self::getColumns($columnMap, self::JSON_SELECT_COLUMNS, $tableAlias);
        $jsonColumns = self::getColumns(
            array_map(
                static function (string $columnDefinition) {
                    return explode(
                        ' ',
                        $columnDefinition
                    )[0]; // Extra filtering for unsupported in OPENJSON syntax like "DEFAULT/NULL/NOT NULL"
                },
                $columnMap
            ),
            self::JSON_COLUMNS
        );
        return sprintf(
            '        
            SELECT %s
            FROM OPENJSON (:%s)
            WITH (%s) %s
            ',
            $selectColumns,
            $jsonPlaceholderName,
            $jsonColumns,
            $tableAlias,
        );
    }

    /**
     * Generates a Temp Table using JSON
     *
     * Ideally a user will build this temp table using a named placeholder like :json
     *
     * There is also a version of this that will declare the table as a variable
     * instead of as a temp table. See the discussion on performance implications.
     *
     * @param string[] $columnMap The column names and types
     * @param string $tableName Temp table name
     * @param string $jsonPlaceholderName The name of the PDO Placeholder
     *
     * @return string the SQL to generate a temp table
     * @throws \Exception
     */
    public static function getTempTableJsonSql(
        array $columnMap,
        string $tableName,
        string $jsonPlaceholderName = self::DEFAULT_JSON_PARAM
    ): string {
        self::validateParamName($tableName);
        self::validateParamName($jsonPlaceholderName);

        $tableColumns = self::getColumns($columnMap, self::TABLE_COLUMNS);
        $insertColumns = self::getColumns($columnMap, self::INSERT_COLUMNS);
        $jsonSelect = self::getSelectJsonSql($columnMap, $jsonPlaceholderName);
        $dropSql = self::getTempTableDropSql($tableName);
        return sprintf(
            '
            %s        
            CREATE TABLE #%s (
            %s        
            )
            INSERT INTO #%s (
            %s        
            )
            %s
            ',
            $dropSql,
            $tableName,
            $tableColumns,
            $tableName,
            $insertColumns,
            $jsonSelect,
        );
    }

    /**
     * Generates sql to drop a temp table if it exists
     *
     * @param string $tableName Temp table name
     *
     * @return string the SQL to drop a temp table
     * @throws \Exception
     */
    protected static function getTempTableDropSql(string $tableName): string
    {
        self::validateParamName($tableName);
        // Note: The official name of tempdb is in lowercase, and this is required when sys.databases.name
        // has a case sensitive collation.
        return sprintf("IF OBJECT_ID('tempdb.dbo.#%s') IS NOT NULL DROP TABLE #%s", $tableName, $tableName);
    }

    /**
     * This will go through our column map to build the necessary string to select/extract columns
     *
     * @param string[] $columnMap the column names and types
     * @param int $columnType the type of columns we want to get back | XML, SELECT, INSERT,  or TABLE
     * @param string|null $tableAlias If not null, alias the select columns with this
     *
     * @return string of columns
     * @throws \Exception
     */
    public static function getColumns(
        array $columnMap,
        int $columnType = self::XML_COLUMNS,
        ?string $tableAlias = null
    ): string {
        $columns = [];
        foreach ($columnMap as $columnName => $sqlType) {
            switch ($columnType) {
                case self::XML_COLUMNS:
                    $columns[] = sprintf("rec.value('(@%s)[1]', '%s' ) AS %s", $columnName, $sqlType, $columnName);
                    break;
                case self::SELECT_COLUMNS:
                case self::INSERT_COLUMNS:
                    $columns[] = $columnName;
                    break;
                case self::TABLE_COLUMNS:
                    $columns[] = $columnName . ' ' . $sqlType;
                    break;
                case self::JSON_SELECT_COLUMNS:
                    $columnName = array_slice(explode('.', $columnName), -1)[0];
                    if (!is_null($tableAlias) && $tableAlias !== '') {
                        $columns[] = $tableAlias . '.' . $columnName;
                    } else {
                        $columns[] = $columnName;
                    }
                    break;
                case self::JSON_COLUMNS:
                    $_column_name = array_slice(explode('.', $columnName), -1)[0];
                    $columns[] = sprintf("%s %s '$.%s'", $_column_name, $sqlType, $columnName);
                    break;
                default:
                    throw new Exception('Invalid Column Type Request');
            }
        }
        return implode(PHP_EOL . ', ', $columns);
    }

    /**
     * This will make sure our param only has letters, numbers, and underscores. If not an exception is thrown
     *
     * @param string $paramString the param string
     *
     * @return void
     * @throws \Exception
     */
    protected static function validateParamName(string $paramString): void
    {
        // if we match any things NOT in the pattern that is invalid
        if ($paramString === '' || preg_match('/[^A-Za-z0-9_]/', $paramString)) {
            // TODO: This should be changed to a less generic Validation_Exception instead
            throw new Exception('Param Names must be alphanumeric including underscore [^A-Za-z0-9_]');
        }
    }


    /**
     * This will generate the SQL necessary to select back from XML nodes. Note you will have to BIND the xml to the param using PARAM_STR.
     * The second param here will be the name of the pdo placeholder as well as the XML variable we select from
     *
     * @param string[] $columnMap the map of column name to type
     * @param string $xmlParamName the xml name to extract from | default 'xml'
     *
     * @return string
     * @throws \Exception
     *
     */
    public static function getSelectXmlSql(array $columnMap, string $xmlParamName = self::DEFAULT_XML_PARAM)
    {
        self::validateParamName($xmlParamName);
        $xmlColumns = self::getColumns($columnMap);
        return sprintf(
            "
            SELECT %s        
            FROM @%s.nodes( '/record' ) AS t(rec)
            ",
            $xmlColumns,
            $xmlParamName
        );
    }

    /**
     * This will create a declare statement for binding xml.
     *
     * @param string $xmlParamName the name of the SQL xml param | defaults to 'xml'
     * @param string|null $xmlBindValue the name of the SQL xml bindValue | defaults to xml_param_name
     *
     * @return string
     */
    public static function getXmlParamBinding(
        string $xmlParamName = self::DEFAULT_XML_PARAM,
        ?string $xmlBindValue = null
    ): string {
        if ($xmlBindValue === null) {
            $xmlBindValue = $xmlParamName;
        }
        self::validateParamName($xmlParamName);
        self::validateParamName($xmlBindValue);
        return sprintf(
            'DECLARE @%s XML = :%s;',
            $xmlParamName,
            $xmlBindValue
        );
    }

    /**
     * Function to take an array and generate and XML Object where they are ELEMENTS
     *
     * @param array $valuesArray the array of value strings, arrays, or models. If values are strings, XML elements are returned with the value in the "value" attribute.
     * @param string[] $columnMap the column map of named values | properties to the SQL data type
     *                               Note the SQL datatable and not PDO data type. Required when $values_array is composed of arrays or models
     * @param array[] $cleaningMap A key value of column name to run a filter VAR against the variable http://php.net/manual/en/filter.filters.sanitize.php
     *                               The map should looks like $col_filter = [
     *                               'test_money' => [
     *                               'filter_id' => FILTER_SANITIZE_NUMBER_FLOAT,
     *                               'filter_flags' => FILTER_FLAG_ALLOW_FRACTION | FILTER_FLAG_ALLOW_THOUSAND | FILTER_FLAG_ALLOW_SCIENTIFIC
     *                               ],
     *                               ];
     *
     * @return string
     * @throws \Exception
     */
    public static function getXmlAsString(array $valuesArray, array $columnMap = [], array $cleaningMap = []): string
    {
        // Get the XML .
        $xml = '';
        foreach ($valuesArray as $row) {
            $xml .= '<record';

            if (is_string($row)) {
                $xml .= ' value="' . htmlspecialchars($row) . '"';
            }

            // Now we need to go through our column type map IN order
            foreach ($columnMap as $columnName => $columnDbType) {
                if (is_string($row)) {
                    // Column mapping is meaningless for strings
                    break;
                }

                // Get the column from the record. If the row is an array get the column otherwise select the property from the object
                // @phpstan-ignore-next-line
                $value = (is_array($row) ? ($row[$columnName] ?? null) : ($row->$columnName ?? null));
                // for XML if the value provided is (bool) false is will result in an empty value rather than bool. that is php for ya
                if (is_bool($value)) {
                    // convert true/false to 1 and 0. if a user provides the string of 'false' that will not be caught here and we will pass 'false' to the DB
                    // that is fine as our DB will parse true and false as well as 'true' and 'false' if you give is 'falser' it will error out
                    $value = $value === true ? 1 : 0;
                }

                // for XML to insert a null value we would have to exclude the element from this record
                if ($value !== null) {
                    // IF we are here we have a non null value
                    // Check if this column has a cleaning flag
                    // if the user set the column BUT passed no value we use the default
                    // I could call the filter var array but that would require all our data to be an array (i.e. no objects in an array)
                    // It also would have us looping through the array multiple times so this way we loop once
                    if (isset($cleaningMap[$columnName])) {
                        // get the filter otherwise default. Default is actully NOTHING. users might want this to skip the html special chars we normally do
                        $filterId = $cleaningMap[$columnName]['filter_id'] ?? FILTER_DEFAULT;
                        // if the user provided flags set that here. all filters SHOULD accept the flag array if they have options
                        $value = filter_var(
                            $value,
                            $filterId,
                            ['flags' => $cleaningMap[$columnName]['filter_flags'] ?? []]
                        );
                    } else {
                        // This simulates our htmlspecialchars and we will encode quotes as well
                        $value = htmlspecialchars($value);
                    }
                    $xml .= ' ' . $columnName . '="' . $value . '"';
                }
            }
            $xml .= '/>';
        }
        return $xml;
    }


    /**
     * This will generate a very simple CTE that the user can further use in an insert or select. Note that a CTE must be used in the next sql statement
     *
     * @param string[] $columnMap the column names and types
     * @param string $cteName the name of the CTE if the user wants to declare multiple
     * @param string|null $xmlBindValue the name of the SQL xml bindValue | defaults to cte_name
     *
     * @return string the SQL to generate a cte
     * @throws \Exception
     */
    public static function getCteXmlSql(
        array $columnMap,
        string $cteName = self::DEFAULT_CTE_NAME,
        ?string $xmlBindValue = null
    ): string {
        self::validateParamName($cteName);
        $xmlDeclare = self::getXmlParamBinding($cteName, $xmlBindValue);
        $xmlSelect = self::getSelectXmlSql($columnMap, $cteName);
        return sprintf(
            '
            %s;
            WITH %s AS (
            %s        
            )
            ',
            $xmlDeclare,
            $cteName,
            $xmlSelect
        );
    }


    /**
     * This will generate temp table sql based on the colum map which we can use with the get_select_xml_sql to build a temp table. This will define the XML variable
     * This is the only helper of the helper methods (i.e. it will include the declare XML, the create table, the insert and select from the xml nodes
     *
     * There were other things...like using as a CTE but there is no need for that. Perhaps if/when someone wants to join on this table they could use CTE but we can add that in later IF someone does it
     *
     * @param string[] $columnMap The column names and types
     * @param string $tableName Temp table name
     * @param string $xmlParamName The name of the SQL xml param | defaults to 'xml'
     * @param string|null $xmlBindValue the name of the SQL xml bindValue | defaults to xml_param_name
     *
     * @return string the SQL to generate a temp table
     * @throws \Exception
     */
    public static function getTempTableXmlSql(
        array $columnMap,
        string $tableName,
        string $xmlParamName = self::DEFAULT_XML_PARAM,
        ?string $xmlBindValue = null
    ) {
        self::validateParamName($tableName);

        $xml_declare = self::getXmlParamBinding($xmlParamName, $xmlBindValue);
        $table_columns = self::getColumns($columnMap, self::TABLE_COLUMNS);
        $insert_columns = self::getColumns($columnMap, self::INSERT_COLUMNS);
        $xml_select = self::getSelectXmlSql($columnMap, $xmlParamName);
        $drop_sql = self::getTempTableDropSql($tableName);

        return sprintf(
            '
            %s
            %s
            CREATE TABLE #%s (
            %s        
            )
            INSERT INTO #%s (
            %s        
            )
            %s
            ',
            $xml_declare,
            $drop_sql,
            $tableName,
            $table_columns,
            $tableName,
            $insert_columns,
            $xml_select
        );
    }
}
