<?php
/**
 * Model representing a Curation Request SKU
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

use WF\Shared\Models\Product_Model;

abstract class Curation_Request_SKU_Base_Model {

  const COLUMN_ID                                = 'id';
  const COLUMN_REQUEST_ID                        = 'request_id';
  const COLUMN_IS_MASTER_CORE_CLASS              = 'is_master_core_class';
  const COLUMN_IS_HOLDOUT_MANUFACTURER           = 'is_holdout_manufacturer';
  const COLUMN_IS_WAYFAIR_CHANNEL                = 'is_wayfair_channel';
  const COLUMN_IS_STANDARD_BRAND                 = 'is_standard_brand';
  const COLUMN_BRAND_TYPE                        = 'brand_type';
  const COLUMN_IS_ACTIVE_JOIN_SUPPLIER           = 'is_active_join_supplier';
  const COLUMN_ELIGIBILITY_STATUS                = 'eligibility_status';
  const COLUMN_IS_ELIGIBLE                       = 'is_eligible';
  const COLUMN_PRODUCT_STATUS                    = 'product_status';
  const COLUMN_READY_FOR_CURATION_ID             = 'ready_for_curation_id';
  const COLUMN_IS_RIGHT_PR_STATUS                = 'is_right_product_status';
  const COLUMN_IRP_ACTIVE                        = 'irp_active';
  const COLUMN_IRO_ACTIVE                        = 'iro_active';
  const COLUMN_BRAND_CATALOG_ID                  = 'brand_catalog_id';
  const COLUMN_PR_KIT                            = 'is_parent_kit';
  const COLUMN_IS_KIT_PR_KIT                     = 'is_kit_pr_sku';
  const COLUMN_IS_RIGHT_ASSIGNED_SUPPLIER_METHOD = 'is_right_assigned_supplier_method';
  const COLUMN_IS_PERIGOLD_ONLY                  = 'is_perigold_only';
  const COLUMN_HAS_IMAGES                        = 'has_images';
  const COLUMN_UPDATED_DATE                      = 'updated_date';
  const COLUMN_IMPORT_STATUS_ID                  = 'import_status_id';
  const COLUMN_IMPORT_DATE                       = 'import_date';
  const COLUMN_VERIFICATION_ITEM_ID              = 'verification_item_id';
  const COLUMN_HAS_HOLDOUT_MANUFACTURER_PART     = 'has_holdout_manufacturer_part';
  const COLUMN_IS_EU                             = 'is_eu';
  const COLUMN_CLASS_ID                          = 'class_id';
  const COLUMN_IS_EXCEEDING_PRICE_CEILING        = 'is_exceeding_price_ceiling';
  const COLUMN_PRICE_MISSING_COUNT               = 'price_missing_count';

  const READY_FOR_CURATION_NOT_APPLICABLE = 0;
  const READY_FOR_CURATION_YES            = 1;
  const READY_FOR_CURATION_NO             = 2;

  const MANUFACTURER_STANDARD_BRAND = 0;
  const MANUFACTURER_LEGACY_BRAND   = 3;

  const IMPORT_STATUS_NOT_APPLICABLE = 0;
  const IMPORT_STATUS_AWAITING       = 1;
  const IMPORT_STATUS_COMPLETE       = 2;
  const IMPORT_STATUS_SKIPPED        = 3;

  const PERIGOLD_FLEX_GROUP_ID = 18226;

  const NOT_ELIGIBLE               = 0;
  const ELIGIBLE_FOR_CURATION      = 1;
  const ELIGIBLE_FOR_STYLE_TAGGING = 2;

  const DEFAULT_MAX_PRICE_MISSING_COUNT = 1000;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container
   */
  protected $requirement_container;

  /**
   * @var int
   */
  protected $id;

  /**
   * @var int
   */
  protected $request_id;

  /**
   * @var string
   */
  protected $sku;

  /**
   * @var int
   */
  protected $brand_catalog_id;

  /**
   * @var int
   */
  protected $eligibility_status;

  /**
   * @var bool
   */
  protected $is_eligible = false;

  /**
   * @var int|null
   */
  protected $collection_id;

  /**
   * @var int|null
   */
  protected $class_id;

  /**
   * @var bool|integer|null
   */
  protected $is_master_core_class;

  /**
   * @var bool|null
   */
  protected $is_holdout_manufacturer;

  /**
   * @var bool
   */
  protected $is_wayfair_channel;

  /**
   * @var bool
   */
  protected $is_standard_brand;

  /**
   * @var bool
   */
  protected $is_active_join_supplier;

  /**
   * @var int
   */
  protected $ready_for_curation_id;

  /**
   * @var int|null
   */
  protected $product_status;

  /**
   * @var bool
   */
  protected $is_right_product_status;

  /**
   * @var bool|null
   */
  protected $has_images;

  /**
   * @var string
   */
  protected $updated_date;

  /**
   * @var int
   */
  protected $import_status_id;

  /**
   * @var string
   */
  protected $import_date;

  /**
   * @var bool
   */
  protected $is_kit_pr_sku;

  /**
   * @var bool
   */
  protected $is_right_assigned_supplier_method;

  /**
   * @var bool|null
   */
  protected $is_perigold_only;

  /**
   * @var int|null
   */
  protected $verification_item_id;

  /**
   * @var bool|null
   */
  protected $has_holdout_manufacturer_part;

  /**
   * @var bool|null
   */
  protected $is_eu;

  /**
   * @var bool|null
   */
  protected $is_exceeding_price_ceiling;

  /**
   * @var int|null
   */
  protected $price_missing_count;

  /**
   * @var  string
   */
  protected string $excludedReasonMessage;

  /**
   * @return int|null
   */
  public function get_id() {
    return $this->id;
  }

  /**
   * @return int
   */
  public function get_request_id() : int {
    return (int)$this->request_id; /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return int
   */
  public function get_eligibility_status() : int {
    return $this->eligibility_status;
  }

  /**
   * @return bool
   */
  public function is_eligible() : bool {
    return $this->is_eligible;
  }

  /**
   * @return bool|null
   */
  public function is_master_core_class() {
    return $this->requirement_container->get_master_core_class_requirement()->is_master_core_class($this->is_master_core_class);
  }

  /**
   * @return bool|null
   */
  public function is_holdout_manufacturer() {
    return $this->requirement_container->get_holdout_manufacturer_requirement()->is_holdout_manufacturer($this->is_holdout_manufacturer);
  }

  /**
   * @return bool|null
   */
  public function is_eu() {
    return $this->is_eu;
  }

  /**
   * @return bool
   */
  public function is_wayfair_channel() {
    return $this->is_wayfair_channel;
  }

  /**
   * @return bool
   */
  public function is_standard_brand() {
    return $this->is_standard_brand;
  }

  /**
   * @return null|bool
   */
  public function is_active_join_supplier() {
    return $this->is_active_join_supplier;
  }

  /**
   * @return null|bool
   */
  public function is_kit_pr_sku() {
    return $this->is_kit_pr_sku;
  }

  /**
   * @return bool
   */
  public function is_right_assigned_supplier_method() {
    return $this->is_right_assigned_supplier_method;
  }

  /**
   * @return int
   */
  public function ready_for_curation_id() : int {
    return $this->ready_for_curation_id;
  }

  /**
   * @return bool
   */
  public function is_right_product_status() : bool {
    return (bool) $this->is_right_product_status; /** @phpstan-ignore-line */
  }

  /**
   * @return bool|null
   */
  public function has_images() {
    return $this->has_images;
  }

  /**
   * @param string $date Date
   *
   * @return void
   */
  public function set_updated_date($date) {
    $this->updated_date = $date;
  }

  /**
   * @return string
   */
  public function get_updated_date() {
    return $this->updated_date;
  }

  /**
   * @param int $status_id Status ID
   *
   * @return void
   */
  public function set_import_status_id(int $status_id) {
    $this->import_status_id = $status_id;
  }

  /**
   * @return int
   */
  public function get_import_status_id() : int {
    return $this->import_status_id;
  }

  /**
   * @param string $date Date
   *
   * @return void
   */
  public function set_import_date($date) {
    $this->import_date = $date;
  }

  /**
   * @return string
   */
  public function get_import_date() {
    return $this->import_date;
  }

  /**
   * @return bool|null
   */
  public function is_perigold_only() {
    return $this->requirement_container->get_perigold_only_requirement()->is_perigold_only($this->is_perigold_only);
  }

  /**
   * @param bool|null $is_perigold_only IsPerigoldOnly
   *
   * @return void
   */
  public function set_perigold_only($is_perigold_only) {
    $this->is_perigold_only = $is_perigold_only;
  }

  /**
   * @return bool|null
   */
  public function has_holdout_manufacturer_part() {
    return $this->requirement_container->get_holdout_manufacturer_part_requirement()->has_holdout_manufacturer_part($this->has_holdout_manufacturer_part);
  }

  /**
   * @param int $brand_type Brand Type
   *
   * @return bool
   */
  public function calculate_standard_brand(int $brand_type) : bool {
    return in_array($brand_type, [self::MANUFACTURER_STANDARD_BRAND, self::MANUFACTURER_LEGACY_BRAND]);
  }

  /**
   * @return bool
   */
  protected function calculate_right_product_status() : bool {
    return $this->requirement_container->get_right_product_status_requirement()->is_right_product_status($this);
  }

  /**
   * Tickcet: 9745081
   * This function will be returning:
   * -True if the SKU has at least 1 image
   * -False if no images
   * -NULL if no images but it is a KIt component
   *
   * @param bool $is_irp_active  Image resource product active
   * @param bool $is_iro_active  Image resource option Active
   * @param int  $product_status PrStatus from db
   *
   * @return bool|null
   */
  protected function calculate_has_images(bool $is_irp_active, bool $is_iro_active, int $product_status) {
    if ($is_irp_active || $is_iro_active) {
      return true;
    } elseif ($product_status === Product_Model::STATUS_KIT_COMPONENT) {
      return null;
    }

    return false;
  }

  /**
   * @return int
   */
  public function calculate_eligible() : int {
    $prelim_eligibility = $this->requirement_container->get_eligibility_requirement()->is_eligible($this);
    if (!$prelim_eligibility) {
      return self::NOT_ELIGIBLE;
    }

    if ($this->is_holdout_manufacturer()) {
      return self::ELIGIBLE_FOR_STYLE_TAGGING;
    }

    return self::ELIGIBLE_FOR_CURATION;
  }

  /**
   * @return int
   */
  public function calculate_ready_for_curation_id() : int {
    if ($this->get_eligibility_status() === self::NOT_ELIGIBLE) {
      return self::READY_FOR_CURATION_NOT_APPLICABLE;
    }

    if ($this->is_right_product_status() && $this->has_images() !== false) {
      return self::READY_FOR_CURATION_YES;
    }

    return self::READY_FOR_CURATION_NO;
  }

  /**
   * @return int
   */
  public function calculate_import_status_id() : int {
    if ($this->ready_for_curation_id() !== self::READY_FOR_CURATION_YES) {
      return self::IMPORT_STATUS_NOT_APPLICABLE;
    }

    return self::IMPORT_STATUS_AWAITING;
  }

  /**
   * @return bool
   */
  public function reevaluate_product_status() : bool {
    $current = $this->is_right_product_status;

    $this->is_right_product_status = $this->calculate_right_product_status();
    $this->ready_for_curation_id   = $this->calculate_ready_for_curation_id();

    return $current !== $this->is_right_product_status;
  }

  /**
   * @param bool $is_irp_active  Is image resource product active
   * @param bool $is_iro_active  Is image resource option active
   * @param int  $product_status PrStatus from db
   *
   * @return bool
   */
  public function reevaluate_images(bool $is_irp_active, bool $is_iro_active, int $product_status) : bool {
    $current = $this->has_images;

    $this->has_images            = $this->calculate_has_images($is_irp_active, $is_iro_active, $product_status);
    $this->ready_for_curation_id = $this->calculate_ready_for_curation_id();

    return $current !== $this->has_images;
  }

  /**
   * @param float $sale_price              Price from Alexeia API
   * @param int   $max_price_missing_count Max price missing count allowed
   *
   * @return bool
   */
  public function reevaluate_price_availability(float $sale_price, int $max_price_missing_count) : bool {
    $current = $this->price_missing_count;
    $this->price_missing_count   = $this->calculate_price_missing_count($sale_price, $max_price_missing_count);
    $this->ready_for_curation_id = $this->calculate_ready_for_curation_id();

    return $current !== $this->price_missing_count;
  }

  /**
   * @param bool|integer|null $is_master_core_class Is master core class
   *
   * @return void
   */
  public function set_master_core_class($is_master_core_class) {
    $this->is_master_core_class = $is_master_core_class;
  }

  /**
   * @param bool|null $is_holdout_manufacturer Is holdout manufacturer
   *
   * @return void
   */
  public function set_holdout_manufacturer($is_holdout_manufacturer) {
    $this->is_holdout_manufacturer = $is_holdout_manufacturer;
  }

  /**
   * @param bool|null $is_wayfair_channel Is Wayfair channel
   *
   * @return void
   */
  public function set_is_wayfair_channel($is_wayfair_channel) {
    $this->is_wayfair_channel = $is_wayfair_channel;
  }

  /**
   * @param bool|null $is_standard_brand Is standard brand
   *
   * @return void
   */
  public function set_standard_brand($is_standard_brand) {
    $this->is_standard_brand = $is_standard_brand;
  }

  /**
   * @param bool|null $is_active_join_supplier Is active join supplier
   *
   * @return void
   */
  public function set_active_join_supplier($is_active_join_supplier) {
    $this->is_active_join_supplier = $is_active_join_supplier;
  }

  /**
   * @param bool|null $is_kit_pr_sku Is KitPrSKU
   *
   * @return void
   */
  public function set_kit_pr_sku($is_kit_pr_sku) {
    $this->is_kit_pr_sku = $is_kit_pr_sku;
  }

  /**
   * @param bool|null $is_right_assigned_supplier_method Is right assigned supplier method
   *
   * @return void
   */
  public function set_right_assigned_supplier_method($is_right_assigned_supplier_method) {
    $this->is_right_assigned_supplier_method = $is_right_assigned_supplier_method;
  }

  /**
   * @return int|null
   */
  public function get_product_status() {
    return $this->product_status;
  }

  /**
   * @param int|null $product_status Product Status
   *
   * @return void
   */
  public function set_product_status($product_status) {
    $this->product_status = $product_status;
  }

  /**
   * @return int|null
   */
  public function get_verification_item_id() {
    return $this->verification_item_id;
  }

  /**
   * @param int|null $verification_item_id VerificationItemID
   *
   * @return void
   */
  public function set_verification_item_id(int $verification_item_id = null) {
    $this->verification_item_id = $verification_item_id;
  }

  /**
   * @param bool|null $has_holdout_manufacturer_part Has holdout manufacturer part
   *
   * @return void
   */
  public function set_holdout_manufacturer_part($has_holdout_manufacturer_part) {
    $this->has_holdout_manufacturer_part = $has_holdout_manufacturer_part;
  }

  /**
   * @param int|null $class_id class ID
   *
   * @return void
   */
  public function set_class_id($class_id) {
    $this->class_id = $class_id;
  }

  /**
   * @return int|null
   */
  public function get_class_id() {
    return $this->class_id;
  }

  /**
   * @param int|null $collection_id collection ID
   *
   * @return void
   */
  public function set_collection_id($collection_id) {
    $this->collection_id = $collection_id;
  }

  /**
   * @return int|null
   */
  public function get_collection_id() {
    return $this->collection_id;
  }

  /**
   * @param int|null $brand_catalog_id Brand Catalog ID
   *
   * @return void
   */
  public function set_brand_catalog_id($brand_catalog_id) {
    $this->brand_catalog_id = $brand_catalog_id;
  }

  /**
   * @return int|null
   */
  public function get_brand_catalog_id() {
    return $this->brand_catalog_id;
  }

  /**
   * @param int $eligibility_status Eligibility status
   *
   * @return void
   */
  public function set_eligibility_status(int $eligibility_status) {
    $this->eligibility_status = $eligibility_status;
  }

  /**
   * @param bool $exceeded_price_ceiling Price ceiling check
   *
   * @return void
   */
  public function set_is_exceeding_price_ceiling(bool $exceeded_price_ceiling) : void {
    $this->is_exceeding_price_ceiling = $exceeded_price_ceiling;
  }

  /**
   * @return bool
   */
  public function is_exceeding_price_ceiling() : bool {
    return $this->is_exceeding_price_ceiling ?? false;
  }

  /**
   * @param int $price_missing_count Current price missing count
   *
   * @return void
   */
  public function set_pricing_missing_count(int $price_missing_count) {
    $this->price_missing_count  = $price_missing_count ;
  }

  /**
   * @return int|null
   */
  public function get_pricing_missing_count() {
    return $this->price_missing_count;
  }

  /**
   * @param float $sale_price              Price from Alexeia API
   * @param int   $max_price_missing_count Price missing count max allowed
   *
   * @return int
   */
  protected function calculate_price_missing_count(float $sale_price, int $max_price_missing_count = self::DEFAULT_MAX_PRICE_MISSING_COUNT) : int {
    if (!isset($this->price_missing_count)) {
      $this->price_missing_count = 0;
    }

    // give up if price unavailability is larger than set value
    if ($this->price_missing_count >= $max_price_missing_count) {
      return 0;
    }

    // price not available when it's 0
    if (empty($sale_price)) {
      return ++$this->price_missing_count;
    }

    // price available now
    $this->price_missing_count = 0;
    return $this->price_missing_count;
  }

  /**
   * @return string
   */
  public function get_sku_excluded_reasons() : string {
    $this->excludedReasonMessage = "";

    if (is_null($this->is_master_core_class()) || !$this->is_master_core_class()) {
      $this->excludedReasonMessage .= "Is Not a Master Class. ";
    }

    if (is_null($this->is_standard_brand()) || !$this->is_standard_brand()) {
        $this->excludedReasonMessage .= "Is Not in standard brand. ";
    }

    if (is_null($this->has_images()) || !$this->has_images()) {
        $this->excludedReasonMessage .= "Has no images. ";
    }

    if (!is_null($this->is_holdout_manufacturer()) && $this->is_holdout_manufacturer()) {
      $this->excludedReasonMessage .= "Is Holdout Manufacturer. ";
    }

    if (!$this->is_wayfair_channel()) {
      $this->excludedReasonMessage .= "Is Not in Wayfair Channel. ";
    }

    if (!$this->is_active_join_supplier()) {
      $this->excludedReasonMessage .= "Is Not an Active Join Supplier. ";
    }

    if (!$this->is_right_assigned_supplier_method()) {
      $this->excludedReasonMessage .= "Is Not Right Assigned Supplier Method. ";
    }

    if (!is_null($this->is_perigold_only()) && $this->is_perigold_only()) {
      $this->excludedReasonMessage .= "Is Perigold Only. ";
    }

    if (!is_null($this->has_holdout_manufacturer_part()) && $this->has_holdout_manufacturer_part()) {
      $this->excludedReasonMessage .= "Has Holdout Manufacturer Part. ";
    }

    return $this->excludedReasonMessage;
  }

}
