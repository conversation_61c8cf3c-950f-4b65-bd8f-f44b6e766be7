<?php

declare(strict_types=1);

namespace App\Application\Helper;

use function pathinfo;

final class ContentTypeHelper implements ContentTypeHelperInterface
{
    private const TEXT_HTML = 'text/html';
    private const IMAGE_JPEG = 'image/jpeg';
    private const APPLICATION_POSTSCRIPT = 'application/postscript';

    private const EXTENSIONS_TO_CONTENT_TYPES = [
        'txt' => 'text/plain',
        'htm' => self::TEXT_HTML,
        'html' => self::TEXT_HTML,
        'php' => self::TEXT_HTML,
        'css' => 'text/css',
        'js' => 'application/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'swf' => 'application/x-shockwave-flash',
        'flv' => 'video/x-flv',

        // images
        'png' => 'image/png',
        'jpe' => self::IMAGE_JPEG,
        'jpeg' => self::IMAGE_JPEG,
        'jpg' => self::IMAGE_JPEG,
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'ico' => 'image/x-icon',
        'tiff' => 'image/tiff',
        'tif' => 'image/tiff',
        'svg' => 'image/svg+xml',
        'svgz' => 'image/svg+xml',

        // archives
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        'exe' => 'application/x-msdownload',
        'msi' => 'application/x-msdownload',
        'cab' => 'application/vnd.ms-cab-compressed',

        // audio/video
        'mp3' => 'audio/mpeg',
        'qt' => 'video/quicktime',
        'mov' => 'video/quicktime',

        // adobe
        'pdf' => 'application/pdf',
        'psd' => 'image/vnd.adobe.photoshop',
        'ai' => self::APPLICATION_POSTSCRIPT,
        'eps' => self::APPLICATION_POSTSCRIPT,
        'ps' => self::APPLICATION_POSTSCRIPT,

        // ms office
        'doc' => 'application/msword',
        'rtf' => 'application/rtf',
        'xls' => 'application/vnd.ms-excel',
        'ppt' => 'application/vnd.ms-powerpoint',

        // open office
        'odt' => 'application/vnd.oasis.opendocument.text',
        'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
    ];

    /**
     * @param string $fileName .
     *
     * @return null|string
     */
    public function getContentType(string $fileName): ?string
    {
        $pathParts = pathinfo($fileName);

        return self::EXTENSIONS_TO_CONTENT_TYPES[$pathParts['extension'] ?? ''] ?? null;
    }
}
