<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage;

use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Helpers\SQL;
use PDO;

class Batch_Evaluation_Tracker_Postgres_DAO {
  use Logging_Trait;

  public const BATCH_SIZE = 200;

  private PostgresConnection $pdo_psql;

  /**
   * Batch_Evaluation_Tracker_DAO constructor.
   *
   * @param PostgresConnection $pdo    PDO
   * @param LoggerInterface|null $logger Logger
   */
  public function __construct(PostgresConnection $pdo_psql, ?LoggerInterface $logger = null) {
    $this->pdo_psql = $pdo_psql;
    $this->logger = $logger;
  }

  /**
   * @param array $sku_list SKU List
   *
   * @return array
   */
  public function get_evaluation_records(array $sku_list) : array {
    $this->log_info('Loading evaluating records', ['skus' => $sku_list]);
    $result = [];
    $sku_chunks = array_chunk($sku_list, self::BATCH_SIZE);
    foreach ($sku_chunks as $chunk) {
      $in  = str_repeat('?,', count($chunk) - 1) . '?';
      $sql = "
      SELECT
        \"ID\",
        \"SKU\",
        \"FirstCheckAt\",
        \"LastCheckAt\",
        \"ReadyDateAt\"
      FROM \"tblCurationSkuEvaluation\"
      WHERE \"SKU\" IN ($in)
      ";

      $statement = $this->pdo_psql->prepare($sql);

      if (!$statement->execute($chunk)) {
        $exception = ExecutionException::forStatement($statement, 'Cannot get curation sku evaluation - ' . implode(';', $statement->errorInfo()));
        $this->log_throwable_error($exception, $exception->getMessage(), ['skus' => $sku_list]);

        throw $exception;
      }

      $result = array_merge($result, $statement->fetchAll());
    }

    return $result;
  }

  /**
   * @param string      $sku            SKU
   * @param string      $first_check_at First Check Date
   * @param string      $last_check_at  Last Check Date
   * @param string|null $ready_date     Ready Date
   *
   * @return void
   */
  public function insert(string $sku, string $first_check_at, string $last_check_at, string $ready_date = null) {
    $this->log_info('Inserting evaluating records', ['sku' => $sku]);

    $sql = '
    INSERT INTO "tblCurationSkuEvaluation" (
      "SKU",
      "FirstCheckAt",
      "LastCheckAt",
      "ReadyDateAt")
      VALUES (
      :sku,
      :first_check_at,
      :last_check_at,
      :ready_date_at
      )
    ';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    $statement->bindValue(':first_check_at', $first_check_at, PDO::PARAM_STR);
    $statement->bindValue(':last_check_at', $last_check_at, PDO::PARAM_STR);
    $statement->bindValue(':ready_date_at', $ready_date, PDO::PARAM_STR);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement(
          $statement,
          'Cannot insert record into curation sku evaluation table - ' . implode(';', $statement->errorInfo())
      );

      $this->log_throwable_error($exception, $exception->getMessage(), ['sku' => $sku]);

      throw $exception;
    }
  }

  /**
   * @param int         $id            ID
   * @param string      $last_check_at Last Check Date
   * @param string|null $ready_date    Ready Date
   *
   * @return void
   */
  public function update(int $id, string $last_check_at, string $ready_date = null) {
    $this->log_info('Updating evaluating record', ['id' => $id]);

    $sql = '
      UPDATE "tblCurationSkuEvaluation"
      SET
          "LastCheckAt" = :last_check_at,
          "ReadyDateAt" = :ready_date_at
      WHERE "ID" = :id
    ';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':last_check_at', $last_check_at, PDO::PARAM_STR);
    $statement->bindValue(':ready_date_at', $ready_date, PDO::PARAM_STR);
    $statement->bindValue(':id', $id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement(
          $statement,
          'Cannot update record in curation sku evaluation table - ' . implode(';', $statement->errorInfo())
      );
      $this->log_throwable_error($exception, $exception->getMessage(), ['evaluation_id' => $id]);

      throw $exception;
    }
  }
}
