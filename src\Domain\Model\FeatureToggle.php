<?php

declare(strict_types=1);

namespace App\Domain\Model;

abstract class FeatureToggle
{
    public const MTBW_ENABLE_DECOUPLED_CURATION_TOOL = 'mtbw_enable_decoupled_curation_tool';
    public const MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST = 'mtbw_enable_decoupled_curation_tool_parallel_test';
    public const CURATION_DATA_DECOUPLING_CODEBASE_SWITCH = 'curation_data_decoupling_codebase_switch';
    public const CURATION_DATA_DECOUPLING_BATCH_MGMT = 'curation_tool_decoupling_postgresql_access';
}
