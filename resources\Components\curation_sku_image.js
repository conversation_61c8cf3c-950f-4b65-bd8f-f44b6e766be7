/**
 * Curation SKU Image with popup
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {APP_ROOT_PATH} from '../Application/Constants'

import {
  Card,
  Box,
  ImageThumbnail,
  Modal,
  Button,
  Link,
  ZoomAndPan,
  Image,
  Loading,
  SelectableCard
} from '@wayfair/homebase-extranet';
import {
  getUrl,
  RESIZE,
  HEIGHT,
  WIDTH,
  THUMBNAIL,
  IMAGE_NOT_FOUND_IREID,
} from '@wayfair/image-url-utils';
import './curation_sku_image.scss';
import {createWretch} from '@wayfair/framework-request-utils';
import {imageContext} from '@wayfair/homebase-provider';
import CarouselImage from "./CarouselImage";

const transactionId = btoa(Math.random());
const wretch = createWretch({
  txid: transactionId,
  canClearCache: true,
});

const TABLEVIEW_SIDE_SIZE = 204;
const THUMBNAIL_SIDE_SIZE = 102;
const PREVIEW_SIDE_SIZE = 600;
const FULL_SIDE_SIZE = 1024;

const thumbnailSizeCommand = {
  [THUMBNAIL]: {
    [HEIGHT]: THUMBNAIL_SIDE_SIZE,
    [WIDTH]: THUMBNAIL_SIDE_SIZE,
  },
};

const tableViewSizeCommand = {
  [THUMBNAIL]: {
    [HEIGHT]: TABLEVIEW_SIDE_SIZE,
    [WIDTH]: TABLEVIEW_SIDE_SIZE,
  },
};

const previewSizeCommand = {
  [RESIZE]: {
    [HEIGHT]: PREVIEW_SIDE_SIZE,
    [WIDTH]: PREVIEW_SIDE_SIZE,
  },
};

const fullSizeCommand = {
  [THUMBNAIL]: {
    [HEIGHT]: FULL_SIDE_SIZE,
    [WIDTH]: FULL_SIDE_SIZE,
  },
};

const INITIAL_STATE = {
  open: false,
  selectedImageId: null,
  images: [],
};

const IMAGE_LOAD_URL = APP_ROOT_PATH + '/get_images';

class CurationSkuImage extends React.PureComponent {
  static propTypes = {
    sku: PropTypes.string.isRequired,
    image: PropTypes.string.isRequired,
  };

  state = INITIAL_STATE;

  openModal = () => {
    this.setState({open, images: [], selectedImageId: null});

    wretch(`${IMAGE_LOAD_URL}?sku=${this.props.sku}`)
      .get()
      .json(({images}) => {
        if (images.length < 1) {
          this.setState({
            open,
            selectedImageId: IMAGE_NOT_FOUND_IREID,
            images: [IMAGE_NOT_FOUND_IREID],
          });
          return;
        }

        this.setState({
          open,
          selectedImageId: images[0],
          images,
        });
      });
  };

  closeModal = () => {
    this.setState(INITIAL_STATE);
    this.setState({
      zoom:1,
    });
  }

  selectImage = selectedImageId => {
    this.setState({
      selectedImageId,
      zoom:1,
    });
  };

  isImageSelected = imageId => imageId === this.state.selectedImageId;

  imagesLoaded = () => {
    return this.state.images.length > 0;
  };
  handleZoomChange = zoom => {
    this.setState({zoom});
  };


  render() {
    const {imageKey, cdnUrl} = this.context;

    return (
      <div className="text_center">
        <CarouselImage sku={this.props.sku} image={this.props.image} openModal={this.openModal}/>
        <Button text onClick={this.openModal}>
          View larger
        </Button>

        {this.state.open && (
          <Modal
            overlayClose
            isOpen={this.state.open}
            onRequestClose={this.closeModal}
            onSecondaryClose={this.closeModal}
            maxWidth="90%"
          >
            {this.imagesLoaded() ? (
              <div className="CurationSkuImage-container">
                <Box mr="16px">
                    <Card showStatusIcon={false}>
                      <ImageThumbnail size={PREVIEW_SIDE_SIZE}>
                        <ZoomAndPan
                          zoom={this.state.zoom}
                          onZoomChange={this.handleZoomChange}
                          imageUrl={getUrl({
                            imageId: this.state.selectedImageId,
                            commands: previewSizeCommand,
                            imageKey,
                            cdnUrl,
                          })}
                          imageTitle={this.props.sku}
                        />
                      </ImageThumbnail>
                    </Card>
                </Box>
                <div className="CurationSkuImage-thumbnailList">
                  {this.state.images.map(imageId => (
                    <Box
                      key={imageId}
                      mr="4px"
                      mb="4px"
                    >
                      <SelectableCard
                        variation="radio"
                        checked={this.isImageSelected(imageId)}
                        onChange={() => this.selectImage(imageId)}
                      >
                        <div className="text_center">
                          <Box
                            alt={this.props.sku}
                            is="img"
                            src={getUrl({
                              imageId,
                              commands: thumbnailSizeCommand,
                              imageKey,
                              cdnUrl,
                            })}
                            display="block"
                            ml="auto"
                            mr="auto"
                          />
                        </div>
                      </SelectableCard>
                    </Box>
                  ))}
                </div>
              </div>
            ) : (
              <Loading size="large" />
            )}
          </Modal>
        )}
      </div>
    );
  }
}

CurationSkuImage.contextType = imageContext;

export default CurationSkuImage;
