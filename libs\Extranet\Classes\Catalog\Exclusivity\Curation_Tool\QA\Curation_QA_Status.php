<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Domain\Enum\AbstractEnumeration;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;

final class Curation_QA_Status extends AbstractEnumeration {
  /**
   * @var int
   */
  private static $not_applicable = QA_Status_Object::NOT_APPLICABLE;

  /**
   * @var int
   */
  private static $pending = QA_Status_Object::PENDING_APPROVAL;

  /**
   * @var int
   */
  private static $approved = QA_Status_Object::APPROVED;

  /**
   * @var int
   */
  private static $rejected = QA_Status_Object::REJECTED;

  /**
   * @var int
   */
  private static $mixed = QA_Status_Object::MIXED;

  /**
   * @var int
   */
  private static $updated = QA_Status_Object::UPDATED;

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function not_applicable() : self {
    return self::get_value_for_option(self::$not_applicable); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function pending() : self {
    return self::get_value_for_option(self::$pending); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function approved() : self {
    return self::get_value_for_option(self::$approved); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function rejected() : self {
    return self::get_value_for_option(self::$rejected); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function mixed() : self {
    return self::get_value_for_option(self::$mixed); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public static function updated() : self {
    return self::get_value_for_option(self::$updated); /** @phpstan-ignore-line */
  }

  /**
   * Creates a value allowed in the enumeration from a string
   *
   * Every enumeration defines the list of allowed options by implementing get_allowed_options
   *
   * @param int $option the option from the database for the corresponding value
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   *
   * @throws \InvalidArgumentException
   */
  public static function create($option) : self {
    if (empty($option)) {
      return self::not_applicable();
    }

    if (!in_array($option, self::get_allowed_options())) {
      throw new \InvalidArgumentException(
          sprintf(
              'Value %s is not supported QA Status',
              $option
          )
      );
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * @return array
   */
  private static function get_allowed_options() : array {
    return [
        self::$not_applicable,
        self::$pending,
        self::$approved,
        self::$rejected,
        self::$mixed,
        self::$updated,
    ];
  }
}
