<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Domain\Enum;

abstract class AbstractEnumeration
{
    /**
     * @var AbstractEnumeration[]
     */
    private static array $storage = [];

    /**
     * @var mixed
     */
    private $value;

    /**
     * @param mixed $value the wrapped value
     */
    final private function __construct($value)
    {
        $this->value = $value;
    }

    /**
     * @param mixed $option option for wrapping
     *
     * @return AbstractEnumeration
     */
    protected static function get_value_for_option($option): self
    {
        $storage_key = static::class . $option;
        if (!isset(self::$storage[$storage_key])) {
            self::$storage[$storage_key] = static::create_value($option);
        }

        return self::$storage[$storage_key];
    }

    /**
     * @param mixed $option option for the value object creation
     *
     * @return AbstractEnumeration
     */
    protected static function create_value($option): self
    {
        return new static($option);
    }

    /**
     * @param AbstractEnumeration $that the object for comparison
     *
     * @return bool
     */
    public function equals(AbstractEnumeration $that): bool
    {
        return $that instanceof static
            && $this->value() === $that->value();
    }

    /**
     * @return mixed
     */
    public function value()
    {
        return $this->value;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string)$this->value;
    }
}
