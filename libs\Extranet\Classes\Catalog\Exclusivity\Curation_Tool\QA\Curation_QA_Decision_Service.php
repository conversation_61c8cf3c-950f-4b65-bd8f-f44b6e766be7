<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage\Curation_QA_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Curation_QA_Decision_Service
{
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage
     */
    private $storage;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO
     */
    private $dao_psql;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage\Curation_QA_Postgres_DAO
   */
    private $dao_qa_psql;

    private FeatureTogglesInterface $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
     */
    private $batch_data_service;

    private $style;

    private $subStyle;

    private array $styleArray = [];


    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer
     */
    private $replacer;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated;
     */
    private $notify_curation_updated;

    /**
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service $batch_data_service Batch data service
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage $storage storage
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer $replacer replacer
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated $notify_curation_updated notify curation updated
     */
    public function __construct(
        Completion_Batch_Data_Service         $batch_data_service,
        Curation_Decision_DAO                 $dao,
        Curation_QA_Decision_Storage          $storage,
        Curation_QA_Kit_Parent_Replacer       $replacer,
        Completion_QA_Notify_Curation_Updated $notify_curation_updated,
        Curation_Decision_Postgres_DAO        $dao_psql,
        FeatureTogglesInterface               $featureToggles,
        Curation_QA_Postgres_DAO              $dao_qa_psql
    )
    {
        $this->batch_data_service = $batch_data_service;
        $this->dao = $dao;
        $this->storage = $storage;
        $this->replacer = $replacer;
        $this->notify_curation_updated = $notify_curation_updated;
        $this->dao_psql = $dao_psql;
        $this->featureToggles = $featureToggles;
        $this->dao_qa_psql = $dao_qa_psql;

    }

    /**
     * @param int $batch_id the batch id
     * @param array $skus skus to approve
     * @param int $employee_id the identifier of the approver
     *
     * @return void
     */
    public function approve(int $batch_id, array $skus, int $employee_id): void
    {
        $without_kits = $this->replacer->replace_kit_parents_with_children($batch_id, $skus);
        $decision = Curation_QA_Decision::approve($batch_id, $without_kits, $employee_id);
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_qa_psql->save_decision($decision);
        } else {
          $this->storage->save_decision($decision);
        }
    }

    /**
     * @param int $batch_id the batch id
     * @param array $skus skus to approve
     * @param int $employee_id the identifier of the rejecter
     * @param string $reason the reason to reject
     *
     * @return void
     */
    public function reject(int $batch_id, array $skus, int $employee_id, string $reason): void
    {
        $without_kits = $this->replacer->replace_kit_parents_with_children($batch_id, $skus);
        $decision = Curation_QA_Decision::reject($batch_id, $without_kits, $employee_id, $reason);

      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $this->dao_qa_psql->save_decision($decision);
      } else {
        $this->storage->save_decision($decision);
      }
    }

    /**
     * @param int $batch_id the batch id
     * @param array $skus skus to approve
     * @param int $employee_id the identifier of the rejecter
     * @param int $price_tier_override new data set for sku
     * @param int $final_style_id new data set for sku
     * @param int $final_substyle_id new data set for sku
     * @param int $final_brand_id new data set for sku
     *
     * @return void
     */

    public function update(
        int   $batch_id,
        array $skus,
        int   $employee_id,
        int   $price_tier_override,
        int   $final_style_id,
        int   $final_substyle_id,
        int   $final_brand_id
    )
    {

       // calling before update;
        if (count($skus) > 1) {
          if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            $batchInfos = $this->dao_psql->getBatchinfoList($skus, $batch_id);
          } else {
            $batchInfos = $this->dao->getBatchinfoList($skus, $batch_id);
          }
            $tempobj = new \stdClass();
            foreach ($batchInfos as $key => $value) {
                $tempobj->sku = $value['ViSKU'];
                if (!is_null($value['ViFinalStyleID'])) {
                  if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                    $Styles = $this->dao_psql->getStleByeId($value['ViFinalStyleID']);
                  } else {
                    $Styles = $this->dao->getStleByeId($value['ViFinalStyleID']);
                  }
                  $tempobj->style = $Styles->VsName;
                }
                if (!is_null($value['ViFinalSubStyleID'])) {
                    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                      $SubStyles = $this->dao_psql->getSubStleByID($value['ViFinalSubStyleID']);
                    } else {
                      $SubStyles = $this->dao->getSubStleByID($value['ViFinalSubStyleID']);
                    }
                    $tempobj->subStyle = $SubStyles->VssName;
                }
                array_push($this->styleArray, $tempobj);
            }

        } else {
          if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            $batchInfo = $this->dao_psql->getBatchinfo($skus, $batch_id);
          } else {
            $batchInfo = $this->dao->getBatchinfo($skus, $batch_id);
          }
            if (!is_null($batchInfo)) {
              if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                if(!empty($batchInfo->ViFinalStyleID)) {
                  $this->style = $this->dao_psql->getStleByeId($batchInfo->ViFinalStyleID);
                }
                if(!empty($batchInfo->ViFinalSubStyleID)) {
                  $this->subStyle = $this->dao_psql->getSubStleByID($batchInfo->ViFinalSubStyleID);
                }
              } else {
                  if(!is_null($batchInfo->ViFinalStyleID) && !is_null($batchInfo->ViFinalSubStyleID)) {
                      $this->style = $this->dao->getStleByeId($batchInfo->ViFinalStyleID);
                      $this->subStyle = $this->dao->getSubStleByID($batchInfo->ViFinalSubStyleID);
                  }
              }
            }
        }
        $without_kits = $this->replacer->replace_kit_parents_with_children($batch_id, $skus);
        $decision = Curation_QA_Decision::update(
            $batch_id,
            $without_kits,
            $employee_id,
            $price_tier_override,
            $final_style_id,
            $final_substyle_id,
            $final_brand_id
        );

      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $this->dao_qa_psql->save_decision_and_styles($decision);
      } else {
        $this->storage->save_decision_and_styles($decision);
      }
        $batch_data = $this->batch_data_service->get($batch_id);
        if($batch_data->getProcessTypeID() === 1) {
            if ($final_style_id !== 0 || $final_substyle_id !== 0) {
              if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                $updatedStyle    = $this->dao_psql->getStleByeId($final_style_id);
                $updatedSubStyle = $this->dao_psql->getSubStleByID($final_substyle_id);
              } else {
                $updatedStyle    = $this->dao->getStleByeId($final_style_id);
                $updatedSubStyle = $this->dao->getSubStleByID($final_substyle_id);
              }
                if (count($skus) > 1) {
                    $this->notify_curation_updated->sendMailForBulkData($batch_id, $this->styleArray, $updatedStyle, $updatedSubStyle, $skus, $batch_data->getAssignedEmployeeId(), $employee_id);
                } else {
                    $this->notify_curation_updated->send($batch_id, $this->style, $this->subStyle, $updatedStyle, $updatedSubStyle, $skus, $batch_data->getAssignedEmployeeId() ?? 0, $employee_id,);
                }
            }
        }
    }

    /**
     * @param int $batch_id the batch id
     * @param array $skus skus to approve
     * @param int $employee_id the identifier of the rejecter
     * @param int $exclusion_reason reason to exclude
     *
     * @return void
     */
    public function excludeFromWL(
        int   $batch_id,
        array $skus,
        int   $employee_id,
        int   $exclusion_reason
    ): void
    {
        $without_kits = $this->replacer->replace_kit_parents_with_children($batch_id, $skus);
        $decision = Curation_QA_Decision::excludeFromWL(
            $batch_id,
            $without_kits,
            $employee_id,
            $exclusion_reason
        );
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $this->dao_qa_psql->save_decision_and_exclude_from_wl($decision);
      } else {
        $this->storage->save_decision_and_exclude_from_wl($decision);
      }
    }

    /**
     * @param array $skus skus to check
     * @param int $batch_id batch id
     *
     * @return bool true if ok to save
     */
    public function is_ready_to_save(array $skus, int $batch_id): bool
    {
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        return $this->dao_qa_psql->check_if_ready_to_save($skus, $batch_id);
      } else {
        return $this->storage->check_if_ready_to_save($skus, $batch_id);
      }
    }
}

