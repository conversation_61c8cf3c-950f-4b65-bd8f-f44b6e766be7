<?php
/**
 * The DAO for the Curation Request
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Source_Model;
use WF\Shared\Models\Store_Model;
use PDO;

use function count;

class Curation_Request_DAO {
  private const JOIN_LIST_PRODUCT_METHOD             = 1;

  const FLAG_CURATION_REQUEST_MANUAL_TYPE_ACTIVE = true;

  private const BATCH_SIZE = 500;

  private ProductConnection $pdo;

  /**
   * Curation_Request_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * @param int $request_id Curation Request ID
   *
   * @return array
   */
  public function get_skus(int $request_id) : array {
    $sql = '
            SELECT
              ID AS id,
              RequestID AS request_id,
              SKU AS sku,
              EligibilityStatus AS eligibility_status,
              IsMasterCoreClass AS is_master_core_class,
              IsHoldoutManufacturer AS is_holdout_manufacturer,
              IsWayfairChannel AS is_wayfair_channel,
              IsStandardBrand AS is_standard_brand,
              IsActiveJoinSupplier AS is_active_join_supplier,
              ReadyForCurationID AS ready_for_curation_id,
              IsRightPrStatus AS is_right_product_status,
              HasImages AS has_images,
              UpdatedDate AS updated_date,
              ImportStatusID AS import_status_id,
              ImportDate AS import_date,
              IsKitPrSku AS is_kit_pr_sku,
              IsRightAssignedSupplierMethod AS is_right_assigned_supplier_method,
              IsPerigoldOnly AS is_perigold_only,
              VerificationItemID AS verification_item_id,
              HasHoldoutManufacturerPart AS has_holdout_manufacturer_part,
              IsEu AS is_eu,
              IsExceedingPriceCeiling as is_exceeding_price_ceiling,
              PriceMissingCount as price_missing_count,
              product.PrStatus AS product_status
            FROM csn_product.dbo.tblCurationRequestSKU AS request_sku WITH (NOLOCK)
            JOIN csn_product.dbo.tblProduct AS product WITH (NOLOCK) ON product.PrSKU = request_sku.SKU
            WHERE RequestID = :request_id
            ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':request_id', $request_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot load SKUs for the request - ' . $request_id);
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $sku_list SKU List
   *
   * @return array
   */
  public function get_skus_data($sku_list) : array {
    $result = [];

    $sku_chunks = array_chunk($sku_list, self::BATCH_SIZE);

    foreach ($sku_chunks as $chunk) {
      $sql = '
      SELECT
        p.PrSKU AS sku,
        p.PrBclgID AS brand_catalog_id,
        ISNULL(gblClass.ClCoreClass, 0) AS is_master_core_class,
        m.MaBrwID AS brand_type,
        prc.PrChannelID AS is_wayfair_channel,
        mwl.NoWhiteLabel AS is_holdout_manufacturer,
        joinSupplier.isActiveJoinSupplier AS is_active_join_supplier,
        IIF(p.PrAssignSupplierMethod = :assign_supplier_method, 1, 0) AS is_right_assigned_supplier_method,
        COALESCE(joinKit.isKit, 0) AS is_parent_kit,
        p.PrStatus AS product_status,
        COALESCE(perigoldInfo.IsPerigoldOnly, 0) AS is_perigold_only,
        COALESCE(manufacturerPartExclusion.HasManufacturerPartHoldout, 0) AS has_holdout_manufacturer_part,
        image_info.irp_active,
        image_info.iro_active,
        jpc.ClID AS class_id,
        is_eu = 
          CASE p.PrBclgID 
            WHEN 2 THEN 1 
            WHEN 3 THEN 1 
            ELSE 0 
          END 
      FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
      JOIN csn_product.dbo.tblManufacturer m WITH (NOLOCK) ON m.MaID = p.PrMaID
      JOIN csn_product.dbo.tblManufacturerWhiteLabel mwl WITH (NOLOCK) ON mwl.MaID = p.PrMaID
      LEFT JOIN csn_product.dbo.tbljoinProductClass jpc WITH (NOLOCK) ON jpc.PrSKU = p.PrSKU AND jpc.PcMasterClass = 1
      OUTER APPLY (
                SELECT ClCoreClass FROM csn_product_global.dbo.fnGblClass(p.PrBclgID) c
                WHERE c.ClID = jpc.ClID
              ) AS gblClass
      LEFT JOIN csn_product.dbo.tbljoinProductChannel prc WITH (NOLOCK) ON prc.PrSKU = p.PrSKU AND prc.PrChannelID = 1
      OUTER APPLY (
                    SELECT TOP 1 1 AS isActiveJoinSupplier
                    FROM csn_product.dbo.tblSupplierPart sp WITH (NOLOCK)
                    WHERE EXISTS(
                      SELECT TOP 1 1 
                      FROM csn_product.dbo.tblSupplierExt WITH(NOLOCK) 
                      INNER JOIN csn_product.dbo.tblOptionCombination op WITH(NOLOCK) ON op.ManufacturerPartID = sp.ManufacturerPartID
                      WHERE SupplierID = SuID AND p.PrSKU = op.PrSKU
                    )
                  ) AS joinSupplier
      OUTER APPLY (
          SELECT TOP 1 1 isKit
          FROM csn_product.dbo.vwExclusivityKitComposition AS kc WITH (NOLOCK)
          WHERE kc.ParentSKU = p.PrSKU
      ) joinKit
      OUTER APPLY (
        SELECT
        COUNT(NULLIF(irp.IrpActive,0)) AS irp_active, -- aggregate bits (nulls add 0)
        COUNT(NULLIF(iro.IroActive,0)) AS iro_active -- aggregate bits (nulls add 0)
        FROM csn_product.dbo.tblProduct AS p_images WITH(NOLOCK)
        LEFT JOIN csn_product.dbo.tbljoinImageResourceProduct AS irp WITH(NOLOCK)
          ON p.PrSKU = irp.irpPrSKU AND irp.IrpActive = 1
        LEFT JOIN csn_product.dbo.tblJoinImageResourceProductOptionGridFileGroup AS iro WITH(NOLOCK)
          ON p.PrSKU = iro.IroPrSKU AND iro.IroActive = 1
        WHERE p_images.PrSKU = p.PrSKU
        ) AS image_info
      OUTER APPLY (
        SELECT TOP 1 1 AS IsPerigoldOnly FROM csn_product.dbo.tblProduct AS p_perigold WITH(NOLOCK)
        LEFT JOIN csn_product.dbo.tblStoreExclusiveManufacturer AS m WITH(NOLOCK) ON m.SemMaID = p_perigold.PrMaID AND m.SemSoID = :perigold_store_id -- Perigold-only manufacturers
        LEFT JOIN csn_product.dbo.tbljoinProductFlexGroup AS fpg WITH(NOLOCK) ON fpg.PrSKU = p_perigold.prSKU AND fpg.FpgID = :perigold_flex_group_id -- Perigold Exclude from WF
        WHERE p_perigold.PrSKU = p.PrSKU AND (m.SemMaID IS NOT NULL OR FPG.PrSKU IS NOT NULL)
      ) AS perigoldInfo
      OUTER APPLY(
        SELECT TOP 1 1 AS HasManufacturerPartHoldout
        FROM csn_product.dbo.tblOptionCombination oc WITH (NOLOCK)
        JOIN csn_product.dbo.tblManufacturerPartEBExclusion eb_exclusion WITH (NOLOCK) ON eb_exclusion.ManufacturerPartID = oc.ManufacturerPartID
        WHERE oc.PrSKU = p.PrSKU
      ) manufacturerPartExclusion
      WHERE p.PrSKU IN (' . $this->pdo->paramsForList(count($chunk), 'sku_list', SQL::nvarchar(8)) . ')
    ';

      $statement = $this->pdo->prepare($sql);
      $statement->bindValuesList(':sku_list', $chunk, SQL::nvarchar(8));
      $statement->bindValue(':perigold_store_id', Store_Model::PERIGOLD_ID, PDO::PARAM_INT);
      $statement->bindValue(':perigold_flex_group_id', Curation_Request_SKU_Source_Model::PERIGOLD_FLEX_GROUP_ID, PDO::PARAM_INT);
      $statement->bindValue(':assign_supplier_method', self::JOIN_LIST_PRODUCT_METHOD, PDO::PARAM_INT);

      if (!$statement->execute()) {
        throw ExecutionException::forStatement($statement, 'Cannot get SKUs data');
      }

      $result = array_merge($result, $statement->fetchAll());
    }

    return $result;
  }
}
