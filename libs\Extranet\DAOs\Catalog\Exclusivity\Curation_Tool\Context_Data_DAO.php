<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use Psr\Log\LoggerInterface;
use PDO;
use WF\Shared\Traits\Logging_Trait;

class Context_Data_DAO
{
    use Logging_Trait;

    private ProductConnection $pdo;

    /**
     * @param ProductConnection $pdo PDO
     * @param \Psr\Log\LoggerInterface|null $logger Logger
     */
    public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null)
    {
        $this->pdo = $pdo;
        $this->logger = $logger;
    }


    /**
     * @return  array
     * only queries the curation table
     * to get source-id from tblVerificationItem based on the sku & batch id
     */
    public function get_verification_source_id_by_sku_and_batch(array $skus, int $batch_id, int $curated_max_days_ago): array
    {
        $sql = 'SELECT         
         vi.ViSKU AS sku ,
         vi.ViSourceID AS source_id ,
          recently_curated.ViFinalBrandMaID AS recently_curated_manufacturer_id
        FROM csn_product.dbo.tblVerificationItem vi WITH (NOLOCK) 
        /** outer apply */
              OUTER APPLY (
                  SELECT TOP 1 ViFinalBrandMaID
                  FROM csn_product.dbo.tblVerificationItem viRecent WITH (NOLOCK)
                  WHERE viRecent.ViSKU = vi.ViSKU  AND viRecent.ViFinalBrandMaID > 0 
                      AND DATEDIFF(DAY, viRecent.ViLockedDate, GETDATE()) < :curated_max_days_ago 
                      AND viRecent.ViBatchID != :batch_id
                  ORDER BY ViID DESC
            ) recently_curated
        /** outer apply end */
         WHERE  vi.ViBatchID =:batch_id2 AND vi.ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));
        $statement = $this->pdo->prepare($sql);
        $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
        $statement->bindValue(':batch_id2', $batch_id, PDO::PARAM_INT);
        $statement->bindValue(':curated_max_days_ago', $curated_max_days_ago, PDO::PARAM_INT);
        $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
        if (!$statement->execute()) {
            $exception = ExecutionException::forStatement($statement, 'Failed to load context data for skus');
            $this->log_throwable_error($exception, $exception->getMessage(), ['batch_id' => $batch_id, 'skus' => $skus]);
            throw $exception;
        }
        return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
    }


    /**
     * @param int $batch_id Batch ID
     * @param array $skus the SKUs to look data for
     * @param int $curated_max_days_ago Curated Max Days Ago
     *
     * @return array
     * Merges the results from non-curation tables & curation tables
     */
    public function get_context_skus_manufacturer_data(int $batch_id, array $skus, int $curated_max_days_ago): array
    {
        if (empty($skus)) {
            return [];
        }
        $this->log_info('Loading context manufacturers data', ['batch_id' => $batch_id, 'skus' => $skus]);
        // calling query on  non-Curation  tables

        $context_skus = $this->get_context_skus_manufacturer_data_ext($batch_id, $skus, $curated_max_days_ago);
        // calling query on curation tables
        $src_skus = $this->get_verification_source_id_by_sku_and_batch($skus, $batch_id, $curated_max_days_ago);
        $context_skus1 = array();
        foreach ($context_skus as $row) {
            if (count($src_skus) > 0) {
                foreach ($src_skus as $sku => $row1) {
                    if ($row['sku'] == $sku) {
                        $context_skus1[$sku]['manufacturer_id'] = $row['manufacturer_id'];
                        $context_skus1[$sku]['ma_brw_id'] = $row['ma_brw_id'];
                        $context_skus1[$sku]['source_id'] = $row1['source_id'];
                        $context_skus1[$sku]['recently_curated_manufacturer_id'] = $row1['recently_curated_manufacturer_id'];
                        $context_skus1[$sku]['price_tier'] = $row['price_tier'];
                    } else {
                        $context_skus1[$row['sku']]['manufacturer_id'] = $row['manufacturer_id'];
                        $context_skus1[$row['sku']]['ma_brw_id'] = $row['ma_brw_id'];
                        $context_skus1[$row['sku']]['source_id'] = null;
                        $context_skus1[$row['sku']]['recently_curated_manufacturer_id'] = null;
                        $context_skus1[$row['sku']]['price_tier'] = $row['price_tier'];
                    }
                }
            } else {
                $context_skus1[$row['sku']]['manufacturer_id'] = $row['manufacturer_id'];
                $context_skus1[$row['sku']]['ma_brw_id'] = $row['ma_brw_id'];
                $context_skus1[$row['sku']]['source_id'] = null;
                $context_skus1[$row['sku']]['recently_curated_manufacturer_id'] = null;
                $context_skus1[$row['sku']]['price_tier'] = $row['price_tier'];
            }
        }
        return $context_skus1;
    }


    /**
     * @param int $batch_id Batch ID
     * @param array $skus the SKUs to look data for
     * @param int $curated_max_days_ago Curated Max Days Ago
     * @return array
     * only queries non-curation tables
     */
    public function get_context_skus_manufacturer_data_ext(int $batch_id, array $skus, int $curated_max_days_ago): array
    {
        if (empty($skus)) {
            return [];
        }
        $this->log_info('Loading context manufacturers data', ['batch_id' => $batch_id, 'skus' => $skus]);
        $sql = 'SELECT p.PrMaID AS manufacturer_id,
                  m.MaBrwId AS ma_brw_id,
                  p.PrSKU AS sku,
                  pt.CptPriceTier AS price_tier
            FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
            JOIN csn_product.dbo.tblManufacturer m WITH (NOLOCK) ON m.MaID = p.PrMaID
            INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
            INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID         
                   CROSS APPLY (
                   SELECT  ISNULL(CASE MAX(CptPriceTier) WHEN 4 THEN 4 ELSE MAX(CptPriceTier) + 1 END, 1) AS CptPriceTier
                   FROM    csn_product.dbo.tblClassPriceTier cpt WITH (NOLOCK)
                   WHERE   CptPriceTierCeiling < p.PrSalePrice 
                     AND   CptClID = cl.ClID
                     AND   CptBclgID = p.PrBclgID
            ) pt
            WHERE p.PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));
        $statement = $this->pdo->prepare($sql);
        //    $statement->bindValue(':batch_id2', $batch_id, PDO::PARAM_INT);
        //    $statement->bindValue(':curated_max_days_ago', $curated_max_days_ago, PDO::PARAM_INT);
        $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

        if (!$statement->execute()) {
            $exception = ExecutionException::forStatement($statement, 'Failed to load context data for skus');
            $this->log_throwable_error($exception, $exception->getMessage(), ['batch_id' => $batch_id, 'skus' => $skus]);
            throw $exception;
        }
        return $statement->fetchAll();
    }
}