# Recent Curation Issues and Run Books

This is a list of recent issues and run books that is work in progress.

## Q: While Curation Tool has a bug to downstream manual batches, how to downstream the stuck batches everyday by hand?

When you see the alert  in **#btbb-brand-workflows-alerts-high** like 
> Curation Batches Stuck In QA Complete Status But Not WLed at [Datadog](https://app.datadoghq.com/monitors/37250961)

Check out the stuck PA manual batches and run 4 scripts to downstream them:
[Directly Send Manual Batches to WL API](../curation-scripts/#directly-send-manual-batches-to-wl-api),
[Update Project Stage](../curation-scripts/#update-project-stage),
[Set Verification ID](../curation-scripts/#set-verification-id) and
[Update Batch Status](../curation-scripts/#update-batch-status)

1. Follow the steps to run the SQL, save the csv file and run the script in [Directly Send Manual Batches to WL API](../curation-scripts/#directly-send-manual-batches-to-wl-api)
2. Follow the steps in [Update Project Stage](../curation-scripts/#update-project-stage) to update the stage to `(E5) Extranet White Label` to indicate that the curation projects were WLed
3. Follow the steps in [Set Verification ID](../curation-scripts/#set-verification-id) to set a dummy verification id to indicate that the curation batches and Vis were WLed.
4. Follow the steps in [Update Batch Status](../curation-scripts/#update-batch-status) to set the batches to `6	Manual Downstreamed` to indicate that all the operations were finished about this batch
Note that this script might be slow to run - it could take 2 hours to set verification ids for around 5000 skus.

## Q: Any idea why [style-suggestion-api DEV](http://kube-style-suggestion-api.service.intradsm1.sdeconsul.csnzoo.com/) is down?

1. Set up kubectl: https://docs.csnzoo.com/docker/kubernetes/getting-started/cluster-access/
2. `kubectl config get-contexts` to get all contexts
3. `kubectl config use-context gke-sdeprod-c1-dsm1` to switch to your context. `gke-sdeprod-c1-dsm1` is DEV. `gke-prod-c2-iad1` is PROD
4. Check `kubectl get pods -n py-style-suggestion-api` and found `No resources found in py-style-suggestion-api namespace.`
5. Need to redeploy DEV. Find the last green build that deployed to DEV and PROD on `master` or `main` branch: https://buildkite.com/wayfair/py-style-suggestion-api/builds?branch=master
6. Click rebuild and click the button to unlock to deploy to DEV (and PROD if needed). Wait for it to finish.
7. Check `kubectl get pods -n py-style-suggestion-api` to find some newly deployed runing pods
8. Run the following command to test is the service is back
```
curl --location 'https://kube-style-suggestion-api.service.intradsm1.sdeconsul.csnzoo.com/api/predict' \
--header 'Content-Type: application/json' \
--data '{
    "skus":["CBOP4392"]
}'
```

## Q: [Curation batch](https://partners.wayfair.com/d/curation-tool/completed?batch_id=1541357) is in a QA Complete status, but is showing as 0 of 1 SKUs have been completed.

1. To find out why `0 out 1 SKUs reviewed` while the batch is complete, run the following query to get some basic status of the batch
```
DECLARE @batchId INT = 1541357

select top(10) * from csn_product.dbo.tblcurationbatchstatushistory history with (nolock) where history.BatchID in (@batchId)
select top(10) * from csn_product.dbo.tblCurationBatch batch with (nolock) where batch.ID in (@batchId)

select vi.* from csn_product.dbo.tblVerificationitem vi with (nolock) where vi.ViBatchID in (@batchId)

select distinct pt2.QuickformID, pt2.ProjectID, pt2.Stage, r.TypeID from csn_product.dbo.tblVerificationItem vi with (nolock)
left outer join csn_product.dbo.tblCurationRequestSKU s with (nolock) on s.SKU = vi.ViSKU
left outer join csn_product.dbo.tblCurationRequest r with (nolock) on r.ID = s.RequestID
left outer join csn_merch_tool.dbo.tblProductionTracking2 pt2 with (nolock) on r.QuickformID = pt2.QuickformID
where vi.ViBatchID in (@batchId)
```
2. You could find that ViQAStatusID is 1, which means `Pending Approval` whereas it should have been 2 `Approved`. Otherwise the item was downstreamed correctly because ViVerificationID is not null
3. Then you can run the following query to find out the history log of how Qa status changed
```
select top 10 * from csn_product..tblverificationitemqastatushistory where sku in ('HGKS7209')
```
But there is no record this means that the batch or sku jumped the gun to complete by something
3. For now, the problem becomes why the batch could be completed when the QA hasn't been done? You need to check Curation Tool logic to find out what could've happend.
4. Meanwhile, you can use Uber loader to update its status to 2 to fixed manually: https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-item/?flt1_0=15998581
5. Refresh https://partners.wayfair.com/d/curation-tool/completed?batch_id=1541357 and find the sku is review

## Q: [Curation batch](https://partners.wayfair.com/d/curation-tool/completed?batch_id=1541342) is stuck in QA complete, but has been completed in the tool. Can the team please help us to push this batch through to downstream?

1. Open the batch and find it's `Type: Manual review` so we can manually downstream it
2. To do so, try to call https://partners.wayfair.com/d/curation-tool/downstream?batch_id=1541342 in browser
3. You got `{"result":true}` which means the manual downstream worked. Otherwise you need to investigate why it failed. A common reason is that the whitelabel is in progress for the skus and cannot be WLed again before the previous batch is completed. To find out, you need to check Kibana Production logs. You can also run the following query to check the WL status for all SKUs in the batch. 3:7 means the WL is complete and not in progress, 1:11 means it is and you need to wait
```sql
select sku.SKU, sku.StatusID sku_status, batch.StateID batch_status, sku.TargetMaID from csn_product.dbo.tblWhiteLabelBatch batch 
join csn_product.dbo.tblWhiteLabelBatchSKU sku on sku.WhiteLabelBatchID = batch.ID
where sku.SKU in (select visku from csn_product.dbo.tblVerificationitem vi where vi.ViBatchID in (1541342))
```
