<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\ProductManagement\Populator;

class Closure_Cache {
  /**
   * @var \Closure[]
   */
  private $storage = [];

  /**
   * @param string   $fqcn      fully qualified class name
   * @param \Closure $populator property map
   *
   * @return void
   */
  public function set($fqcn, $populator) {
    $this->storage[$fqcn] = $populator;
  }

  /**
   * @param string $fqcn fully qualified class name to get property map for
   *
   * @return \Closure|null
   */
  public function get($fqcn) {
    if (isset($this->storage[$fqcn])) {
      return $this->storage[$fqcn];
    }

    return null;
  }
}
