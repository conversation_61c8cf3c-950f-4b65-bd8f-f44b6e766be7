<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\Curation;

use WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date;

class Curation_Item_Model {
  const SKU_TYPE_SIMPLE               = 1;
  const SKU_TYPE_COLLECTION_COMPONENT = 2;
  const SKU_TYPE_KIT_COMPONENT        = 4;

  const SOURCE_LEGACY           = 0;
  const SOURCE_WPP              = 1;
  const SOURCE_COLLISION        = 2;
  const SOURCE_LEGACY_ADDED     = 3;
  const SOURCE_PRODUCT_ADDITION = 4;
  const SOURCE_MANUALLY_ADDED   = 5;

  /**
   * It is null if it was not loaded from tblVerificationItem
   *
   * @var int|null
   */
  public $id;

  /**
   * @var string
   */
  public $sku;

  /**
   * @var int
   */
  public $source_id;

  /**
   * @var int
   */
  public $verification_id;

  /**
   * @var int
   */
  public $locked_em_id;

  /**
   * @var string
   */
  public $locked_employee_name;

  /**
   * @var string
   */
  public $locked_date;

  /**
   * @var int
   */
  public $final_style_id;

  /**
   * @var int
   */
  public $final_sub_style_id;

  /**
   * @var int
   */
  public $final_brand_id;

  /**
   * @var int
   */
  public $excluded_reason_id;

  /**
   * @var string
   */
  public $price_tier_override;

  /**
   * @var string
   */
  public $price_tier;

  /**
   * @var string
   */
  public $sale_price;

  /**
   * @var int
   */
  public $price_options_count;

  /**
   * @var string
   */
  public $eb_investment_sku;

  /**
   * @var bool
   */
  public $is_kitsco;

  /**
   * @var int
   */
  public $qa_status_id;

  /**
   * @var \WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object
   */
  public $qa_status_object;

  /**
   * @var string
   */
  public $created_at;

  /**
   * @var int
   */
  public $context_xn_id;

  /**
   * @var string
   */
  public $context_xn_name = '';

  /**
   * @var bool
   */
  public $is_kit_component;

  /**
   * @var int
   */
  public $batch_id;

  /**
   * @var string
   */
  public $product_name;

  /**
   * @var int
   */
  public $manufacturer_id;

  /**
   * @var string
   */
  public $manufacturer_name;

  /**
   * @var int
   */
  public $manufacturer_brw_id;

  /**
   * @var int
   */
  public $product_status_id;

  /**
   * @var int|null
   */
  public $product_status_reason_code;

  /**
   * @var string
   */
  public $product_status_name;

  /**
   * @var int
   */
  public $marketing_category_id;

  /**
   * @var string
   */
  public $supplier_ids_string;

  /**
   * @var array
   */
  public $supplier_names;

  /**
   * @var bool
   */
  public $is_canadian_only = false;

  /**
   * @var string
   */
  public $class_name;

  /**
   * @var int
   */
  public $brand_catalog_id;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date
   */
  public $last_cloned = null;

  /**
   * @var string|null
   */
  public $pull_in_exclusion_employee_name = null;

  /**
   * Curation_Item_Model constructor.
   */
  public function __construct() {
    // prevent passing dao
  }

  /**
   * @return array
   */
  public function get_supplier_ids() {
    return array_map(
        'intval',
        $this->supplier_ids_string !== '' ? explode(',', $this->supplier_ids_string) : []
    );
  }

  /**
   * @param int $checked_type check if the current SKU is of the given type (simple, collection/kit component)
   *
   * @return bool
   */
  public function of_type(int $checked_type) : bool {
    $type_flag = self::SKU_TYPE_SIMPLE;

    if (!empty($this->is_kit_component)) {
      $type_flag |= self::SKU_TYPE_KIT_COMPONENT;
      $type_flag &= ~self::SKU_TYPE_SIMPLE;
    }

    if (!empty($this->context_xn_id)) {
      $type_flag |= self::SKU_TYPE_COLLECTION_COMPONENT;
      $type_flag &= ~self::SKU_TYPE_SIMPLE;
    }

    return ($type_flag & $checked_type) === $checked_type;
  }

  /**
   * Populates this model from a key/value map whose keys match up to the property names of this model. This makes
   * sense to have this function in the base class so that collections can use it to populate lots of objects quickly
   * and so that any model that needs to be saved can be quickly populated from an array of form values
   *
   * @param array $map a map whose keys match up to the property names of this model.
   *
   * @return \WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model This model for method chaining.
   */
  public function populate($map) {
    foreach (array_keys(get_object_vars($this)) as $key) {
      if (isset($map[$key])) {
        $this->$key = $map[$key]; /** @phpstan-ignore-line */
      }
    }

    $this->last_cloned = new Last_Clone_Date($map['last_clone_date'] ?? null);

    return $this;
  }
}
