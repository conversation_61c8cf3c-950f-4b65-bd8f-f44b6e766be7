<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use App\Application\Translation\TranslatorInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;

class Missing_Class_Validation implements Curation_Data_Consistency_Validation {
  /**
   * @var TranslatorInterface
   */
  private TranslatorInterface $translate;

  /**
   * @var Curation_Data_Consistency_Validation_Storage
   */
  private Curation_Data_Consistency_Validation_Storage $dao;

  /**
   * @param TranslatorInterface                          $translate Translator
   * @param Curation_Data_Consistency_Validation_Storage $dao       DAO
   */
  public function __construct(TranslatorInterface $translate, Curation_Data_Consistency_Validation_Storage $dao) {
    $this->translate = $translate;
    $this->dao       = $dao;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return Curation_Data_Consistency_Validation_Result
   */
  public function validate(int $batch_id, array $sections): Curation_Data_Consistency_Validation_Result {
    $result = new Curation_Data_Consistency_Validation_Result();
    $skus   = $this->extract_skus_from_sections($sections);

    if (empty($skus)) {
      return $result;
    }

    $skus_missing_class = $this->dao->get_skus_missing_class($skus);

    foreach ($skus_missing_class as $sku) {
      $result->add_error($sku, $this->translate->trans('Validation.CurationSkuMissingClass', ['{sku}' => $sku]));
    }

    return $result;
  }

  /**
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  private function extract_skus_from_sections(array $sections): array {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        $skus[] = $item->get_sku();
      }
    }

    return $skus;
  }
}
