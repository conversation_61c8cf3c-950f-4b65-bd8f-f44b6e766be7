<?php

namespace WF\Shared\Traits;

use App\Application\Logger\LoggerTrait;
use Throwable;

trait Logging_Trait
{
    use LoggerTrait;

    /**
     * @param Throwable $exception
     * @param string $message
     * @param array $context
     *
     * @deprecated use `error` method instead
     */
    protected function log_throwable_error($exception, $message, array $context = []): void
    {
        $this->error($message, array_merge($context, ['exception' => $exception]));
    }

    /**
     * @param string $message
     * @param array $context
     *
     * @deprecated use `debug` method instead
     */
    protected function log_debug($message, array $context = []): void
    {
        $this->info($message, $context);
    }

    /**
     * @param string $message
     * @param array $context
     *
     * @deprecated use `info` method instead
     */
    protected function log_info($message, array $context = []): void
    {
        $this->info($message, $context);
    }

    /**
     * @param string $message
     * @param array $context
     *
     * @deprecated use `warning` method instead
     */
    protected function log_warning($message, array $context = []): void
    {
        $this->warning($message, $context);
    }

    /**
     * @param string $message
     * @param array $context
     *
     * @deprecated use `error` method instead
     */
    protected function log_error($message, array $context = []): void
    {
        $this->error($message, $context);
    }

    /**
     * Log throwable error to Kibana
     *
     * @param Throwable $exception Throw error to log
     * @param string $message Optional message to use instead of default
     * @param array $context Extra details to include in log
     *
     * @return void
     * @deprecated use `warning` method instead
     */
    protected function log_throwable_warning(Throwable $exception, $message = '', $context = []): void
    {
        if (empty($message)) {
            $message = 'Throwable error thrown: ' . $exception->getMessage();
        }

        $this->warning($message, array_merge($context, ['exception' => $exception]));
    }
}
