<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

class Section implements \JsonSerializable {
  /**
   * @var string
   */
  private $title;

  /**
   * @var int
   */
  private int $skusTotalCount = 0;

    /**
     * @var int
     */
    private int $skusSavedCount = 0;

    /**
     * @var int
     */
    private int $filteredSkusTotalCount = 0;

    /**
     * @var int
     */
    private int $filteredSkusSavedCount = 0;

    /**
     * @var int
     */
    private int $xnId = 0;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  private $curation_items = [];

  /**
   * @return string
   */
  public function get_title() : string {
    return $this->title;
  }

    /**
     * @return int
     */
    public function get_skusTotalCount() : int {
        return $this->skusTotalCount;
    }

    /**
     * @return int
     */
    public function get_skusSavedCount() : int {
        return $this->skusSavedCount;
    }

    /**
     * @return int
     */
    public function get_xnId() : int {
        return $this->xnId;
    }

    /**
     * @param int $skusTotalCount skusTotalCount
     *
     * @return void
     */
    public function set_skusTotalCount(int $skusTotalCount) {
        $this->skusTotalCount =  $skusTotalCount;
    }

    /**
     * @param int $skusSavedCount skusTotalCount
     *
     * @return void
     */
    public function set_skusSavedCount(int $skusSavedCount) {
        $this->skusSavedCount =  $skusSavedCount;
    }

    /**
     * @param int $skusTotalCount skusTotalCount
     *
     * @return void
     */
    public function set_filteredSkusTotalCount(int $filteredSkusTotalCount) {
        $this->filteredSkusTotalCount =  $filteredSkusTotalCount;
    }

    /**
     * @param int $skusSavedCount skusTotalCount
     *
     * @return void
     */
    public function set_filteredSkusSavedCount(int $filteredSkusSavedCount) {
        $this->filteredSkusSavedCount =  $filteredSkusSavedCount;
    }

    /**
     * @param int $xnId skusTotalCount
     *
     * @return void
     */
    public function set_xnId(int $xnId) {
        $this->xnId =  $xnId;
    }
  /**
   * @param string $title Title
   *
   * @return void
   */
  public function set_title(string $title) {
    $this->title = $title;
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  public function get_curation_items() : array {
    return $this->curation_items;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curation_items Curation Items
   *
   * @return void
   */
  public function set_curation_items(array $curation_items) {
    $this->curation_items = $curation_items;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'title'         => $this->title,
        'curationItems' => $this->curation_items,
        'skusSavedCount' => $this->skusSavedCount,
        'skusTotalCount' => $this->skusTotalCount,
        'filteredSkusSavedCount' => $this->filteredSkusSavedCount,
        'filteredSkusTotalCount' => $this->filteredSkusTotalCount,
        'id' => $this->xnId
    ];
  }
}