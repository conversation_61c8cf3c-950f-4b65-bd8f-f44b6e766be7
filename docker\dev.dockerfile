## Build Base Image
FROM wayfair/php-fpm:7.4.27-2023.01.16 as php74

LABEL \
  com.wayfair.app=brand-workflows-curation-tool \
  com.wayfair.description="The Curation Tool provides an ability to assign styles & brands to the SKUs & send them for WL"

## Build local dev
RUN wf-install-extension pdo pdo-sqlsrv pdo-pgsql \
    && wf-install-composer --2 \
    && wf-install-xdebug \
    && wf-disable-heavy-cache \
    && wf-enable-errors \
    && rm -rf /var/cache/yum/* \
    && yum clean all \
    && echo "xdebug.mode=debug" >> /etc/php.d/xdebug.ini  \
    && echo "xdebug.mode=coverage" >> /etc/php.d/xdebug.ini


## Build Base Image
FROM wayfair/php-fpm:8.1.10-2023.01.16 as php81

LABEL \
  com.wayfair.app=brand-workflows-curation-tool \
  com.wayfair.description="The Curation Tool provides an ability to assign styles & brands to the SKUs & send them for WL"

## Build local dev
RUN wf-install-extension pdo pdo-sqlsrv pdo-pgsql \
    && wf-install-composer --2 \
    && wf-install-xdebug \
    && wf-disable-heavy-cache \
    && wf-enable-errors \
    && rm -rf /var/cache/yum/* \
    && yum clean all \
    && echo "xdebug.mode=debug" >> /etc/php.d/xdebug.ini  \
    && echo "xdebug.mode=coverage" >> /etc/php.d/xdebug.ini

## Build Frontend Image
FROM wayfair/node:12.22.6-2023.1.10 AS frontend

ENV SASS_BINARY_SITE https://artifactorybase.service.csnzoo.com/artifactory/list/resources-general/node-binaries/node-sass/

#
# node-sass requires python2, which is no longer bundled with base docker image
# param "--setopt=tsflags=nodocs" is used to restrict installation of documentation, thanks to this we save space and time
# param "-y" will not prompt confirmation during yum install
#
USER root
RUN yum --setopt=tsflags=nodocs -y install python2 \
    && rm -rf /var/cache/yum/* \
    && yum clean all

RUN echo -n 'Yarn version and config' \
    && yarn --version \
    && yarn config set registry https://artifactorybase.service.csnzoo.com/artifactory/api/npm/npm \
    && yarn config get registry
