<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister_Interface;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

class Batch_Persister_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch|\Prophecy\Prophecy\ObjectProphecy
     */
    private $batch;

    /**
     * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $dao;

    /**
     * @var \WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister
     */
    private $subject;


    /**
     * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO
     */
    private $batch_sku_postgresql_dao;


    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Batch_SKU_DAO::class);
        $this->batch = $this->prophesize(Batch::class);
        $this->batch_sku_postgresql_dao = $this->prophesize(Batch_SKU_Postgresql_DAO::class);

        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->subject = new Batch_Persister(
            $this->dao->reveal(),
            $this->batch_sku_postgresql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_is_persister()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->assertInstanceOf(Batch_Persister_Interface::class, $this->subject);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_persists_batch_when_it_has_no_id()
    {
        $this->dao->save_batch(Argument::any())->willReturn(3);
        $this->subject->persist_batch($this->batch->reveal());

        $this->dao->save_batch($this->batch)->shouldHaveBeenCalled();
        $this->batch->set_id(3)->shouldHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_does_not_persist_when_batch_has_id()
    {
        $this->batch->get_id()->willReturn(3);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->subject->persist_batch($this->batch->reveal());
        $this->dao->save_batch(Argument::any())->shouldNotHaveBeenCalled();
        $this->batch->set_id(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     * @dataProvider db_cleanup_expectations
     *
     * @param bool   $cleanup_result if cleanup was successful
     * @param string $_              if id should be cleared
     *
     * @return void
     */
    //@codingStandardsIgnoreStart
    public function it_does_not_do_cleanup_for_batch_without_id(bool $cleanup_result, string $_)
    {
        //@codingStandardsIgnoreEnd

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->batch->get_id()->willReturn(null);
        $this->dao->cleanup_batch(Argument::any())->willReturn($cleanup_result);

        $this->subject->attempt_batch_cleanup($this->batch->reveal());

        $this->batch->set_id(Argument::any())->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     * @dataProvider db_cleanup_expectations
     *
     * @param bool   $cleanup_result   if cleanup was successful
     * @param string $call_expectation if id should be cleared
     *
     * @return void
     */
    public function it_cleans_batch_id_when_managed_to_clean_db(bool $cleanup_result, string $call_expectation)
    {
        $this->batch->get_id()->willReturn(3);
        $this->batch->set_id(Argument::any())->willReturn(null);
        $this->dao->cleanup_batch(Argument::any())->willReturn($cleanup_result);

        $this->subject->attempt_batch_cleanup($this->batch->reveal());

        $this->batch->set_id(null)->$call_expectation();
    }

    /**
     * @return array
     */
    public function db_cleanup_expectations()
    {
        return [
            'db managed to clean up' => [true, 'shouldHaveBeenCalled'],
            'db was not able to clean up' => [false, 'shouldNotHaveBeenCalled']
        ];
    }
}
