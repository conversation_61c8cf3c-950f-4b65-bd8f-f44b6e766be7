apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: brand-workflows-curation-tool
  description: Brand Workflows Curation Tool
  tags:
    - php
    - curation
    - merch-workflows-and-services
  annotations:
    github.com/project-slug: shared/brand-workflows-curation-tool
    sonarqube.org/project-key: brand-workflows-curation-tool
    buildkite.com/project-slug: wayfair/brand-workflows-curation-tool
spec:
  tier: 2 # based on https://docs.google.com/spreadsheets/d/1shKYBXFyhYYEfEJ0vCFyCQVDfRlLmGP_tw5MuSEdyWo/edit
  type: service
  lifecycle: production
  owner: brandworkflowsengineering
