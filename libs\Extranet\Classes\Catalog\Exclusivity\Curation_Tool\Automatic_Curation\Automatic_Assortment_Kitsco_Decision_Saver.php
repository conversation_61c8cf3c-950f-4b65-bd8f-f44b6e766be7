<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer;

class Automatic_Assortment_Kitsco_Decision_Saver implements Automatic_Curation_Saver {

  private Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader;

  private Curation_Decision_Service $curationDecisionService;

  private Kitsco_Manufacturer $kitscoManufacturerService;

  /**
   * @param Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader Assortment decision loader
   * @param Curation_Decision_Service           $curationDecisionService          Curation decision service
   * @param Kitsco_Manufacturer                 $kitscoManufacturerService        Kitsco Manufacturer service
   */
  public function __construct(
      Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader,
      Curation_Decision_Service $curationDecisionService,
      Kitsco_Manufacturer $kitscoManufacturerService
  ) {
    $this->assortmentCurationDecisionLoader = $assortmentCurationDecisionLoader;
    $this->curationDecisionService          = $curationDecisionService;
    $this->kitscoManufacturerService        = $kitscoManufacturerService;
  }

  /**
   * @param int       $batchId    Batch ID
   * @param Section[] $sections   Section data
   * @param int       $employeeId Employee ID
   *
   * @return int
   * @throws \Exception
   */
  public function execute(int $batchId, array $sections, int $employeeId) : int {
    $moveToKitscoSkus = $this->getSKUsToMoveToKitsco($batchId, $sections);

    if (empty($moveToKitscoSkus)) {
      return 0;
    }

    $this->saveAssortmentDecisionForMoveToKitsco($batchId, $employeeId, $moveToKitscoSkus);

    return count($moveToKitscoSkus);
  }

  /**
   * @param int       $batchId  Batch id
   * @param Section[] $sections Section data
   *
   * @return string[]
   */
  private function getSKUsToMoveToKitsco(int $batchId, array $sections) : array {
    $skus = $this->getSharedSkuList($sections);

    $assortmentDecisionCollection = $this->assortmentCurationDecisionLoader->assortment_decision_data_for_skus($batchId, $skus);
    $moveToKitsco                 = [];

    foreach ($assortmentDecisionCollection->get_skus_with_decision() as $sku) {
      $assortmentDecision = $assortmentDecisionCollection->get_for_sku($sku);
      // Kitsco SKUs are saved as "Move to tail brand"
      if (!$assortmentDecision->should_move_to_tail_brand()) {
        continue;
      }

      $moveToBrandData = $assortmentDecision->get_move_to_brand_decision_data();
      if (!empty($moveToBrandData) && $this->kitscoManufacturerService->is_kitsco_manufacturer($moveToBrandData->get_target_manufacturer_id())) {
        $moveToKitsco[] = $sku;
      }
    }

    return $moveToKitsco;
  }

  /**
   * @param int      $batchId        Batch Id
   * @param int      $employeeId     Employee ID
   * @param string[] $newContextSkus SKUs
   *
   * @return void
   * @throws \Exception
   */
  private function saveAssortmentDecisionForMoveToKitsco(int $batchId, int $employeeId, array $newContextSkus) {
    $savedAt = date('Y-m-d H:i:s');
    foreach ($newContextSkus as $sku) {
      $this->curationDecisionService->save_as_kitsco(
          $batchId,
          $sku,
          $savedAt,
          $employeeId,
          Curation_Decision_Source::automatic_by_assortment_decision()
      );
    }
  }

  /**
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  private function getSharedSkuList(array $sections) : array {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        if ($curationItem->get_type() === Curation_Item_Type::kit_parent()
            || $curationItem->get_type() === Curation_Item_Type::shared()
        ) {
          $skus[] = $curationItem->get_sku();
        }
      }
    }

    return $skus;
  }
}
