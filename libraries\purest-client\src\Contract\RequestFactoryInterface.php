<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Contract;

use Psr\Http\Message\RequestInterface;

interface RequestFactoryInterface
{
    /**
     * @param string $service PuREST service name
     * @param string $resource PuREST resource name
     * @param string $body Request body
     * @param string[] $headers HTTP Headers
     *
     * @return RequestInterface
     */
    public function createRequest(
        string $service,
        string $resource,
        string $body,
        array $headers = []
    ): RequestInterface;
}
