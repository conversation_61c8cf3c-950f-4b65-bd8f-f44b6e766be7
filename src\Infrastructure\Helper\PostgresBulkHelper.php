<?php

declare(strict_types=1);
/**
 * Static Helper to take an array of data (array or models) and generate SQL insert/select statements
 *
 * The reason for building was to generate an XML string that could be bound for insert statements along with the XML parsing nodes
 * From the XML a user could generate a temp table, insert directly into a table, select the data, or return a CTE
 *
 * Knowing we want to move to JSON in the future it was named SQL_Bulk_Helper
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Helper;

use Exception;

use function array_map;
use function array_slice;
use function explode;
use function implode;
use function is_null;

use function is_numeric;
use function preg_match;
use function rtrim;
use function sprintf;
use const PHP_EOL;

class PostgresBulkHelper
{
    public const DEFAULT_XML_PARAM = 'xml';
    public const DEFAULT_JSON_PARAM = 'json';
    public const DEFAULT_CTE_NAME = 'cteXML';
    public const DEFAULT_JSON_NAME = 'cteJSON';
    public const XML_COLUMNS = 1;
    public const SELECT_COLUMNS = 2;
    public const INSERT_COLUMNS = 3;
    public const TABLE_COLUMNS = 4;
    public const JSON_COLUMNS = 5;
    public const JSON_SELECT_COLUMNS = 6;

    /**
     * This will go through our column map to build the necessary string to select/extract columns
     *
     * @param string[] $columnMap the column names and types
     * @param int $columnType the type of columns we want to get back | XML, SELECT, INSERT,  or TABLE
     * @param string|null $tableAlias If not null, alias the select columns with this
     *
     * @return string of columns
     * @throws \Exception
     */
    public static function getColumns(
        array $columnMap,
        int $columnType = self::XML_COLUMNS,
        ?string $tableAlias = null
    ): string {
        $columns = [];
        foreach ($columnMap as $columnName => $sqlType) {
            switch ($columnType) {
                case self::XML_COLUMNS:
                    $columns[] = sprintf("rec.value('(@%s)[1]', '%s' ) AS %s", $columnName, $sqlType, $columnName);
                    break;
                case self::SELECT_COLUMNS:
                case self::INSERT_COLUMNS:
                    $columns[] = $columnName;
                    break;
                case self::TABLE_COLUMNS:
                    $columns[] = $columnName . ' ' . $sqlType;
                    break;
                case self::JSON_SELECT_COLUMNS:
                    $columnName = array_slice(explode('.', $columnName), -1)[0];
                    if (!is_null($tableAlias) && $tableAlias !== '') {
                        $columns[] = $tableAlias . '.' . $columnName;
                    } else {
                        $columns[] = $columnName;
                    }
                    break;
                case self::JSON_COLUMNS:
                    $_column_name = array_slice(explode('.', $columnName), -1)[0];
                    $columns[] = sprintf("%s %s '$.%s'", $_column_name, $sqlType, $columnName);
                    break;
                default:
                    throw new Exception('Invalid Column Type Request');
            }
        }
        return implode(PHP_EOL . ', ', $columns);
    }

    /**
     * This will make sure our param only has letters, numbers, and underscores. If not an exception is thrown
     *
     * @param string $paramString the param string
     *
     * @return void
     * @throws \Exception
     */
    protected static function validateParamName(string $paramString): void
    {
        // if we match any things NOT in the pattern that is invalid
        if ($paramString === '' || preg_match('/[^A-Za-z0-9_]/', $paramString)) {
            // TODO: This should be changed to a less generic Validation_Exception instead
            throw new Exception('Param Names must be alphanumeric including underscore [^A-Za-z0-9_]');
        }
    }


    /**
     * This will generate the SQL necessary to select back from XML nodes. Note you will have to BIND the xml to the param using PARAM_STR.
     * The second param here will be the name of the pdo placeholder as well as the XML variable we select from
     *
     * @param string[] $columnMap the map of column name to type
     * @param string $xmlParamName the xml name to extract from | default 'xml'
     *
     * @return string
     * @throws \Exception
     *
     */
    public static function getSelectXmlSql(array $columnMap, string $xmlParamName = self::DEFAULT_XML_PARAM)
    {
        self::validateParamName($xmlParamName);
        $xmlColumns = self::getColumns($columnMap);
        return sprintf(
            "
            SELECT %s        
            FROM @%s.nodes( '/record' ) AS t(rec)
            ",
            $xmlColumns,
            $xmlParamName
        );
    }

    /**
     * This will create a declare statement for binding xml.
     *
     * @param string $xmlParamName the name of the SQL xml param | defaults to 'xml'
     * @param string|null $xmlBindValue the name of the SQL xml bindValue | defaults to xml_param_name
     *
     * @return string
     */
    public static function getXmlParamBinding(
        string $xmlParamName = self::DEFAULT_XML_PARAM,
        ?string $xmlBindValue = null
    ): string {
        if ($xmlBindValue === null) {
            $xmlBindValue = $xmlParamName;
        }
        self::validateParamName($xmlParamName);
        self::validateParamName($xmlBindValue);
        return sprintf(
            'DECLARE @%s XML = :%s;',
            $xmlParamName,
            $xmlBindValue
        );
    }

    /**
     * This will crconvertArrayToValuesString
     *
     * @param array $dataArray list array
     *
     * @return string
     */
    public static function convertArrayToValuesString($dataArray)
    {
        $valuesString = '';

        foreach ($dataArray as $data) {
            $values = array_map(static function ($value) {
                if ($value == '') {
                    return '0';
                }
                return is_numeric($value) ? $value : "'" . $value . "'"; // mysql_real_escape_string
            }, $data);

            $valuesString .= '(' . implode(', ', $values) . '), ';
        }

        // Remove the trailing comma and space
        $valuesString = rtrim($valuesString, ', ');

        return $valuesString;
    }
}
