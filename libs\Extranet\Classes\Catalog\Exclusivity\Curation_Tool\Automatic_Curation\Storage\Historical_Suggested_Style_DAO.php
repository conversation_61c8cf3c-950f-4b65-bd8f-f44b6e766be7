<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Historical_Suggested_Style_Loader_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Entity\Historical_Suggested_Style;
use PDO;
use WF\Shared\Traits\Logging_Trait;

class Historical_Suggested_Style_DAO implements Historical_Suggested_Style_Loader_Storage {
  use Logging_Trait;

  private ProductConnection $pdo;

  /**
   * @param ProductConnection    $pdo    PDO connection to SQLPRODUCT(aka BOSQLC6)
   * @param LoggerInterface|null $logger Logger
   */
  public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null) {
    $this->pdo = $pdo;
    $this->logger = $logger;
  }

  /**
   * @param int $batch_id batch identifier
   *
   * @return Historical_Suggested_Style[]
   */
  public function get_cached_suggested_style_info_for_batch(int $batch_id) : array {
    $this->info('Loading style suggestions info for batch', ['batch' => $batch_id]);

    $sql = '
      SELECT info.SKU AS sku, info.Notes AS notes
      FROM csn_product.dbo.tblVerificationAutomaticStyleCurationInfo info WITH (NOLOCK)
      WHERE info.BatchID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id,  PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get cached style suggestion info');
      $this->log_throwable_error(
          $exception,
          $exception->getMessage(),
          ['batch_id' => $batch_id, 'sql' => $sql]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Historical_Suggested_Style::class);
  }
}
