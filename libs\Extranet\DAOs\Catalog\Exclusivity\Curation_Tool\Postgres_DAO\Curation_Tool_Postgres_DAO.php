<?php

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Curation\ExclusivityAssortment\Infrastructure\Helper\SQLBulkHelper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Brand_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Granular_Style_Data;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Curation\Api\Cost\CostApiService;
class Curation_Tool_Postgres_DAO {
  use Logging_Trait;
  private const REJECTION_REASON_ACTIVE = true;
  private const DEFAULT_MARGIN = 0.49;
  private const ROWS_COUNT = 5;
  private PostgresConnection $pdo_psql;
  private ProductConnection $pdo_sql;

  private bool $useStrictMode;
  private CostApiService $costApiClient;
  /**
   * Curation_Tool_DAO constructor.
   *
   * @param PostgresConnection $pdo_psql_psql          PDO
   * @param LoggerInterface   $logger        logger
   * @param bool              $useStrictMode In strict mode we exclude SKUs which have no assigned class via JOIN
   */
  public function __construct(
      PostgresConnection $pdo_psql,
      ProductConnection $pdo_sql,
      LoggerInterface $logger,
      bool $useStrictMode = false,
      CostApiService $costApiClient
  ) {
    $this->pdo_psql      = $pdo_psql;
    $this->pdo_sql       = $pdo_sql;
    $this->logger        = $logger;
    $this->useStrictMode = $useStrictMode;
    $this->costApiClient = $costApiClient;
  }

  /**
   * Load predefined exclusion reasons from pick list
   *
   * @return array excluded reasons
   */
  public function get_exclusion_reasons() : array {
    $this->info("Loading exclusion reasons from pick list");

    $sql = 'SELECT "PwlerID" AS id,
                   "PwlerReasonName" AS name
            FROM "tblplProductWhiteLabelExcludedReason"
            WHERE "PwlerIsAutomatedReason" = :reasom_val
            ORDER BY "PwlerReasonName" ASC';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':reasom_val', 0, PDO::PARAM_BOOL);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load exclusion reasons');
      $this->log_throwable_error($exception, $exception->getMessage());
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * Load predefined exclusion reasons from pick list
   *
   * @return array all excluded reasons
   */
  public function get_all_exclusion_reasons(): array {
    $this->info("Loading all exclusion reasons from pick list");

    $sql = 'SELECT "PwlerID" AS id,
                   "PwlerReasonName" AS name
            FROM "tblplProductWhiteLabelExcludedReason"
            ORDER BY "PwlerReasonName" ASC';

    $statement = $this->pdo_psql->prepare($sql);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load all exclusion reasons');
      $this->log_throwable_error($exception, $exception->getMessage());
      throw $exception;
    }

    return $statement->fetchAll();
  }



  /**
   * Get styles
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_styles(int $region_id) : array {
    $this->info(
      sprintf('Loading Primary Styles for Region "%s"', $region_id),
      ['region_id' => $region_id]
    );
    $manufacturers=$this->get_styles_and_substyle_and_manufacturer_details($region_id);
    $result=[];
    foreach ($manufacturers as $manufacturer){
          unset($manufacturer['sub_style_id']);
          unset($manufacturer['price_tier']);
          unset($manufacturer['manufacturer_id']);
          $result[]=$manufacturer;
       }
    return array_unique($result,SORT_REGULAR);
  }


  /**
   * Get substyles
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_substyles(int $region_id): array {
    $this->info(
      sprintf('Loading substyles for Region "%s"', $region_id),
      ['region_id' => $region_id]
    );

    $sub_styles = $this->get_sub_styles_from_table($region_id);
    $manufacturers = $this->get_styles_and_substyle_and_manufacturer_details($region_id);

    $result=[];
    foreach ($manufacturers as $manufacturer){
      foreach ($sub_styles as $sub_style){
        if($manufacturer['sub_style_id']==$sub_style['substyle_id']){
          $sub_style['style_id']=$manufacturer['id'];
          $result[]=$sub_style;
          break;
        }
      }
    }
    return array_unique($result,SORT_REGULAR);
  }



  public function get_styles_and_substyle_and_manufacturer_details(int $region_id): array {

      $this->info('Loading style, substyle, pricetier and manufacturer details for Region',
          ['region_id' => $region_id]
      );

      $sql = 'SELECT DISTINCT 
                style."VsID" AS id,
                style."VsName" AS name,
                sptm."SptmSubStyleID" AS sub_style_id,
                sptm."SptmPriceTier" AS price_tier,
                sptm."SptmMaID" AS manufacturer_id
            FROM
                "tbljoinStylesPriceTierManufacturer" sptm
            JOIN "tblVerificationStyle" style ON sptm."SptmStyleID" = style."VsID"
            WHERE
                sptm."SptmBclgID" = :region_id';

      $statement = $this->pdo_psql->prepare($sql);

      $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

      if ($statement->execute() === false) {
          $exception = ExecutionException::forStatement($statement, 'Cannot load price details');
          $this->log_throwable_error(
              $exception,
              'Cannot load style, pricetier and manufacturer details for Region "%s"',
              ['region_id' => $region_id]
          );
          throw $exception;
      }

      return $statement->fetchAll();
  }

  /**
   * Fetch styles from "tblVerificationStyle" table
   * Exclude "tbljoinStylesPriceTierManufacturer" table
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  private function get_sub_styles_from_table(int $region_id): array {
    $this->info(
      sprintf('Loading styles for Region "%s"', $region_id),
      ['region_id' => $region_id]
    );

    $sql = 'SELECT DISTINCT
                vss."VssID" AS substyle_id,
                vss."VssName" AS name
            FROM
                "tblVerificationSubStyle" vss
            ORDER BY
                vss."VssName" ASC';

    $statement = $this->pdo_psql->prepare($sql);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load Styles');
      $this->log_throwable_error(
        $exception,
        sprintf('Cannot load Styles for Region "%s"', $region_id),
        ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }


  /**
   * Get Granular Styles
   *
   * @param int $region_id Region ID
   *
   * @return Granular_Style_Data[]
   */
  public function get_granular_styles(int $region_id) : array {
    $this->info(
        sprintf('Loading Granular Styles for Region "%s"', $region_id),
        ['region_id' => $region_id]
    );

    $sql = 'SELECT "VgsID" AS id,
                 "VssID" AS substyle_id,
                 "VgsName" AS name
            FROM "tblplVerificationGranularStyle" granular_style
            JOIN "tbljoinSubStyleGranularStyle" ssgs
            ON ssgs."SsgsGranularStyleID" = granular_style."VgsID"
            JOIN "tblVerificationSubStyle" sub_style
            ON ssgs."SsgsSubStyleID" = sub_style."VssID"
            WHERE granular_style."VgsBclgID" = :region_id
            ORDER BY "VgsName" ASC';


    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load Granular Styles');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load Granular Styles for Region "%s"', $region_id),
          ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Granular_Style_Data::class);
  }

  /**
   * @param int $batch_id Batch ID
   * @param array $skus SKUs
   *
   * @return array
   */
  public function get_sku_data(int $batch_id, array $skus): array
  {
    $this->info(
        'get_sku_data, Loading information about SKUs',
        ['skus' => $skus, 'batch_id' => $batch_id]
    );

    $result = [];
    $curated_data = $this->get_non_curation_data($skus);
    $verification_item_data = $this->get_verification_item_data($batch_id);

    $verification_items_sku_list = [];
    $verification_items_vi_locked_em_id_list = [];
    foreach ($verification_item_data as $verification_item_row) {
      $verification_items_sku_list[$verification_item_row['ViSKU']] = $verification_item_row;
      $verification_items_vi_locked_em_id_list[$verification_item_row['ViLockedEmID']] = $verification_item_row;
    }

    $vi_locked_em_ids = array_keys($verification_items_vi_locked_em_id_list);
    $employees_from_product_db = $this->get_employee_data_from_product_db($vi_locked_em_ids);
    $employees_from_product_db_em_id_list = [];
    foreach ($employees_from_product_db as $employee) {
      $employees_from_product_db_em_id_list[$employee['EmID']] = $employee;
    }

    $verification_item_qa_history = $this->get_verification_item_qa_history_data($skus);
    $verification_item_qa_history_list_with_empId_key = [];
    $employee_array_by_verification_item_qa_history = [];
    foreach ($verification_item_qa_history as $verification_item_row) {
      $employee_id = $verification_item_row["EmployeeID"];
      if (!in_array($employee_id, $employee_array_by_verification_item_qa_history)) {
        $employee_array_by_verification_item_qa_history[] = $employee_id;
      }
      $verification_item_qa_history_list_with_empId_key[$verification_item_row["EmployeeID"]]
          = $verification_item_row;
    }

    $employees_from_csn_hr_db = $this->get_employee_data_from_csn_hr_db($employee_array_by_verification_item_qa_history);
    $employees_from_csn_hr_db_list = [];
    foreach ($employees_from_csn_hr_db as $employees_from_csn_hr_row) {
      $employees_from_csn_hr_db_list[$employees_from_csn_hr_row["HRDbEmployeeID"]] = $employees_from_csn_hr_row;
    }

    $verification_item_qa_history_list_with_sku_key = [];
    $empty_employee_csn_hr_db_row = array("HRDbEmployeeID" => null, "last_qa_by" => null);
    foreach ($verification_item_qa_history_list_with_empId_key as $key => $value) {
      if (array_key_exists($key, $employees_from_csn_hr_db_list)) {
        $verification_item_qa_history_list_with_sku_key[$value["ViqshSKU"]]
            = array_merge($value, $employees_from_csn_hr_db_list[$key]);
      } else {
        $verification_item_qa_history_list_with_sku_key[$value["ViqshSKU"]]
            = array_merge($value, $empty_employee_csn_hr_db_row);
      }
    }


    $empty_verification_item_row = array("ViBatchID" => null, "ViSKU" => null, "excluded_reason_id" => null,
                                         "price_tier" => null, "final_style_id" => null, "final_sub_style_id" => null, "final_brand_id" => null,
                                         "final_granular_style_id" => null, "locked_date" => null, "is_kitsco" => null, "qa_status_id" => null,
                                         "decision_source_id" => null);
    $empty_employee_product_db_row = array("EmID" => null, "locked_employee_name" => null);

    $empty_verification_item_qa_history_and_employee_row
        = array_merge(array("ViqshSKU" => null, "last_qa_at" => null,
                            "last_qa_note" => null, "EmployeeID" => null), $empty_employee_csn_hr_db_row);

    foreach ($curated_data as $curation_row) {
      $temp_row = [];
      if (array_key_exists($curation_row['sku'], $verification_items_sku_list)) {
        $temp_verification_item_obj = $verification_items_sku_list[$curation_row['sku']];
        $temp_employee = $empty_employee_product_db_row;
        if (array_key_exists($temp_verification_item_obj['ViLockedEmID'],
                             $employees_from_product_db_em_id_list)) {
          $temp_employee = $employees_from_product_db_em_id_list[$temp_verification_item_obj['ViLockedEmID']];
        }
        $temp_row = array_merge($curation_row, $temp_verification_item_obj, $temp_employee);
      } else {
        $temp_row = array_merge($curation_row, $empty_verification_item_row, $empty_employee_product_db_row);
      }
      if (array_key_exists($curation_row['sku'], $verification_item_qa_history_list_with_sku_key)) {
        $temp_row = array_merge($temp_row,
                                $verification_item_qa_history_list_with_sku_key[$curation_row['sku']]);
      } else {
        $temp_row = array_merge($temp_row,
                                $empty_verification_item_qa_history_and_employee_row);
      }

      if (!array_key_exists('automatic_excluded_reason', $temp_row)) {
          $temp_row['automatic_excluded_reason'] = null;
      }

      $result[] = $temp_row;
    }
    return $result;
  }

  /**
   * @param array $skus SKUs
   *
   * @return array
   */
  public function get_non_curation_data(array $skus): array
  {
    $this->info(
        'Loading information about SKUs',
        ['skus' => $skus]
    );

    $sql = 'SELECT
            pr.PrSKU AS sku,
            pr.prname AS product_name,
            cl.ClID AS class_id,
            cl.ClName AS class_name,
            maOriginal.maname AS manufacturer_name,
            maOriginal.maBrwID AS manufacturer_brw_id,
            maBrand.BrandTypeID AS brand_type,
            bt.Name AS brand_name,
            bc.BclgID AS brand_catalog_id,
            bc.BclgName AS brand_catalog_name,
            2 AS price_options_count,
            clone_log.LastCloneDate AS last_clone_date,
            holdOutManufacturer.NoWhiteLabel AS is_hold_out_manufacturer
        FROM tblProduct pr WITH (NOLOCK)
        JOIN tblBrandCatalog bc WITH (NOLOCK) ON bc.BclgID = pr.PrBclgID
        INNER JOIN tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = pr.PrSKU AND PcMasterClass = 1
        INNER JOIN tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
        LEFT JOIN tblManufacturer maOriginal WITH (NOLOCK) ON maOriginal.MaID = pr.PrMaID
        LEFT JOIN tblManufacturerBrand maBrand WITH (NOLOCK) ON maBrand.MaID = pr.PrMaID
        LEFT JOIN tblPlBrandType bt WITH (NOLOCK) ON bt.ID = maBrand.BrandTypeID
        OUTER APPLY (
            SELECT MAX(SclgCloneDate) AS LastCloneDate
            FROM csn_product.dbo.tblSKUCloneLog WITH(NOLOCK)
            WHERE SclgClonedPrSku = pr.PrSKU AND SclgSuccessfullyCloned = 1) clone_log
        LEFT JOIN csn_product.dbo.tblManufacturerWhiteLabel holdOutManufacturer WITH (NOLOCK) ON pr.PrMaID = holdOutManufacturer.MaID
        WHERE pr.PrSKU IN (' . $this->pdo_sql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')
        ORDER BY pr.PrSKU;';

    $statement = $this->pdo_sql->prepare($sql);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load sku data');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load data for SKUs "%s"', implode(",", $skus)),
          ['skus' => $skus]
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }


  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_verification_item_data(int $batch_id): array
  {
    $sql = 'SELECT
                "ViBatchID",
                "ViSKU",
                "ViLockedEmID",
                "ViExcludedReasonID" AS "excluded_reason_id",
                "ViPriceTierOverride" AS "price_tier",
                "ViFinalStyleID" AS "final_style_id",
                "ViFinalSubStyleID" AS "final_sub_style_id",
                "ViFinalBrandMaID" AS "final_brand_id",
                "ViFinalGranularStyleID" AS "final_granular_style_id",
                "ViLockedDate" AS "locked_date",
                "ViIsKitsco" AS "is_kitsco",
                "ViQAStatusID" AS "qa_status_id",
                "ViDecisionSourceID" AS "decision_source_id",
                "ViRemark" AS "automatic_excluded_reason"
            FROM "tblVerificationItem"
            WHERE "ViBatchID" = :batch_id';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load sku data');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load data for batch_id "%s"', $batch_id),
          ['$batch_id' => $batch_id]
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }

  /**
   * @param array $skus employeeIds
   *
   * @return array
   */
  private function get_employee_data_from_product_db(array $vi_locked_em_ids): array
  {

    $sql = 'SELECT
                EmID,
                EmFirstName + \' \' + emp.EmLastName AS locked_employee_name
              FROM tblEmployee emp WITH (NOLOCK) 
              WHERE EmID in (' . $this->pdo_sql->paramsForList(count($vi_locked_em_ids), 'ids', SQL::int) . ')';

    // need to think on nvarchar
    $statement = $this->pdo_sql->prepare($sql);
    $statement->bindValuesList(':ids', $vi_locked_em_ids, SQL::int);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load tblEmployee');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load tblEmployee')
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }

  /**
   * @param array $skus SKUs
   *
   * @return array
   */
  private function get_verification_item_qa_history_data(array $skus): array
  {
    $skus_data =  $this->get_verification_item_qa_history_internal_data($skus);
    $verification_sku_data = [];
    foreach ($skus_data as $skus_data_row) {
      if ($skus_data_row["rn"] = 1) {
        $verification_sku_data[] = [
            'ViqshSKU'     => $skus_data_row['SKU'],
            'last_qa_at'   => $skus_data_row['Date'],
            'last_qa_note' => $skus_data_row['Message'],
            'EmployeeID'   => $skus_data_row['EmployeeID']
        ];
      }
    }
    return  $verification_sku_data;
  }


  /**
   * @param array $skus SKUs
   *
   * @return array
   */
  private function get_verification_item_qa_history_internal_data(array $skus): array
  {

    $sku_d = $this->pdo_psql->paramsForLists($skus);
    if($sku_d === ""){return array();}
    $sql = "SELECT \"SKU\",
                       \"Date\",
                       \"Message\",
                       \"EmployeeID\",
                       ROW_NUMBER() OVER (PARTITION BY \"SKU\" ORDER BY \"Date\" DESC) AS rn
                FROM \"tblVerificationItemQAStatusHistory\"
                WHERE \"SKU\" IN  ($sku_d)";
    $statement = $this->pdo_psql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load tblVerificationItemQAStatusHistory');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load tblVerificationItemQAStatusHistory')
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }
  /**
   * @return array
   */
  private function get_employee_data_from_csn_hr_db(array $employee_array_by_verification_item_qa_history): array
  {
    $this->info(
        'Loading get_employee_data_from_csn_hr_db'
    );

    if (empty($employee_array_by_verification_item_qa_history)) {
      return [];
    }
    // Create a comma-separated list of EmID values from the array
    $em_id_list = implode(',', $employee_array_by_verification_item_qa_history);

    $sql = 'SELECT
                EmID as HRDbEmployeeID,
                EmFirstName + \' \' + emp.EmLastName AS last_qa_by
            FROM csn_hr.dbo.tblEmployee emp WITH (NOLOCK)
            WHERE EmID IN (' . $em_id_list . ');';
    $statement = $this->pdo_sql->prepare($sql);
    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load csn_hr.dbo.tblEmployee');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load csn_hr.dbo.tblEmployee')
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[]
   */
  public function get_rejection_reasons() : array {
    $this->info('Loading rejection reasons');

    $sql = 'SELECT 
                "ID" AS id, 
                "Name" AS name
            FROM "tblplCurationDecisionRejectionReason"
            WHERE "Active" = :active 
            ORDER BY "DisplayIndex"';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':active', self::REJECTION_REASON_ACTIVE, PDO::PARAM_BOOL);
    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get rejection reasons');
      $this->log_throwable_error(
          $exception,
          'Cannot get rejection reasons'
      );
      throw $exception;
    }
    return $statement->fetchAll(PDO::FETCH_CLASS, Rejection_Reason::class);
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
   */
  public function get_suggested_style_rejection_reasons() : array {
    $this->info('Loading suggested style rejection reasons');

    $sql = 'SELECT 
                "ID" AS id, 
                "Name" AS name
            FROM "tblplCurationDecisionSuggestedStyleRejectionReason"
            ORDER BY "ID"';
    $statement = $this->pdo_psql->prepare($sql);
    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load suggested style rejection reasons');
      $this->log_throwable_error(
          $exception,
          'Cannot load suggested style rejection reasons'
      );
      throw $exception;
    }
    return $statement->fetchAll(PDO::FETCH_CLASS, Suggested_Style_Rejection_Reason::class);
  }

  /**
   * Get Manufacturers for Region
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_manufacturers(int $region_id) : array {
    $this->info('Loading manufacturers for Region',
                ['region_id' => $region_id]
    );
    if ($region_id===0){
      return [];
    }
    $data = [];

    $style_substyle_manufactures = $this->get_styles_and_substyle_and_manufacturer_details($region_id);
    $manufacturing_ids = array_unique(array_column($style_substyle_manufactures,"manufacturer_id"));
    $manufacture_info_data = $this->get_manufacture_info_dtls_external($manufacturing_ids);

    foreach($style_substyle_manufactures as $row){
      $row['style_id']=$row['id'];
      $row['substyle_id']=$row['sub_style_id'];
      unset($row['id']);
      unset($row['sub_style_id']);
      unset($row['name']);
      $index = array_search($row["manufacturer_id"], array_column($manufacture_info_data,"MaID"));
      $manufacture_data = $manufacture_info_data[$index];
      unset($manufacture_data["MaID"]);
      $data[] = array_merge($row, $manufacture_data);
    }
    $sorted_data = $this->array_orderBy($data, 'brand_class', SORT_DESC, 'name', SORT_ASC);
    return $sorted_data;
  }

  /**
   * Get Manufacture Information for manufactureId Array
   *
   * @param array $manufacture_ids manufacture Ids
   *
   * @return array
   */
  public function get_manufacture_info_dtls_external(array $manufacture_ids = []) : array {
    $this->info('Loading manufacture details for manufactureIds',
                ['manufacture_ids' => $manufacture_ids]
    );

    $sql = 'SELECT 
                ma.MaID,
                ma.MaName AS name,
                bt.Name AS brand_class
            FROM 
                tblManufacturer ma WITH (NOLOCK)
            INNER JOIN 
                tblManufacturerBrand brand WITH (NOLOCK) ON brand.MaID = ma.MaID
            INNER JOIN 
                tblPlBrandType bt WITH (NOLOCK) ON bt.ID = brand.BrandTypeID
            WHERE 
                ma.MaID IN (' . $this->pdo_sql->paramsForList(count($manufacture_ids), 'manufacture_ids', SQL::int) . ')
            ';

    $statement = $this->pdo_sql->prepare($sql);

    $statement->bindValuesList(':manufacture_ids', $manufacture_ids, SQL::int);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load manufacture details');
      $this->log_throwable_error(
          $exception,
          'Cannot load manufacture details for manufactureIds',
          ['manufacture_ids' => $manufacture_ids]
      );
      throw $exception;
    }
    return $statement->fetchAll();
  }


  /**
   * Database-Style Sorting Results In PHP By Using 'array_multisort'
   * It'll create intermediate arrays before passing control on to array_multisort()
   */
  public function array_orderBy() {
    $args = func_get_args();
    $data = array_shift($args);
    foreach ($args as $n => $field) {
      if (is_string($field)) {
        $tmp = array();
        foreach ($data as $key => $row)
          $tmp[$key] = $row[$field];
        $args[$n] = $tmp;
      }
    }
    $args[] = &$data;
    call_user_func_array('array_multisort', $args);
    return array_pop($args);
  }

  /**
   * Loading Brand Catalog for batch
   *
   * @param int $batch_id Batch ID
   *
   * @return int|null
   */
  public function get_batch_brand_catalog(int $batch_id) {
    $this->info(
        sprintf('Loading Brand Catalog for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );

    if ($batch_id===0){
      return null;
    }

    $sql = 'SELECT "BrandCatalogID" FROM "tblCurationBatch" WHERE "ID" = :batch_id';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load batch brand catalog');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load batch brand catalog for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    return $statement->fetchColumn();
  }

  /**
   * Load Batch Items
   *
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_batch_items(int $batch_id, int $qaStatus) : array {
    $this->info(
        sprintf('Loading batch items for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );
    if ($qaStatus != -1) {
        $andCondition = ' AND "ViQAStatusID"='.$qaStatus.' ';
    } else {
        $andCondition = '';
    }
    $sql = 'SELECT vi."ViSKU" AS sku,
                   vi."ViContextXnID" AS context_xn_id,
                   vi."ViQAStatusID" AS qa_status
            FROM  "tblVerificationItem" vi
            WHERE "ViBatchID" = :batch_id'.$andCondition.'
            ORDER BY "ViSKU"';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load batch items');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load batch items for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

    /**
     * Load Batch Items
     *
     * @param int $batch_id Batch ID
     *
     * @return array
     */
    public function get_batch_items_with_xn_id(int $batch_id, int $contextXnId, int $limit, int $offset) : array {
        $this->info(
            sprintf('Loading batch items for BatchId "%s"', $batch_id),
            ['batch_id' => $batch_id]
        );



        if ( $contextXnId == 1) {
            $sql = 'SELECT vi."ViSKU" AS sku,
                   vi."ViContextXnID" AS context_xn_id,
                   vi."ViQAStatusID" AS qa_status
            FROM  "tblVerificationItem" vi
            WHERE "ViBatchID" = :batch_id
            AND ( "ViContextXnID" is NULL OR "ViContextXnID" = 0)
            ORDER BY "ViSKU"
            LIMIT :limit OFFSET :offset
            ';

            $statement = $this->pdo_psql->prepare($sql);
            $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
            $statement->bindValue(':limit', $limit, PDO::PARAM_INT);
            $statement->bindValue(':offset', $offset, PDO::PARAM_INT);
        } else {
            $sql = 'SELECT vi."ViSKU" AS sku,
                   vi."ViContextXnID" AS context_xn_id,
                   vi."ViQAStatusID" AS qa_status
            FROM  "tblVerificationItem" vi
            WHERE "ViBatchID" = :batch_id
            AND  "ViContextXnID" = :contextXnId
            ORDER BY "ViSKU"
            LIMIT :limit OFFSET :offset
            ';

            $statement = $this->pdo_psql->prepare($sql);
            $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
            $statement->bindValue(':contextXnId', $contextXnId, PDO::PARAM_INT);
            $statement->bindValue(':limit', $limit, PDO::PARAM_INT);
            $statement->bindValue(':offset', $offset, PDO::PARAM_INT);
        }


        if ($statement->execute() === false) {
            $exception = ExecutionException::forStatement($statement, 'Cannot load batch items');
            $this->log_throwable_error(
                $exception,
                sprintf('Cannot load batch items for BatchId "%s"', $batch_id),
                ['batch_id' => $batch_id]
            );
            throw $exception;
        }

        return $statement->fetchAll();
    }

  /**
   * @param int   $batch_id Batch ID
   * @param array $skus     skus
   *
   * @return array
   */
  public function get_kitsco(int $batch_id, array $skus) : array {
    $this->info(
        sprintf('Loading kitsco for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id, 'skus' => $skus]
    );
    $skus = $this->pdo_psql->paramsForLists($skus);
    $sql = 'SELECT
            "ViSKU" AS sku
            FROM "tblVerificationItem"
            WHERE "ViBatchID" = :batch_id  AND "ViIsKitsco" = TRUE AND "ViSKU" IN ('.$skus.')';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get kitsco');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get kitsco for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id, 'skus' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project|null
   */
  public function get_rebrand_project_for_batch(int $batch_id) {
    $this->info(
        sprintf('Loading rebrand project for batch "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );
    $result = null;
    $curation_request_rebrand_details = $this->get_rebrand_project_for_batch_internal($batch_id);
    if ($curation_request_rebrand_details) {
      $rebrand_project_id = $curation_request_rebrand_details['rebrand_project_id'];
      $rebrand_project_details = $this->get_rebrand_project_details_external($rebrand_project_id);
      if ($rebrand_project_details) {
        $result = new Rebrand_Project();
        $result->setId($rebrand_project_details['id']);
        $result->setName($rebrand_project_details['name']);
      }
    }
    return $result ?: null;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  private function get_rebrand_project_for_batch_internal(int $batch_id) {
    $this->info(
        sprintf('Loading rebrand project internal data for batch "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );
    $sql = '
            SELECT
                "requestSku"."VerificationItemID" AS verification_item_id,
                "importRequest"."RebrandProjectID" AS rebrand_project_id,
                COUNT(DISTINCT "verificationItem"."ViID") AS skus_count
            FROM "tblVerificationItem" "verificationItem"
            JOIN "tblCurationRequestSKU" "requestSku" ON "requestSku"."VerificationItemID" = "verificationItem"."ViID"
            JOIN "tblCurationRequest" request ON request."ID" = "requestSku"."RequestID"
            JOIN "tblCurationImportRequest" "importRequest" ON "importRequest"."ID" = request."ImportRequestID"
            WHERE "verificationItem"."ViBatchID" = :batch_id
            GROUP BY "requestSku"."VerificationItemID", "importRequest"."RebrandProjectID"
            ORDER BY skus_count DESC
            LIMIT 1';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get rebrand project for batch');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get rebrand project for batch "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    if(gettype($result)==="array"){
      return $result;
    } else {
      return null;
    }
  }

  /**
   * This method tries to fetch rebrand project details through passed rebrand project id.
   *
   * @param int $rebrand_project_id Rebrand Project ID
   *
   * @return array
   */
  private function get_rebrand_project_details_external($rebrand_project_id) {

    if (empty($rebrand_project_id)) {
      $this->info(
          sprintf('No rebrand project id passed "%s"', $rebrand_project_id),
          ['rebrand_project_id' => $rebrand_project_id]
      );
      return [];
    }

    $rebrand_project_id = (int) $rebrand_project_id;
    $sql = '
    SELECT ID AS id, Name AS name
    FROM csn_product.dbo.tblPlRebrandProject rebrandProject WITH (NOLOCK)
    WHERE ID = :rebrand_project_id
    ';

    $statement = $this->pdo_sql->prepare($sql);
    $statement->bindValue(':rebrand_project_id', $rebrand_project_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get rebrand project details');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get rebrand project for batch "%s"', $rebrand_project_id),
          ['rebrand_project_id' => $rebrand_project_id]
      );
      throw $exception;
    }
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    if(gettype($result)==="array"){
      return $result;
    } else {
      return null;
    }
  }

    /**
     * @param string[] $skus to fetch the prices for
     *
     * @return array
     */
    public function get_skus_prices(array $skus) : array {
        $sql = 'SELECT pr.PrSKU as sku, pr.PrPrice as price
            FROM csn_product.dbo.tblProduct pr WITH (NOLOCK)
            WHERE pr.PrSKU IN (' . $this->pdo_sql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')';

        $statement = $this->pdo_sql->prepare($sql);
        $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

        $res = [];
        $wscCosts = [];
        $skusWithInvalidPrices = [];
        if ($statement->execute()) {
            $rows = $statement->fetchAll(PDO::FETCH_ASSOC);
            foreach ($rows as $row) {
                if (!Curation_Price_Loader::isPriceValid($row['price'])) {
                    $skusWithInvalidPrices[$row['sku']] = $row['sku'];
                }
            }
            if (!empty($skusWithInvalidPrices)) {
                $wscCosts = $this->get_skus_wsc_cost_with_margin(array_values($skusWithInvalidPrices));
            }

            foreach ($rows as $row) {
                $price = $row['price'];
                $sku = $row['sku'];
                if (!Curation_Price_Loader::isPriceValid($price) && array_key_exists($sku, $wscCosts)) {
                    $price = $wscCosts[$sku];
                }
                $res[$sku] = $price;
            }
        } else {
            $exception = ExecutionException::forStatement($statement, 'Cannot load fallback prices for SKUs');
            $this->log_throwable_error(
                $exception,
                'Cannot load fallback prices for SKUs'
            );
            throw $exception;
        }
        return $res;
    }

    /**
     * @param string[] $skus to fetch the prices for
     *
     * @return array
     * @throws \Exception
     */
    public function get_skus_wsc_cost_with_margin(array $skus) : array {

        $sql = 'select * from 
                (select *, ROW_NUMBER() over (partition by sku order by sku) AS ROWNUMBER from 
                    (SELECT   o.PrSKU as sku, s.ID, marginByClass.margin
                        FROM csn_product.dbo.tblOptionCombination o WITH (NOLOCK)
                        INNER JOIN csn_product.dbo.tblSupplierPart s WITH (NOLOCK)
                        ON s.ManufacturerPartID = o.ManufacturerPartID
                        INNER JOIN csn_product.dbo.tblJoinProductClass AS joinProductClass WITH(NOLOCK)
                        ON joinProductClass.prsku = o.PrSKU
                        LEFT JOIN csn_product.dbo.tblCurationAvgMarginByClass as marginByClass WITH(NOLOCK)
                        ON marginByClass.clid = joinProductClass.clid
                    WHERE o.PrSKU in (' . $this->pdo_sql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')) as sku_supplier_tbl) as get_five_supplierids
            where ROWNUMBER <=' . SELF::ROWS_COUNT;

        $statement = $this->pdo_sql->prepare($sql);
        $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));
        $res = [];
        if ($statement->execute()) {
            $rows = $statement->fetchAll(PDO::FETCH_ASSOC);
            //Start
            $this->info('List of Skus & SupplierIDs by rows', $rows);
            foreach ($skus as $singleSku) {
                $avgBaseCost = 0;
                foreach ($rows as $row) {
                    if($singleSku === $row['sku']) {
                        $this->info('List of Skus & SupplierIDs by each row: ', $row);
                        $resultAPI = $this->costApiClient->get_part_cost($row['ID']);
                        $this->info('costApiClient get_part_cost returned average baseCost for this sku by supplierPartID ', [$row['ID'] => [$resultAPI]]);
                        if ($row['margin'] === null) {
                            $row['margin'] = SELF::DEFAULT_MARGIN;
                        }
                        $avgBaseCost = $resultAPI;
                        if ($avgBaseCost != 0) {
                            break;
                        }
                    }
                }
                $res[$singleSku] = $avgBaseCost != 0 ? $avgBaseCost : CostApiService::FALLBACK_PRICE;
                $this->info('Final value average baseCost for this Sku ',[$singleSku => $res[$singleSku]]);
            }
            //end
        } else {
            $exception = ExecutionException::forStatement($statement, 'Cannot load WSC fallback prices for SKUs');
            $this->log_throwable_error(
                $exception,
                'Cannot load WSC fallback prices for SKUs'
            );
            throw $exception;
        }
        return $res;
    }

    public function get_curation_price_tiers_for_skus(array $skusPrices) : array {
        $this->info('Loading Price tiers for SKUs from Postgres DB');
        $result = [];

        if (empty($skusPrices)) {
            return $result;
        }

        $skusWithInvalidPrices = [];
        $fallbackCosts = [];
        foreach (array_chunk($skusPrices, 1000, true) as $chunk) {
            foreach ($chunk as $sku => $price) {
                if (!Curation_Price_Loader::isPriceValid($price)) {
                    $skusWithInvalidPrices[$sku] = $sku;
                }
            }
        }
        if (!empty($skusWithInvalidPrices)) {
            $fallbackCosts = $this->get_skus_prices(array_values($skusWithInvalidPrices));
        }
        $final_result = [];
        foreach (array_chunk($skusPrices, 1000, true) as $chunk) {
            // create temp table to keep same $skus order
            $column_map  = [
                'SKU'   => SQL::nvarchar(8),
                'Price' => SQL::money,
            ];
            $inserted_data = [];
            foreach ($chunk as $sku => $price) {
                if (!Curation_Price_Loader::isPriceValid($price) && array_key_exists($sku, $fallbackCosts)) {
                    $price = $fallbackCosts[$sku];
                }
                $inserted_data[] = [
                    'SKU'   => $sku,
                    'Price' => $price,
                ];
            }

            $sql = SQLBulkHelper::get_temp_table_json_sql($column_map, 'tmpCurationPriceTierSkus');

            $sql .= '
              SELECT
                    p.PrSKU AS sku,
                    tmp.Price AS sale_price,
                    cl.ClID AS cl_id,
                    p.PrBclgID AS pr_bclg_id
              FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                    INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
                    INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
                    INNER JOIN #tmpCurationPriceTierSkus tmp ON p.PrSKU = tmp.SKU';

            $statement = $this->pdo_sql->prepare($sql);
            $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($inserted_data), PDO::PARAM_STR);

            if (!$statement->execute()) {
                throw ExecutionException::forStatement($statement, 'Failed to load context data for skus');
            }

            $result = $statement->fetchAll();

            foreach ($result as $row) {
                $price_tier = $this->get_price_tier(floatval($row["sale_price"]), $row["cl_id"], $row["pr_bclg_id"]);
                $final_result[$row['sku']] = $price_tier;
            }
        }

        foreach ($final_result as $sku => $price_tier) {
            $this->info('sku:' . $sku . ' pricetier:' . $price_tier);
        }

        return $final_result;
    }

    public function get_price_tier(float $sale_price, int $cl_id, int $b_clg_id): int {
        $this->log_info('Curation_Tool_Postgres_DAO: Loading context manufacturers data', ['SalePrice' => $sale_price, 'clID' => $cl_id, 'bclgId' => $b_clg_id]);

        $sql = '
        SELECT COALESCE(
            CASE WHEN MAX("CptPriceTier") = 4 THEN 4 ELSE MAX("CptPriceTier") + 1 END, 1
            ) AS CptPriceTier
        FROM "tblClassPriceTier" cpt
        WHERE "CptPriceTierCeiling" < :salePrice
            AND "CptClID" = :clID
            AND "CptBclgID" = :bclgId';

        $statement = $this->pdo_psql->prepare($sql);
        $statement->bindValue(':salePrice', $sale_price, PDO::PARAM_STR);
        $statement->bindValue(':clID', $cl_id, PDO::PARAM_INT);
        $statement->bindValue(':bclgId', $b_clg_id, PDO::PARAM_INT);

        if (!$statement->execute()) {
            $exception = ExecutionException::forStatement($statement, 'Failed to load context data for skus');
            $this->log($exception, $exception->getMessage(), ['SalePrice' => $sale_price, 'clID' => $cl_id, 'bclgId' => $b_clg_id]);
            throw $exception;
        }
        return $statement->fetch(PDO::FETCH_COLUMN);
    }

}