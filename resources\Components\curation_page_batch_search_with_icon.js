/**
 * Renders search form with icon to search for a batch
 *
 * <AUTHOR> <em<PERSON><PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

import React from 'react';
import Translation from '@wayfair/translation';
import {TextInput} from '@wayfair/homebase-extranet';
import {InputGroup} from '@wayfair/homebase-extranet';
import {IconV2 as Icon} from '@wayfair/homebase-extranet';
import {Button} from '@wayfair/homebase-extranet';
import {faSearch} from '@fortawesome/free-solid-svg-icons';

export const CurationPageBatchSearchWithIcon = () => (
  <form>
    <InputGroup>
      <TextInput
        isGrouped
        label={Translation({
          msgid:
            'CurationTool.CurationPageBatchSearchBatchIDLabel',
        })}
        name="batch_id"
      />
      <Button type="submit" secondary>
        <Icon icon={faSearch} className="margin_right_small" />{' '}
        <Translation msgid="CurationTool.CurationPageBatchS<PERSON>chSearchButton" />
      </Button>
    </InputGroup>
  </form>
);
