<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader_Interface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Curation_Item_Group_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
    * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
    */
    private $postgres_dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
     */
    private $dao;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $feature_toggle;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader_Interface
     */
    private $itemLoader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory
     */
    private $factory;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader
     */
    private $curation_item_group_loader;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->postgresql_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $this->feature_toggle = $this->prophesize(FeatureTogglesInterface::class);
        $this->itemLoader = $this->prophesize(Curation_Item_Loader_Interface::class);
        $this->factory = $this->prophesize(Curation_Item_Group_Factory::class);
        $this->logger = $this->prophesize(LoggerInterface::class);

        $this->curation_item_group_loader = new Curation_Item_Group_Loader(
            $this->itemLoader->reveal(),
            $this->dao->reveal(),
            $this->factory->reveal(),
            $this->postgresql_dao->reveal(),
            $this->feature_toggle->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function populate_kitsco_feature_toggle_on()
    {
        $batch_id = 1;
        $sku = 'ABC';
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn(1);
        $curationItem->get_price_tier()->willReturn(3);
        $curationItem->get_saved_at()->willReturn(null);
        $curationItem->get_final_granular_style_id()->willReturn(10);
        $curationItem->get_kit_parents()->willReturn([1]);
        $curationItem->get_type()->willReturn(Curation_Item_Type::shared());

        $data = [
            ['sku' => 'ABC'],
        ];
        
        $this->itemLoader->load($batch_id, -1)->willReturn([$curationItem]);
        $this->feature_toggle->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->dao->get_statuses(Argument::cetera())->willReturn([]);
        $this->postgresql_dao->get_kitsco($batch_id, [$sku])->willReturn($data);
        $this->dao->get_parents(Argument::cetera())->willReturn([]);
        $this->factory->createFromCurationItems(Argument::cetera())->willReturn([]);
        $this->curation_item_group_loader->load($batch_id, 0, 100, 1, '', -1);
    }
}
