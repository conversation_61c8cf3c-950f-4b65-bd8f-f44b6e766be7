<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Substyle_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Suggestion_Info_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Suggestion_Info_Saver_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_Postgres_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Style_Suggestion_Storage;

class Automatic_Style_Suggestion_Info_Saver_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Automatic_Suggestion_Info_Saver_Storage
     */
    private $dao;

    /**
     * @var Style_Suggestion_Storage
     */
    private $style_suggestion_storage;

    /**
     * @var Automatic_Suggestion_Info_Saver
     */
    private $subject;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_Postgres_DAO
     */
    private $postgresql_dao;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Automatic_Suggestion_Info_Saver_Storage::class);
        $this->postgresql_dao = $this->prophesize(Automatic_Suggestion_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->style_suggestion_storage = $this->prophesize(Style_Suggestion_Storage::class);
        $this->logger = $this->prophesize(LoggerInterface::class);

        $this->subject = new Automatic_Suggestion_Info_Saver(
            $this->dao->reveal(),
            $this->style_suggestion_storage->reveal(),
            $this->featureToggles->reveal(),
            $this->postgresql_dao->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_saves_suggested_info_for_curated_eligible_skus()
    {
        $batch_id = 1;
        $skus = ['A', 'B'];
        $style_id = 1;
        $substyle_id = 1;
        $saved_at = 'today';
        $notes = 'Some notes';
        $reason = 1;

        $eligible_skus = ['A'];

        $suggested_styles_array = [
            [
                'sku' => 'A',
                'threshold' => '0.7',
                'suggestion_id' => 1,
                'suggestion_name' => 'test',
                'rank' => 1,
                'probability' => 1,
            ]
        ];

        /** @var Automatic_Style_Suggestion_Collection $style_suggestion_collection */
        $style_suggestion_collection = $this->prophesize(Automatic_Style_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_style_suggestion($skus)->willReturn($style_suggestion_collection->reveal());
        $style_suggestion_collection->get_skus_with_styles()->willReturn($eligible_skus);
        $style_suggestion_collection->to_array()->willReturn($suggested_styles_array);

        /** @var Automatic_Substyle_Suggestion_Collection $substyle_suggestion_collection */
        $substyle_suggestion_collection = $this->prophesize(Automatic_Substyle_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_substyle_suggestion($skus)->willReturn($substyle_suggestion_collection->reveal());
        $substyle_suggestion_collection->get_skus_with_substyles()->willReturn($eligible_skus);
        $substyle_suggestion_collection->to_array()->willReturn($suggested_styles_array);

        $this->dao->save_suggested_style_info($batch_id, $eligible_skus, $suggested_styles_array, $style_id, $saved_at, $notes, $reason);
        $this->dao->save_suggested_substyle_info($batch_id, $eligible_skus, $suggested_styles_array, $style_id, $substyle_id, $saved_at, $notes, $reason);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledTimes(2);
        $this->postgresql_dao->save_suggested_style_info($batch_id, $eligible_skus, $suggested_styles_array, $style_id, $saved_at, $notes, $reason);
        $this->postgresql_dao->save_suggested_substyle_info($batch_id, $eligible_skus, $suggested_styles_array, $style_id, $substyle_id, $saved_at, $notes, $reason);
        $this->subject->save_suggested_info_for_curated_skus($batch_id, $skus, $style_id, $substyle_id, $saved_at, $notes, $reason);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_removes_suggested_for_excluded_eligible_skus()
    {
        $batch_id = 1;
        $skus = ['A', 'B'];

        $eligible_skus = ['A'];

        /** @var Automatic_Style_Suggestion_Collection $style_suggestion_collection */
        $style_suggestion_collection = $this->prophesize(Automatic_Style_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_style_suggestion($skus)->willReturn($style_suggestion_collection->reveal());
        $style_suggestion_collection->get_skus_with_styles()->willReturn($eligible_skus);

        /** @var Automatic_Substyle_Suggestion_Collection $substyle_suggestion_collection */
        $substyle_suggestion_collection = $this->prophesize(Automatic_Substyle_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_substyle_suggestion($skus)->willReturn($substyle_suggestion_collection->reveal());
        $substyle_suggestion_collection->get_skus_with_substyles()->willReturn($eligible_skus);

        $this->dao->remove_suggested_style_info($batch_id, $eligible_skus)->shouldBeCalled();
        $this->dao->remove_suggested_substyle_info($batch_id, $eligible_skus)->shouldBeCalled();
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledTimes(2);
        $this->postgresql_dao->remove_suggested_style_info($batch_id, $eligible_skus);
        $this->postgresql_dao->remove_suggested_substyle_info($batch_id, $eligible_skus);
        $this->subject->remove_suggested_info_for_excluded_skus($batch_id, $skus);
    }

    /**
     * @test
     *
     * @return void
     */
    public function does_not_save_suggested_info_for_empty_skus_array()
    {
        $batch_id = 1;
        $skus = [];
        $style_id = 1;
        $substyle_id = 1;
        $saved_at = 'today';
        $notes = 'Some notes';
        $reason = 1;

        $eligible_skus = [];
        $suggested_styles = [];

        /** @var Automatic_Style_Suggestion_Collection $style_suggestion_collection */
        $style_suggestion_collection = $this->prophesize(Automatic_Style_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_style_suggestion($skus)->willReturn($style_suggestion_collection->reveal());
        $style_suggestion_collection->get_skus_with_styles()->willReturn($eligible_skus);
        $style_suggestion_collection->to_array()->willReturn($suggested_styles);

        /** @var Automatic_Substyle_Suggestion_Collection $substyle_suggestion_collection */
        $substyle_suggestion_collection = $this->prophesize(Automatic_Substyle_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_substyle_suggestion($skus)->willReturn($substyle_suggestion_collection->reveal());
        $substyle_suggestion_collection->get_skus_with_substyles()->willReturn($eligible_skus);
        $substyle_suggestion_collection->to_array()->willReturn($suggested_styles);

        $this->dao->save_suggested_style_info($batch_id, $eligible_skus, $suggested_styles, $style_id, $saved_at, $notes, $reason)->shouldNotBeCalled();
        $this->dao->save_suggested_substyle_info($batch_id, $eligible_skus, $suggested_styles, $style_id, $substyle_id, $saved_at, $notes, $reason)->shouldNotBeCalled();
        $this->subject->save_suggested_info_for_curated_skus($batch_id, $skus, $style_id, $substyle_id, $saved_at, $notes, $reason);
    }

    /**
     * @test
     *
     * @return void
     */
    public function does_not_call_remove_suggested_for_empty_skus_array()
    {
        $batch_id = 1;
        $skus = [];

        $eligible_skus = [];

        /** @var Automatic_Style_Suggestion_Collection $style_suggestion_collection */
        $style_suggestion_collection = $this->prophesize(Automatic_Style_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_style_suggestion($skus)->willReturn($style_suggestion_collection->reveal());
        $style_suggestion_collection->get_skus_with_styles()->willReturn($eligible_skus);

        /** @var Automatic_Substyle_Suggestion_Collection $substyle_suggestion_collection */
        $substyle_suggestion_collection = $this->prophesize(Automatic_Substyle_Suggestion_Collection::class);

        $this->style_suggestion_storage->get_substyle_suggestion($skus)->willReturn($substyle_suggestion_collection->reveal());
        $substyle_suggestion_collection->get_skus_with_substyles()->willReturn($eligible_skus);

        $this->dao->remove_suggested_style_info($batch_id, $eligible_skus)->shouldNotBeCalled();
        $this->dao->remove_suggested_substyle_info($batch_id, $eligible_skus)->shouldNotBeCalled();

        $this->subject->remove_suggested_info_for_excluded_skus($batch_id, $skus);
    }
}
