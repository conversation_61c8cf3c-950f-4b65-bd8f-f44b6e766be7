/**
 * EB Curation style decision note component
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';

import {Tooltip, IconV2 as Icon, Text, TEXT_STYLE, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {faInfoCircle} from '@fortawesome/free-solid-svg-icons';

class QACurationStyleDecisionNote extends React.Component {
  static propTypes = {
    decisionNote: PropTypes.string.isRequired,
  };

  state = {isOpen: false};

  openTooltip = () => this.setState({isOpen: true});

  closeTooltip = () => this.setState({isOpen: false});

  render() {
    return (
      <Tooltip
        placement="left"
        showClose={false}
        isOpen={this.state.isOpen}
        onRequestClose={this.closeTooltip}
        content={
          <>
            <Text fontStyle={TEXT_STYLE.BOLD} is="span">
              <Translation msgid="CurationTool.qaDecisionNotesLabelText" />
            </Text>
            {` "${this.props.decisionNote}"`}
          </>
        }
        target={
          <Text align={TEXT_ALIGNMENTS.CENTER}>
            <Icon
              icon={faInfoCircle}
              onMouseEnter={this.openTooltip}
              onMouseLeave={this.closeTooltip}
              title={
                <Translation msgid="CurationTool.qaDecisionNoteIconTitleText" />
              }
            />
          </Text>
        }
      />
    );
  }
}

export default QACurationStyleDecisionNote;
