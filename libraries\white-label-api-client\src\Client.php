<?php

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi;

use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;
use WF\Curation\WhiteLabelApi\DTO\RequestBuilder;
use WF\Curation\WhiteLabelApi\DTO\Response;
use WF\Curation\WhiteLabelApi\DTO\ResponseBuilder;
use WF\Curation\WhiteLabelApi\Entity\Batch;
use WF\Curation\WhiteLabelApi\Exception\ConfigException;

class Client
{
    private const BATCH_CREATE_ENDPOINT = '/v1/batch/create';

    /**
     * @var ClientConfig
     */
    private $config;

    /**
     * Client constructor.
     *
     * @param  ClientConfig $config
     * @throws \Exception
     */
    public function __construct(?ClientConfig $config = null)
    {
        if (!empty($config)) {
            $this->config = $config;
        }
    }

    /**
     * @return ClientConfig
     */
    public function getConfig(): ClientConfig
    {
        return $this->config;
    }

    /**
     * @param  ClientConfig  $config
     * @return void
     */
    public function setConfig(ClientConfig $config)
    {
        $this->config = $config;
    }

    /**
     * @param  Batch[]  $batches
     * @return Response
     *
     * @throws ConfigException
     */
    public function createBatch(array $batches): ?Response
    {
        $payload = RequestBuilder::buildJsonRequest($batches);
        $response = $this->post(sprintf('%s%s', $this->config->getBaseUri(), self::BATCH_CREATE_ENDPOINT), $payload);

        return $response !== null ? ResponseBuilder::buildResponseDTO($response) : null;
    }

    /**
     * @param string $endpoint
     * @param array  $payload
     *
     * @return ResponseInterface|null
     * @throws ConfigException
     */
    protected function post(string $endpoint, array $payload): ?ResponseInterface
    {
        $requestOptions = [
            'json' => $payload
        ];

        $logger = $this->config->getLogger();
        $httpClient = $this->config->getHttpClient();
        try {
            $response = $httpClient->post($endpoint, $requestOptions);

            $context = [
                'request' => $payload,
                'responseCode' => $response->getStatusCode(),
                'responseReason' => $response->getReasonPhrase(),
                'response' => (string)$response->getBody(),
            ];

            if ($response->getStatusCode() !== 200) {
              $logger->error("POST {$endpoint}", $context);
            } else {
              $logger->info("POST {$endpoint}", $context);
            }

            return $response;
        } catch (RequestException $exception) {
            $response = $exception->getResponse();
            $context = [
                'request' => $payload,
                'responseCode' => ($response !== null ? $response->getStatusCode() : $exception->getCode()),
                'responseReason' => ($response !== null ? $response->getReasonPhrase() : $exception->getMessage()),
                'response' => null
            ];

            $logger->error("POST {$endpoint}", $context);

            return $response;
        }
    }
}
