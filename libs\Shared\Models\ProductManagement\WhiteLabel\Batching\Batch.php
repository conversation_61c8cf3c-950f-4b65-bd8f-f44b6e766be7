<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\Globalization\Brand_Catalog_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model;

/**
 * Class Batch
 */
class Batch implements Downstreamable_Batch {
  /**
   * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_SKU[] An ordered array of Model objects.
   */
  public $models = [];

  const PRODUCT_ADDITION_BATCH     = 0;
  const MANUAL_ADDITION_BATCH      = 1;
  const PARTIAL_WHITE_LABEL_BATCH  = 2;
  const NON_EB_STYLE_TAGGING_MANUAL = 3;
  const NON_EB_STYLE_TAGGING_NEW_INTRO = 4;

  const NON_EB_STYLE_TAGGING_BATCH_STATUS = [
      self::NON_<PERSON>B_STYLE_TAGGING_MANUAL,
      self::NON_EB_STYLE_TAGGING_NEW_INTRO
  ];

  const UNKNOWN_CURATION_REQUEST_TYPE = -1;
  const UNKNOWN_MANUAL_REQUEST_TYPE = -1;
  const UNKNOWN_BATCH_BRAND_CATALOG   = 0;

  const PROCESSING_STATUS = 0;
  const DOWNSTREAMED_STATUS = 6;

  const PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW = 1;
  const PROCESS_TYPE_CURATION_BATCH_AUTOMATED = 2;

  const CURATION_AUTOMATION_TYPE_DS_MODEL = 1;
  const CURATION_AUTOMATION_TYPE_AUTOMAPPED = 2;

  const CURATION_REQUEST_TYPE_MAP = [
      self::PRODUCT_ADDITION_BATCH => 'Product Addition',
      self::MANUAL_ADDITION_BATCH  => 'Curation Loader',
  ];

  /**
   * @var int
   */
  private $id;

  /**
   * @var int
   */
  private $process_type = self::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW;

  /**
   * @var null|int
   */
  private $automation_type = null;

  /**
   * @var int
   */
  private $supplier_id = 0;

  /**
   * @var int
   */
  private $brand_catalog_id = 0;

  /**
   * @var string
   */
  private $brand_catalog_short_name = '';

  /**
   * @var int
   */
  private $curation_request_type = self::UNKNOWN_MANUAL_REQUEST_TYPE;

  /**
   * @var int|null
   */
  private $curation_manual_request_type;

  /**
   * @var \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model[]
   */
  private $curation_requests = [];

  /**
   * written during fetching DB results
   * @var string|null
   */
  private $assigned_employee; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $curation_batch_process_type = self::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW;

  /**
   * @return int|null
   */
  public function get_id() : ?int {
    return $this->id;
  }

  /**
   * @param int|null $id id
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function set_id($id) {
    $this->id = $id;

    return $this;
  }

  /**
   * @return int
   */
  public function get_process_type(): int {
    return $this->process_type;
  }

  /**
   * @param int $process_type process_type
   *
   * @return self
   */
  public function set_process_type(int $process_type): self {
    $this->process_type = $process_type;

    return $this;
  }

  /**
   * @return int
   */
  public function get_automation_type(): int {
    return $this->automation_type;
  }

  /**
   * @param int $automation_type automation_type
   *
   * @return self
   */
  public function set_automation_type(int $automation_type): self {
    $this->automation_type = $automation_type;

    return $this;
  }

  /**
   * @return int
   */
  public function get_supplier_id() : int {
    return $this->supplier_id;
  }

  /**
   * @param int $supplier_id supplier id
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function set_supplier_id(int $supplier_id) {
    $this->supplier_id = $supplier_id;

    return $this;
  }

  /**
   * @param int $brand_catalog_id brand catalog id
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function set_brand_catalog_id(int $brand_catalog_id) {
    $this->brand_catalog_id = $brand_catalog_id;

    return $this;
  }

  /**
   * @return int
   */
  public function get_brand_catalog_id() : int {
    return $this->brand_catalog_id;
  }

  /**
   * @param string $brand_catalog_short_name Brand catalog short name value
   *
   * @return void
   */
  public function set_brand_catalog_short_name(string $brand_catalog_short_name): void {
    $this->brand_catalog_short_name = $brand_catalog_short_name;
  }

  /**
   * @return string
   */
  public function get_brand_catalog_short_name() : string {
    return $this->brand_catalog_short_name;
  }

  /**
   * @param int $type_id type id
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function set_curation_request_type(int $type_id) {
    $this->curation_request_type = $type_id;

    return $this;
  }

  /**
   * @return int
   */
  public function get_curation_request_type() : int {
    return $this->curation_request_type;
  }

  /**
   * @param int $curation_batch_process_type process type
   *
   * @return void
   */
  public function set_curation_batch_process_type(int $curation_batch_process_type): void {
    $this->curation_batch_process_type = $curation_batch_process_type;
  }

  /**
   * @return int
   */
  public function get_curation_batch_process_type() : int {
    return $this->curation_batch_process_type;
  }

  /**
   * @return string
   */
  public function get_curation_request_type_string() : string {
    if ($this->curation_request_type === static::UNKNOWN_CURATION_REQUEST_TYPE) {
      return '';
    }

    return Batch::CURATION_REQUEST_TYPE_MAP[$this->curation_request_type];
  }

  /**
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_SKU[]
   */
  public function get_skus() : array {
    return $this->models;
  }

  /**
   * @param string $sku sku
   *
   * @return bool
   */
  public function has_sku(string $sku) : bool {
    return isset($this->models[$sku]);
  }

  /**
   * @param string $sku SKU
   *
   * @return null|\WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_SKU
   */
  public function get_sku(string $sku) {
    if (isset($this->models[$sku])) {
      return $this->models[$sku];
    }

    foreach ($this->models as $sku_index => $batch_sku) {
      if (strtoupper($sku) === strtoupper($sku_index)) {
        return $batch_sku;
      }
    }

    return null;
  }

  /**
   * Overrides the default behavior to speed up the sku existence lookup
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_SKU $model the model to be added
   *
   * @return void
   */
  public function push(Batch_SKU $model): void {
    $this->models[$model->get_sku()] = $model;
  }

  /**
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model $curation_request Curation Requests
   *
   * @return void
   */
  public function push_curation_request(Curation_Request_Model $curation_request) {
    $this->curation_requests[$curation_request->get_id()] = $curation_request;
  }

  /**
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model[]
   */
  public function get_curation_requests() : array {
    return $this->curation_requests;
  }

  /**
   * @return bool
   */
  public function is_display_sku_eligible() : bool {
    if ($this->get_brand_catalog_id() !== Brand_Catalog_Model::BRAND_CATALOG_WAYFAIR_US
        || $this->get_curation_request_type() !== static::PRODUCT_ADDITION_BATCH
    ) {
      return false;
    }

    foreach ($this->get_skus() as $sku) {
      if (!($sku instanceof Verified_Batch_SKU)) {
        return false;
      }

      if ($sku->blocks_display_sku_eligibility()) {
        return false;
      }
    }

    return true;
  }

  /**
   * @return int|null
   */
  public function get_curation_manual_request_type() : ?int {
    return $this->curation_manual_request_type;
  }

  /**
   * @param int|null $curation_manual_request_type Manual Request Type
   *
   * @return void
   */
  public function set_curation_manual_request_type(?int $curation_manual_request_type) : void {
    $this->curation_manual_request_type = $curation_manual_request_type;
  }

  /**
   * @return int|null
   */
  public function get_pt_ticket_id() {
    return null;
  }

    /**
     * Allows setting the Models as array elements.
     *
     * @param mixed $offset the array index to use
     * @param mixed $value  the value to be assigned
     *
     * @return void
     *
     * @see ArrayAccess::offsetSet()
     */
    #[\ReturnTypeWillChange]
    // @codingStandardsIgnoreLine - ignoring mixed casing sniff of method names.
    public function offsetSet($offset, $value) {
        if (is_null($offset)) {
            $this->models[] = $value;
        } else {
            $this->models[$offset] = $value;
}
    }

    /**
     * Checks to see if an index exists.
     *
     * @param mixed $offset the array index to use
     *
     * @return bool
     *
     * @see ArrayAccess::offsetExists()
     */
    #[\ReturnTypeWillChange]
    // @codingStandardsIgnoreLine - ignoring mixed casing sniff of method names.
    public function offsetExists($offset) {
        return isset($this->models[$offset]);
    }

    /**
     * Allows unsetting a Model.
     *
     * @param mixed $offset the array index to use
     *
     * @return void
     *
     * @see ArrayAccess::offsetUnset()
     */
    #[\ReturnTypeWillChange]
    // @codingStandardsIgnoreLine - ignoring mixed casing sniff of method names.
    public function offsetUnset($offset) {
        unset($this->models[$offset]);
    }

    /**
     * Gets the values specified by the offset.
     *
     * @param mixed $offset the array index to use
     *
     * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_SKU|null the Model at the given offset
     *
     * @see ArrayAccess::offsetGet()
     */
    #[\ReturnTypeWillChange]
    // @codingStandardsIgnoreLine - ignoring mixed casing sniff of method names.
    public function offsetGet($offset) {
        // Cannot be return type bool due to return of null
        return isset($this->models[$offset]) ? $this->models[$offset] : null;
    }

    /**
     * Returns the number of Models currently in the Collection.
     *
     * @return int Number of Models
     */
    #[\ReturnTypeWillChange]
    public function count() {
        return count($this->models);
    }

    /**
     * Get the iterator for the internal Model array.
     *
     * @return \ArrayIterator
     */
    #[\ReturnTypeWillChange]
    // @codingStandardsIgnoreLine - ignoring mixed casing sniff of method names.
    public function getIterator() {
        return new \ArrayIterator($this->models);
    }
}
