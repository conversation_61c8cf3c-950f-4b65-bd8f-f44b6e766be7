<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\DTO;

use Psr\Http\Message\ResponseInterface;

final class ResponseBuilder
{
    private function __construct()
    {
    }

    /**
     * @param  ResponseInterface $response
     * @return Response
     */
    public static function buildResponseDTO(ResponseInterface $response): Response
    {
        $responseCode = $response->getStatusCode();
        $responseBody = (string)$response->getBody();
        $responseData = \json_decode($responseBody, true);

        switch ($responseCode) {
            case 200:
                return new Response(true);
            case 422:
                $isRequestSuccessful = false;
                $isValid = false;

                $validationDetails = array_map(
                    function ($data) {
                        $batchId = $data['batchId'];
                        $errors = $data['errors'];
                        $skus = $data['skus'];

                        return new ValidationDetail($batchId, $errors, $skus);
                    },
                    $responseData['validationErrors']
                );

                return new ValidationErrorResponse($isRequestSuccessful, $isValid, $validationDetails);
            case 400:
                $isRequestSuccessful = false;
                $errorCode = (int) $responseData['errorCode'];
                $errorMessage = $responseData['errorMessage'];

                return new ResponseError($isRequestSuccessful, $errorCode, $errorMessage);
            default:
                return new ResponseError(false, 0, 'Unidentified response code');
        }
    }
}
