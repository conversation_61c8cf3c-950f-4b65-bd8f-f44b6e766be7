<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;

class Curation_QA_Decision_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_creates_approval_decision()
    {
        $decision = Curation_QA_Decision::approve(443, ['SKU1'], 111);

        $this->assertInstanceOf(Curation_QA_Decision::class, $decision);
        $this->assertEquals(443, $decision->get_batch_id());
        $this->assertTrue($decision->get_qa_status()->equals(Curation_QA_Status::approved()));
        $this->assertEquals(['SKU1'], $decision->get_skus());
        $this->assertEquals(111, $decision->get_employee_id());
        $this->assertEquals('', $decision->get_reason());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_creates_rejection_decision()
    {
        $decision = Curation_QA_Decision::reject(443, ['SKU1'], 111, 'reason');

        $this->assertInstanceOf(Curation_QA_Decision::class, $decision);
        $this->assertEquals(443, $decision->get_batch_id());
        $this->assertTrue($decision->get_qa_status()->equals(Curation_QA_Status::rejected()));
        $this->assertEquals(['SKU1'], $decision->get_skus());
        $this->assertEquals(111, $decision->get_employee_id());
        $this->assertEquals('reason', $decision->get_reason());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_creates_updated_decision()
    {
        $decision = Curation_QA_Decision::update(
            443,
            ['SKU1'],
            123,
            1,
            2,
            3,
            4
        );

        $this->assertInstanceOf(Curation_QA_Decision::class, $decision);
        $this->assertEquals(443, $decision->get_batch_id());
        $this->assertTrue($decision->get_qa_status()->equals(Curation_QA_Status::updated()));
        $this->assertEquals(['SKU1'], $decision->get_skus());
        $this->assertEquals(123, $decision->get_employee_id());
        $this->assertEquals(1, $decision->get_price_tier_override());
        $this->assertEquals(2, $decision->get_final_style_id());
        $this->assertEquals(3, $decision->get_final_substyle_id());
        $this->assertEquals(4, $decision->get_final_brand_id());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_creates_updated_but_excluded_decision()
    {
        $decision = Curation_QA_Decision::excludeFromWL(443, ['SKU1'], 111, 1);

        $this->assertInstanceOf(Curation_QA_Decision::class, $decision);
        $this->assertEquals(443, $decision->get_batch_id());
        $this->assertTrue($decision->get_qa_status()->equals(Curation_QA_Status::updated()));
        $this->assertEquals(['SKU1'], $decision->get_skus());
        $this->assertEquals(111, $decision->get_employee_id());
        $this->assertEquals(1, $decision->get_exclusion_reason());
    }
}
