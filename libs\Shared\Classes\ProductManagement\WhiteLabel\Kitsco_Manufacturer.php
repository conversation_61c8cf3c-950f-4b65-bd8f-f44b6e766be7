<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel;

use WF\Shared\Classes\ProductManagement\Curation\World_Regions;

class Kitsco_Manufacturer {
  const KITSCO_US = 42338;
  const KITSCO_EU = 43648;

  /**
   * @var array
   */
  private $region_kitsco_manufacturer_map = [
      World_Regions::REGION_US => self::KITSCO_US,
      World_Regions::REGION_EU => self::KITSCO_EU,
  ];

  /**
   * @return int[]
   */
  private function get_kitsco_ids() {
    return [
      static::KITSCO_US,
      static::KITSCO_EU,
    ];
  }

  /**
   * @param int $user_region User Region Value
   *
   * @return int
   */
  public function get_manufacturer_id(int $user_region) : int {
    return $this->region_kitsco_manufacturer_map[$user_region];
  }

  /**
   * @param int $manufacturer_id manufacturer id
   *
   * @return bool
   */
  public function is_kitsco_manufacturer(int $manufacturer_id) {
    return in_array($manufacturer_id, $this->get_kitsco_ids());
  }
}
