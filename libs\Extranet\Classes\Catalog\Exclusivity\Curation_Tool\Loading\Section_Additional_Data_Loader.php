<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Curation_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Substyle_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Loader;
use WF\Extranet\Classes\Curation\Redirect\Redirect_Product_Page_URL_Helper;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Candidate_DTO;
use WF\Shared\Curation\Api\Exclusivity_<PERSON>sortment\Exclusivity_Assortment_Service;
use Psr\Log\LoggerInterface;

class Section_Additional_Data_Loader {
  use Logging_Trait;

  public const SIDE_THUMBNAIL = 204;

  /**
   * @var Supplier_Data_Loader
   */
  private $supplier_data_loader;

  /**
   * @var Redirect_Product_Page_URL_Helper
   */
  private $product_url_helper;

  /**
   * @var Section_Historical_Suggested_Style_Loader
   */
  private $cached_suggestion_info_loader;

  /**
   * @var Investment_Workflow_Storage
   */
  private $investment_storage;

  /**
   * @var Style_Suggestion_Storage
   */
  private $style_suggestion_storage;

  /**
   * @var Exclusivity_Assortment_Service
   */
  private $exclusivity_assortment_service;

  /**
   * Section_Additional_Data_Loader constructor.
   *
   * @param Supplier_Data_Loader                      $supplier_data_loader           Supplier_Data_Loader
   * @param Redirect_Product_Page_URL_Helper          $product_url_helper             Redirect_Product_Page_URL_Helper
   * @param Section_Historical_Suggested_Style_Loader $cached_suggestion_info_loader  Cached suggestions loader
   * @param Exclusivity_Assortment_Service            $exclusivity_assortment_service Exclusivity Assortment service
   * @param Investment_Workflow_Storage               $investment_workflow_storage    The Storage proxy for the API_Investment_Workflow
   * @param Style_Suggestion_Storage                  $style_suggestion_storage       Style Suggestion Storage
   * @param LoggerInterface|null                      $logger                         Logger
   */
  public function __construct(
      Supplier_Data_Loader $supplier_data_loader,
      Redirect_Product_Page_URL_Helper $product_url_helper,
      Section_Historical_Suggested_Style_Loader $cached_suggestion_info_loader,
      Exclusivity_Assortment_Service $exclusivity_assortment_service,
      Investment_Workflow_Storage $investment_workflow_storage,
      Style_Suggestion_Storage $style_suggestion_storage,
      ?LoggerInterface $logger = null
  ) {
    $this->supplier_data_loader           = $supplier_data_loader;
    $this->product_url_helper             = $product_url_helper;
    $this->cached_suggestion_info_loader  = $cached_suggestion_info_loader;
    $this->exclusivity_assortment_service = $exclusivity_assortment_service;
    $this->investment_storage             = $investment_workflow_storage;
    $this->style_suggestion_storage       = $style_suggestion_storage;
    $this->logger                         = $logger;
  }

  /**
   * @param int   $batch_id Batch ID
   * @param array $sections Sections
   *
   * @return void
   */
  public function populate(
      int $batch_id,
      array $sections
  ) {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        $skus[$item->get_sku()] = $item;
      }
    }

    $skus_list = array_keys($skus);

    $supplier_data_collection = $this->supplier_data_loader->get_for_skus($skus_list);

    $this->log_info('Fetching investment metrics for SKUs', ['batch_id' => $batch_id, 'skus' => $skus_list]);

    /* @var Investment_Sku_Metric_Collection */
    $investment_skus              = $this->investment_storage->fetch_investment_metrics($skus_list);
    $whitelabel_downstream_skus = array_map(
        static function (Exclusivity_Assortment_Candidate_DTO $candidate) {
          return $candidate->get_sku();
        }, $this->exclusivity_assortment_service->fetch_approved_for_whitelabel_sku_list($skus_list)
    );

    $this->log_info('Loading historical suggested style info for batch', ['batch_id' => $batch_id, 'skus' => $skus_list]);
    $historical_style_suggestions = $this->cached_suggestion_info_loader->get_historical_suggested_style_info_for_batch($batch_id);

    try {
      $this->log_info('Loading style suggestions', ['batch_id' => $batch_id, 'skus' => $skus_list]);
      $suggested_styles_collection = $this->style_suggestion_storage->get_style_suggestion($skus_list);
    } catch (Automatic_Style_Curation_Exception $exception) {
      $this->log_throwable_warning($exception);
      $suggested_styles_collection = new Automatic_Style_Suggestion_Collection([]);
    }

    try {
      $this->log_info('Loading substyle suggestions', ['batch_id' => $batch_id, 'skus' => $skus_list]);
      $suggested_substyles_collection = $this->style_suggestion_storage->get_substyle_suggestion($skus_list);
    } catch (Automatic_Style_Curation_Exception $exception) {
      $this->log_throwable_warning($exception);
      $suggested_substyles_collection = new Automatic_Substyle_Suggestion_Collection([]);
    }

    foreach ($skus as $sku => $item) {
      $this->log_info('Loading additional info for sku item', ['batch_id' => $batch_id, 'sku' => $sku]);
      /* @var Curation_Item $item */
      $supplier_data  = $supplier_data_collection->get_for_sku($item->get_sku());
      $investment_sku = $investment_skus->get($item->get_sku());

      $item->set_is_whitelabel_downstream(in_array($sku, $whitelabel_downstream_skus, true));
      $item->set_is_predicted_winner($investment_sku !== null && $investment_sku->is_predicted_winner());
      $item->set_suppliers($supplier_data->get_names());
      $item->set_canadian_supplier_only($supplier_data->is_canadian_only());
      $item->set_url($this->product_url_helper->get_product_page_url($sku));
      $item->set_image($item->get_image_resource_id());
      $item->set_suggested_styles($suggested_styles_collection->get_styles_ids_for_sku($sku));
      $item->set_suggested_substyles($suggested_substyles_collection->get_substyles_ids_for_sku($sku));

      $historical_style_suggestion = $historical_style_suggestions->get_for_sku($sku);
      $item->set_style_decision_notes($historical_style_suggestion->get_notes());
    }
  }
}
