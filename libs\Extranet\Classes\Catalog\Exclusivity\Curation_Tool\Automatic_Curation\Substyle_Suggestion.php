<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Substyle_Suggestion {

  /**
   * @var int
   */
  private $substyle_id;

  /**
   * @var string
   */
  private $substyle_name;

  /**
   * @var float
   */
  private $probability;

  /**
   * @var int
   */
  private $rank;

  /**
   * Style_Suggestion constructor.
   *
   * @param int    $substyle_id   Style Id
   * @param string $substyle_name Style Name
   * @param float  $probability   Probability
   * @param int    $rank          Rank
   */
  public function __construct(int $substyle_id, string $substyle_name, float $probability, int $rank) {
    $this->substyle_id   = $substyle_id;
    $this->substyle_name = $substyle_name;
    $this->probability   = $probability;
    $this->rank          = $rank;
  }

  /**
   * @return int
   */
  public function get_substyle_id() : int {
    return $this->substyle_<PERSON>;
  }

  /**
   * @return string
   */
  public function get_substyle_name() : string {
    return $this->substyle_name;
  }

  /**
   * @return float
   */
  public function get_probability() : float {
    return round($this->probability, 2);
  }

  /**
   * @return int
   */
  public function get_rank() : int {
    return $this->rank;
  }
}
