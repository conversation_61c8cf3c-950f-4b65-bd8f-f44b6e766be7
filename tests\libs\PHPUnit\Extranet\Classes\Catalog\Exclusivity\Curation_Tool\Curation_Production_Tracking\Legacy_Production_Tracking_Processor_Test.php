<?php

namespace App\Tests\libs\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Legacy_Production_Tracking_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO;

class Legacy_Production_Tracking_Processor_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage_production_tracking_sql_dao;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage_production_tracking_psql_dao;


    /**
     * @var ObjectProphecy|LoggerInterface
     */
    private $logger;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->storage_production_tracking_sql_dao = $this->prophesize(Curation_Production_Tracking_DAO::class);
        $this->storage_production_tracking_psql_dao = $this->prophesize(Curation_Production_Tracking_PostgreSQL_DAO::class);

        $this->legacy_pt_processor = new Legacy_Production_Tracking_Processor(
            $this->storage_production_tracking_sql_dao->reveal(),
            $this->storage_production_tracking_psql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * Test
     */
    public function test_update_production_tracking_status_feature_toggle_false()
    {
        $this->legacy_pt_processor->update_production_tracking_status([1]);
    }

    /**
     * Test
     */
    public function test_update_production_tracking_status_feature_toggle_true()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->legacy_pt_processor->update_production_tracking_status([1]);
    }
}
