<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

class Curation_Data_Consistency_Validation_Result {
  /**
   * @var array
   */
  private $errors = [];

  /**
   * @param string $sku   SKU
   * @param string $error Error
   *
   * @return void
   */
  public function add_error(string $sku, string $error) {
    $this->errors[$sku] = $error;
  }

  /**
   * @return string[]
   */
  public function get_errors() : array {
    return array_values($this->errors);
  }

  /**
   * @return array
   */
  public function get_error_sku_map() : array {
    return $this->errors;
  }
}