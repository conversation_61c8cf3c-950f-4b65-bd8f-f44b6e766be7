{"name": "wayfair/brand-workflows-purest-client", "description": "PuREST API Client", "type": "library", "license": "proprietary", "version": "dev-master", "config": {"sort-packages": true, "allow-plugins": {"infection/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}, "require": {"php": "^7.4", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"phpunit/phpunit": "^9", "phpstan/phpstan": "^1.8", "infection/infection": "^0.26.6", "wayfair/php-platform-coding-standard": "^3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy-phpunit": "^2.0"}, "autoload": {"psr-4": {"WF\\BrandWorkflows\\PuREST\\": "src/"}}, "autoload-dev": {"psr-4": {"WF\\BrandWorkflows\\PuREST\\Tests\\": "tests/"}}, "repositories": {"packagist": false, "artifactory-github": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-github", "options": {"ssl": {"verify_peer": false}}}, "artifactory-wayfair": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-wayfair", "options": {"ssl": {"verify_peer": false}}}}, "scripts": {"comment": ["The scripts here allow you to locally run the same checks that the pipelines will run.", "You are welcome to add your own, but please don't modify `phpunit`, `phpstan` or `checker`.", "And please make sure the `verify` script includes at least those three.", "Note that we use the `php -d` format instead of calling the binary directly so the scripts", " won't fail on dev VMs, where auto_prepend.php will get run by default."], "verify": ["@phpunit", "@phpstan", "@checker"], "test": "@phpunit", "phpunit": "php -d auto_prepend_file=Off vendor/bin/phpunit --verbose", "phpstan": "php -d auto_prepend_file=Off vendor/bin/phpstan analyze -c phpstan.neon --memory-limit=-1 ./", "checker": "php -d auto_prepend_file=Off vendor/bin/phpcs src --standard=PSR12"}}