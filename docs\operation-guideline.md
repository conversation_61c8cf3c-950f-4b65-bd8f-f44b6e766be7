# Operation & On-call Guideline

> Keep calm and save the world.

On-call engineer’s rotation is weekly - shift starts every Wednesday.
On call schedules can be found here:

* [Assortment & Exclusivity](https://wayfair.pagerduty.com/schedules#PKMKQRY)
* [Curation](https://wayfair.pagerduty.com/schedules#PX63RJ8)

## On-call Engineer Roles & Responsibilities
* The rotation is per `week`, which switches on `Wednesdays`.
* Triage issues that come from different channels including **#btbb-brand-workflows, #btbb-brand-workflows-alerts-high** and **#merch-tech-brand-workflows-alerts**.
* It is expected that on-call engineer won’t be an expert on everything, but it is his/her responsibility to find and work with the expert for the reported issue.
* When you are on-call for the week, your per sprint capacity can be reduced to `5` points
* There are 2 on-call schedules, which means there are 2 engineers on-call at the same time for **Assortment & Exclusivity** and **Curation**. While monitoring the channels above, the 2 engineers know which domain the request or alert is about and each engineer only deals with his/her own domain.
* In very urgent cases, while the domain engineer is on off hours, the other on-call engineer may help find an expert (if needed) and mitigate the issue.
* The steps when a stakeholder or another employee asks questions or reports a bug in **#btbb-brand-workflows**:
    1. the domain engineer tries to understand the scope of the ask and respond. If it is an issue reported by stakeholder and there is no ticket, remind him/her to `Report an issue` by clicking the bookmark link on the top and a jira ticket will be created.
    2. the domain engineer triage the issue with 2 attributes: `urgency` and `importance` with the best judgement
    3. follow the table below for the next step regarding the ticket
       1. If you found this issue was brought in by a recent code change, revert it in Buildkite to the last deployed healthy version and assign the ticket to the engineer who made this change
       2. The engineer who made the change should take it as a high priority and should on a fix PR
    4. follow up the Slack thread to update the stakeholder with ETA
    5. update the stakeholder again when the issue is fixed/mitigated

| ASAP                                                                                                                                                              | < 3 days                 | 7 ~ 10 days                                                           | > 10 days                                                                 |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------|-----------------------------------------------------------------------|---------------------------------------------------------------------------|
| Move the Jira ticket in the current sprint (Curation or Assort-Exclusivity) using epic link `Critical Issues`, set priority to `Critical` and start working on it | Same but priority `High` | Same but priority `Medium` and work on higher priority tickets if any | Same but put the ticket to the next sprint and prioritize it as `Medium`  |
| If there is longer term resolution, create a ticket in the `To Review` board (Curation or Assort-Exclusivity)                                                     | Same                     | Same                                                                  | Same                                                                      |                           |                          |                                                                       |                                                                           |

<img src="../img/ticket_2.png" alt="Create ticket 1">
<img src="../img/ticket_1.png" alt="Create ticket 2">

* The steps when you see an alert **#btbb-brand-workflows-alerts-high**:
    1. the domain engineer tries to understand the scope of the alert and triage.
    2. follow the table above under `ASAP`
    3. update under the alert Slack thread when it's fixed/mitigated

!!! info
        Monitor and refine our alert channel including #btbb-brand-workflows-criticals and #btbb-brand-workflows-alerts-high. It is important to continuously refine our alerts to reduce the noise we have in the channels.

* General guideline to triage a ticket:
    * **Urgent and Important**: This needs to be addressed right away otherwise normal business cannot continue. This kind of issue will not be handed over to the next on-call engineer, it needs to be fixed by the same person to avoid unnecessary transition.
    * **Important but not Urgent**: It can wait for some time, but it’s something we definitely need to address.
    * **Urgent but not Important**: Likely there will be a workaround for this, we shouldn’t have this every often.

> Reference Doc: [Brand Workflow Support Guidelines](https://docs.google.com/document/d/1hqpC9jkon2pJyYQeEg7ftQ4H6fnuw6g_cIFnPZXdTEc/edit#)
