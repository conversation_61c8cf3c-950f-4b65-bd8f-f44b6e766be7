/**
 * EB Curation SKU manufacturer info
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Alert, Text, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {BRAND_TYPE_TAIL} from "./curation_tool_shapes";


const MANUFACTURER_BRWID_WL_BRAND = 2;
const MANUFACTURER_BRWID_CATEGORY_BRAND = 4;

class CurationSkuManufacturer extends React.Component {
  static propTypes = {
    name: PropTypes.string.isRequired,
    brwid: PropTypes.number,
    brandType: PropTypes.number,
    brandName: PropTypes.string,
    isHoldoutManufacturer: PropTypes.bool,
    isWhitelabelDownstream: PropTypes.bool,
  };

  static defaultProps = {
    brwid: 0,
    brandType: 0,
    brandName: '',
    isHoldoutManufacturer: false,
    isWhitelabelDownstream: false,
  };

  shouldComponentUpdate() {
    return false;
  }

  render() {
    return (
      <div>
        <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.name}</Text>
        {this.props.isWhitelabelDownstream && (
          <Alert variation="success">
            <Translation msgid="CurationTool.EBAutoWhitelabel" />
          </Alert>
        )}
        {this.props.brandType === BRAND_TYPE_TAIL && (
          <Alert variation="warning">
            {this.props.brandName}
          </Alert>
        )}
        {this.props.brandType && this.props.brandType !== BRAND_TYPE_TAIL && !this.props.isWhitelabelDownstream && (
          <Alert variation="success">
            {this.props.brandName}
          </Alert>
        )}
        {this.props.brwid === MANUFACTURER_BRWID_WL_BRAND && (
          <Alert variation="warning">
            <Translation msgid="CurationTool.siteWhiteLabelBrandLabel" />
          </Alert>
        )}
        {this.props.brwid === MANUFACTURER_BRWID_CATEGORY_BRAND && (
          <Alert variation="warning">
            <Translation msgid="CurationTool.categoryBrandLabel" />
          </Alert>
        )}
        {this.props.isHoldoutManufacturer && (
          <Alert variation="alert">
            <Translation msgid="CurationTool.holdOutManufacturerLabel" />
          </Alert>
        )}
      </div>
    );
  }
}

export default CurationSkuManufacturer;
