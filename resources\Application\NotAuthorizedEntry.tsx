import React from 'react';
import ErrorBoundary from 'react-error-boundary';
import Translation from '@wayfair/translation';
import {Box, PageContainer} from '@wayfair/homebase-extranet';
import ErrorScreen from './ErrorScreen';
import PropTypes from "prop-types";

type ErrorMessageType = {
    errorMessage: string;
};

class AccessDeniedEntry extends React.Component<ErrorMessageType> {
    static propTypes = {
        errorMessage: PropTypes.string
    }

    static defaultProps = {
        errorMessage: <Translation msgid="notAuthorizedMessage"/>,
    }

    render() {
        // eslint-disable-next-line new-cap
        document.title = Translation({
            msgid: 'notAuthorizedTitle',
        });
        return (
            <Box px={45}>
                <PageContainer
                    pageTitle={<Translation msgid="notAuthorizedTitle"/>}
                    actions={<PageContainer.HelpLink href="#"/>}
                >
                    <ErrorBoundary FallbackComponent={ErrorScreen}>
                        <p>
                            {this.props.errorMessage}
                        </p>
                    </ErrorBoundary>
                </PageContainer>
            </Box>
        );
    }
}

export default AccessDeniedEntry;
