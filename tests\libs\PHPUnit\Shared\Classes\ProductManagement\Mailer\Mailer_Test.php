<?php
/**
 * Mailer Test
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Mailer;

use PHPMailer\PHPMailer\PHPMailer;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface;

class Mailer_Test extends TestCase
{
    use ProphecyTrait;
    /** @var \PHPMailer\PHPMailer\PHPMailer|\Prophecy\Prophecy\ObjectProphecy */
    private $mailer;
    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message|\Prophecy\Prophecy\ObjectProphecy */
    private $message;
    /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mailer */
    private $subject;

    /**
     * Set up test environment
     *
     * @return void
     */
    protected function setUp(): void
    {
        $this->message = $this->get_message_double();
        $this->mailer = $this->prophesize(PHPMailer::class);

        $this->subject = new Mailer($this->mailer->reveal());
    }

    /**
     * @test
     * @return void
     */
    public function it_is_mailer()
    {
        $this->assertInstanceOf(Mailer_Interface::class, $this->subject);
    }

    /**
     * @test
     * @return void
     */
    public function it_creates_instance_of_Mail_Message()
    {
        $this->assertInstanceOf(Mail_Message::class, $this->subject->create_message());
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_sender_from_given_message()
    {
        $this->message->get_sender()->willReturn(['<EMAIL>', 'Sender Name']);

        $this->subject->send($this->message->reveal());

        $this->mailer->setFrom('<EMAIL>', 'Sender Name')->shouldHaveBeenCalled();
    }

    /**
     * @test
     * @return void
     */
    public function it_sends_recipients_from_given_message()
    {
        $this->message->get_recipients()->willReturn(
            [
                ['<EMAIL>', 'First Name'],
                ['<EMAIL>', 'Second Name']
            ]
        );

        $this->subject->send($this->message->reveal());

        $this->mailer->addAddress('<EMAIL>', 'First Name')->shouldHaveBeenCalled();
        $this->mailer->addAddress('<EMAIL>', 'Second Name')->shouldHaveBeenCalled();
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_html_body_from_given_message()
    {
        $this->message->get_html_body()->willReturn('<h1>test</h1>');

        $this->subject->send($this->message->reveal());

        $this->mailer->msgHTML('<h1>test</h1>')->shouldHaveBeenCalled();
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_text_body_from_given_message()
    {
        $this->message->get_text_body()->willReturn('text body');

        $this->subject->send($this->message->reveal());

        $this->assertEquals('text body', $this->mailer->AltBody);
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_subject_from_given_message()
    {
        $this->message->get_subject()->willReturn('subject');

        $this->subject->send($this->message->reveal());

        $this->assertEquals('subject', $this->mailer->Subject);
    }

    /**
     * @test
     * @return void
     */
    public function it_adds_attachments_from_given_message()
    {
        $this->message->get_attachments()->willReturn(
            [
                ['test1', 'test1.txt', 'plain/text', 'base64', 'attachment'],
                ['test2', 'test2.txt', 'plain/csv', '8bit', 'inline'],
            ]
        );

        $this->subject->send($this->message->reveal());

        $this->mailer->addStringAttachment('test1', 'test1.txt', 'base64', 'plain/text', 'attachment')->shouldHaveBeenCalled();
        $this->mailer->addStringAttachment('test2', 'test2.txt', '8bit', 'plain/csv', 'inline')->shouldHaveBeenCalled();
    }

    /**
     * @test
     * @return void
     */
    public function it_wraps_send_result_into_Mail_Result()
    {
        $mailer = $this->get_mailer_double();
        $subject = new Mailer($mailer->reveal());

        $mailer->send(Argument::cetera())->will(
            function () {
                $this->ErrorInfo = 'Some error info';

                return true;
            }
        );

        $result = $subject->send($this->message->reveal());

        $this->assertNull($result);
        // $this->assertTrue($result->is_successful());
        // $this->assertEquals('Some error info', $result->get_reason());
    }

    /**
     * @test
     * @return void
     */
    public function it_resets_PHPMailer_state_between_runs()
    {
        $mailer = $this->get_mailer_double();
        $subject = new Mailer($mailer->reveal());

        $subject->send($this->message->reveal());

        $mailer->clearAllRecipients()->shouldHaveBeenCalled();
        $mailer->clearAttachments()->shouldHaveBeenCalled();
        $mailer->clearCustomHeaders()->shouldHaveBeenCalled();
        $mailer->clearReplyTos()->shouldHaveBeenCalled();
        //TODO check that ErrorInfo is also being cleared
    }

    /**
     * Mimics mail message. If you need to update, see the default values in Mail_Message class
     *
     * @return \Prophecy\Prophecy\ObjectProphecy
     */
    private function get_message_double()
    {
        /** @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message|\Prophecy\Prophecy\ObjectProphecy $message */
        $message = $this->prophesize(Mail_Message::class);
        $message->get_recipients()->willReturn([]);
        $message->get_sender()->willReturn(['', '']);
        $message->get_subject()->willReturn('');
        $message->get_text_body()->willReturn('');
        $message->get_html_body()->willReturn('');
        $message->get_attachments()->willReturn([]);

        return $message;
    }

    /**
     * @return \Prophecy\Prophecy\ObjectProphecy
     */
    private function get_mailer_double()
    {
        $mailer = $this->prophesize(PHPMailer::class);
        $mailer->send(Argument::cetera())->willReturn(false);
        $mailer->clearAllRecipients()->willReturn(null);
        $mailer->clearAttachments()->willReturn(null);
        $mailer->clearCustomHeaders()->willReturn(null);
        $mailer->clearReplyTos()->willReturn(null);
        $mailer->setFrom(Argument::cetera())->willReturn(null);
        $mailer->addAddress(Argument::cetera())->willReturn(null);
        $mailer->msgHTML(Argument::cetera())->willReturn(null);
        $mailer->addStringAttachment(Argument::cetera())->willReturn(null);

        return $mailer;
    }
}
