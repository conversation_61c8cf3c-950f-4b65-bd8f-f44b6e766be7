<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;
use WF\Shared\Traits\Logging_Trait;

class Completion_QA_Batch_Checker {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface
   */
  private $sectionLoader;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface $sectionLoader Section Loader
   * @param LoggerInterface|null                                                          $logger        Logger
   */
  public function __construct(Section_Loader_Interface $sectionLoader, ?LoggerInterface $logger = null) {
    $this->sectionLoader = $sectionLoader;
    $this->logger        = $logger;
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status
   */
  public function getCompletionQaStatus(int $batchId) : Completion_QA_Batch_Status {
    $sections = $this->sectionLoader->getSections($batchId);

    $pending      = $this->batchHasPendingItems($sections, $batchId);
    $approvedOnly = false;

    // if it is no longer pending, check if all items were approved
    if (!$pending) {
      $approvedOnly = !$this->batchHasRejectedItems($sections);
    }

    return new Completion_QA_Batch_Status($pending, $approvedOnly);
  }

  /**
   * True if all curation items have QA decision
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Section data with curation items
   *
   * @return bool
   */
  private function batchHasRejectedItems(array $sections) : bool {
    foreach ($sections as $section) {
      if ($this->sectionHasRejectedItems($section)) {
        return true;
      }
    }

    return false;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section Section data with curation items
   *
   * @return bool
   */
  private function sectionHasRejectedItems(Section $section) : bool {
    foreach ($section->get_curation_items() as $curationItem) {
      if ($curationItem->is_rejected_qa()) {
        return true;
      }
    }

    return false;
  }

  /**
   * True if all curation items have QA decision
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Section data with curation items
   * @param int                                                                      $batchId  Batch ID
   *
   * @return bool
   */
  private function batchHasPendingItems(array $sections, int $batchId): bool {
    foreach ($sections as $section) {
      if ($this->sectionHasPendingItems($section, $batchId)) {
        $this->log_warning(
            sprintf(
                'QA is not completed in section "%s" for batch_id=%s',
                $section->get_title(),
                $batchId
            ),
            [
                'batch_id' => $batchId,
                'section'  => $section->get_title()
            ]
        );

        return true;
      }
    }

    return false;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section Section data with curation items
   * @param int                                                                    $batchId Batch ID
   *
   * @return bool
   */
  private function sectionHasPendingItems(Section $section, int $batchId): bool {
    foreach ($section->get_curation_items() as $curationItem) {
      if ($curationItem->is_pending_qa()) {
        $this->log_warning(
            sprintf(
                'QA is not completed for SKU "%s" in section "%s" of batch_id=%s',
                $curationItem->get_sku(),
                $section->get_title(),
                $batchId
            ),
            [
                'batch_id' => $batchId,
                'section'  => json_encode($curationItem),
                'sku'      => $curationItem->get_sku()
            ]
        );

        return true;
      }
    }

    return false;
  }
}
