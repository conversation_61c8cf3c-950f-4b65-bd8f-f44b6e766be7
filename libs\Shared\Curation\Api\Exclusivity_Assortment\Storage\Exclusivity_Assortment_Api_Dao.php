<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment\Storage;

use App\Infrastructure\Connection\MerchConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Candidate_DTO;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Filter_Request_DTO;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Loader_Interface;
use WF\Shared\Helpers\SQL;
use PDO;

class Exclusivity_Assortment_Api_Dao implements Exclusivity_Assortment_Loader_Interface {

  private MerchConnection $pdo_merch;

  /**
   * Exclusivity_Assortment_Api_Dao constructor.
   *
   * @param MerchConnection $pdo_merch connection with the Merch database
   */
  public function __construct(MerchConnection $pdo_merch) {
    $this->pdo_merch = $pdo_merch;
  }

  /**
   * @param Exclusivity_Assortment_Filter_Request_DTO $filter the filter for loading
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function load_sku_list(Exclusivity_Assortment_Filter_Request_DTO $filter) : array {
    $sql = '
        SELECT ' . $this->get_field_list() . '
        FROM csn_merch_tool.dbo.tblAssortmentCandidate WITH (NOLOCK)
    ';

    $where = [];
    if ($filter->get_with_rebrand_project_only()) {
      $where[] = 'RebrandProjectID > 0';
    }

    $status = $filter->get_status();
    if (!empty($status)) {
      $where[] = 'Status = :status';
    }

    $investment_status = $filter->get_investment_status();
    if (!empty($investment_status)) {
      $where[] = 'InvestmentStatusID = :investment_status_id';
    }

    $whitelabel_status = $filter->get_whitelabel_status();
    if (!empty($whitelabel_status)) {
      $where[] = 'WhiteLabelStatusId = :whitelabel_status_id';
    }

    $selected_skus = $filter->get_selected_skus();
    if (!empty($selected_skus)) {
      $where[] = 'SKU IN (' . $this->pdo_merch->paramsForList(count($filter->get_selected_skus()), 'selected_skus', SQL::nvarchar(8)) . ')';
    }

    if (!empty($where)) {
      $sql .= 'WHERE ' . implode(' AND ', $where);
    }

    $statement = $this->pdo_merch->prepare($sql);

    if (!empty($status)) {
      $statement->bindValue(':status', $status, PDO::PARAM_INT);
    }

    if (!empty($investment_status)) {
      $statement->bindValue(':investment_status_id', $investment_status, PDO::PARAM_INT);
    }

    if (!empty($whitelabel_status)) {
      $statement->bindValue(':whitelabel_status_id', $whitelabel_status, PDO::PARAM_INT);
    }

    if (!empty($selected_skus)) {
      $statement->bindValuesList(':selected_skus', $selected_skus, SQL::nvarchar(8));
    }

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Could not load the SKU list');
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Exclusivity_Assortment_Candidate_DTO::class);
  }

  /**
   * @return string
   */
  private function get_field_list() : string {
    $field_list = [
        'SKU as sku',
        'RebrandProjectID as rebrand_project_id',
        'Status as status'
    ];

    return implode(', ', $field_list);
  }
}
