<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;

class Completion_QA_Automated_Batch_Checker implements LoggerAwareInterface {
  use LoggerTrait;
  use LoggerAwareTrait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface
   */
  private $section_loader;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface $section_loader Section Loader
   */
  public function __construct(Section_Loader_Interface $section_loader) {
    $this->section_loader = $section_loader;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status
   */
  public function getCompletionQaStatus(int $batch_id) : Completion_QA_Batch_Status {
    $sections = $this->section_loader->getSections($batch_id);

    $pending      = $this->batchHasPendingItems($sections, $batch_id);
    $approved_only = false;

    // if it is no longer pending, check if all items were approved
    if (!$pending) {
      $approved_only = !$this->batchHasRejectedItems($sections);
    }

    return new Completion_QA_Batch_Status($pending, $approved_only);
  }

  /**
   * Check if Batch has SKUS with 'updated' qa status
   *
   * @param int $batch_id Batch ID
   *
   * @return bool
   */
  public function batchHasUpdatedSkus(int $batch_id) : bool {
    $sections = $this->section_loader->getSections($batch_id);

    foreach ($sections as $section) {
      if ($this->sectionHasUpdatedItems($section)) {
        return true;
      }
    }

    return false;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section Section data with curation items
   *
   * @return bool
   */
  private function sectionHasUpdatedItems(Section $section) : bool {
    foreach ($section->get_curation_items() as $curation_item) {
      if ($curation_item->is_updated_qa()) {
        return true;
      }
    }

    return false;
  }

  /**
   * True if all curation items have QA decision
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Section data with curation items
   *
   * @return bool
   */
  private function batchHasRejectedItems(array $sections) : bool {
    foreach ($sections as $section) {
      if ($this->sectionHasRejectedItems($section)) {
        return true;
      }
    }

    return false;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section Section data with curation items
   *
   * @return bool
   */
  private function sectionHasRejectedItems(Section $section) : bool {
    foreach ($section->get_curation_items() as $curation_item) {
      if ($curation_item->is_rejected_qa()) {
        return true;
      }
    }

    return false;
  }

  /**
   * True if all curation items have QA decision
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Section data with curation items
   * @param int                                                                      $batch_id Batch ID
   *
   * @return bool
   */
  private function batchHasPendingItems(array $sections, int $batch_id): bool {
    foreach ($sections as $section) {
      if ($this->sectionHasPendingItems($section, $batch_id)) {
        $this->warning(
            sprintf(
                'QA is not completed in section "%s" for batch_id=%s',
                $section->get_title(),
                $batch_id
            ),
            [
                'batch_id' => $batch_id,
                'section'  => $section->get_title()
            ]
        );

        return true;
      }
    }

    return false;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section  Section data with curation items
   * @param int                                                                    $batch_id Batch ID
   *
   * @return bool
   */
  private function sectionHasPendingItems(Section $section, int $batch_id): bool {
    foreach ($section->get_curation_items() as $curation_item) {
      if ($curation_item->is_pending_qa()) {
        $this->warning(
            sprintf(
                'QA is not completed for SKU "%s" in section "%s" of batch_id=%s',
                $curation_item->get_sku(),
                $section->get_title(),
                $batch_id
            ),
            [
                'batch_id' => $batch_id,
                'section'  => json_encode($curation_item),
                'sku'      => $curation_item->get_sku()
            ]
        );

        return true;
      }
    }

    return false;
  }
}
