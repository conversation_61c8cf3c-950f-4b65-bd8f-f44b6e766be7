<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Exclusion_Reason_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Manufacturer_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Price_Tier_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Manufacturer_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Substyle_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Granular_Style_Data;
use WF\Shared\Traits\Logging_Trait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Config_Loader {
  use Logging_Trait;

  protected const MIN_PRICE_TIER = 1;
  protected const MAX_PRICE_TIER = 4;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service
   */
  private $regionService;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $psql_dao;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */


  private FeatureTogglesInterface $featureToggles;


  /**
   * Config_Loader constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO       $dao           Curation Tool DAO
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service $regionService Region Service
   * @param LoggerInterface|null                                              $logger        Logger
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                                                      $featureToggles                   Feature toggle
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO       $psql_dao           Curation Tool Postgres DAO
   */

  public function __construct(Curation_Tool_DAO $dao,
                              Region_Service $regionService,
                              FeatureTogglesInterface $featureToggles,
                              Curation_Tool_Postgres_DAO $dao_psql,
                              ?LoggerInterface $logger = null
  ) {
    $this->dao            = $dao;
    $this->regionService  = $regionService;
    $this->logger         = $logger;
    $this->featureToggles = $featureToggles;
    $this->psql_dao       = $dao_psql;
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data
   */
  public function getConfig(int $batchId, bool $getAllExclusionReasons): Config_Data {
    $this->log_info('Loading region for batch', ['batch_id' => $batchId]);
    $region = $this->regionService->get_region($batchId);

    $this->log_info('Loading manufacturer for region', ['region_id' => $region->getId()]);
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $manufacturerData = $this->psql_dao->get_manufacturers($region->getId());
    } else {
      $manufacturerData = $this->dao->get_manufacturers($region->getId());
    }

    $exclusionReasons = $getAllExclusionReasons ? $this->getAllExclusionReasons() : $this->getExclusionReasons();

    return new Config_Data(
        $exclusionReasons,
        $this->getPriceTiers(),
        $this->getStyles($region->getId()),
        $this->getSubstyles($region->getId()),
        $this->getManufacturers($manufacturerData),
        $this->getStyleManufacturers($manufacturerData),
        $this->getGranularStyles($region->getId())
    );
  }

  /**
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Exclusion_Reason_Data[]
   */
  public function getAllExclusionReasons(): array {
    $this->log_info('Loading all exclusion reasons');

    $rows = $this->psql_dao->get_all_exclusion_reasons();

    $exclusionReasons = [];
    foreach ($rows as $row) {
      $exclusionReasons[] = new Exclusion_Reason_Data($row['id'], $row['name']);
    }

    return $exclusionReasons;
  }


  /**
   * @param int $batchID Batch ID
   *
   * @return Style_Manufacturer_Data[]
   */
  public function getStyleManufacturersForBatch(int $batchID) : array {
    $this->log_info('Loading style manufacturers for batch', ['batch_id' => $batchID]);
    $styleManufacturerData = [];
    $region = $this->regionService->get_region($batchID);

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $manufacturerData = $this->psql_dao->get_manufacturers($region->getId());
    } else {
      $manufacturerData = $this->dao->get_manufacturers($region->getId());
    }

    foreach ($manufacturerData as $row) {
      $styleManufacturerData[] = new Style_Manufacturer_Data(
          $row['manufacturer_id'],
          $row['substyle_id'],
          $row['price_tier']
      );
    }

    return $styleManufacturerData;
  }

  /**
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Exclusion_Reason_Data[]
   */
  public function getExclusionReasons() : array {
    $this->log_info('Loading exclusions reasons');
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $rows = $this->psql_dao->get_exclusion_reasons();
    } else {
      $rows = $this->dao->get_exclusion_reasons();
    }

    $views   = [];
    foreach ($rows as $row) {
      $views[] = new Exclusion_Reason_Data($row['id'], $row['name']);
    }

    return $views;
  }

  /**
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Price_Tier_Data[]
   */
  public function getPriceTiers() : array {
    $views = [];
    $this->log_info('Building Price Tiers');
    for ($i = self::MIN_PRICE_TIER; $i <= self::MAX_PRICE_TIER; $i++) {
      $views[] = new Price_Tier_Data($i, (string)$i);
    }

    return $views;
  }

  /**
   * @param int $regionId Region ID
   *
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Data[]
   */
  public function getStyles(int $regionId): array {
    $this->log_info('Loading styles for region', ['region_id' => $regionId]);

    if($regionId===0){
      return [];
    }

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $rows = $this->psql_dao->get_styles($regionId);
    } else {
      $rows = $this->dao->get_styles($regionId);
    }

    $views   = [];
    foreach ($rows as $row) {
      $views[] = new Style_Data($row['id'], $row['name']);
    }

    return $views;
  }

  /**
   * @param int $regionId Region ID
   *
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Substyle_Data[]
   */
  public function getSubstyles(int $regionId) : array {
    $this->log_info('Loading substyles for region', ['region_id' => $regionId]);

    if($regionId===0){
      return [];
    }

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $rows = $this->psql_dao->get_substyles($regionId);
    } else {
      $rows = $this->dao->get_substyles($regionId);
    }

    $views = [];

    foreach ($rows as $row) {
      $views[] = new Substyle_Data($row['substyle_id'], $row['name'], $row['style_id']);
    }

    return $views;
  }

  /**
   * @param int $regionId Region ID
   *
   * @return Granular_Style_Data[]
   */
  public function getGranularStyles(int $regionId) : array {
    $this->log_info('Loading granular styles', ['regionId' => $regionId]);

    if($regionId === 0){
      return [];
    }

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $return_data = $this->psql_dao->get_granular_styles($regionId);
    } else {
      $return_data = $this->dao->get_granular_styles($regionId);
    }
    return $return_data;
  }

  /**
   * @param array $manufacturers Manufacturers data
   *
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Manufacturer_Data[]
   */
  public function getManufacturers(array $manufacturers) : array {
    $this->log_info('Building manufacturers', ['manufacturers' => $manufacturers]);
    $views = [];

    if(sizeof($manufacturers) === 0){
      return $views;
    }

    foreach ($manufacturers as $row) {
      if (isset($views[$row['manufacturer_id']])) {
        continue;
      }

      $views[$row['manufacturer_id']] = new Manufacturer_Data(
          $row['manufacturer_id'],
          $row['name'],
          $row['brand_class']
      );
    }

    return array_values($views);
  }

  /**
   * @param array $manufacturers Manufacturers data
   *
   * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Manufacturer_Data[]
   */
  public function getStyleManufacturers(array $manufacturers) : array {
    $this->log_info('Building Style manufacturers', ['manufacturers' => $manufacturers]);
    $views = [];

    if(sizeof($manufacturers) === 0){
      return $views;
    }

    foreach ($manufacturers as $row) {
      $views[] = new Style_Manufacturer_Data(
          $row['manufacturer_id'],
          $row['substyle_id'],
          $row['price_tier']
      );
    }

    return $views;
  }
}
