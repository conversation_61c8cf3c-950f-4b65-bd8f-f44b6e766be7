{"project_guid": "10f455f8-9111-49e1-b18d-41a208e2e064", "answers": {"user_sam_account_name": "rt092g", "default_business_reviewer": "mvargas2", "experimental_project": false, "project_name": "Brand Workflows Curation Tool", "project_description": "Curation Tool decouplead out of Monolith", "owner": "brandworkflowsengineering", "owner_email": "<EMAIL>", "repo_name": "brand-workflows-curation-tool", "workgroup_id": "11594", "entire_workgroup_owners": false, "jira_project": "MTBW", "datacenter": "GCP", "use_shared_domain_routing": "partners", "uses_graphql": false}, "generated_at": "2022-09-14 10:19:48.758956+00:00", "template_sha": "7abd0fdb616d798d8a30dd2b2b53a81877a35d74", "template_repo_namespace": "shared", "template_repo_name": "php-mamba-templates", "template_name": "symfony-api", "template_repo_url": "https://github.com/wayfair-shared/php-mamba-templates", "template_branch": "main"}