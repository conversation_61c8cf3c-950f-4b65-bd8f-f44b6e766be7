<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

interface Completion_QA_Batch_SKU_Storage {
  /**
   * @param int $batch_id  Batch ID
   * @param int $qa_status QA Status
   *
   * @return array
   */
  public function get_qa_batch_skus(int $batch_id, int $qa_status) : array;
}