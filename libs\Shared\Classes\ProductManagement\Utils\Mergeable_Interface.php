<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Utils;

interface Mergeable_Interface {
  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return bool
   */
  public function isDependent(Mergeable_Interface $item) : bool;

  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return void
   */
  public function merge(Mergeable_Interface $item);
}