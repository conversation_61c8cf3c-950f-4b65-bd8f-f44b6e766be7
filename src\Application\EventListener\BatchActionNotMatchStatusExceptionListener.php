<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\EventListener;

use App\Application\Exception\BatchActionNotMatchStatusException;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\Routing\RouterInterface;

class BatchActionNotMatchStatusExceptionListener
{
    private RouterInterface $router;

    public function __construct(RouterInterface $router)
    {
        $this->router = $router;
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $request = $event->getRequest();
        $exception = $event->getThrowable();

        if ($exception instanceof BatchActionNotMatchStatusException
            && $event->isMainRequest()
            && !$request->isXmlHttpRequest()) {
            $event->setResponse(
                new RedirectResponse(
                    $this->router->generate(
                        $exception->getExpectedAction() . '.1',
                        // this magic with `.1` if you have both prefixed and non-prefixed routes support
                        ['batch_id' => $exception->getBatchId()]
                    )
                )
            );
        }
    }
}
