<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Curation\Redirect;

use WF\Shared\Helpers\URL_Helper;
use WF\Shared\Models\Globalization\Brand_Catalog_Model;

/**
 * @todo rewrite to Symfony way or use one from monolith
 */
class Redirect_Product_Page_URL_Helper {
  const BASE_PATH = '/v/curation/redirect/go_to_pdp?';

  /**
   * @param string   $sku              SKU
   * @param int|null $brand_catalog_id Brand Catalog ID (optional)
   *
   * @return string
   */
  public function get_product_page_url(string $sku, int $brand_catalog_id = null) : string {
    $params = [];

    $params['sku'] = $sku;

    if (!empty($brand_catalog_id)) {
      $params['brand_catalog_id'] = $brand_catalog_id;
    }

    return self::BASE_PATH . http_build_query($params);
  }
}