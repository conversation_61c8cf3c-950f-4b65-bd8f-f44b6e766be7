/**
 * Main page of the curation tool
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

/**
 * Main page of the curation tool
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import ErrorBoundary from 'react-error-boundary';
import PropTypes from 'prop-types';
import {PageContainer, Toast, IconV2 as Icon} from '@wayfair/homebase-extranet';
import {faUser} from '@fortawesome/free-solid-svg-icons';
import Translation from '@wayfair/translation';
import CurationPageSection from './curation_page_section';
import CurationToolShapes, {
  COLLECTION_LIMIT,
  getQaCodeByCurationStatus,
  getSuppliersString,
  PAGE_LIMIT,
} from './curation_tool_shapes';
import {Column, Grid} from '@wayfair/homebase-extranet';
import {Box} from '@wayfair/homebase-extranet';
import {ALIGNMENT, WIDTHS} from './common_layout_constants';
import CurationToolPageHeader from './curation_tool_page_header';
import {SKU_TYPE} from './curation_sku_constants';
import {getCurationPageQAFilterFunction} from './qa_curation_status_filter';
import {
  TOAST_ERROR_TYPE,
  TOAST_SUCCESS_TYPE,
  SAVE_ERROR_MESSAGE,
  TIME_BEFORE_PAGE_REFRESH,
} from './curation_tool_constants';
import {
  saveDecisionService,
  toggleKitscoService,
  completeCurationService,
  getBatchData,
  getBatchDetails,
  getBatchDetailsFirstPage,
} from './curation_tool_services';
import CurationPageSaveAllConfirmationModal from './curation_page_save_all_confirmation_modal';
import CurationPageRemoveFromKitscoConfirmationModal from './curation_page_remove_from_kitsco_confirmation_modal';
import ErrorScreen from '../Application/ErrorScreen';
import {Loading} from '@homebase/core';

const INITIAL_DECISION_STATE = {
  styleId: null,
  substyleId: null,
  granularStyleId: null,
  priceTier: 1,
  manufacturerId: null,
  exclusionReasonId: null,
  automaticExcludedReason: null,
  suggestedStyleRejectionReasonId: null,
};

const INITIAL_SECTION_STATE = {
  selectedRows: [],
  changedRows: [],
  decision: INITIAL_DECISION_STATE,
};

const limit = PAGE_LIMIT;

const getNewContextSKUs = (skus) =>
  skus
    .filter((sku) => sku.type === SKU_TYPE.CONTEXT && !sku.savedAt)
    .map((sku) => sku.sku);

// Declare and export the rebrandProject variable
export let rebrandProject = PropTypes.shape({
  id: PropTypes.number,
  name: PropTypes.string,
});

class CurationPage extends React.Component {
  // PropTypes array to initialize the property types from BackEnd

  static propTypes = {
    batchId: PropTypes.number.isRequired,
    batchData: CurationToolShapes.batchShape.isRequired,
    rebrandProject: PropTypes.shape({
      id: PropTypes.number,
      name: PropTypes.string,
    }),
    curationConfig: CurationToolShapes.curationConfigShape.isRequired,
    shouldCollectSuggestedStyleFeedback: PropTypes.bool.isRequired,
    isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
    isAutomaticCurationPostQaEnabled: PropTypes.bool.isRequired,
    defaultNote: PropTypes.string.isRequired,
    suggestedStyleRejectionReasons: PropTypes.arrayOf(
      CurationToolShapes.reasonShape
    ).isRequired,
    warnings: PropTypes.arrayOf(PropTypes.string),
    error: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    rebrandProject: null,
    warnings: [],
    error: [],
    defaultNote: 'x',
    isAutomaticCurationPostQaEnabled: false,
  };

  state = {
    sections: [],
    suppliers: [],
    pageCurationItemMap: {},
    batchDataLoading: false,
    isSaving: false,
    skusSaving: [],
    toast: {
      isOpen: false,
      status: null,
      message: '',
    },
    filters: {},
    saveAllConfirmation: {},
    removeFromKitscoConfirmation: {},
    saveDecisionSuggestedStyleFeedback: {},
  };

  componentDidMount = async () => {
    if (this.props.batchId) {
      const firstPage = await getBatchDetailsFirstPage(
          this.props.batchId,
          limit,
          -1,
          "",
          "curation"
      );
      const filterJson = JSON.stringify({});
      const encodedFilters = Buffer.from(filterJson).toString('base64');
      const pageCurationItemMap = this.state.pageCurationItemMap;
      const sections = []
      for (const collectionKey in firstPage) {
        if (firstPage[collectionKey].title) {
          const section = {
            ...INITIAL_SECTION_STATE,
            ...firstPage[collectionKey],
            isExpanded: false,
            changedRows: getNewContextSKUs(firstPage[collectionKey].curationItems),
            selectedRows: [],
            isLoading: false,
            filteredSkusTotalCount: firstPage[collectionKey].skusTotalCount,
            filteredSkusSavedCount: firstPage[collectionKey].skusSavedCount,
          }
          sections.push({...section})
          section.isExpanded = true;
          section.currentPage = 1;
          if (parseInt(collectionKey, 10) < COLLECTION_LIMIT) {
            pageCurationItemMap[
                `${firstPage[collectionKey].title} - 1 - ${encodedFilters} - ${limit}`
                ] = section;
          }

        }
      }

      this.setState({
        batchDataLoading: true,
        sections,
        suppliers: firstPage.suppliers,
        pageCurationItemMap
      });

    }
  };

  getClosedToast = () => ({
    isOpen: false,
    status: null,
    message: '',
  });

  getOpenToast = (status, message) => ({
    isOpen: true,
    status,
    message,
  });

  closeToast = () => this.setState({toast: this.getClosedToast()});

  handleSaveAllConfirmationModalConfirm = () => {
    const {sectionIndex} = this.state.saveAllConfirmation;

    this.setState({saveAllConfirmation: {}});

    this.saveAllSelectedRows(sectionIndex);
  };

  handleSaveAllConfirmationModalCancel = () => {
    this.setState({saveAllConfirmation: {}});
  };

  handleRemoveFromKitscoConfirm = (sku) => {
    const sectionIndex = this.state.removeFromKitscoConfirmation.sectionIndex;

    this.setState({removeFromKitscoConfirmation: {}});

    this.handleSharedSkuSaveClick(sectionIndex, sku);
  };

  handleRemoveFromKitscoCancel = () => {
    this.setState({removeFromKitscoConfirmation: {}});
  };

  handleSuggestedStyleFeedbackSubmit = ({notes, reason}) => {
    const saveDecision = this.state.saveDecisionSuggestedStyleFeedback;

    this.setState({saveDecisionSuggestedStyleFeedback: {}});

    this.saveDecision({
      ...saveDecision,
      suggestedNotes: notes,
      suggestedStyleRejectionReason: reason,
    });
  };

  handleSuggestedStyleFeedbackCancel = () => {
    this.setState({saveDecisionSuggestedStyleFeedback: {}});
  };

  // creates a copy of sections data applying updates on target section / skus
  setSectionData = (
    sections,
    target,
    {onSectionUpdate, onCurationItemUpdate}
  ) => {
    return sections.map((section, index) => {
      if (index !== target.sectionIndex) {
        return section;
      }

      const curationItems = section.curationItems.map((item) => {
        if (
          onCurationItemUpdate !== null &&
          Array.isArray(target.skus) &&
          target.skus.includes(item.sku)
        ) {
          return onCurationItemUpdate(item);
        }

        return item;
      });

      if (onSectionUpdate !== null) {
        return onSectionUpdate(section, curationItems);
      }

      return {...section, curationItems};
    });
  };

  handleSelectionChange = ({sectionIndex, sku, relatedKits, isChecked}) => {
    const affectedRows = relatedKits.length > 0 ? relatedKits : [sku];

    const onSectionUpdate = (section) => {
      // if the row is now checked, add it to the list of selected rows, otherwise remove it
      const selectedRows = isChecked
        ? section.selectedRows.concat(affectedRows)
        : section.selectedRows.filter((item) => !affectedRows.includes(item));

      return {
        ...section,
        selectedRows,
      };
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(
          prevState.sections,
          {sectionIndex},
          {onSectionUpdate}
        ),
      };
    });
  };

  handleSaveAllClick = (sectionIndex) => {
    // check if any visible skus (after applying filters) have suggested styles
    const skus = this.getSelectedSkusWithSuggestedStyles(sectionIndex);

    // if they are selected skus with suggested styles, show confirmation modal before applying save all
    if (skus.length > 0) {
      this.setState({
        saveAllConfirmation: {
          sectionIndex,
          skus,
        },
      });
    } else {
      this.saveAllSelectedRows(sectionIndex);
    }
  };

  handleRemoveFromKitscoClick = (sectionIndex, sku) => {
    const itemSku = this.findSkuInSection(sectionIndex, sku);

    if (typeof itemSku === 'undefined') {
      return;
    }

    if (itemSku.isKitsco === true) {
      this.setState({
        removeFromKitscoConfirmation: {
          sectionIndex,
          sku,
        },
      });
    } else {
      this.handleSharedSkuSaveClick(sectionIndex, sku);
    }
  };

  getSelectedSkusWithSuggestedStyles = (sectionIndex) => {
    const {curationItems, selectedRows} = this.state.sections[sectionIndex];

    // check if any visible skus (after applying filters) have suggested styles
    return this.getCurationItems(curationItems)
      .filter(
        (curationItem) =>
          selectedRows.includes(curationItem.sku) &&
          curationItem.suggestedStyles.length > 0 &&
          !this.shouldIgnoreSku(curationItem)
      )
      .map((item) => item.sku);
  };

  // Ignore the SKU if it is curated by offshore user and the assortment decision is move to header brand
  shouldIgnoreSku = (curationItem) =>
    this.props.isAssortmentWorkflowOffshoreUser &&
    curationItem.shouldMoveToHeaderBrand;

  saveAllSelectedRows = (sectionIndex) => {
    const {decision, curationItems, selectedRows} =
      this.state.sections[sectionIndex];

    // get the array of visible skus (after applying filters)
    const visibleSkus = this.getCurationItems(curationItems)
      .filter(
        (curationItem) =>
          !this.shouldIgnoreSku(curationItem) || decision.exclusionReasonId
      )
      .map((curationItem) => curationItem.sku);

    // get the selected rows that are visible
    const skus = selectedRows.filter((sku) => visibleSkus.includes(sku));

    const target = {sectionIndex, skus};

    this.setDecisionToSkus(target, decision);

    this.saveDecisionWithSuggestedStyleFeedback(
      target,
      decision,
      true,
      sectionIndex
    );
  };

  handleSaveClick = async (sectionIndex, sku) => {
    const itemSku = this.findSkuInSection(sectionIndex, sku);

    if (typeof itemSku === 'undefined') {
      return;
    }

    const target = {
      sectionIndex,
      skus: this.getRelatedSkus(itemSku),
    };

    this.saveDecisionWithSuggestedStyleFeedback(
      target,
      itemSku.decision,
      null,
      sectionIndex
    );
  };

  handleSharedSkuSaveClick = (sectionIndex, sku) => {
    const target = {
      sectionIndex,
      skus: [sku],
    };

    const itemSku = this.findSkuInSection(sectionIndex, sku);

    if (typeof itemSku === 'undefined') {
      return;
    }

    // toggle the isKitsco value
    const newIsKitsco = !itemSku.isKitsco;

    const sharedComponents = this.state.sections[sectionIndex].curationItems
      .filter((item) => item.type === SKU_TYPE.SHARED)
      .map((item) => {
        return {
          sku: item.sku,
          is_kitsco: item.sku === sku ? newIsKitsco : item.isKitsco,
          kit_parents: item.kitParents,
        };
      });

    this.toggleKitsco(target, newIsKitsco, sharedComponents);
  };

  handleCurationItemDecisionChange = (sectionIndex, sku, decision) => {
    const target = {
      sectionIndex,
      skus: this.getRelatedSkus(this.findSkuInSection(sectionIndex, sku)),
    };

    this.setDecisionToSkus(target, decision);
  };

  getRelatedSkus = (item) => {
    return item.relatedKits.length > 0 ? item.relatedKits : [item.sku];
  };

  findSkuInSection = (sectionIndex, sku) => {
    return this.state.sections[sectionIndex].curationItems.find(
      (item) => item.sku === sku
    );
  };

  handleSelectAll = (sectionIndex, isChecked) => {
    const onSectionUpdate = (section, curationItems) => {
      const selectedRows = isChecked
        ? curationItems.map((curationItem) => curationItem.sku)
        : [];

      return {
        ...section,
        selectedRows,
      };
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(
          prevState.sections,
          {sectionIndex},
          {onSectionUpdate}
        ),
      };
    });
  };

  handleHeaderDecisionChange = (sectionIndex, decision) => {
    const onSectionUpdate = (section) => {
      return {
        ...section,
        decision,
      };
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(
          prevState.sections,
          {sectionIndex},
          {onSectionUpdate}
        ),
      };
    });
  };

  fetchSectionDataFromApi = async (sectionIds, sectionNames, pageNum, limit, sectionIndices) => {
    const filtersTemp = {};
    for (const property in this.state.filters) {
      if (
        property === 'suppliers' &&
        this.state.filters[property].length !== 0
      ) {
        filtersTemp[property] = [...this.state.filters[property]];
      } else if (
        property === 'qaStatus' &&
        this.state.filters[property] != null
      ) {
        filtersTemp[property] = this.state.filters[property];
      }
    }
    const filterJson = JSON.stringify(filtersTemp);
    const encodedFilters = Buffer.from(filterJson).toString('base64');
    const unMemoizedSectionIds = [];
    const unMemoizedSectionNames = [];
    const unMemoizedSectionIndices = [];
    const newSections = this.state.sections;
    for (let j = 0; j < sectionIds.length; j++)  {
      const pageCurationItemKey = `${sectionIds[j]} - ${sectionNames[j]} - ${pageNum} - ${encodedFilters} - ${limit}`;
      if (this.state.pageCurationItemMap[pageCurationItemKey]) {
        for (let i = 0; i < this.state.sections.length; i++) {
          if (this.state.sections[i].id === sectionIds[j] && this.state.sections[i].title === sectionNames[j]) {
            newSections[i] = {
              ...this.state.pageCurationItemMap[pageCurationItemKey],
            };
          }
        }
      } else {
        unMemoizedSectionIds.push(sectionIds[j]);
        unMemoizedSectionNames.push(sectionNames[j]);
        unMemoizedSectionIndices.push(sectionIndices[j]);
      }
    }


    if (unMemoizedSectionIds.length === 0) {
      this.setState({sections: newSections});
      return;
    } else {
      // eslint-disable-next-line no-param-reassign
      sectionIds = [...unMemoizedSectionIds];
      sectionNames = [...unMemoizedSectionNames];
      // eslint-disable-next-line no-param-reassign
      sectionIndices = [...unMemoizedSectionIndices];
    }
    const length = sectionIds.length;
    const sectionsPre = newSections.map((section) => {
      for (let i = 0; i < length; i++) {
        if (section.id === sectionIds[i] && section.title === sectionNames[i]) {
          section = {
            ...section,
            isLoading: true,
            isExpanded: true,
            currentPage: pageNum,
            error: '',
          };
        }
      }
      return {
        ...section,
      };
    });

    this.setState({sections: sectionsPre});
    let filters = this.state.filters;
    if (!filters) {
      filters = {};
    }
    const qaStatus = getQaCodeByCurationStatus(filters.qaStatus);
    const supplier = getSuppliersString(filters.suppliers);
    const isFilterApplied = supplier || qaStatus !== -1;
    for (let i = 0; i < sectionIds.length; i++) {
      const sectionId = sectionIds[i];
      const sectionName = sectionNames[i];
      try {
        const receivedSection = await getBatchDetails(
          this.props.batchId,
          sectionName,
          pageNum,
          limit,
          qaStatus,
          supplier,
            'curation',
            this.state.sections[sectionIndices[i]].id
        );
        const sections = this.state.sections.map((section) => {
          if (section.id === sectionId && section.title === sectionName) {
            for (const j in receivedSection) {
              if (receivedSection[j].id === sectionId && receivedSection[j].title === sectionName) {
                let filteredSkusTotalCount = 0;
                let filteredSkusSavedCount = 0;
                if (supplier) {
                  filteredSkusTotalCount = receivedSection[j].filteredSkusTotalCount;
                  filteredSkusSavedCount = receivedSection[j].filteredSkusSavedCount;
                  section = {
                    ...section,
                    filteredSkusTotalCount,
                    filteredSkusSavedCount
                  }
                }
                section = {
                  ...section,
                  curationItems: receivedSection[j].curationItems,
                  isLoading: false,
                  isExpanded: true,
                  error: '',
                  currentPage: pageNum,
                };
                const pageCurationItemMap = this.state.pageCurationItemMap;

                pageCurationItemMap[
                    `${section.id} - ${sectionNames[j]} - ${pageNum} - ${encodedFilters} - ${limit}`
                    ] = section;
                this.setState((prevState) => {
                  return {
                    ...prevState,
                    pageCurationItemMap,
                  };
                });
              }
            }

          }
          return {
            ...section,
            changedRows: getNewContextSKUs(section.curationItems),
          };
        });

        this.setState({sections});
      } catch (e) {
        const sections = this.state.sections.map((section) => {
          if (section.id === sectionId && section.title === sectionName) {
            section = {
              ...section,
              curationItems: [],
              isLoading: false,
              isExpanded: true,
              currentPage: pageNum,
              error: 'There is issue while loading data. Please try again!!!',
            };
          }
          return {
            ...section,
            changedRows: [],
          };
        });

        this.setState({sections});
      }
    }
  };
  handleExpandClick = async (sectionIndex, isExpanded, sectionId, sectionName) => {
    if (isExpanded === true) {
      await this.fetchSectionDataFromApi([sectionId], [sectionName], 1, limit, [sectionIndex]);
    }
    const onSectionUpdate = (section) => {
      return {
        ...section,
        isExpanded,
      };
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(
          prevState.sections,
          {sectionIndex},
          {onSectionUpdate}
        ),
      };
    });
  };

  /* expand or collapse all sections based on expandState */
  flipExpandAll = (expandState) => {
    this.setState((prevState) => {
      return {
        ...prevState,
        sections: prevState.sections.map((section) => {
          return {
            ...section,
            isExpanded: expandState,
          };
        }),
      };
    });
  };

  handleClickExpandAll = async () => {
    // this.flipExpandAll(true);
    const sectionIds = [];
    const sectionNames = [];
    const sectionIndices = [];

    for (let i = 0; i < this.state.sections.length; i++) {
      // if (this.state.sections[i].isExpanded === false) {
        sectionIds.push(this.state.sections[i].id);
        sectionNames.push(this.state.sections[i].title);
        sectionIndices.push(i)
      // }
    }

    await this.fetchSectionDataFromApi(sectionIds, sectionNames, 1, limit, sectionIndices);
  };

  handleClickCollapseAll = () => {
    this.flipExpandAll(false);
  };

  isExpandedAll = () => {
    return !this.state.sections.find((item) => !item.isExpanded);
  };

  saveDecisionWithSuggestedStyleFeedback = (
    target,
    decision,
    clearSelection,
    sectionIndex
  ) => {
    if (decision.exclusionReasonId) {
      this.saveDecision({target, decision, clearSelection, sectionIndex});
      return;
    }

    if (decision.suggestedStyleRejectionReasonId) {
      const reason = this.props.suggestedStyleRejectionReasons.find(
        (item) => item.id === decision.suggestedStyleRejectionReasonId
      ).name;

      const isClear = false;
      const note = this.props.defaultNote;
      this.saveDecision({
        target,
        decision,
        clearSelection: isClear,
        suggestedNotes: note,
        suggestedStyleRejectionReason: reason,
        sectionIndex,
      });

      return;
    }

    this.saveDecision({target, decision, clearSelection, sectionIndex});
  };

  saveDecision = ({
    target,
    decision,
    clearSelection,
    suggestedNotes,
    suggestedStyleRejectionReason,
    sectionIndex,
  }) => {
    this.setState({isSaving: true, skusSaving: [...this.state.skusSaving, ...target.skus]});
    if (target.skus.length === 0) {
      return;
    }

    saveDecisionService({
      batchId: this.props.batchId,
      skus: target.skus,
      decision,
      suggestedNotes,
      suggestedStyleRejectionReason,
    })
      .then((saveInfo) => {
        const sections = [...this.state.sections];
        for (let i = 0; i < sections[sectionIndex].curationItems.length; i++) {

          for (let j = 0; j < saveInfo.skus.length; j++) {
            if (
              sections[sectionIndex].curationItems[i].sku === saveInfo.skus[j]
            ) {
              if (
                !(
                  sections[sectionIndex].curationItems[i].savedAt &&
                  sections[sectionIndex].curationItems[i].savedBy
                )
              ) {
                sections[sectionIndex].skusSavedCount =
                  sections[sectionIndex].skusSavedCount + 1;
                sections[sectionIndex].filteredSkusSavedCount =
                  sections[sectionIndex].filteredSkusSavedCount + 1;
                sections[sectionIndex].curationItems[i].savedAt =
                  saveInfo.savedAt;
                sections[sectionIndex].curationItems[i].savedBy =
                  saveInfo.savedBy;
              }
              sections[sectionIndex].curationItems[i].savedAt =
                saveInfo.savedAt;
              sections[sectionIndex].curationItems[i].savedBy =
                saveInfo.savedBy;
            }
          }
        }
        const pageCurationItemMap = {...this.state.pageCurationItemMap};
        let newFilters;
        for (const pageCurationItemKey in pageCurationItemMap) {
          const splitKey = pageCurationItemKey.split(' - ');
          const id = splitKey[0];
          const pageNum = parseInt(splitKey[2], 10);
          if (sections[sectionIndex].id === id && sections[sectionIndex].title === title) {
            pageCurationItemMap[pageCurationItemKey].skusSavedCount = sections[sectionIndex].skusSavedCount;
            pageCurationItemMap[pageCurationItemKey].filteredSkusSavedCount = sections[sectionIndex].filteredSkusSavedCount;
            const encodedFilters = splitKey[2];
            const filterJson = Buffer.from(encodedFilters, 'base64').toString('utf-8');
            const filters = JSON.parse(filterJson);
            if (
              filters.qaStatus === 'pending' ||
              filters.qaStatus === 'saved_decision'
            ) {
              delete pageCurationItemMap[pageCurationItemKey];
              newFilters = {...this.state.filters};
              newFilters.qaStatus = null;
            } else if (sections[sectionIndex].currentPage === pageNum) {
              pageCurationItemMap[pageCurationItemKey] = {...sections[sectionIndex]};
            }
          }
        }

        const skusSaving = [];

        const changedRows = [];

        for (let j = 0; j < this.state.skusSaving.length; j++) {
            if(!target.skus.includes(this.state.skusSaving[j])){
              skusSaving.push(this.state.skusSaving[j]);
            }

        }

        for (let i = 0; i < sections[sectionIndex].changedRows.length; i++) {
          if(!target.skus.includes(sections[sectionIndex].changedRows[i])){
            changedRows.push(sections[sectionIndex].changedRows[i]);
          }
        }

        sections[sectionIndex].changedRows = changedRows;

        this.setState({
          isSaving: skusSaving.length>0,
          skusSaving,
          sections,
          pageCurationItemMap,
          filters: newFilters,
        });

        this.decisionSaved(target, saveInfo, clearSelection);
      })
      .catch(() => {
        this.setState({
          isSaving: false,
          toast: this.getOpenToast(TOAST_ERROR_TYPE, SAVE_ERROR_MESSAGE),
        });
      });
  };

  toggleKitsco = (target, isKitsco, sharedComponents) => {
    toggleKitscoService({
      sku: target.skus[0],
      batchId: this.props.batchId,
      isKitsco,
      sharedComponents,
    })
      .then((response) => {
        this.kitscoSaved({
          target,
          saveInfo: response.saveInfo,
          isKitsco,
          kitParentGroups: response.kitParentGroups,
        });
      })
      .catch(() => {
        this.setState({
          toast: this.getOpenToast(TOAST_ERROR_TYPE, SAVE_ERROR_MESSAGE),
        });
      });
  };

  kitscoSaved = ({target, saveInfo, isKitsco, kitParentGroups}) => {
    const setSectionForSavedKitsco = (sections) =>
      sections.map((section, index) => {
        if (index !== target.sectionIndex) {
          return section;
        }

        const curationItems = section.curationItems.map((item) => {
          // Updates the shared component
          if (Array.isArray(target.skus) && target.skus.includes(item.sku)) {
            return {
              ...item,
              ...saveInfo,
              isKitsco,
            };
          }

          const kitParent = kitParentGroups.find(
            (parent) => parent.sku === item.sku
          );

          if (typeof kitParent === 'undefined') {
            return item;
          }

          // updates kit parents
          return {
            ...item,
            decision: !isKitsco ? INITIAL_DECISION_STATE : item.decision,
            relatedKits: kitParent.related_kits,
          };
        });

        return {...section, curationItems};
      });

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: setSectionForSavedKitsco(prevState.sections),
        toast: this.getClosedToast(),
      };
    });
  };

  decisionSaved = (target, saveInfo, clearSelection) => {
    const onCurationItemUpdate = (item) => {
      return {
        ...item,
        ...saveInfo,
      };
    };

    const onSectionUpdate = (section, curationItems) => {
      // remove saved rows from pending changes
      const changedRows = section.changedRows.filter(
        (item) => !target.skus.includes(item)
      );

      if (clearSelection) {
        return {
          ...section,
          curationItems,
          ...INITIAL_SECTION_STATE,
          changedRows,
        };
      }

      return {...section, curationItems, changedRows};
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(prevState.sections, target, {
          onSectionUpdate,
          onCurationItemUpdate,
        }),
        toast: this.getClosedToast(),
      };
    });
  };

  setDecisionToSkus = (target, decision) => {
    const onCurationItemUpdate = (item) => {
      return {
        ...item,
        decision,
      };
    };

    const onSectionUpdate = (section, curationItems) => {
      const changedRows = section.changedRows.concat(target.skus);

      return {...section, curationItems, changedRows};
    };

    this.setState((prevState) => {
      return {
        ...prevState,
        sections: this.setSectionData(prevState.sections, target, {
          onSectionUpdate,
          onCurationItemUpdate,
        }),
        toast: this.getClosedToast(),
        saveDecisionSuggestedStyleFeedback: {},
      };
    });
  };

  isSectionCheckedIndeterminate = (sectionIndex) => {
    const {selectedRows, curationItems} = this.state.sections[sectionIndex];

    return (
      selectedRows.length > 0 && selectedRows.length < curationItems.length
    );
  };

  isSectionCheckedAll = (sectionIndex) => {
    return (
      this.state.sections[sectionIndex].selectedRows.length ===
      this.state.sections[sectionIndex].curationItems.length
    );
  };

  getSkusSavedCount = () => {
    return this.state.sections.reduce((accumulator, section) => {
      return accumulator + section.skusSavedCount;
    }, 0);
  };

  getRebrandProject = () => {
    rebrandProject = this.props.rebrandProject;
    return rebrandProject;
  }

  getSkusTotalCount = () => {
    return this.state.sections.reduce((accumulator, section) => {
      return accumulator + section.skusTotalCount;
    }, 0);
  };

  handleCompleteClick = () => {
    completeCurationService(this.props.batchId)
      .then((response) => {
        if (response.status) {
          this.setState({
            toast: this.getOpenToast(
              TOAST_SUCCESS_TYPE,
              <Translation msgid="CurationTool.CurationIsCompletedAndRefresh" />
            ),
          });

          setTimeout(
            () => window.location.reload(true),
            TIME_BEFORE_PAGE_REFRESH
          );
        } else {
          const message = response.reason ? (
            <Translation
              msgid="CurationTool.failedToCompleteCurationReason"
              params={{reason: response.reason}}
            />
          ) : (
            <Translation msgid="CurationTool.ThereAreMoreSKUsToCuratedDotThePageWillRefreshDot" />
          );
          this.setState({
            toast: this.getOpenToast(TOAST_ERROR_TYPE, message),
          });

          setTimeout(
            () => window.location.reload(true),
            TIME_BEFORE_PAGE_REFRESH
          );
        }
      })
      .catch((e) => {
        const error = e?.message && JSON.parse(e.message);
        const message = error?.reason ? (
          <Translation
            msgid="CurationTool.failedToCompleteCurationReason"
            params={{reason: error.reason}}
          />
        ) : (
          <Translation msgid="CurationTool.FailedToCompleteCuration" />
        );
        this.setState({
          toast: this.getOpenToast(TOAST_ERROR_TYPE, message),
        });
      });
  };

  handleChangeFilter = async (selectedFilter) => {
    const filters = {...this.state.filters, ...selectedFilter};
    await this.setState({filters});

    const sectionIds = [];
    const sectionNames = [];
    const sectionIndices = []
    for (let i = 0; i < this.state.sections.length; i++) {
      if (this.state.sections[i].isExpanded) {
        sectionIds.push(this.state.sections[i].id);
        sectionNames.push(this.state.sections[i].title);
        sectionIndices.push(i)
      }
    }
    await this.fetchSectionDataFromApi(sectionIds, sectionNames, 1, limit, sectionIndices);
    // this.setState(({sections}) => ({
    //   sections: sections.map(section => ({
    //     ...section,
    //     selectedRows: [],
    //   })),
    //   filters,
    // }));
  };

  getFilterOptions = () => {
    return {
      suppliers: this.state.suppliers.map((supplier) => {
        return {value: supplier};
      }),
      canFilterByHavingSuggestedStyles: this.state.sections.some((section) =>
        section.curationItems.some(
          (curationItem) => curationItem.suggestedStyles.length > 0
        )
      ),
    };
  };

  matchSupliers = (supplier) => {
    return this.state.filters.suppliers.some((item) => item.value === supplier);
  };

  getCurationItems = (sectionItems) => {
    // We want to make sure to apply these sequentially to all section items
    let curationItems = sectionItems;

    let filters = this.state.filters;

    if (!filters) {
      filters = {};
    }

    if (filters.qaStatus) {
      const filterFunction = getCurationPageQAFilterFunction(filters.qaStatus);
      curationItems = filterFunction(sectionItems);
    }

    if (filters.suppliers && filters.suppliers.length > 0) {
      curationItems = curationItems.filter((curationItem) => {
        return curationItem.suppliers.some(this.matchSupliers);
      });
    }

    return curationItems;
  };

  /**
   * Format an array of scalars to a structure understood by Dropdown components
   */

  getPageTitle = () => {
    if (this.props.isAutomaticCurationPostQaEnabled) {
      return (
        <Grid alignItems={ALIGNMENT.CENTER}>
          <Box>
            <Icon icon={faUser} size="25px" />
          </Box>
          <Column className="icon_text_align" size={WIDTHS.WIDTH_6}>
            <div className="icon_text_align">
              <Translation msgid="CurationTool.Curation" />
            </div>
          </Column>
        </Grid>
      );
    } else {
      return <Translation msgid="CurationTool.CurationTool" />;
    }
  };

  getSections() {
    const curationPageSections = [];
    for (let i = 0; i < this.state.sections.length; i++) {

      // const curationItems = this.getCurationItems(this.state.sections[i].curationItems);

      curationPageSections.push(
          // curationItems.length > 0 && (
          <CurationPageSection
              key={this.state.sections[i].id}
              savingState={{isSaving: this.state.isSaving, skusSaving: this.state.skusSaving}}
              title={this.state.sections[i].title}
              curationConfig={this.props.curationConfig}
              curationItems={this.state.sections[i].curationItems}
              selectedRows={this.state.sections[i].selectedRows}
              changedRows={this.state.sections[i].changedRows}
              checkedIndeterminate={this.isSectionCheckedIndeterminate(i)}
              checkedAll={this.isSectionCheckedAll(i)}
              headerDecision={this.state.sections[i].decision}
              isExpanded={this.state.sections[i].isExpanded}
              onHeaderDecisionChange={(decision) =>
                  this.handleHeaderDecisionChange(i, decision)
              }
              onCurationItemDecisionChange={(sku, decision) =>
                  this.handleCurationItemDecisionChange(i, sku, decision)
              }
              onSelectAll={(isChecked) =>
                  this.handleSelectAll(i, isChecked)
              }
              onSelectionChange={(sku, relatedKits, isChecked) =>
                  this.handleSelectionChange({
                    sectionIndex: i,
                    sku,
                    relatedKits,
                    isChecked,
                  })
              }
              onSaveAllClick={() => this.handleSaveAllClick(i)}
              onSaveClick={(sku) => this.handleSaveClick(i, sku)}
              onSharedSkuSave={(sku) =>
                  this.handleRemoveFromKitscoClick(i, sku)
              }
              onExpandClick={(isExpanded) =>
                  this.handleExpandClick(i, isExpanded, this.state.sections[i].id, this.state.sections[i].title)
              }
              fetchSectionDataFromApi={(pageNum, limit) =>
                  this.fetchSectionDataFromApi([this.state.sections[i].id], [this.state.sections[i].title], pageNum, limit, [i])
              }
              isAssortmentWorkflowOffshoreUser={
                this.props.isAssortmentWorkflowOffshoreUser
              }
              suggestedStyleRejectionReasons={
                this.props.suggestedStyleRejectionReasons
              }
              skusTotalCount={this.state.sections[i].filteredSkusTotalCount}
              skusSavedCount={this.state.sections[i].filteredSkusSavedCount}
              isLoading={this.state.sections[i].isLoading}
              error={this.state.sections[i].error}
              currentPage={this.state.sections[i].currentPage}
          />
          // )
      );

    }
    return curationPageSections;
  }

  render() {
    return (
      <PageContainer pageTitle={this.getPageTitle()} layoutWidth="fullWidth">
        <ErrorBoundary FallbackComponent={ErrorScreen}>
        <CurationToolPageHeader
          batchId={this.props.batchId}
          batchData={this.props.batchData}
          batchDataLoading={this.state.batchDataLoading}
          rebrandProject={this.getRebrandProject()}
          skusSavedCount={this.getSkusSavedCount()}
          skusTotalCount={this.getSkusTotalCount()}
          onCompleteClick={this.handleCompleteClick}
          onExpandAllClick={this.handleClickExpandAll}
          onCollapseAllClick={this.handleClickCollapseAll}
          selectedFilters={this.state.filters}
          filterOptions={this.getFilterOptions()}
          onChangeFilter={this.handleChangeFilter}
          isExpandedAll={this.isExpandedAll()}
          labelComplete={
            <Translation msgid="CurationTool.CompleteCuration" />
          }
          progressLabel={
            <Translation msgid="CurationTool.CompleteCuration" />
          }
          isAutomaticCurationPostQaEnabled={
            this.props.isAutomaticCurationPostQaEnabled
          }
          warnings={this.props.warnings}
        />
          {
            this.getSections()
          }
        {/*{this.state.sections.map((section, i) => {*/}
        {/*  */}
        {/*  })}*/}
          <Toast
            isOpen={this.state.toast.isOpen}
            status={this.state.toast.status}
            onRequestClose={this.closeToast}
          >
            {this.state.toast.message}
          </Toast>
          <CurationPageSaveAllConfirmationModal
            isOpen={
              this.state.saveAllConfirmation.skus &&
              this.state.saveAllConfirmation.skus.length > 0
            }
            skus={this.state.saveAllConfirmation.skus || []}
            onConfirm={this.handleSaveAllConfirmationModalConfirm}
            onCancel={this.handleSaveAllConfirmationModalCancel}
          />
          <CurationPageRemoveFromKitscoConfirmationModal
            isOpen={!!this.state.removeFromKitscoConfirmation.sku}
            sku={this.state.removeFromKitscoConfirmation.sku || ''}
            onConfirm={this.handleRemoveFromKitscoConfirm}
            onCancel={this.handleRemoveFromKitscoCancel}
          />
        </ErrorBoundary>
      </PageContainer>
    );
  }


}

export default CurationPage;
