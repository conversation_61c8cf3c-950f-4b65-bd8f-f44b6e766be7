<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Curation\ExclusivityAssortment\Infrastructure\Helper\SQLBulkHelper;
use WF\Shared\Helpers\SQL;
use PDO;

class Assortment_Curation_Decision_Loader_Postgres_DAO {
  private ProductConnection $pdo;

  private PostgresConnection $pdo_psql;

  /**
   * @param ProductConnection $pdo PDO
   * @param PostgresConnection $pdo_psql PDO_PSQL
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
  }

    /**
     * @param string[] $skus skus
     *
     * @return int[]
     */
  public function get_curation_price_tiers_for_skus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }
    $sql = 'SELECT kitComposition.ChildSku as ChildSku
        FROM csn_product.dbo.vwExclusivityKitCompositionActive kitComposition WITH (NOLOCK)
        WHERE kitComposition.ParentSKU IN (' . $this->pdo->paramsForList(count($skus), 'child_sku', SQL::varchar(8)) . ')
       ';
    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList(':child_sku', $skus, SQL::varchar(8));
    $statement->execute();
    $child_sku_lst = $statement->fetchAll();

    $sku_lst = array_merge($skus, array_column($child_sku_lst, 'ChildSku'));
    $cl_sql = 'SELECT DISTINCT cl.ClID
                FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                 INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
                 INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
                WHERE
                    p.PrSKU IN (' . $this->pdo->paramsForList(count($sku_lst), 'skus', SQL::varchar(8)) . ')
            ';

    $statement = $this->pdo->prepare($cl_sql);
    $statement->bindValuesList(':skus', $sku_lst, SQL::varchar(8));
    $statement->execute();
    $data = $statement->fetchAll();
    $clId_data = array_column($data, 'ClID');

    $tbl_cpt_data = $this->get_tbl_class_price_tier_data_from_pg($clId_data);

    // create temp price tier table by getting data from postgres db
    $pt_column_map    = [
        'CptPriceTierCeiling' => SQL::money,
        'CptClID' => SQL::int,
        'CptPriceTier' => SQL::int,
    ];
    $pt_inserted_data = [];
    foreach ($tbl_cpt_data as $cpt_data) {
      $pt_inserted_data[] = [
          'CptPriceTierCeiling' => $cpt_data['CptPriceTierCeiling'],
          'CptClID' => $cpt_data['CptClID'],
          'CptPriceTier' => $cpt_data['CptPriceTier'],
      ];
    }

    $pt_sql = SQLBulkHelper::get_temp_table_json_sql($pt_column_map, 'tmpClassPriceTier', 'tmpClassPriceTierData');
    $pt_sql .= 'SELECT
                    p.PrSKU AS sku,
                    pt.CptPriceTier AS price_tier
                FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                 INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
                 INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
                 CROSS APPLY (
                    SELECT ISNULL(CASE MAX(CptPriceTier) WHEN 4 THEN 4 ELSE MAX(CptPriceTier) + 1 END, 1) AS CptPriceTier
                    FROM #tmpClassPriceTier cpt
                    WHERE CptPriceTierCeiling < p.PrSalePrice AND CptClID = cl.ClID
                ) pt
                WHERE
                    p.PrSKU IN (' . $this->pdo->paramsForList(count($sku_lst), 'sku_lst', SQL::varchar(8)) . ')
            ';

    $statement = $this->pdo->prepare($pt_sql);
    $statement->bindValue(':tmpClassPriceTierData', json_encode($pt_inserted_data), PDO::PARAM_STR);
    $statement->bindValuesList(':sku_lst', $sku_lst, SQL::varchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to load context data for skus');
    }

    return $statement->fetchAll(PDO::FETCH_KEY_PAIR);
  }

  private function get_tbl_class_price_tier_data_from_pg(array $cptClIdArray): array {
    $cptClId_lst  = $this->pdo_psql->paramsForLists($cptClIdArray);
    if($cptClId_lst === ""){return array();}
    $sql = "
        SELECT \"CptPriceTierCeiling\", \"CptClID\", \"CptPriceTier\"
        FROM \"tblClassPriceTier\" cpt
        WHERE \"CptClID\" IN ($cptClId_lst)
    ";
    $statement = $this->pdo_psql->prepare($sql);
    $statement->execute();
    return $statement->fetchAll();
  }
}
