<?php
/**
 * PHP version 7
 *
 * Curation_Collider_DAO Legacy without Perigold Replacement
 *
 * @todo      We need to remove this file once the perigold replacement logic is confirmed
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\Curation;

class Curation_Collider_Legacy_DAO extends Curation_Collider_DAO {
  /**
   * @param string[] $skus SKUs
   *
   * @return string[]
   */
  public function getPerigoldWhiteLabelSkus(array $skus) : array {
    return [];
  }
}
