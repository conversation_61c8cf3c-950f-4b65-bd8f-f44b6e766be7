<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Traits\Logging_Trait;
use Psr\Log\LoggerInterface;

class Curation_Item_Group_Loader implements Curation_Item_Group_Loader_Interface {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory
   */
  private $factory;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader_Interface
   */
  private $itemLoader;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $postgres_dao;

  private FeatureTogglesInterface $feature_toggle;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader_Interface $itemLoader     Curation_Item_Loader
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                         $dao            Curation_Tool_DAO
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory    $factory        Curation_Item_Group_Factory
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO   $postgres_dao   Curation_Tool_Postgres_DAO
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                            $feature_toggle Feature Toggle
   * @param \Psr\Log\LoggerInterface|null                                                                 $logger         Logger
   */
  public function __construct(
      Curation_Item_Loader_Interface $itemLoader,
      Curation_Tool_DAO $dao,
      Curation_Item_Group_Factory $factory,
      Curation_Tool_Postgres_DAO $postgres_dao,
      FeatureTogglesInterface $feature_toggle,
      ?LoggerInterface $logger = null
  ) {
      $this->itemLoader     = $itemLoader;
      $this->dao            = $dao;
      $this->factory        = $factory;
      $this->postgres_dao   = $postgres_dao;
      $this->feature_toggle = $feature_toggle;
      $this->logger         = $logger;
  }

  /**
   * @param int $batchId BatchID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group[]
   */
  public function load(int $batchId, int $contextXnId, int $limit, int $pageNumber, string $supplierID, int $qaStatus) : array {
    $offset = ($pageNumber - 1) * $limit;
    $this->log_info('Loading Curation Item Groups', ['batch_id' => $batchId,
        'itemLoader' => get_class($this->itemLoader)]);
    if ($limit != 0 && $contextXnId!=0 && $supplierID=="" && $qaStatus==-1){
        $curationItems = $this->itemLoader->loadWithCollection($batchId, $contextXnId, $limit, $offset, $qaStatus);
    } else {
        $curationItems = $this->itemLoader->load($batchId, $qaStatus);
    }

    $skusList = [];
    foreach ($curationItems as $item) {
        $skusList[] = $item -> get_sku();
    }
    $this->log_info('Loaded Curation Item Groups', ['batch_id' => $batchId, 'skus' => $skusList]);
    $this->log_info('Populating status for Curation Item Groups', ['batch_id' => $batchId]);
    $this->populateStatus($curationItems);
    $this->log_info('Populating kit parents for Curation Item Groups', ['batch_id' => $batchId]);
    $this->populateKitParents($curationItems);
    $this->log_info('Populating kitsko for Curation Item Groups', ['batch_id' => $batchId]);
    $this->populateKitsco($batchId, $curationItems);

    return $this->factory->createFromCurationItems($curationItems);
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curationItems Curation Items
   *
   * @return void
   */
  private function populateStatus(array $curationItems) {
    $skus = array_keys($curationItems);

    $rows = $this->dao->get_statuses($skus);
    foreach ($rows as $row) {
      if (!isset($curationItems[$row['sku']])) {
        $this->log_error(
            sprintf('Failed status: SKU %s does not exist on the list: [%s]', $row['sku'], print_r($skus, true)),
            ['sku' => $row['sku'], 'skus' => $skus]
        );
        continue;
      }
      $curationItems[$row['sku']]->set_status($row['status']);
    }
  }

  /**
   * @param int                                                                            $batchId       Batch ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curationItems Curation Items
   *
   * @return void
   */
  private function populateKitsco(int $batchId, array $curationItems) {
    $skus = [];
    foreach ($curationItems as $item) {
      if (!empty($item->get_kit_parents())) {
        $skus[] = $item->get_sku();
      }
    }

    if (empty($skus)) {
      return;
    }
    if ($this->feature_toggle->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $rows = $this->postgres_dao->get_kitsco($batchId, $skus);
    } else {
      $rows = $this->dao->get_kitsco($batchId, $skus);
    }
    foreach ($rows as $row) {
      if (!isset($curationItems[$row['sku']])) {
        $this->log_error(
            sprintf('Failed kitsco: SKU %s does not exist on the list: [%s]', $row['sku'], print_r($skus, true)),
            ['sku' => $row['sku'], 'skus' => $skus, 'batch_id' => $batchId]
        );

        continue;
      }
      $curationItems[$row['sku']]->set_kitsco(true);
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curationItems Curation Items
   *
   * @return void
   */
  private function populateKitParents(array $curationItems) {
    $skus = [];
    foreach ($curationItems as $item) {
      $skus[] = $item->get_sku();
    }

    $kitData = $this->dao->get_parents($skus);
    foreach ($kitData as $row) {
      if (!isset($curationItems[$row['childsku']])) {
        $this->log_error(
            sprintf('Failed kit parents: SKU %s does not exist on the list: [%s]', $row['childsku'], print_r($skus, true)),
            ['childsku' => $row['childsku'], 'skus' => $skus]
        );
        continue;
      }
      $parents   = $curationItems[$row['childsku']]->get_kit_parents();
      $parents[] = $row['parentsku'];
      $curationItems[$row['childsku']]->set_kit_parents($parents);
    }
  }
}
