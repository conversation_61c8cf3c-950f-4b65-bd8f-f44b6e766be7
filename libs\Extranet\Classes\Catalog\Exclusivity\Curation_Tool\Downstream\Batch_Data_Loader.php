<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_SKU_Service;
use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

final class Batch_Data_Loader {
  /**
   * @var Curation_Batch_DAO
   */
  private $dao;

  /**
   * @var Curation_Batch_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @var Batch_SKU_Service
   */
  private $batch_sku_service;

  /**
   * @var Curation_Request_Factory
   */
  private $curation_request_factory;

  /**
   * @var Batch_Factory
   */
  private $batch_factory;

  /**
   * @var FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;


  /**
   * @param Curation_Batch_DAO       $dao                      Dao class
   * @param Curation_Batch_Postgres_DAO       $dao_psql                  Dao class
   * @param Batch_SKU_Service        $batch_sku_service        Batch SKU service
   * @param Curation_Request_Factory $curation_request_factory Curation Request Factory
   * @param Batch_Factory            $batch_factory            Batch Factory
   * @param FeatureTogglesInterface $featureToggles            FeatureTogglesInterface
   */
  public function __construct(
      Curation_Batch_DAO $dao,
      Curation_Batch_Postgres_DAO $dao_psql,
      Batch_SKU_Service $batch_sku_service,
      Curation_Request_Factory $curation_request_factory,
      Batch_Factory $batch_factory,
      FeatureTogglesInterface $featureToggles
  ) {
    $this->dao                      = $dao;
    $this->dao_psql                 = $dao_psql;
    $this->batch_sku_service        = $batch_sku_service;
    $this->curation_request_factory = $curation_request_factory;
    $this->batch_factory            = $batch_factory;
    $this->featureToggles            = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function load_data(int $batch_id) : Batch {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $batch = $this->batch_factory->create($this->dao_psql->get_batch_info($batch_id));
    } else {
      $batch = $this->batch_factory->create($this->dao->get_batch_info($batch_id));
    }
    $batch = $this->batch_sku_service->load_verified_skus($batch);

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $batch->set_curation_manual_request_type($this->dao_psql->get_manual_curation_type_for_batch($batch_id));
    } else {
      $batch->set_curation_manual_request_type($this->dao->get_manual_curation_type_for_batch($batch_id));
    }

    if ($batch->get_curation_request_type() === Batch::PRODUCT_ADDITION_BATCH) {
      $batch = $this->load_curation_requests($batch);
    }

    return $batch;
  }

  /**
   * Loads curation requests with id and quckiform id
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch that should load data
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  private function load_curation_requests(Batch $batch) : Batch {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $curation_requests = $this->dao_psql->get_batch_curation_requests($batch);
    } else {
      $curation_requests = $this->dao->get_batch_curation_requests($batch);
    }

    foreach ($curation_requests as $curation_request) {
      $batch->push_curation_request($this->curation_request_factory->create_request_model($curation_request));
    }

    return $batch;
  }
}
