# Production Tracking API client

API Client for the Production Tracking API Service based on GraphQL owned by ME5.
Detail regarding the API can be found in this [Infohub article](https://infohub.corp.wayfair.com/display/MENG/Production+Tracking+API)
 
 ---
 
 ## Usage
 
 ```php
 <?php
     use WF\Curation\ProductionTrackingApi\Client;
     use WF\Curation\ProductionTrackingApi\ClientConfig;
 
     $logger = new Logger();
 
     $config = new ClientConfig($logger, 3, 3, 'client-id', 'clcient-secret','auth-url','api-url');
     
     $wlClient = new Client($config);
 
     //Example Query with Variables
     $query = '
         mutation update($projectId: Int32!, $stage: String!){
           productTracking {
             update(
               projectEntry: [
                 {
                   projectId: $projectId,
                   stage: $stage
                 }
               ]
             )
           }
         }
     ';
     $variables = [
         'projectId' => 1,
         'stage'     => '(E5) Extranet White Label'
     ];
 
     $response = $this->client->updateProductionTrackingStatus($query, $variables);
     
     $response = $wlClient->createBatch([$batch]);
 
     echo "Response was successful? {$response->isRequestSuccessful()}";
 ```

 
 ME6 - Curation team
 
 * Slack: #merch-eng-brand-workflows 