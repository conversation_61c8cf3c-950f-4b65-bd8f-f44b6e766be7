<?php

declare (strict_types=1);

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool;

class Config_Data implements \JsonSerializable {
  /**
   * @var Exclusion_Reason_Data[]
   */
  private $exclusionReasons;

  /**
   * @var Price_Tier_Data[]
   */
  private $priceTiers;

  /**
   * @var Style_Data[]
   */
  private $styles;

  /**
   * @var Substyle_Data[]
   */
  private $substyles;

  /**
   * @var Manufacturer_Data[]
   */
  private $manufacturers;

  /**
   * @var Style_Manufacturer_Data[]
   */
  private $styleManufacturers;

  /**
   * @var Granular_Style_Data[]
   */
  private $granularStyles;

  /**
   * Config_Data constructor.
   *
   * @param Exclusion_Reason_Data[]   $exclusionReasons   exclusionReasons
   * @param Price_Tier_Data[]         $priceTiers         priceTiers
   * @param Style_Data[]              $styles             styles
   * @param Substyle_Data[]           $substyles          substyles
   * @param Manufacturer_Data[]       $manufacturers      manufacturers
   * @param Style_Manufacturer_Data[] $styleManufacturers styleManufacturers
   * @param Granular_Style_Data[]     $granularStyles     granularstyles
   */
  public function __construct(
      array $exclusionReasons,
      array $priceTiers,
      array $styles,
      array $substyles,
      array $manufacturers,
      array $styleManufacturers,
      array $granularStyles
  ) {
    $this->exclusionReasons   = $exclusionReasons;
    $this->priceTiers         = $priceTiers;
    $this->styles             = $styles;
    $this->substyles          = $substyles;
    $this->manufacturers      = $manufacturers;
    $this->styleManufacturers = $styleManufacturers;
    $this->granularStyles     = $granularStyles;
  }

  /**
   * @return Exclusion_Reason_Data[]
   */
  public function exclusionReasons() : array {
    return $this->exclusionReasons;
  }

  /**
   * @return Price_Tier_Data[]
   */
  public function priceTiers() : array {
    return $this->priceTiers;
  }

  /**
   * @return Style_Data[]
   */
  public function styles() : array {
    return $this->styles;
  }

  /**
   * @return Substyle_Data[]
   */
  public function substyles() : array {
    return $this->substyles;
  }

  /**
   * @return Manufacturer_Data[]
   */
  public function manufacturers() : array {
    return $this->manufacturers;
  }

  /**
   * @return Style_Manufacturer_Data[]
   */
  public function styleManufacturers() : array {
    return $this->styleManufacturers;
  }

  /**
   * @return Granular_Style_Data[]
   */
  public function granularStyles() : array {
    return $this->granularStyles;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize()
  {
    return [
      'exclusionReasons' => $this->exclusionReasons(),
      'styles' => $this->styles(),
      'substyles' => $this->substyles(),
      'priceTiers' => $this->priceTiers(),
      'manufacturers' => $this->manufacturers(),
      'styleManufacturers' => $this->styleManufacturers(),
      'granularStyles' => $this->granularStyles(),
    ];
  }
}
