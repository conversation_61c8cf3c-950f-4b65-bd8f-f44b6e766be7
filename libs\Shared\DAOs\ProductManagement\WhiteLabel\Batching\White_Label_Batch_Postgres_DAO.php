<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;

class White_Label_Batch_Postgres_DAO {
  private ProductConnection $pdo;
  private PostgresConnection $pdo_psql;

  /**
   * White_Label_Batch_DAO constructor.
   *
   * @param ProductConnection $pdo
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
  }

  /**
   * @param int $batch_id the batch id to load info for
   *
   * @return array
   */
  public function get_batch(int $batch_id) : array {
    $external_data = $this->get_batch_external($batch_id);
    $internal_data = $this->get_batch_internal($batch_id);
    $data = [];
    if (sizeof($external_data) != 0) {
      if (sizeof($internal_data) == 0) {
        $data = array_merge($external_data, ['pt_ticket_id'=>null]);
      } else {
        $data = array_merge($external_data, $internal_data[0]);
      }
    }
    return $data;
  }

  private function get_batch_external(int $batch_id) : array {
    $sql = '
      SELECT
        wlb.ID AS id,
        wlb.StateID AS state_id,
        wlb.BrandCatalogID AS brand_catalog_id,
        wlb.CreatedAt AS created_at,
        wlb.UpdatedAt AS updated_at,
        wlb.isNewStagingProcess AS is_new_staging_process
      FROM csn_product.dbo.tblWhiteLabelBatch wlb WITH (NOLOCK)
      WHERE
        wlb.ID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Cannot load batch: %s', implode(',', $statement->errorInfo())));
    }

    return $statement->fetchAll()[0] ?? [];
  }

  private function get_batch_internal(int $batch_id) : array {
    $sql = '
          SELECT v."CloningPrtID" AS pt_ticket_id 
          FROM "tblVerification" v
          INNER JOIN "tblVerificationItem" vi ON vi."ViVerificationID" = v."VerificationID"
          WHERE vi."ViBatchID" = :batch_id
          LIMIT 1
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Cannot load batch: %s', implode(',', $statement->errorInfo())));
    }

    return $statement->fetchAll();
  }
}
