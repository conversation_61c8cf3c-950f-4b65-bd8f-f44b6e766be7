<?php

declare(strict_types=1);

/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Automatic_Class_Curation_Item {
  /**
   * @var string
   */
  private string $sku; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private int $class_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private int $price_tier; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private int $style_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private int $substyle_id; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private int $manufacturer_id; /** @phpstan-ignore-line */

  /**
   * Automatic_Class_Curation_Item constructor
   */
  private function __construct() {
    /**
     * Can be created only by PDO
     */
  }

  /**
   * @return string
   */
  public function getSku() : string {
    return $this->sku;
  }

  /**
   * @return int
   */
  public function getClassId() : int {
    return $this->class_id;
  }

  /**
   * @return int
   */
  public function getPriceTier() : int {
    return $this->price_tier;
  }

  /**
   * @return int
   */
  public function getStyleId() : int {
    return $this->style_id;
  }

  /**
   * @return int
   */
  public function getSubstyleId() : int {
    return $this->substyle_id;
  }

  /**
   * @return int
   */
  public function getManufacturerId() : int {
    return $this->manufacturer_id;
  }
}
