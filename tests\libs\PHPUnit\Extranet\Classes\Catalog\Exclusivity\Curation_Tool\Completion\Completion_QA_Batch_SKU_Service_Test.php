<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use function count;

class Completion_QA_Batch_SKU_Service_Test extends TestCase
{
    use Prophecy<PERSON>rait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage
     */
    private $dao;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service
     */
    private $subject;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Completion_QA_Batch_SKU_Storage::class);
        $this->dao_psql = $this->prophesize(Batch_Management_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->subject = new Completion_QA_Batch_SKU_Service(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_empty_array()
    {
        $batchId = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->get_qa_batch_skus($batchId, Curation_QA_Status::rejected()->value())->willReturn([])->shouldBeCalled();

        $actualResult = $this->subject->getRejectedSkus($batchId);

        $this->assertEquals(0, count($actualResult));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_rejected_skus_feature_toggle_off()
    {
        $batchId = 1;
        $sku = 'A';
        $message = 'Rejected';

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->get_qa_batch_skus($batchId, Curation_QA_Status::rejected()->value())->willReturn([['sku' => $sku, 'message' => $message]])->shouldBeCalled();

        $actualResult = $this->subject->getRejectedSkus($batchId);

        $this->assertEquals(1, count($actualResult));
        $completionQaSku = $actualResult[0];
        $this->assertEquals($sku, $completionQaSku->get_sku());
        $this->assertEquals($message, $completionQaSku->get_message());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_rejected_skus_feature_toggle_on()
    {
        $batchId = 1;
        $sku = 'A';
        $message = 'Rejected';

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->get_qa_batch_skus($batchId, Curation_QA_Status::rejected()->value())->willReturn([['sku' => $sku, 'message' => $message]])->shouldBeCalled();

        $actualResult = $this->subject->getRejectedSkus($batchId);

        $this->assertEquals(1, count($actualResult));
        $completionQaSku = $actualResult[0];
        $this->assertEquals($sku, $completionQaSku->get_sku());
        $this->assertEquals($message, $completionQaSku->get_message());
    }
}
