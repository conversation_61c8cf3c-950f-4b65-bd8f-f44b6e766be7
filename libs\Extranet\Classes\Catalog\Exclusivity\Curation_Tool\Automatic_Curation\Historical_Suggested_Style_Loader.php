<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Entity\Historical_Suggested_Style_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Historical_Suggested_Style_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Historical_Suggested_Style_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
class Historical_Suggested_Style_Loader implements Section_Historical_Suggested_Style_Loader {
  /**
   * @var Historical_Suggested_Style_Loader_Storage
   */
  private $storage;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */


  private FeatureTogglesInterface $featureToggles;

  /**
   * @var Historical_Suggested_Style_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @param Historical_Suggested_Style_Loader_Storage $storage storage
   * @param Historical_Suggested_Style_Postgres_DAO       $dao_psql Historical_Suggested_Style_Postgres_DAO
   * @param FeatureTogglesInterface                 $featureToggles           Feature toggle
   */
  public function __construct(Historical_Suggested_Style_Loader_Storage $storage,Historical_Suggested_Style_Postgres_DAO $dao_psql,FeatureTogglesInterface $featureToggles) {
    $this->storage = $storage;
    $this->dao_psql                 = $dao_psql;
    $this->featureToggles           = $featureToggles;
  }

  /**
   * @param int $batch_id batch identifier
   *
   * @return Historical_Suggested_Style_Collection
   */
  public function get_historical_suggested_style_info_for_batch(int $batch_id) : Historical_Suggested_Style_Collection {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $infos = $this->dao_psql->get_cached_suggested_style_info_for_batch($batch_id);
    } else {
      $infos = $this->storage->get_cached_suggested_style_info_for_batch($batch_id);
    }

    return Historical_Suggested_Style_Collection::create(...$infos);
  }
}
