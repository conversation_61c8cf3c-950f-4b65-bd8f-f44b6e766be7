<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Mailer;

use Exception;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Mailer\Email_Helper_Adapter;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Result;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface;
use WF\Shared\Helpers\Email_Helper;

class Email_Helper_Adapter_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
     */
    private $message;

    /**
     * @var \WF\Shared\Helpers\Email_Helper
     */
    private $mailer;

    /**
     * @var \WF\Shared\Classes\ProductManagement\Mailer\Email_Helper_Adapter
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->mailer = $this->prophesize(Email_Helper::class);

        $this->message = $this->prophesize(Mail_Message::class);
        $this->message->get_sender()->willReturn(['', '']);
        $this->message->get_recipients()->willReturn([]);
        $this->message->get_attachments()->willReturn([]);
        $this->message->get_subject()->willReturn('');
        $this->message->get_html_body()->willReturn('');
        $this->message->get_text_body()->willReturn('');

        $this->subject = new Email_Helper_Adapter($this->mailer->reveal());
    }

    /**
     * @test
     * @return void
     */
    public function it_is_mailer()
    {
        $this->assertInstanceOf(Mailer_Interface::class, $this->subject);
    }

    /**
     * @test
     * @return void
     */
    public function it_creates_instance_of_mail_message()
    {
        $this->assertInstanceOf(Mail_Message::class, $this->subject->create_message());
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_sender_from_given_message()
    {
        $this->message->get_sender()->willReturn(['<EMAIL>', 'Sender Name']);

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            ['name' => 'Sender Name', 'email' => '<EMAIL>'],
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            ['name' => 'Sender Name', 'email' => '<EMAIL>']
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_sends_recipients_from_given_message()
    {
        $this->message->get_recipients()->willReturn(
            [
                ['<EMAIL>', 'First Name'],
                ['<EMAIL>', 'Second Name']
            ]
        );

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            [
                ['email' => '<EMAIL>', 'name' => 'First Name'],
                ['email' => '<EMAIL>', 'name' => 'Second Name']
            ],
            Argument::cetera()
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_html_body_from_given_message()
    {
        $this->message->get_html_body()->willReturn('<h1>test</h1>');

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            '<h1>test</h1>',
            Argument::cetera()
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_text_body_from_given_message()
    {
        $this->message->get_text_body()->willReturn('text body');

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            'text body',
            Argument::cetera()
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_sets_subject_from_given_message()
    {
        $this->message->get_subject()->willReturn('some subject');

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            'some subject',
            Argument::cetera()
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_attaches_first_attachment()
    {
        $this->message->get_attachments()->willReturn(
            [
                ['test1', 'test1.txt', 'plain/text', 'base64', 'attachment'],
                ['test2', 'test2.txt', 'plain/csv', '8bit', 'inline'],
            ]
        );

        $this->mailer->send_email(
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            Argument::any(),
            '',
            'test1',
            'test1.txt',
            'plain/text',
            Argument::cetera()
        )->willReturn('123-wat-321');

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('mmreqid: 123-wat-321', $result->get_reason());
        $this->assertTrue($result->is_successful());
    }

    /**
     * @test
     * @return void
     */
    public function it_catches_exception_from_helper()
    {
        $this->mailer->send_email(Argument::cetera())->will(
            static function () {
                throw new Exception('exception raised');
            }
        );

        $result = $this->subject->send($this->message->reveal());
        $this->assertInstanceOf(Mail_Result::class, $result);
        $this->assertEquals('Error sending email: exception raised', $result->get_reason());
        $this->assertFalse($result->is_successful());
    }
}
