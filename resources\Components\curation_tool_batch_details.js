/**
 * Curation Tool Batch details widget
 *
 * <AUTHOR> <em<PERSON><PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {ProgressStepper} from '@wayfair/homebase-extranet'
import Translation from '@wayfair/translation';
import {Box} from '@wayfair/homebase-extranet';
import {Block, FLEX_KEYWORDS, SPACING} from '@wayfair/homebase-extranet';
import {Column, Grid} from '@wayfair/homebase-extranet';
import {Divider} from '@wayfair/homebase-extranet'
import {ALIGNMENT, WIDTHS} from './common_layout_constants';
import {Text, TEXT_STYLE} from '@wayfair/homebase-extranet'
import {Link} from '@wayfair/homebase-extranet'
import {STATUS_LABEL_MAP, STATUSES} from './curation_tool_constants';
import {getPageByStatus} from './curation_tool_utils';

const URL_CURATION_BATCH_MANAGEMENT_REBRAND_PROJECT =
  '/v/catalog/curation_batch/index?rebrand_project_id=';

const BatchDetails = ({
  id,
  status,
  createdAt,
  processType,
  curator,
  rebrandProject,
}) => (
  <Box
    display="flex"
    mb={4}
    p={4}
    flexDirection={FLEX_KEYWORDS.COLUMN}
    borderStyle="solid"
    borderWidth="1px"
    borderColor="gray.20"
    gridRowGap={4}
  >
    <Text fontStyle={TEXT_STYLE.BOLD}>
      <Translation msgid="CurationTool.batchDetailsTitle" />
    </Text>
    {rebrandProject && (
      <Block mb={SPACING.SPACE_MEDIUM}>
        <Link
          href={`${URL_CURATION_BATCH_MANAGEMENT_REBRAND_PROJECT}${rebrandProject.id}`}
          openInNewWindow
        >
          <Translation
            msgid="CurationTool.CurationBatchQaPageRebrandProjectX"
            params={{rebrandProjectname: rebrandProject.name}}
          />
        </Link>
      </Block>
    )}
    <Grid alignItems={ALIGNMENT.CENTER}>
      <Column size={WIDTHS.WIDTH_6}>
        <Box display="flex" flexDirection={FLEX_KEYWORDS.ROW} gridColumnGap={2}>
          <Text fontStyle={TEXT_STYLE.BOLD}>
            <Translation msgid="CurationTool.batchDetailsId" />
          </Text>
          <Text>{id}</Text>
        </Box>
      </Column>
      <Column size={WIDTHS.WIDTH_6}>
        <Box display="flex" flexDirection={FLEX_KEYWORDS.ROW} gridColumnGap={2}>
          <Text fontStyle={TEXT_STYLE.BOLD}>
            <Translation msgid="CurationTool.batchDetailsCreated" />
          </Text>
          <Text>{createdAt}</Text>
        </Box>
      </Column>
    </Grid>
    <Grid alignItems={ALIGNMENT.CENTER}>
      <Column size={WIDTHS.WIDTH_6}>
        <Box display="flex" flexDirection={FLEX_KEYWORDS.ROW} gridColumnGap={2}>
          <Text fontStyle={TEXT_STYLE.BOLD}>
            <Translation msgid="CurationTool.batchDetailsType" />
          </Text>
          <Text>{processType}</Text>
        </Box>
      </Column>
      <Column size={WIDTHS.WIDTH_6}>
        <Box display="flex" flexDirection={FLEX_KEYWORDS.ROW} gridColumnGap={2}>
          <Text fontStyle={TEXT_STYLE.BOLD}>
            <Translation msgid="CurationTool.batchDetailsCurator" />
          </Text>
          <Text>{curator}</Text>
        </Box>
      </Column>
    </Grid>
    <Divider />
    <Box
      display="flex"
      flexDirection={FLEX_KEYWORDS.ROW}
      alignItems={ALIGNMENT.CENTER}
      mt={4}
    >
      <Column size={WIDTHS.WIDTH_2}>
        <Text fontStyle={TEXT_STYLE.BOLD}>
          <Translation msgid="CurationTool.batchDetailsStatusTitle" />
        </Text>
      </Column>
      <Column>
        <ProgressStepper isInteractive={false}>
          {STATUSES.map(statusStep => {
            const step = getPageByStatus(status);
            const label = STATUS_LABEL_MAP[statusStep];
            return (
              <ProgressStepper.Step
                key={statusStep}
                showTopLabel
                isCurrent={step === statusStep}
                isComplete={
                  step > statusStep || step === STATUSES[STATUSES.length - 1]
                }
                isDisabled={step < statusStep}
                stepIdentifier={statusStep}
                topLabel={label}
              />
            );
          })}
        </ProgressStepper>
      </Column>
    </Box>
  </Box>
);

BatchDetails.propTypes = {
  id: PropTypes.number.isRequired,
  status: PropTypes.number.isRequired,
  createdAt: PropTypes.string.isRequired,
  processType: PropTypes.string.isRequired,
  curator: PropTypes.string.isRequired,
  rebrandProject: PropTypes.object,
};

BatchDetails.defaultProps = {
  rebrandProject: {},
};

export default BatchDetails;
