<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use App\Application\Translation\TranslatorInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;

class Kit_Part_Of_Other_Kit_Validation implements Curation_Data_Consistency_Validation {
  /**
   * @var TranslatorInterface
   */
  private TranslatorInterface $translate;

  /**
   * @var Curation_Data_Consistency_Validation_Storage
   */
  private Curation_Data_Consistency_Validation_Storage $dao;

  /**
   * @param TranslatorInterface                          $translate Translator
   * @param Curation_Data_Consistency_Validation_Storage $dao       DAO
   */
  public function __construct(TranslatorInterface $translate, Curation_Data_Consistency_Validation_Storage $dao) {
    $this->translate = $translate;
    $this->dao       = $dao;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return Curation_Data_Consistency_Validation_Result
   */
  public function validate(int $batch_id, array $sections): Curation_Data_Consistency_Validation_Result {
    $result  = new Curation_Data_Consistency_Validation_Result();
    $parents = $this->extract_skus_from_sections($sections);

    if (count($parents) === 0) {
      return $result;
    }

    $rows = $this->dao->get_kit_parents_with_children($parents);

    foreach ($parents as $parent) {
      $other = $this->get_another_kit_parent_with_all_current_kit_components($parent, $rows);

      if (empty($other)) {
        continue;
      }

      $error = $this->translate->trans('Validation.CurationKitPartOfOtherKit', ['{parent}' => $parent, '{other}' => $other]);
      $result->add_error($parent, $error);
    }

    return $result;
  }

  /**
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  private function extract_skus_from_sections(array $sections): array {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        if ($item->get_type() !== Curation_Item_Type::kit_parent()) {
          continue;
        }

        $skus[] = $item->get_sku();
      }
    }

    return $skus;
  }

  /**
   * @param string $parent Parent
   * @param array  $rows   Rows
   *
   * @return null|string
   */
  private function get_another_kit_parent_with_all_current_kit_components(string $parent, array $rows): ?string {
    $parents = array_keys($rows);

    foreach ($parents as $other) {
      if ($other === $parent) {
        continue;
      }

      // check if if all current kit children are included in the other kit parent
      if (empty(array_diff($rows[$parent], $rows[$other]))) {
        return $other;
      }
    }

    return null;
  }
}
