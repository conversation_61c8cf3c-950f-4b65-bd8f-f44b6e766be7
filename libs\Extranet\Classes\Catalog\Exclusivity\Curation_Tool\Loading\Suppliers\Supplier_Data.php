<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers;

class Supplier_Data {
  /**
   * @var string[]
   */
  private $names = [];

  /**
   * @var bool
   */
  private $is_canadian_only = false;

  /**
   * Supplier_Data constructor.
   *
   * @param string[] $names            Names
   * @param bool     $is_canadian_only Is Canadian Only
   */
  public function __construct(array $names, bool $is_canadian_only) {
    $this->names            = $names;
    $this->is_canadian_only = $is_canadian_only;
  }

  /**
   * @return string[]
   */
  public function get_names() : array {
    return $this->names;
  }

  /**
   * @return bool
   */
  public function is_canadian_only() : bool {
    return $this->is_canadian_only;
  }
}