/**
 * Curation - Save All Confirmation Modal
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {ConfirmationModal, SPACING, Text} from '@wayfair/homebase-extranet';

const CurationPageSaveAllConfirmationModal = ({
  isOpen,
  onConfirm,
  onCancel,
  skus,
}) => (
  <ConfirmationModal
    title={<Translation msgid="CurationTool.CurationToolSaveAllConfirmationModalTitle" />}
    isOpen={isOpen}
    onConfirm={onConfirm}
    onCancel={onCancel}
  >
    <Text>
      <Translation msgid="CurationTool.CurationToolSaveAllSelectedConfirmationModalText" />
    </Text>
    <Text mb={SPACING.SPACE_SMALL}>
      <Translation
        msgid="CurationTool.CurationToolSaveAllConfirmationModalSkus"
        params={{
          skus: skus.join(', '),
        }}
      />
    </Text>
    <Text>
      <Translation msgid="CurationTool.CurationToolSaveAllConfirmationModalQuestion" />
    </Text>
  </ConfirmationModal>
);

export default CurationPageSaveAllConfirmationModal;

CurationPageSaveAllConfirmationModal.propTypes = {
  isOpen: PropTypes.bool,
  skus: PropTypes.arrayOf(PropTypes.string),
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
};

CurationPageSaveAllConfirmationModal.defaultProps = {
  isOpen: false,
  skus: [],
};
