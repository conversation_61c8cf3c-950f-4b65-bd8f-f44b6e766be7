<?php

declare(strict_types=1);
/**
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\Graphql;

use App\Application\Logger\LoggerTrait;
use Exception;
use GuzzleHttp\Client as Guzzle;
use GuzzleHttp\Exception\RequestException;
use Psr\Log\LoggerAwareTrait;
use WF\Shared\Environment;
use function array_column;
use function json_decode;

class GraphClient
{
    use LoggerTrait;
    use LoggerAwareTrait;

    /**
     * @var string
     */
    protected string $url;

    /**
     * @var Guzzle
     */
    protected Guzzle $guzzle;

    /**
     * Number of seconds to wait before request timeout
     *
     * @var int
     */
    protected int $timeout;

    /**
     * @var array
     */
    protected array $codes_logged;

    public const CURATION_GRAPH_PROD_URL = 'http://kube-btbb-bc-curation-graphql.service.intraiad1.consul.csnzoo.com/graphql';
    public const CURATION_GRAPH_DEV_URL = 'http://kube-btbb-bc-curation-graphql.service.intradsm1.sdeconsul.csnzoo.com/graphql';

    /**
     * Client constructor.
     *
     * @param string      $environment base url
     * @param Guzzle|null $guzzle      - guzzle http client
     * @param int         $timeout     timeout value
     */
    public function __construct(string $environment, Guzzle $guzzle = null, int $timeout = 2)
    {
        $this->url = $environment === Environment::PRODUCTION ? self::CURATION_GRAPH_PROD_URL : self::CURATION_GRAPH_DEV_URL;
        $this->guzzle = $guzzle ?? new Guzzle();
        $this->timeout = $timeout;
        $this->codes_logged = [];
    }

    /**
     * Make a GraphQL Request and get the raw guzzle response.
     *
     * @param string $query     GraphQL query
     * @param array  $variables GraphQL variables
     * @param array  $headers   http request headers
     * @param int    $timeout   Number of seconds to wait before request timeout
     *
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function getRawGuzzleResponseToQuery(string $query, array $variables = [], array $headers = [], int $timeout = 0)
    {
        try {
            return $this->guzzle->request(
                'POST',
                $this->url,
                [
                    'headers' => $headers,
                    'timeout' => $timeout,
                    'json' =>
                        [
                            'debug' => true,
                            'query' => $query,
                            'variables' => $variables
                        ],
                ]
            );
        } catch (RequestException $e) {
            // Only log each response code once per application lifetime
            $code = $e->getCode();
            if (!isset($this->codes_logged[$code])) {
                $this->codes_logged[$code] = true;
                $this->error(
                    GRAPHQL_DID_NOT_PROVIDE_VALID_RESPONSE,
                    ['exception' => $e, 'code' => $code]
                );
            }
            throw $e;
        }
    }

    /**
     * Make a GraphQL Request and get the raw guzzle response.
     *
     * @param string $query     GraphQL query
     * @param array  $variables GraphQL variables
     * @param array  $headers   http request headers
     * @param string $endpoint  CostApiGraphQL Endpoint
     * @param int    $timeout   Number of seconds to wait before request timeout
     *
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function getCostApiRawGuzzleResponseToQuery(string $query, array $variables = [], array $headers = [], string $endpoint, int $timeout = 0)
    {
        try {
            return $this->guzzle->request(
                'POST',
                $endpoint,
                [
                    'headers' => $headers,
                    'timeout' => $timeout,
                    'json' =>
                        [
                            'debug' => true,
                            'query' => $query,
                            'variables' => $variables
                        ],
                ]
            );
        } catch (RequestException $e) {
            // Only log each response code once per application lifetime
            $code = $e->getCode();
            if (!isset($this->codes_logged[$code])) {
                $this->codes_logged[$code] = true;
                $this->error(
                    GRAPHQL_DID_NOT_PROVIDE_VALID_RESPONSE,
                    ['exception' => $e, 'code' => $code]
                );
            }
            throw $e;
        }
    }
  
    /**
     * Make a GraphQL Request and get the response body in JSON form.
     *
     * @param string $query     GraphQL query
     * @param array  $variables GraphQL variables
     * @param array  $headers   http request headers
     * @param bool   $assoc     1 - return associative array
     * @param int    $timeout   Number of seconds to wait before request timeout
     *
     * @return mixed
     *
     * @throws GraphException
     * @throws Exception
     */
    public function getJsonResponseToQuery(string $query, array $variables = [], array $headers = [], bool $assoc = false, int $timeout = 0)
    {
        $response = $this->getRawGuzzleResponseToQuery($query, $variables, $headers, $timeout);
        $content = $response->getBody()->getContents();
        $response_json = json_decode($content, $assoc);
        if ($response_json === null) {
            throw new Exception(GRAPHQL_DID_NOT_PROVIDE_VALID_RESPONSE);
        }
        if (isset($response_json->errors)) {
            throw new GraphException(array_column($response_json->errors, 'message'));
        }
  
        return $response_json;
    }

    /**
     * Make a GraphQL Request and get the response body in JSON form.
     *
     * @param string $query     GraphQL query
     * @param bool   $assoc     1 - return associative array
     * @param string $endpoint  GraphQL Endpoint
     * @param int    $timeout   Number of seconds to wait before request timeout
     * @param array  $variables GraphQL variables
     * @param array  $headers   http request headers
     *
     * @return mixed
     *
     * @throws GraphException
     * @throws Exception
     */
    public function getCostApiJsonResponseToQuery(string $query, string $endpoint, bool $assoc = false, int $timeout = 0, array $variables = [], array $headers = [])
    {
        $response = $this->getCostApiRawGuzzleResponseToQuery($query, $variables, $headers, $endpoint, $timeout);
        $content = $response->getBody()->getContents();
        $response_json = json_decode($content, $assoc);
        if ($response_json === null) {
            throw new Exception(GRAPHQL_DID_NOT_PROVIDE_VALID_RESPONSE);
        }
        if (isset($response_json->errors)) {
            throw new GraphException(array_column($response_json->errors, 'message'));
        }

        return $response_json;
    }
}
