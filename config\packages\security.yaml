security:
  enable_authenticator_manager: true
  providers:
    partnerHomeUsers:
      id: wf.ph.UserProvider
  firewalls:
    # needed for K8S as its health checker (internal) doesn't have session
    healthcheck:
      pattern: ^(/d/curation-tool/)?internal/ping
      security: false
    staticContent:
      pattern: ^(/d/curation-tool/)?/bundles
      security: false
    skuImages:
      pattern: ^(/d/curation-tool/)?/get_images
      security: false
    debugSKU:
      pattern: ^(/d/curation-tool/)?/debug_groups
      security: false
    partnerHomeUsers:
      provider: partnerHomeUsers
      stateless: true
      lazy: true
      guard:
        authenticators:
          - wf.ph.UserAuthenticator
  access_control:
    - { path: ^/d/curation-tool/internal, roles: PUBLIC_ACCESS }
#    - { path: ^(/d/curation-tool/)?/bundles, roles: PUBLIC_ACCESS }
#    - { path: ^(/d/curation-tool/)?/get_images, roles: PUBLIC_ACCESS }
#    - { path: ^(/d/curation-tool/)?/debug_groups, roles: PUBLIC_ACCESS }
    - { path: ^/d/curation-tool, roles: IS_AUTHENTICATED_REMEMBERED }
#    - { path: '^/d/my-service/admin', roles: ['PH_IS_ADMIN'] }
