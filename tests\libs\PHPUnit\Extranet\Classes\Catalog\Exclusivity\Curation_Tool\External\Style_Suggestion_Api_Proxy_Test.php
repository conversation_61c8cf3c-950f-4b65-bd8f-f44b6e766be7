<?php

declare(strict_types=1);

namespace App\Tests\Integration\WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External;

use App\Tests\TestingKernel;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Style_Suggestion\Style_Suggestion_Api_Client;

final class Style_Suggestion_Api_Proxy_Test extends KernelTestCase
{
    protected static function getKernelClass(): string
    {
        return TestingKernel::class;
    }



    /**
     * @test
     * @throws API_Request_Exception
     */
    public function getStyleSuggestion_AfterRetriesSuccess(): void
    {
        $this->markTestSkipped('for now skipping this test, need find out the solution for "Infection runs the test suite in a RANDOM order" for mutation test fails');

        $container = self::getContainer();

        $mockHandler = new MockHandler([
            new Response(200, [], '{"access_token": "abc"}'),         // Auth Token: ok
            new Response(
                200,
                [],
                '
                        [
                          {
                            "sku": "WFSKU12222",
                            "threshold": {
                              "classId": 32,
                              "className": "Name of the class",
                              "value": 0.75
                            },
                            "suggestions": [
                              {
                                "styleId" : 1,
                                "styleName":  "name",
                                "probability" : 99,
                                "rank" : 4,
                              }
                            ]
                          }
                        ]
                '
            ),
        ]);

        $handlerStack = $container->get(HandlerStack::class);
        $handlerStack->setHandler($mockHandler);
        $sku = 'WFSKU1';
        $styleSuggestionClient = $container->get(Style_Suggestion_Api_Client::class);
        $response = [];
        $response = $styleSuggestionClient->get_style_suggestion([$sku], 1);
        self::assertIsArray($response);
        self::assertCount(1, $response);
        $style_Suggestion_DTO = '';
        if (!empty($response[0])) {
            $style_Suggestion_DTO = $response[0];
        }
    }
}
