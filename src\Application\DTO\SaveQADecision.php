<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\DTO;

class SaveQADecision extends AbstractCurationRequest
{
    protected const FIELD_BATCH_ID = 'batchId';
    protected const FIELD_SKUS = 'skus';
    protected const FIELD_EXCLUSION_REASON = 'exclusionReason';
    protected const FIELD_QA_STATUS = 'qaStatus';
    protected const FIELD_PRICE_TIER_OVERRIDE = 'priceTierOverride';
    protected const FIELD_FINAL_STYLE_ID = 'finalStyleID';
    protected const FIELD_FINAL_SUB_STYLE_ID = 'finalSubStyleID';
    protected const FIELD_FINAL_BRAND_ID = 'finalBrandMalID';
    protected const FIELD_REASON = 'reason';
    protected const FIELD_SUGGESTED_NOTES = 'suggestedNotes';
    protected const FIELD_SUGGESTED_REASON = 'suggestedReason';

    /**
     * @var string[]
     */
    protected array $skus;
    protected int $exclusionReason;
    protected int $finalBrandId;
    protected int $finalStyleId;
    protected int $finalSubStyleId;
    protected int $priceTierOverride;
    protected string $reason;
    protected int $qaStatus;
    private string $suggestedNotes;
    private int $suggestedReason;

    /**
     * @param int $batchId
     * @param string[] $skus
     * @param int $exclusionReason
     * @param int $qaStatus
     * @param int $finalBrandId
     * @param int $finalStyleId
     * @param int $finalSubStyleId
     * @param int $priceTier
     * @param string $reason
     * @param string $suggestedNotes
     * @param int $suggestedReason
     */
    public function __construct(
        int $batchId = 0,
        array $skus = [],
        int $exclusionReason = -1,
        int $qaStatus = -1,
        int $finalBrandId = 0,
        int $finalStyleId = 0,
        int $finalSubStyleId = 0,
        int $priceTier = 0,
        string $reason = '',
        string $suggestedNotes = '',
        int $suggestedReason = -1
    ) {
        parent::__construct($batchId);

        $this->skus = $skus;
        $this->exclusionReason = $exclusionReason;
        $this->qaStatus = $qaStatus;
        $this->finalBrandId = $finalBrandId;
        $this->finalStyleId = $finalStyleId;
        $this->finalSubStyleId = $finalSubStyleId;
        $this->priceTierOverride = $priceTier;
        $this->reason = $reason;
        $this->suggestedNotes = $suggestedNotes;
        $this->suggestedReason = $suggestedReason;
    }

    /**
     * @return string[]
     */
    public function getSkus(): array
    {
        return $this->skus;
    }

    /**
     * @return int
     */
    public function getExclusionReason(): int
    {
        return $this->exclusionReason;
    }

    /**
     * @return int
     */
    public function getFinalBrandId(): int
    {
        return $this->finalBrandId;
    }

    /**
     * @return int
     */
    public function getFinalStyleId(): int
    {
        return $this->finalStyleId;
    }

    /**
     * @return int
     */
    public function getFinalSubStyleId(): int
    {
        return $this->finalSubStyleId;
    }

    /**
     * @return int
     */
    public function getPriceTierOverride(): int
    {
        return $this->priceTierOverride;
    }

    /**
     * @return string
     */
    public function getReason(): string
    {
        return $this->reason;
    }


    /**
     * @return int
     */
    public function getQAStatus(): int
    {
        return $this->qaStatus;
    }

    /**
     * @return string
     */
    public function getSuggestedNotes(): string
    {
        return $this->suggestedNotes;
    }

    /**
     * @return int
     */
    public function getSuggestedReason(): int
    {
        return $this->suggestedReason;
    }

    /**
     * @param array $params
     * @return self
     */
    public static function fromArray(array $params): self
    {
        return new self(
            (int)($params[self::FIELD_BATCH_ID] ?? 0),
            $params[self::FIELD_SKUS] ?? [],
            (int)($params[self::FIELD_EXCLUSION_REASON] ?? -1),
            (int)($params[self::FIELD_QA_STATUS] ?? -1),
            (int)($params[self::FIELD_FINAL_BRAND_ID] ?? 0),
            (int)($params[self::FIELD_FINAL_STYLE_ID] ?? 0),
            (int)($params[self::FIELD_FINAL_SUB_STYLE_ID] ?? 0),
            (int)($params[self::FIELD_PRICE_TIER_OVERRIDE] ?? 0),
            (string)($params[self::FIELD_REASON] ?? ''),
            (string)($params[self::FIELD_SUGGESTED_NOTES] ?? ''),
            (int)($params[self::FIELD_SUGGESTED_REASON] ?? -1)
        );
    }
}
