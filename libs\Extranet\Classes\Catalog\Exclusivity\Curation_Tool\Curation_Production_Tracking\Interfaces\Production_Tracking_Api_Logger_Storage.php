<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces;

interface Production_Tracking_Api_Logger_Storage {

  /**
   * @param string $level       Level
   * @param string $message     Message
   * @param string $context     Context
   * @param string $application Application
   * @param int    $employeeId  Employee Id
   *
   * @return void
   */
  public function addLog(string $level, string $message, string $context, string $application, int $employeeId) : void;
}
