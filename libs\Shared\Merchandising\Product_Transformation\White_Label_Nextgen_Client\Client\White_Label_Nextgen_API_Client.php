<?php
declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client;

use Guz<PERSON><PERSON>ttp\Psr7\Request;
use Psr\Cache\InvalidArgumentException;
use Psr\Log\LoggerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestInterface;
use RuntimeException;
use WF\Shared\Environment;
use WF\Shared\Merchandising\Product_Transformation\Client_Authenticator_Service;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Base_Response;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Error_Response;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Response;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_SKU_Status;

use function json_decode;
use function json_encode;

class White_Label_Nextgen_API_Client {
  private const WL_NEXTGEN_API_DEV_URL         = 'https://kube-white-label-nextgen-api.service.intradsm1.sdeconsul.csnzoo.com';
  private const WL_NEXTGEN_API_PROD_URL        = 'https://kube-white-label-nextgen-api.service.intraiad1.consul.csnzoo.com';
  private const PWL_NEXTGEN_API_DEV_URL         = 'https://kube-curation-job-consumer.service.intradsm1.sdeconsul.csnzoo.com';
  private const PWL_NEXTGEN_API_PROD_URL        = 'https://kube-curation-job-consumer.service.intraiad1.consul.csnzoo.com';
  private const BASE_ENDPOINT_BATCHES          = '/admin/nextgen-white-label-batch-event/publish';
  private const BASE_ENDPOINT_SKUS             = '/v1/skus/';
  private const REQUEST_METHOD_GET             = 'GET';
  private const REQUEST_METHOD_POST            = 'POST';
  private const REQUEST_HEADER_ACCEPT          = 'Accept';
  private const REQUEST_HEADER_AUTHORIZATION   = 'Authorization';
  private const REQUEST_HEADER_CONTENT_TYPE    = 'Content-Type';
  private const REQUEST_HEADER_CACHE_CONTROL   = 'Cache-Control';
  private const REQUEST_CACHE_CONTROL_NO_CACHE = 'no-cache';
  private const REQUEST_CONTENT_TYPE           = 'application/json';
  private const RESPONSE_CODE_OK               = 200;
  private const RESPONSE_CODE_BAD_REQUEST      = 400;

  /**
   * @var ClientInterface
   */
  private $httpClient;

  /**
   * @var Client_Authenticator_Service
   */
  private Client_Authenticator_Service $clientAuthenticatorService;

  /**
   * @var LoggerInterface
   */
  private LoggerInterface $logger;

    /**
     * @var string
     */
    private string $baseUrl;

    /**
     * @var string
     */
    private string $baseUrlPWL;

  /**
   * Client constructor.
   *
   * @param ClientInterface $httpClient HTTP Client
   * @param Client_Authenticator_Service $clientAuthenticatorService Client Authenticator Service
   * @param LoggerInterface $logger Logger
   * @param string $environment ENV
   */
  public function __construct(
      ClientInterface $httpClient,
      Client_Authenticator_Service $clientAuthenticatorService,
      LoggerInterface $logger,
      string $environment
  ) {
      $this->httpClient = $httpClient;
      $this->clientAuthenticatorService = $clientAuthenticatorService;
      $this->logger = $logger;
      $this->baseUrl = $environment === Environment::PRODUCTION
          ? self::WL_NEXTGEN_API_PROD_URL
          : self::WL_NEXTGEN_API_DEV_URL;
      $this->baseUrlPWL = $environment === Environment::PRODUCTION
          ? self::PWL_NEXTGEN_API_PROD_URL
          : self::PWL_NEXTGEN_API_DEV_URL;
  }

  /**
   * @param array $sourceData source Data
   *
   * @return White_Label_SKU_Status[]
   * @throws ClientExceptionInterface
   * @throws InvalidArgumentException
   */
  public function getStatusBySkus(array $sourceData) : array { /** @phpstan-ignore-line */
    $request = $this->prepareRequestForSkuStatus($sourceData, $this->clientAuthenticatorService->getToken());
    $this->logger->info("Sending Request to White Label NextGen API: " . $request->getBody()->getContents());
    $httpResponse = $this->httpClient->sendRequest($request);

    if ($httpResponse->getStatusCode() !== self::RESPONSE_CODE_OK) {
      throw new RuntimeException(
          "The request was sent, but there was an error. Response Code: " . $httpResponse->getStatusCode() .
          " Reason: " . $httpResponse->getReasonPhrase()
      );
    }

    $content = json_decode($httpResponse->getBody()->getContents(), true);
    $this->logger->info("White Label NextGen API returned content : ", $content);

    return array_map(
        function ($status) {
          return new White_Label_SKU_Status(
              $status["sku"],
              $status["skuStatus"],
              $status["skuExcluded"],
              $status["batchCancelled"],
              $status["batchState"]
          );
        },
        $content ?? []
    );
  }

  /**
   * @param array  $sourceData Source Data
   * @param string $token      Token
   *
   * @return RequestInterface
   */
  private function prepareRequestForSkuStatus(array $sourceData, string $token) : RequestInterface {
    $data    = implode(',', $sourceData);
    $url     = sprintf('%s%s%s%s', $this->baseUrl, self::BASE_ENDPOINT_SKUS, $data, '/status');
    $headers = [
        self::REQUEST_HEADER_ACCEPT        => self::REQUEST_CONTENT_TYPE,
        self::REQUEST_HEADER_CONTENT_TYPE  => self::REQUEST_CONTENT_TYPE,
        self::REQUEST_HEADER_CACHE_CONTROL => self::REQUEST_CACHE_CONTROL_NO_CACHE,
        self::REQUEST_HEADER_AUTHORIZATION => 'Bearer ' . $token
    ];

    return new Request(self::REQUEST_METHOD_GET, $url, $headers);
  }

  /**
   * @param array $batch Batch data
   *
   * @return White_Label_Batch_Base_Response
   * @throws \Exception
   */
  public function createBatch(array $batch) : int {
      $response_code = self::RESPONSE_CODE_OK;
      $badResponseCode = self::RESPONSE_CODE_BAD_REQUEST;
    try {
      $request = $this->prepareRequestForBatchCreation($batch);
      $this->logger->info('Posting batch to white label: ' . json_encode($batch));

      $httpResponse = $this->httpClient->sendRequest($request);
        $responseBody  = json_decode($httpResponse->getBody()->getContents(), true);
        $this->logger->info("Got response from White Label NextGen API: " . json_encode($responseBody) . "for batch id: " . $batch['whiteLabelBatchId']);
        if ($httpResponse->getStatusCode() == self::RESPONSE_CODE_OK) {
        $this->logger->info('White label response: ' . json_encode($responseBody));
        return $response_code;
      }
        return $badResponseCode;

    } catch (InvalidArgumentException | ClientExceptionInterface $e) {
        $this->logger->error(
            'Exception happen during WL API call: ' . $e->getMessage(),
            [
                'class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trances' => $e->getTraceAsString(),
            ]
        );

      if (method_exists($e, 'getResponse')) {
        $response       = $e->getResponse();
        $responseBody   = json_decode($response->getBody()->getContents(), true);

        $errorCode     = $responseBody['errorCode'] ?? $e->getMessage();
        $errorMessages = $responseBody['errorMessages'] ?? [$e->getMessage()];
      } else {
        $errorCode     = $e->getMessage();
        $errorMessages = [$e->getMessage()];
      }

       return $badResponseCode;
    }
  }

  /**
   * @param array $batch The batch data to construct the request from
   *
   * @return RequestInterface
   * @throws InvalidArgumentException
   */
  private function prepareRequestForBatchCreation(array $batch) : RequestInterface { /** @phpstan-ignore-line */
    $token   = $this->clientAuthenticatorService->getToken();

    $url = sprintf('%s%s', $this->baseUrlPWL, self::BASE_ENDPOINT_BATCHES);

    $headers = [
        self::REQUEST_HEADER_ACCEPT        => self::REQUEST_CONTENT_TYPE,
        self::REQUEST_HEADER_CONTENT_TYPE  => self::REQUEST_CONTENT_TYPE,
        self::REQUEST_HEADER_CACHE_CONTROL => self::REQUEST_CACHE_CONTROL_NO_CACHE,
        self::REQUEST_HEADER_AUTHORIZATION => 'Bearer ' . $token
    ];

    return new Request(self::REQUEST_METHOD_POST, $url, $headers, json_encode($batch));
  }
}
