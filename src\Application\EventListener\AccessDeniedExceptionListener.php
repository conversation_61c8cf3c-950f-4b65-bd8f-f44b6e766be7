<?php

declare(strict_types=1);

namespace App\Application\EventListener;

use App\Application\Service\ViewRenderer;
use App\Application\View\AccessDeniedView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

class AccessDeniedExceptionListener
{
    private ViewRenderer $renderingService;

    public function __construct(ViewRenderer $renderingService)
    {
        $this->renderingService = $renderingService;
    }

    /**
     * @param ExceptionEvent $event
     * @return void
     */
    public function onKernelException(ExceptionEvent $event): void
    {
        $request = $event->getRequest();
        $exception = $event->getThrowable();

        if ($exception instanceof AuthenticationException && $event->isMainRequest()) {
            $event->setResponse(
                new Response(
                    $this->renderingService->render(
                        new AccessDeniedView($exception->getMessage()),
                        $request->query->get('webpack_public_path_root') ?? '/',
                        $request->server->get('DOCUMENT_ROOT')
                    ),
                    Response::HTTP_UNAUTHORIZED
                )
            );
        }
    }
}
