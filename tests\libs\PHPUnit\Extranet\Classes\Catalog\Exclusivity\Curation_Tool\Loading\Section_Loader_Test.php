<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU;
use WF\Shared\SLO\SLOFactory;

class Section_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
     */
    private $postgresql_dao;


    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader_Interface
     */
    private $itemGroupLoader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service
     */
    private $contextDataService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader
     */
    private $assortmentCurationDecisionLoader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper
     */
    private $kitParentGrouper;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader
     */
    private $imageLoader;

    /**
     * @var World_Region_EU
     */
    private $regionEU;

    /**
     * @var WF\Shared\SLO\SLOFactory
     */

    private SLOFactory $slo;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader
     */
    private $priceLoader;

    /**
     * @var \App\Domain\Service\Loading\CurationItemFactory
     */
    private $curationItemFactory;

    /**
     * @var \App\Infrastructure\Connection\Graphql\CurationGraphApi
     */
    private $curationGraphApi;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section|\Prophecy\Prophecy\ObjectProphecy
     */
    private $section;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->contextDataService = $this->prophesize(Context_Data_Service::class);
        $this->assortmentCurationDecisionLoader = $this->prophesize(Assortment_Curation_Decision_Loader::class);
        $this->imageLoader = $this->prophesize(Curation_Image_Loader::class);
        $this->itemGroupLoader = $this->prophesize(Curation_Item_Group_Loader_Interface::class);
        $this->kitParentGrouper = $this->prophesize(Kit_Parent_Grouper::class);
        $this->regionEU = $this->prophesize(World_Region_EU::class);
        $this->priceLoader = $this->prophesize(Curation_Price_Loader::class);
        $this->curationItemFactory = $this->prophesize(CurationItemFactory::class);
        $this->regionEU = $this->prophesize(World_Region_EU::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->curationGraphApi = $this->prophesize(CurationGraphApi::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->postgresql_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $this->section = $this->prophesize(Section::class);


        $this->subject = new Section_Loader(
            $this->dao->reveal(),
            $this->contextDataService->reveal(),
            $this->assortmentCurationDecisionLoader->reveal(),
            $this->imageLoader->reveal(),
            $this->itemGroupLoader->reveal(),
            $this->kitParentGrouper->reveal(),
            $this->regionEU->reveal(),
            $this->priceLoader->reveal(),
            $this->curationItemFactory->reveal(),
            $this->featureToggles->reveal(),
            $this->postgresql_dao->reveal(),
            $this->logger->reveal(),
            $this->prophesize(Section_Additional_Data_Loader::class)->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_sku_data_call_feature_toggle_off()
    {
        $batchId = 1;
        $skus = [
            'sku1',
            'sku2',
            'sku3',
            'sku4',
        ];
        $this->dao->get_sku_data(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_sku_data(Argument::cetera())->willReturn([]);
        $this->subject->get_sku_data_call($batchId, $skus);
    }


    /**
     * @test
     *
     * @return void
     */
    public function get_sku_data_call_feature_toggle_on()
    {
        $batchId = 1;
        $skus = [
            'sku1',
            'sku2',
            'sku3',
            'sku4',
        ];
        $this->dao->get_sku_data(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_sku_data(Argument::cetera())->willReturn([]);
        $this->subject->get_sku_data_call($batchId, $skus);
    }
}
