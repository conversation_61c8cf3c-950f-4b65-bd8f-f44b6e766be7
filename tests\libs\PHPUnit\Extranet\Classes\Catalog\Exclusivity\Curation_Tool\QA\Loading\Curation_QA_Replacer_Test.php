<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Loading;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Loading\Curation_QA_Replacer;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader;

class Curation_QA_Replacer_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader|\Prophecy\Prophecy\ObjectProphecy
     */
    private $curation_kit_children_loader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Loading\Curation_QA_Replacer
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->curation_kit_children_loader = $this->prophesize(Curation_Kit_Children_Loader::class);

        $this->subject = new Curation_QA_Replacer($this->curation_kit_children_loader->reveal());
    }

    /**
     * @test
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_curation_qa_kit_parent_replacer()
    {
        $rc = new ReflectionClass(Curation_QA_Replacer::class);

        $this->assertTrue($rc->implementsInterface(Curation_QA_Kit_Parent_Replacer::class));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_replaces_kits_with_children_without_skipping_kitsco()
    {
        $this->curation_kit_children_loader->replaceKitParents(Argument::cetera())->willReturn(['ChildSKU1', 'ChildSKU2']);

        $result = $this->subject->replace_kit_parents_with_children(123, ['ParentSKU1']);

        $this->assertEquals(['ChildSKU1', 'ChildSKU2'], $result);
        $this->curation_kit_children_loader->replaceKitParents(['ParentSKU1'], 123, false)->shouldHaveBeenCalled();
    }
}
