<?php

/**
 * Prettifies <PERSON><PERSON><PERSON>ailer
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

use PHPMailer\PHPMailer\PHPMailer;

class Mailer implements Mailer_Interface {
  /**
   * @var PHPMailer
   */
  private $mailer;

  /**
   * Mailer constructor.
   *
   * @param PHPMailer|null $mailer internal php mailer to send out emails
   */
  public function __construct(PHPMailer $mailer = null) {
    $this->mailer = $mailer ?? new PHPMailer();
  }

  /**
   * Creates new message which is accepted to send by current mailer
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function create_message() {
    return new Mail_Message();
  }

  /**
   * Sends out given mail message
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $message message to send
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Result
   */
  public function send(Mail_Message $message, $isSMTP = false)
  {
      $this->reset();

      list($email, $name) = $message->get_sender();
      $this->mailer->setFrom($email, $name);

      foreach ($message->get_recipients() as $recipient) {
          list($email, $name) = $recipient;
          $this->mailer->addAddress($email, $name);
      }

      $body = $message->get_html_body();
      if (mb_strlen($body) > 0) {
          $this->mailer->msgHTML($body);
      }

      $body = $message->get_text_body();
      if (mb_strlen($body) > 0) {
          $this->mailer->AltBody = $body;
      }

      $this->mailer->Subject = $message->get_subject();

      foreach ($message->get_attachments() as $attachment) {
          list($content, $filename, $mime, $encoding, $disposition) = $attachment;
          $this->mailer->addStringAttachment($content, $filename, $encoding, $mime, $disposition);
      }
      if ($isSMTP) {
          try {
              $this->phpMailerDebugOutput = '';
              $this->mailer->Debugoutput = function ($str, $level) {
                  $this->phpMailerDebugOutput .= "debug level $level; message: $str";
              };
              $this->mailer->SMTPDebug = 4;
              $this->mailer->isSMTP();
              $this->mailer->Host = 'smtp.corp.wayfair.com';
              $this->mailer->Port = 25;
              $this->mailer->SMTPAuth = false;
              $this->mailer->SMTPOptions = array(
                  'ssl' => array(
                      'verify_peer' => false,
                      'verify_peer_name' => false,
                      'allow_self_signed' => true
                  ),
                  'tls' => array(
                      'verify_peer' => false,
                      'verify_peer_name' => false,
                      'allow_self_signed' => true
                  ),
                  'none' => array(
                      'verify_peer' => false,
                      'verify_peer_name' => false,
                      'allow_self_signed' => true
                  )
              );


              //$send_result = $this->mailer->send();

              //return new Mail_Result($send_result, $this->mailer->ErrorInfo);
              return $this->mailer->send();
          } catch (\phpmailerException $e) {
              Logger::use_logger(Logger_Names::DRAY_CARRIER_EMAIL_SERVICE)->error($e->getMessage());
              throw new Reportable_Exception(
                  'CouldNotSendDrayCarrierEmails',
                  'Could Not Send Dray Carrier Emails',
                  [],
                  $e->getMessage()
              );
          }
      }
  }
  /**
   * Due to specific implementation, internal mailer has to be reset or recreated on each send attempt
   *
   * @return void
   */
  private function reset() {
    $this->mailer->clearAllRecipients();
    $this->mailer->clearAttachments();
    $this->mailer->clearCustomHeaders();
    $this->mailer->clearReplyTos();
    $this->mailer->ErrorInfo = '';
  }
}
