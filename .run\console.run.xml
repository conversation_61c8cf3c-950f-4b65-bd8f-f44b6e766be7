<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="console" type="PhpLocalRunConfigurationType" factoryName="PHP Console" path="$PROJECT_DIR$/bin/console" scriptParameters="debug:config security">
    <CommandLine workingDirectory="$PROJECT_DIR$">
      <PhpTestInterpreterSettings>
        <option name="interpreterName" value="PHP 7.4" />
      </PhpTestInterpreterSettings>
      <envs>
        <env name="APP_ENV" value="dev" />
        <env name="APP_SECRET" value="1" />
        <env name="DB_PASSWORD_SECRET_NAME" value="csn_extranet" />
        <env name="DB_USER" value="csn_extranet" />
        <env name="DD_AGENT_HOST" value="apmproxy.query.sdeconsul.csnzoo.com" />
        <env name="DD_SERVICE_NAME" value="brand-workflows-curation-tool" />
        <env name="WF_ENV" value="dev" />
        <env name="WF_INI_PATH" value="./docker/local/wayfair/etc/wf-config.ini" />
        <env name="WF_SECRET_CREDENTIALS_PATH" value="./docker/local/wayfair/etc/priv/credentials" />
        <env name="WF_SECRET_KEY_PATH" value="./docker/local/wayfair/etc/priv/dek/credentials.aes128" />
      </envs>
    </CommandLine>
    <method v="2" />
  </configuration>
</component>