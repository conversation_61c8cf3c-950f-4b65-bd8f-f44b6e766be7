<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */


namespace WF\Tests\PHPUnit\Extranet\Classes\Curation\Batch_Data;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use StdClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Batch_Data\Get_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;

class Get_Batch_Data_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
     */
    private $dao;

    /**
     * @var Get_Batch_Data_Service
     */
    private $subject;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Completion_Batch_Data_Storage::class);
        $this->itemLoader = $this->prophesize(Section_Loader_Interface::class);
        $this->dao_psql = $this->prophesize(Batch_Management_Postgres_DAO::class);
        $this->supplier_data_loader = $this->prophesize(Supplier_Data_Loader::class);
        $this->sectionAdditionalDataLoader = $this->prophesize(Section_Additional_Data_Loader::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Get_Batch_Data_Service(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->itemLoader->reveal(),
            $this->supplier_data_loader->reveal(),
            $this->sectionAdditionalDataLoader->reveal(),
            $this->featureToggles->reveal(),
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_empty_array()
    {
        $emp_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch_list($emp_id)->willReturn([])->shouldBeCalled();
        $actualResult = $this->subject->get_batch_list($emp_id);
        $this->assertEquals([], $actualResult);
    }

    /**
     * @test
     * @dataProvider get_it_batch_data_array_data
     * @return void
     */
    public function it_returns_expected_array_feature_toggle_on(array $batch_data)
    {
        $emp_id = 0;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_batch_list($emp_id)->willReturn($batch_data)->shouldBeCalled();
        $actualResult = $this->subject->get_batch_list($emp_id);
        $this->assertEquals($batch_data, $actualResult);
    }

    /**
    * @test
    * @dataProvider get_it_batch_data_array_data
    * @return void
    */
    public function it_returns_expected_array_feature_toggle_off(array $batch_data)
    {
        $emp_id = 0;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch_list($emp_id)->willReturn($batch_data)->shouldBeCalled();
        $actualResult = $this->subject->get_batch_list($emp_id);
        $this->assertEquals($batch_data, $actualResult);
    }


    /**
    * @test
    * @dataProvider get_batch_data
    * @return void
    */
    public function it_returns_expected_final_array(Object $employeeData)
    {
        $emp_id = 1;
        $batch_id = 1;
        $batch_data_list = $this->subject->get_batch_list($emp_id)->willReturn()->shouldBeCalled();
        $actualResult = $this->subject->batch_list_filter($batch_data_list, $batch_id);
        $this->assertEquals($employeeData, $actualResult);
    }


    /**
    * @test
    * @return void
    */
    public function it_returns_expected_final_empty_array()
    {
        $emp_id = 0;
        $batch_id = 1;
        $batch_data_list = [];
        $actualResult = $this->subject->batch_list_filter($batch_data_list, $batch_id);
        $this->assertEquals([], $actualResult);
    }

    /**
    * @test
    * @return void
    */
    public function it_returns_expected_final_batch_empty_array()
    {
        $emp_id = 0;
        $batch_id = 0;
        $actualResult = $this->subject->get_batch_list_call($emp_id, $batch_id);
        $this->assertEquals(['reason' => 'Bad request. Try to update your page!'], $actualResult);
    }

    /**
    * @test
    * @dataProvider get_batch_data
    * @return void
    */
    public function it_returns_expected_batch_final_array(Object $batchData)
    {
        $emp_id = 1;
        $batch_id = 1;
        $batch_data_list = $this->subject->get_batch_list($emp_id)->willReturn()->shouldBeCalled();
        $actualResult = $this->subject->get_batch_list_call($emp_id, $batch_id);
        $this->assertEquals($batchData, $actualResult);
    }


    /**
    * @test
    * @dataProvider get_it_batch_data_array_data
    * @return void
    */
    public function test_batch_final_empty_array(array $batch_data)
    {
        $emp_id = 0;
        $batch_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch_list($emp_id)->willReturn([])->shouldBeCalled();
        $actualResult = $this->subject->get_batch_list_call($emp_id, $batch_id);
        $this->assertEquals(['result' => []], $actualResult);
    }


    /**
     * @return array
     */
    public function get_it_batch_data_array_data(): array
    {
        return [
            [
                [ 'batch_id' => 123214,
                    'AssignedEmID' => 1,
                    'StatusID' => 5,
                ], [
                    'batch_id' => 123215,
                    'AssignedEmID' => 1,
                    'StatusID' => 5,
                ],
            ]
        ];
    }

    /**
    * @test
    * @dataProvider get_batch_data
    * @return void
    */
    public function test_batch_final_array(Object $batchData)
    {
        $emp_id = 1;
        $batch_id = 1;
        $batch_data_list = $this->subject->get_batch_list($emp_id)->willReturn()->shouldBeCalled();
        $this->subject->batch_list_filter($batch_data_list, $batch_id);
        $actualResult = $this->subject->get_batch_list_call($emp_id, $batch_id);
        $this->assertEquals(['result' => $batchData], $actualResult);
    }

    /**
    * @return array
    */
    public function get_batch_data_up(): array
    {
        $batchData = [
            'batch_id' => 123214,
            'AssignedEmID' => 1,
            'StatusID' => 1,
        ];
        return $batchData;
    }
    /**
    * @return object
    */
    public function get_batch_data(): object
    {
        $batchData = new StdClass();
        $batchData->content = [
            (object) [
                'batch_id' => 123214,
                'AssignedEmID' => 1,
                'StatusID' => 1,
            ]
        ];
        return $batchData;
    }
}
