<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Infrastructure;

use Psr\Http\Message\RequestFactoryInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\StreamFactoryInterface;
use WF\BrandWorkflows\PuREST\Contract\RequestFactoryInterface as PuRESTRequestFactoryInterface;
use WF\BrandWorkflows\PuREST\Contract\UrlBuilderInterface;

class RequestFactory implements PuRESTRequestFactoryInterface
{
    private const HTTP_METHOD_POST = 'POST';

    private const CONTENT_TYPE_JSON_HEADER = 'application/json';
    private const HEADER_CONTENT_TYPE = 'Content-Type';

    private UrlBuilderInterface $urlBuilder;
    private RequestFactoryInterface $requestFactory;
    private StreamFactoryInterface $streamFactory;

    /**
     * @param UrlBuilderInterface $urlBuilder
     * @param RequestFactoryInterface $requestFactory
     * @param StreamFactoryInterface $streamFactory
     */
    public function __construct(
        UrlBuilderInterface $urlBuilder,
        RequestFactoryInterface $requestFactory,
        StreamFactoryInterface $streamFactory
    ) {
        $this->urlBuilder = $urlBuilder;
        $this->requestFactory = $requestFactory;
        $this->streamFactory = $streamFactory;
    }

    /**
     * @param string $service PuREST service name
     * @param string $resource PuREST resource name
     * @param string $body Request body
     * @param string[] $headers HTTP Headers
     *
     * @return RequestInterface
     * @throws Exception\PuRESTUrlBuilderException
     */
    public function createRequest(
        string $service,
        string $resource,
        string $body,
        array $headers = []
    ): RequestInterface {
        $url = $this->urlBuilder->buildUrl($service, $resource);

        $request = $this->requestFactory->createRequest(self::HTTP_METHOD_POST, $url);
        $request = $request->withBody($this->streamFactory->createStream($body));
        $request = $request->withHeader(self::HEADER_CONTENT_TYPE, self::CONTENT_TYPE_JSON_HEADER);

        foreach ($headers as $name => $value) {
            $request = $request->withAddedHeader($name, $value);
        }

        return $request;
    }
}
