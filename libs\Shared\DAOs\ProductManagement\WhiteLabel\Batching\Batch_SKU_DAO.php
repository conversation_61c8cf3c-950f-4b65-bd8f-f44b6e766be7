<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\Connection\DatabaseConstantsInterface;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use PDO;

class Batch_SKU_DAO {
  const CURATION_PROCESSING_STATUS_ID = 0;

  private ProductConnection $pdo;

  /**
   * Batch_SKU_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * @param array $skus list of SKUs to load
   *
   * @return array
   */
  public function load_list(array $skus) : array {
    $sql = '
      SELECT
        crs.SKU AS sku,
        cr.SuID AS supplier_id,
        cr.BrandCatalogID AS brand_catalog_id,
        IIF(kit.InKit = 1, 1, 0) AS in_kit,
        IIF(p.PrXnID <> 0, 1, 0) AS in_collection
      FROM csn_product.dbo.tblCurationRequestSKU AS crs WITH (NOLOCK)
        JOIN csn_product.dbo.tblCurationRequest AS cr WITH (NOLOCK) ON cr.ID = crs.RequestID
        JOIN csn_product.dbo.tblProduct AS p WITH (NOLOCK) ON p.PrSKU = crs.SKU
      OUTER APPLY (
        SELECT TOP 1 1 AS InKit
        FROM csn_product.dbo.vwExclusivityKitComposition AS kc WITH (NOLOCK)
        WHERE kc.ChildSKU = crs.SKU
      ) kit
      WHERE crs.SKU IN (' . $this->pdo->paramsForList(count($skus), 'sku',  SQL::nvarchar(8)) . ')
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));


    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get a new SKU for curation - ' . implode(';', $statement->errorInfo()));
    }

    //PDO::FETCH_UNIQUE makes key to be PrSKU
    //PDO::FETCH_GROUP makes value be single entry instead of array in this case
    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch to save
   *
   * @return int
   */
  public function save_batch(Batch $batch) : int {
    $sql = '
      SET NOCOUNT ON
      DECLARE @Output TABLE(ID INT NOT NULL)
      INSERT INTO csn_product.dbo.tblCurationBatch (
        CurationRequestTypeID,
        SuID,
        BrandCatalogID,
        StatusID,
        CurationBatchProcessTypeID,
        CreatedAt,
        SentWLAt
      )
      OUTPUT inserted.ID
      INTO @Output
      VALUES (
        :curation_request_type,
        :supplier_id,
        :brand_catalog_id,
        :status_id,
        :curation_batch_process_type,
        SYSDATETIMEOFFSET(),
        NULL
      )

      SELECT ID FROM @Output
    ';

    $hints = [
      'curation_request_type'       => 'int',
      'supplier_id'                 => 'int',
      'brand_catalog_id'            => 'int',
      'status_id'                   => 'int',
      'curation_batch_process_type' => 'int'
    ];

    $statement = $this->pdo->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);
    $statement->bindValue('curation_request_type', $batch->get_curation_request_type(), PDO::PARAM_INT);
    $statement->bindValue('supplier_id', $batch->get_supplier_id(), PDO::PARAM_INT);
    $statement->bindValue('brand_catalog_id', $batch->get_brand_catalog_id(), PDO::PARAM_INT);
    $statement->bindValue('status_id', self::CURATION_PROCESSING_STATUS_ID, PDO::PARAM_INT);
    $statement->bindValue('curation_batch_process_type', $batch->get_process_type(), PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot insert batch - ' . implode(';', $statement->errorInfo()));
    }

    $batch_info = $statement->fetchAll(PDO::FETCH_COLUMN, 0);

    return $batch_info[0] ?? 0;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch
   *
   * @return bool
   */
  public function cleanup_batch(Batch $batch) {
    $sql = '
      DELETE cb
      FROM csn_product.dbo.tblCurationBatch AS cb
      WHERE
        ID = :id
        AND NOT EXISTS(
          SELECT TOP 1 1 FROM csn_product.dbo.tblVerificationItem AS vi WITH (NOLOCK) WHERE vi.ViBatchID = cb.ID
        )

      SELECT @@ROWCOUNT AS row_count
    ';

    $hints = [':id' => 'int'];
    $statement = $this->pdo->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);
    $statement->bindValue('id', $batch->get_id(), PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot cleanup batch - ' . implode(';', $statement->errorInfo()));
    }

    $batch_info = $statement->fetchAll(PDO::FETCH_COLUMN, 0);

    return ((int)$batch_info[0]) > 0;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch object
   *
   * @return array
   */
  public function get_batch_skus(Batch $batch) : array {
    $sql = '
      SELECT vi.ViID  AS id,
             vi.ViSKU AS sku,
             vi.ViFinalBrandMaID AS target_ma_id,
             IIF(vi.ViExcludedReasonID > 0, 1, 0) AS is_excluded_from_wl,
             vi.ViIsKitsco AS is_kitsco,
             CONVERT(BIT, COALESCE(kit_info.is_kit_component, 0)) AS is_kit_component,
             vi.ViSourceId AS source_id,
             p.PrXnID AS collection_id,
             p.PrStatus AS status
      FROM csn_product.dbo.tblVerificationItem vi WITH(NOLOCK)
      JOIN csn_product.dbo.tblProduct p WITH(NOLOCK) ON p.PrSKU = vi.ViSKU
      OUTER APPLY (
        SELECT TOP 1 1 AS is_kit_component FROM csn_product.dbo.vwExclusivityKitCompositionActive kca WITH (NOLOCK)
        WHERE kca.ChildSKU = vi.ViSKU
      ) kit_info
      WHERE ViBatchID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch->get_id(), PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Cannot fetch batch skus - %s', implode(';', $statement->errorInfo())));
    }

    return $statement->fetchAll();
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $child_batch_sku Child sku object
   *
   * @return array
   */
  public function get_kit_parent_skus(Verified_Batch_SKU $child_batch_sku) : array {
    $sql = '
      SELECT kc.ParentSKU AS sku
      FROM csn_product.dbo.vwExclusivityKitCompositionActive AS kc WITH (NOLOCK)
      WHERE kc.ChildSKU = :child_sku
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':child_sku', $child_batch_sku->get_sku(), PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Cannot fetch parent kit skus - %s', implode(';', $statement->errorInfo())));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }
}
