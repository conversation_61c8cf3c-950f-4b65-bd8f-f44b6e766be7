<?php
/**
 * Model representing a single item in the batch verification pipeline.
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

class Batch_Evaluation_Item_Model {

  /**
   * @var int
   */
  private $id;

  /**
   * SKU
   *
   * @var string
   */
  private $sku;

  /**
   * QA Status ID - possible values in the pick list tblplVerificationItemQAStatus
   *
   * @var int
   */
  private $qa_status_id;

  /**
   * Batch_Evaluation_Item_Model constructor.
   *
   * @param int    $id           id
   * @param string $sku          sku
   * @param int    $qa_status_id qa status id
   */
  public function __construct(int $id, string $sku, int $qa_status_id) {
    $this->id           = $id;
    $this->sku          = $sku;
    $this->qa_status_id = $qa_status_id;
  }

  /**
   * @return int
   */
  public function get_id() : int {
    return $this->id;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return int
   */
  public function get_qa_status_id() : int {
    return $this->qa_status_id;
  }
}
