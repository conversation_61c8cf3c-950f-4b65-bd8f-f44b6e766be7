<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;

interface ApiProductContextCollectionNextgen {

  /**
   * @param string ...$skus SKUs
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function findProductContextCollection(string ...$skus) : array;

  /**
   * @param string[] $skus list of SKUS to get collisions
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function findSkuCollisions(array $skus) : array;
}
