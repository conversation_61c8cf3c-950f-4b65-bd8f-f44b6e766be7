<?php
/**
 * Model representing a Curation Request.
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

class Curation_Request_Model {

  public const STATUS_AWAITING = 0;
  public const STATUS_PARTIAL  = 1;
  public const STATUS_COMPLETE = 2;

  public const TYPE_PRODUCT_ADDITION       = 0;
  public const TYPE_AMT_OR_CURATION_LOADER = 1; //AKA so-called "Curation Loader"
  public const TYPE_NEST                   = 4; //AKA Non-EB Style Tagging

  /**
   * @var int
   */
  public $id;

  /**
   * @var int
   */
  public $import_request_id;

  /**
   * @var int
   */
  public $type_id = self::TYPE_PRODUCT_ADDITION;

  /**
   * @var int
   */
  public $quickform_id;

  /**
   * @var int
   */
  public $brand_catalog_id;

  /**
   * @var int|null
   */
  public $employee_id;

  /**
   * @var int
   */
  public $status_id;

  /**
   * @var int|null Manual Curation Type ID
   */
  public $manual_type_id;

  /**
   * @var int|null
   */
  public $supplier_id;

  /**
   * @var \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model[]
   */
  private $sku_list = [];

  /**
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $sku_model Curation_Request_SKU_Base_Model $sku_model Curation_Request_SKU_Base_Model
   *
   * @return void
   */
  public function add_sku(Curation_Request_SKU_Base_Model $sku_model) {
    $this->sku_list[] = $sku_model;
  }

  /**
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model[]
   */
  public function get_sku_list() : array {
    return $this->sku_list;
  }

  /**
   * @return int
   */
  public function get_id() : int {
    return (int)$this->id; /** @phpstan-ignore-line */
  }

  /**
   * @return int
   */
  public function get_status_id() : int {
    return (int)$this->status_id; /** @phpstan-ignore-line */
  }

  /**
   * @return int|null
   */
  public function get_quickform_id() {
    return $this->quickform_id;
  }

  /**
   * @return int
   */
  public function get_type_id() : int {
    return $this->type_id;
  }

  /**
   * @return int
   */
  public function get_brand_catalog_id() : int {
    return $this->brand_catalog_id;
  }

  /**
   * @return int|null
   */
  public function get_employee_id() : ?int {
    return $this->employee_id;
  }
}
