<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Predicted_Winner\Predicted_Winner_Info_SKU_DTO;

interface Api_Predicted_Winner {

  /**
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  public function get_predicted_winners() : array;

  /**
   * @param array $skus list of SKUS to check if they are predicted winners or not
   *
   * @return Predicted_Winner_Info_SKU_DTO[]
   * @throws API_Request_Exception
   */
  public function find_predicted_winners(array $skus) : array;
}
