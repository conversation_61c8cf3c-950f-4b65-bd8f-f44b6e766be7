notify:
  - slack: "#merch-eng-brand-workflows-pipeline-alerts"

.docker-plugin: &docker-plugin
  plugins:
    - ssh://**************/wayfair-secure/docker-buildkite-plugin#v3.2.11:
        image: "wayfair/php-cli:7.4.27-2022.08.04"
        mount-buildkite-agent: true
        propagate-environment: true
  agents:
    queue: docker

#.docker-compose-plugin: &docker-compose-plugin
#  plugins:
#    - ssh://**************/wayfair-secure/docker-compose-buildkite-plugin.git#v3.7.3:
#        config: docker-compose.yaml
#        run: devbox
#        volumes:
#          - "/usr/bin/buildkite-agent:/usr/bin/buildkite-agent"
#        env:
#          - BUILDKITE_JOB_ID
#          - BUILDKITE_AGENT_ACCESS_TOKEN
#  agents:
#    queue: docker

steps:

  - label: ":k8s: + :helm: :lint-roller: He<PERSON> linting"
    plugins:
      - ssh://**************/wayfair-shared/k8s-unit-test-buildkite-plugin#0.3.0:
          path: "./k8s.yaml"
    agents:
      queue: docker

  - label: Add matrix steps
    command: echo "Adding matrix steps"
    plugins:
      - ssh://**************/wayfair-shared/docker-compose-matrix-plugin#v0.1.0:
          template: .buildkite/test-different-php-versions.yaml
          config: docker-compose.yaml
          services:
            - devbox
            - devbox81
          labels:
            - "PHP 7.4"
            - "PHP 8.1"

  - label: "PHPUnit Coverage Report"
    <<: *docker-plugin
    commands:
      - wf-install-extension pdo pdo-sqlsrv  pdo-pgsql xdebug && echo "xdebug.mode=coverage" >> /etc/php.d/xdebug.ini
      - wf-install-composer
      - echo "--- Composer Install"
      - composer install -n
      - echo "+++ PHPUnit"
      - composer phpunit --no-interaction
      - sed -i "s/\/app\///g" phpunit.coverage.xml
      - sed -i "s/\/app\///g" phpunit.report.xml
      - buildkite-agent artifact upload phpunit.coverage.xml
      - buildkite-agent artifact upload phpunit.report.xml

  - label: "Coding standard checks"
    <<: *docker-plugin
    commands:
      - wf-install-extension pdo-sqlsrv pdo-pgsql
      - wf-install-composer
      - echo "--- Composer Install"
      - composer install --no-interaction
      - echo "+++ ECS"
      - composer ecs --no-interaction

  - label: "PHPStan"
    <<: *docker-plugin
    commands:
      - wf-install-extension pdo-sqlsrv pdo-pgsql
      - wf-install-composer
      - echo "--- Composer Install"
      - composer install --no-interaction
      - echo "+++ PHPStan"
      - composer phpstan --no-interaction
    soft_fail:
      - exit_status: 1

  - label: "Mutational testing"
    <<: *docker-plugin
    commands:
      - wf-install-extension pdo pdo-sqlsrv  pdo-pgsql xdebug && echo "xdebug.mode=coverage" >> /etc/php.d/xdebug.ini
      - wf-install-composer
      - echo "--- Composer Install"
      - composer install -n
      - echo "+++ Mutation Test"
      - composer tests-mutation --no-interaction

#  - label: "Build JS Assets"
#    command: echo "Building JS Assets"
#    plugins:
#      - ssh://**************/wayfair-secure/docker-compose-buildkite-plugin.git#v3.7.3:
#          config: docker-compose.yaml
#          run: assets
#    agents:
#      queue: docker

  - wait: ~

  - label: ":sonarqube: SonarQube"
    plugins:
      - ssh://**************/wayfair-shared/sonarscanner-buildkite-plugin.git#v1.4.5:
          artifacts:
            - phpunit.coverage.xml
            - phpunit.report.xml
          sonarqube_host: https://sonarqube-enterprise.service.csnzoo.com/
          project_key: brand-workflows-curation-tool
          sources: ./
          enable_branch_scan: true
          branch_scan_target: main
          enable_pull_request_scan: true
          additional_flags:
            - -Dsonar.php.tests.reportPath=phpunit.report.xml
            - -Dsonar.php.coverage.reportPaths=phpunit.coverage.xml
            - -Dsonar.tests=tests/
            - -Dsonar.exclusions=tests/**/*,resources/**/*
            - >
              -Dsonar.coverage.exclusions=
              src/Kernel.php
              ,src/Application/Controller/InternalController.php
              ,src/Infrastructure/Connection/Driver/PDOConnectionPgql.php
              ,src/Infrastructure/Helper/PostgresBulkHelper.php
              ,src/Infrastructure/Connection/Driver/ConnectionPsqlProperties.php
              ,src/Application/DTO/*
              ,src/Application/Exception/*
              ,src/Infrastructure/Connection/QueryFailedException.php
              ,**/*DAO.php
              ,**/*Client.php
              ,libs/Extranet/Classes/Catalog/Exclusivity/Curation_Tool/Loading/Rebrand_Project.php
              ,libs/Shared/Classes/ProductManagement/Curation/Batch_Downstream/Reevaluation/Reevaluator.php
            - -Dsonar.pullrequest.github.repository=shared/brand-workflows-curation-tool
    agents:
      queue: sonarqube
    soft_fail:
      - exit_status: "*"

#  - label: "Build docs :notebook:"
#    plugins:
#      - ssh://**************/wayfair-shared/mkdocs-buildkite-plugin#v0.20.0: ~

  - label: ":docker: build"
    agents:
      queue: docker_prod
    plugins:
      - ssh://**************/wayfair-secure/docker-builder-buildkite-plugin#v5:
          service_name: prod
          compose_config: docker-compose.yaml
          ignore_existing_images_error: true

  - wait: ~

  - input: ":shipit: Deploy to DEV"
    key: dev-deploy

  - label: ":rocket: Deploy to Development"
    depends_on: dev-deploy
    plugins:
      - ssh://**************/wayfair-secure/deploy-buildkite-plugin#v4:
          environment: "dev"
    agents:
      queue: docker_deploy

  - wait: ~

  - input: ":shipit: Deploy to PROD"
    key: prod-deploy
    branches: "main"

  - label: ":rocket: Deploy to Production"
    depends_on: prod-deploy
    branches: "main"
    plugins:
      - ssh://**************/wayfair-secure/deploy-buildkite-plugin#v4:
          environment: "prod"
    agents:
      queue: docker_deploy
    concurrency: 1
    concurrency_group: 'shared/brand-workflows-curation-tool/deploy-prod'
