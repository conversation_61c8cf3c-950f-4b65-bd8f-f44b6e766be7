<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Collider;

use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO;

class Curation_Collection_Collider {
  /**
   * @var \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO
   */
  private $dao;

  /**
   * Curation_Collection_Collider constructor.
   *
   * @param \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO $dao Collection_Items_DAO
   */
  public function __construct(Curation_Collider_DAO $dao) {
    $this->dao = $dao;
  }

  /**
   * @param array $collection_ids Collection IDs
   * @param array $exclude_skus   Exclude SKUs
   *
   * @return array
   */
  public function get_all_collisions_collections(array $collection_ids, array $exclude_skus) : array {
    return $this->dao->getAllCollectionCollisions($collection_ids, $exclude_skus);
  }

  /**
   * @param int   $collection_id Collection ID
   * @param array $exclude_skus  Exclude SKUs
   *
   * @return array
   */
  public function get_collisions_for_collection(int $collection_id, array $exclude_skus) : array {
    return $this->dao->getCollectionCollisions($collection_id, $exclude_skus);
  }

  /**
   * @param int[] $collection_ids Collection IDs
   * @param array $exclude_skus   Exclude SKUs
   *
   * @return array
   */
  public function get_collisions_for_collections(array $collection_ids, array $exclude_skus) : array {
    $skus = [];

    foreach ($collection_ids as $id) {
      $skus = array_merge($skus, $this->dao->getCollectionCollisions($id, $exclude_skus));
    }

    return array_values(array_unique($skus));
  }
}
