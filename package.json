{"name": "@wayfair/curation-tool", "private": true, "version": "0.0.1", "dependencies": {"@fortawesome/fontawesome-common-types": "^0.3.0", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^5.12", "@fortawesome/free-solid-svg-icons": "^5.12", "@homebase/common": "^3.6.0", "@homebase/core": "^6.0.3", "@homebase/icons": "^2.9.0", "@homebase/provider": "^3.5.0", "@loadable/component": "^5.0.0", "@loadable/server": "^5.0.0", "@ungap/url-search-params": "^0", "@wayfair/bootstrap": "^3.2.1", "@wayfair/cookie": "^7.0.0", "@wayfair/core-library-bundle": "^3.1.0", "@wayfair/extranet-layout": "4.0.4", "@wayfair/framework-request-utils": "^2.0.6", "@wayfair/global-patterns": "^0", "@wayfair/homebase-admin": "^2.16.3", "@wayfair/homebase-common": "^2", "@wayfair/homebase-design-tokens": "^3.2.0", "@wayfair/homebase-enterprise-reset": "^1.2.0", "@wayfair/homebase-extranet": "^2.9.7", "@wayfair/homebase-provider": "^2.1.0", "@wayfair/image-url-utils": "^3.0.2", "@wayfair/lnrs": "^3.0.5", "@wayfair/logger": "^7", "@wayfair/logger-transport-wretch": "^0", "@wayfair/partners-analytics": "^0", "@wayfair/partners-auth": "^0", "@wayfair/performance-stats": "^3.0.0", "@wayfair/performance-utils": "^4.0.0", "@wayfair/scheduler": "^2.0.0", "@wayfair/string-utils": "^2", "@wayfair/tracker": "^3.0.0", "@wayfair/tracking-scribe": "^1.0.0", "@wayfair/translation": "^3.0.4", "@wayfair/wretch": "5.0.3", "bowser": "^2.9.0", "classnames": "^2.2.6", "commander": "^6.0.0", "debounce": "^1.2.0", "domready": "^1.0.0", "formik": "^2.2.6", "gettext-parser": "2.0.0", "gettext-to-messageformat": "0.3.1", "graphql": "^15.3.0", "graphql-tag": "^2.10.1", "json-loader": "0.5.7", "miragejs": "^0.1.37", "moment": "^2.29.2", "po-loader": "0.7.0", "po2json": "1.0.0-beta-3", "prop-types": "^15.7.2", "react": "^16.14.0", "react-dom": "16.13.1", "react-error-boundary": "1.2.5", "react-intl": "^3.11", "react-is": "^16.13.1", "react-router-dom": "^4.3.0", "styled-components": "^5.2", "typescript": "3.7.5", "underscore": "^1.13.0", "web-vitals": "^1.0.0", "wretch": "1.1.1"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/plugin-transform-runtime": "7.8.3", "@babel/runtime": "^7.10.5", "@wayfair/app": "^8.14.8", "@wayfair/eslint-plugin-wf-rules": "^3.0.1", "@wayfair/homebase-codemods": "^1.6.0", "babel-core": "6.26.3", "babel-plugin-module-resolver": "^4.0.0", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.2", "eslint": "6.8.0", "eslint-import-resolver-babel-module": "^5.2.0", "eslint-plugin-import": "^2.22.1", "prettier": "^2.0.5"}, "scripts": {"build!": "wayfair-workspaces build", "watch!": "wayfair-workspaces build --watch", "build": "wayfair-app build --production", "watch": "wayfair-app build --watch --defaultTranslations --localhostServer", "builddev": "wayfair-app build --defaultTranslations --localhostServer", "test": "wayfair-app test", "format": "wayfair-app format"}, "wayfairApp": {"type": "application", "localhostMountPath": "public/d/curation-tool/bundles", "brands": {"extranet": {"locales": ["en-US", "en-GB", "de-DE"]}}}, "resolutions": {"**/mkdirp-promise/mkdirp": "0.5.1", "conventional-changelog-angular": "5.0.3", "conventional-commits-filter": "2.0.2", "conventional-changelog-preset-loader": "2.1.1", "conventional-commits-parser": "3.0.3", "conventional-recommended-bump": "5.0.0", "conventional-changelog-writer": "4.0.6", "@babel/traverse": "7.23.2"}}