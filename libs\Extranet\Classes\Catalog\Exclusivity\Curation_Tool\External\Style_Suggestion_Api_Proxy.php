<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Curation_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Curation_Timeout_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Substyle_Suggestion_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\SKU_Style_Suggestion;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\SKU_Substyle_Suggestion;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Style_Suggestion;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Substyle_Suggestion;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Style_Suggestion_Storage;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Curation\Api\Api_Style_Suggestion;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\SKU_Style_Suggestion_DTO;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\Style_Suggestion as Style_Suggestion_DTO;

class Style_Suggestion_Api_Proxy implements Style_Suggestion_Storage {
  use Logging_Trait;

  /**
   * @var Api_Style_Suggestion
   */
  private Api_Style_Suggestion $api;

  /**
   * @var int
   */
  private int $suggestion_limit_per_sku;

  /**
   * Style_Suggestion_Api_Proxy constructor.
   *
   * @param Api_Style_Suggestion $style_suggestion_api             Curation API
   * @param int          $suggestion_limit_per_sku Number of suggestions per sku
   * @param LoggerInterface|null  $logger                   Logger
   */
  public function __construct(Api_Style_Suggestion $style_suggestion_api, int $suggestion_limit_per_sku, ?LoggerInterface $logger = null) {
    $this->api                      = $style_suggestion_api;
    $this->suggestion_limit_per_sku = $suggestion_limit_per_sku;
    $this->logger                   = $logger;
  }

  /**
   * @param string[] $skus SKU list
   *
   * @return Automatic_Style_Suggestion_Collection
   * @throws Automatic_Style_Curation_Exception
   */
  public function get_style_suggestion(array $skus) : Automatic_Style_Suggestion_Collection {
    try {
      $this->info('Calling Style Suggestion API for SKUS', ['skus' => $skus]);
      $style_suggestions = $this->api->get_style_suggestion($skus, $this->suggestion_limit_per_sku);

      return $this->get_style_suggestion_collection($style_suggestions);
    } catch (API_Request_Exception $api_exception) {
      $this->error($api_exception->getMessage(), ['skus' => $skus]);

      if ($api_exception->getCode() === CURLE_OPERATION_TIMEDOUT) {
        throw new Automatic_Style_Curation_Timeout_Exception($api_exception->getMessage(), $api_exception->getCode(), $api_exception);
      }

      throw new Automatic_Style_Curation_Exception($api_exception->getMessage(), $api_exception->getCode(), $api_exception);
    }
  }

  /**
   * @param SKU_Style_Suggestion_DTO[] $skus_style_suggestions Style suggestions DTO
   *
   * @return Automatic_Style_Suggestion_Collection
   */
  private function get_style_suggestion_collection(array $skus_style_suggestions) : Automatic_Style_Suggestion_Collection {
    $map = [];

    foreach ($skus_style_suggestions as $sku_style_suggestion_dto) {
      $suggestions = array_map(
          function (Style_Suggestion_DTO $style_suggestion_dto) {
            return new Style_Suggestion(
                $style_suggestion_dto->get_style_id(),
                $style_suggestion_dto->get_style_name(),
                $style_suggestion_dto->get_probability(),
                $style_suggestion_dto->get_rank()
            );
          }, $sku_style_suggestion_dto->get_suggestions()
      );

      $sku       = $sku_style_suggestion_dto->get_sku();
      $threshold = empty($sku_style_suggestion_dto->get_threshold()) ? null : $sku_style_suggestion_dto->get_threshold()->get_value();
      $map[$sku] = new SKU_Style_Suggestion($sku, $threshold, $suggestions);
    }

    return new Automatic_Style_Suggestion_Collection($map);
  }

  /**
   * @param string[] $skus SKU list
   *
   * @return Automatic_Substyle_Suggestion_Collection
   * @throws Automatic_Style_Curation_Exception
   */
  public function get_substyle_suggestion(array $skus) : Automatic_Substyle_Suggestion_Collection {
    try {
      $substyle_suggestions = $this->api->get_substyle_suggestion($skus, $this->suggestion_limit_per_sku);

      return $this->get_substyle_suggestion_collection($substyle_suggestions);
    } catch (API_Request_Exception $api_exception) {
      $this->error($api_exception->getMessage(), ['skus' => $skus]);

      if ($api_exception->getCode() === CURLE_OPERATION_TIMEDOUT) {
        throw new Automatic_Style_Curation_Timeout_Exception($api_exception->getMessage(), $api_exception->getCode(), $api_exception);
      }

      throw new Automatic_Style_Curation_Exception($api_exception->getMessage(), $api_exception->getCode(), $api_exception);
    }
  }

  /**
   * @param SKU_Style_Suggestion_DTO[] $skus_style_suggestions Style suggestions DTO
   *
   * @return Automatic_Substyle_Suggestion_Collection
   */
  private function get_substyle_suggestion_collection(array $skus_style_suggestions) : Automatic_Substyle_Suggestion_Collection {
    $map = [];

    foreach ($skus_style_suggestions as $sku_style_suggestion_dto) {
      $suggestions = array_map(
          function (Style_Suggestion_DTO $style_suggestion_dto) {
            return new Substyle_Suggestion(
                $style_suggestion_dto->get_style_id(),
                $style_suggestion_dto->get_style_name(),
                $style_suggestion_dto->get_probability(),
                $style_suggestion_dto->get_rank()
            );
          }, $sku_style_suggestion_dto->get_suggestions()
      );

      $sku       = $sku_style_suggestion_dto->get_sku();
      $threshold = empty($sku_style_suggestion_dto->get_threshold()) ? null : $sku_style_suggestion_dto->get_threshold()->get_value();
      $map[$sku] = new SKU_Substyle_Suggestion($sku, $threshold, $suggestions);
    }

    return new Automatic_Substyle_Suggestion_Collection($map);
  }
}
