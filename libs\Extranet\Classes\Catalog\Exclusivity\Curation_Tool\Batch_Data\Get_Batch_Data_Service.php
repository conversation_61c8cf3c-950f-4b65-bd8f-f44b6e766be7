<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Batch_Data;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Loader;
use WF\Shared\Traits\Logging_Trait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Get_Batch_Data_Service{
  use LoggerAwareTrait;
  use LoggerTrait;
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage;
   */
  private $dao;
  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
   */
  private $dao_psql;
    private $itemLoader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
     */
    private $sectionAdditionalDataLoader;


    /**
     * @var Supplier_Data_Loader
     */
    private $supplier_data_loader;

  private FeatureTogglesInterface $featureToggles;
  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage $dao Completion_Batch_Data_Storage
   */

    public function __construct(Completion_Batch_Data_Storage $dao, Batch_Management_Postgres_DAO $dao_psql, Section_Loader_Interface $itemLoader,  Supplier_Data_Loader $supplier_data_loader, Section_Additional_Data_Loader $sectionAdditionalDataLoader, FeatureTogglesInterface $featureToggles)
    {
        $this->dao = $dao;
        $this->itemLoader = $itemLoader;
        $this->dao_psql = $dao_psql;
        $this->supplier_data_loader = $supplier_data_loader;
        $this->sectionAdditionalDataLoader = $sectionAdditionalDataLoader;
        $this->featureToggles = $featureToggles;
    }

  /**
   * @param int $employee_id Employee ID
   * @return  array
   */
  public function get_batch_list(int $employee_id): array
  {
      $this->info('Loading Batch by employee_id', ['employee_id' => $employee_id]);
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $data = $this->dao_psql->get_batch_list($employee_id);
      } else {
          $data = $this->dao->get_batch_list($employee_id);
      }
      if (empty($data)) {
          $this->error("Batch data not exist", ['employee_id' => $employee_id]
          );
          return [];
      }
      return $data;
  }


    /**
     * @param int $employee_id Employee ID
     * @param int $batch_id Batch ID
     * @return  array
     */
    public function get_batch_list_call(int $employee_id, int $batch_id): array
    {
        if ($batch_id === 0) {
            $this->error(
                'Failed to complete QA for batch, missing batch_id',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $employee_id
                ]
            );
            return ['reason' => 'Bad request. Try to update your page!'];
        }
        $batch_data_list = $this->get_batch_list($employee_id);
        if (empty($batch_data_list)) {
            $this->error(
                'Batch data not exist',
                ['employee_id' => $employee_id]
            );
            return ['result' => []];
        }
        return $this->batch_list_filter($batch_data_list, $batch_id);
    }


    /**
     * @param array $batch_data_list batch list
     * @return  array
     */
    public function batch_list_filter(array $batch_data_list, int $batch_id): array
    {
        $this->info('Current Batch id', ['batch_id' => $batch_id]);
        $current_batch_pos = '';
        $current_batch_pos = array_search($batch_id, array_column($batch_data_list, 'batch_id'), TRUE);
        $data = array_slice($batch_data_list, $current_batch_pos + 1, 1);
        if (empty($data)) {
            $this->error("Next Batch Id are not exist for ", ['batch_id' => $batch_id]
            );
            return [];
        }
        return $data;
    }

    /**
     *
     * @return  array
     */
    public function getSectionsListCount(int $batchId, string $countBase): array
    {
        $groups = $this->itemLoader->getSectionsCountAndData($batchId, true, '', 0, 0, "", '', '', 0);
        $this->sectionAdditionalDataLoader->populate($batchId, $groups);
        $dataArr = [];
        $count = 0;
        $savedCount = 0;
        $suppliers = [];
        $countSupplier = 0;
        foreach ($groups as $section) {
            foreach ($section->get_curation_items() as $item) {
                foreach ($item->get_suppliers() as $supplier) {
                    $suppliers[$countSupplier] = $supplier;
                    $countSupplier++;
                }
            }
        }

        foreach ($groups as $value) {
            foreach ($value->get_curation_items() as $item) {
                if($countBase == "QA"){
                    if ($item->get_qa_status() != null && $item->get_qa_status()->value() != 1 && $item->get_type()->value() != "shared") {
                        $savedCount++;
                    }
                }else if ($countBase == "CURATION" && $item->get_type()->value() != "shared" && $item->get_qa_status()->value() == 1) {
                    $savedCount++;
                }
            }
            $dataArr[$count] = [
                'title' => $value->get_title(),
                'skusTotalCount' => count($value->get_curation_items()),
                'skusSavedCount' => $savedCount,
                'curationItems' => []
            ];
            $count++;
            $savedCount = 0;
        }

        $skuData = array('collections'=>$dataArr, 'suppliers'=> array_values(array_unique($suppliers)));
        return $skuData;
    }

    /**
     *
     * @return  array
     */
    public function getSectionsList(int $batchId, int $pageNum, int $limit, string $sectionName, string $supplier, string $countBase , string $pageType, int $xnId): array
    {
        $groups = $this->itemLoader->getSectionsCountAndData($batchId, false, $sectionName, $pageNum, $limit, $supplier , $countBase, $pageType, $xnId);
        return $groups;
    }
}