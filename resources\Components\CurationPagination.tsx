/**
 * Renders Pagination
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import {TextInput, Button, Column, Grid, Dropdown} from '@wayfair/homebase-extranet';
import {Pagination} from '@homebase/core';
import './CurationPagination.scss';
import {PAGE_LIMIT} from "./curation_tool_shapes";

export interface ICurationPaginationComponentState extends ICurationPaginationProps, ICurationPaginationState {}
export interface ICurationPaginationProps {
    fetchSectionDataFromApi(pageNum: number, limit: number): void;
}
export interface ICurationPaginationState {
    readonly totalSkus: number;
    readonly currentPage: number;
}
export interface Option {
    readonly text: string;
    readonly value: string;
}

const limit = PAGE_LIMIT
export default function CurationPagination (props: ICurationPaginationComponentState) {
    // const defaultOption : Option = {value: limit.toString(), text: limit.toString()};
    // const[option, setOption] = React.useState(defaultOption);

    // const changePageLimit = (option: Option) =>{
    //     const page : number = 1;
    //     setOption(option);
    //     props.fetchSectionDataFromApi(page, (option.value as number));
    // }
    //
    // const getOptionLabel = option => (option ? option.text : '');
    // const getOptionValue = option => (option ? option : null);
    const handlePageLinkClick = (pageNumber, e) => {
        props.fetchSectionDataFromApi(pageNumber, (limit));
    };

    return (
        <Grid alignItems="flex-start" className={'grid'}>
            {/*<Column size={4}>*/}
            {/*</Column>*/}
            <Column size={12}>
                <Pagination
                    numberOfItems={props.totalSkus}
                    itemsPerPage={limit}
                    pagesToShow={5}
                    currentPageNumber={props.currentPage}
                    onPageLinkClick={handlePageLinkClick}
                    useDirectLinks
                />
            </Column>
            {/*<Column size={2}>*/}
            {/*    <div className={'dropdownDiv'}>*/}
            {/*        <Dropdown*/}
            {/*            isFullWidth={false}*/}
            {/*            label={'Items per Page'}*/}
            {/*            defaultValue={option.value}*/}
            {/*            options={[*/}
            {/*                defaultOption,*/}
            {/*                {value: '200', text: '200'},*/}
            {/*                {value: '300', text: '300'},*/}
            {/*                {value: '500', text: '500'}*/}
            {/*            ]}*/}
            {/*            value={getOptionValue(option)}*/}
            {/*            onValueChange={option => changePageLimit(option)}*/}
            {/*            getOptionLabel={getOptionLabel}*/}
            {/*        />*/}
            {/*    </div>*/}
            {/*</Column>*/}
            {/*<Column size={5}>*/}
            {/*</Column>*/}
        </Grid>
    );
}