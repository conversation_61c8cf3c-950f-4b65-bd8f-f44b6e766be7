<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPunit\Shared\ProductManagement\Populator;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\ProductManagement\Populator\Closure_Cache;

class Closure_Cache_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\ProductManagement\Populator\Closure_Cache
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->subject = new Closure_Cache();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_stores_single_map_for_fqcn()
    {
        $map = ['a', 'b', 'c'];
        $fqcn = 'Some\Namespace';

        $this->subject->set($fqcn, $map);
        $this->assertEquals($map, $this->subject->get($fqcn, $map));

        $this->subject->set($fqcn, ['test']);
        $this->assertEquals(['test'], $this->subject->get($fqcn, $map));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_null_by_default()
    {
        $this->assertEquals(null, $this->subject->get('Some\Namespace'));
    }
}
