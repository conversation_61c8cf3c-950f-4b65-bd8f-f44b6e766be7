<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Rejection_Reason_Loader {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $postgres_DAO;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * Rejection_Reason_Loader constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO $dao    Curation_Tool_DAO
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO $postgres_DAO    Curation_Tool_Postgres_DAO
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface              $featureToggles                   Feature toggle
   * @param LoggerInterface|null                                                           $logger Logger
   */
  public function __construct(Curation_Tool_DAO $dao,
                              Curation_Tool_Postgres_DAO $postgres_DAO,
                              FeatureTogglesInterface $featureToggles,
                              ?LoggerInterface $logger = null) {
    $this->dao    = $dao;
    $this->postgres_DAO = $postgres_DAO;
    $this->featureToggles = $featureToggles;
    $this->logger = $logger;
  }

  /**
   * These rejection reasons are for the QA person to tell the curator why the curation selection was rejected
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[]
   */
  public function getRejectionReasons() : array {
    $this->log_info('Loading rejections reasons');
    try {
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
        return $this->postgres_DAO->get_rejection_reasons();
      } else {
        return $this->dao->get_rejection_reasons();
      }

    } catch (\Throwable $exception) {
      $this->log_throwable_error(
          $exception,
          sprintf('Exception occurred loading rejections reasons: %s', $exception->getMessage()),
      );

      throw $exception;
    }
  }

  /**
   * These rejection reasons are for the Curator to provide feedback to Data Science team why the suggested style was not used
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
   */
  public function getSuggestedStyleRejectionResons(): array {
    $this->log_info('Loading Suggested styles rejection reasons');
    try {
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
        return $this->postgres_DAO->get_suggested_style_rejection_reasons();
      } else {
        return $this->dao->get_suggested_style_rejection_reasons();
      }

    } catch (\Throwable $exception) {
      $this->log_throwable_error(
          $exception,
          sprintf('Exception occurred loading suggested styles rejection reasons: %s', $exception->getMessage()),
      );

      throw $exception;
    }
  }
}
