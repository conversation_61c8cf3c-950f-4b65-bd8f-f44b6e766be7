<?php
/**
 * PHP Version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers;

class Supplier_Data_Collection {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data[]
   */
  private $items = [];

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data
   */
  private $empty_supplier_data;

  /**
   * Supplier_Data_Collection constructor.
   */
  public function __construct() {
    $this->empty_supplier_data = new Empty_Supplier_Data();
  }

  /**
   * @param string $sku SKU
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data
   */
  public function get_for_sku(string $sku) : Supplier_Data {
    return $this->items[$sku] ?? $this->empty_supplier_data;
  }

  /**
   * @param string                                                                                 $sku           SKU
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data $supplier_data Supplier Data
   *
   * @return void
   */
  public function set_for_sku(string $sku, Supplier_Data $supplier_data) {
    $this->items[$sku] = $supplier_data;
  }
}