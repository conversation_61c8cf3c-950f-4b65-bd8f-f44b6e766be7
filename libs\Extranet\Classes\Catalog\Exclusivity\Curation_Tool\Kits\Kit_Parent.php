<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits;

class Kit_Parent implements \JsonSerializable {
  /**
   * @var string
   */
  public $sku;

  /**
   * @var array
   */
  public $children = [];

  /**
   * @var array
   */
  public $related = [];

  /**
   * Specify data which should be serialized to JSON
   *
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'sku'          => $this->sku,
        'related_kits' => $this->related
    ];
  }
}