<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Curation_Production_Tracking_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_Batch_Service_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\White_Label_Batch_Already_Exists_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Exception\Production_Tracking_Status_Not_Updated_Exception;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function in_array;
use function sprintf;

class DownstreamController extends AbstractBaseController
{
    protected $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @param Downstream_Batch_Service_Interface $downstream_batch_service Downstream service
     * @param Curation_Only_Batch_Service $curation_only_batch_service Curation batch service
     * @param Curation_Production_Tracking_Service $production_tracking_service Production Tracking service
     * @param Completion_Batch_Data $batch_data Completion_Batch_Data_Service
     * @param Completion_Batch_Data_Service $batch_data_service
     * @return JsonResponse
     * @throws Production_Tracking_Status_Not_Updated_Exception
     * @throws Throwable
     * @Route(path="/downstream", methods={"GET"}, name="downstream")
     */
    public function downstream(
        Downstream_Batch_Service_Interface $downstream_batch_service,
        Curation_Only_Batch_Service $curation_only_batch_service,
        Curation_Production_Tracking_Service $production_tracking_service,
        Completion_Batch_Data_Service $batch_data_service,
        Completion_Batch_Data $batch_data
    ): JsonResponse {
        $batch_id = $batch_data->getBatchId();

        if ($batch_id === 0) {
            $this->logger->error(
                'Failed to downstream batch, missing batch_id',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId()
                ]
            );

            return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            $this->info(
                'Loading batch data',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId()
                ]
            );
            $batch_data = $batch_data_service->get($batch_id);
        } catch (Throwable $exception) {
            $this->logger->error(
                sprintf('Failed to load batch data: %s', $exception->getMessage()),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception
                ]
            );

            return $this->json([
                'result' => false,
                'errorCode' => $exception->getCode(),
                'errorMessage' => $exception->getMessage()
            ]);
        }

        // safety check - TODO: change this condition to if(!$this->isExpectedBatchStatus(...))
        if (!in_array($batch_data->getStatus(), [Batch::STATUS_MANUAL_QA_COMPLETE, Batch::STATUS_MANUAL_DOWNSTREAMED], true)) {
            $this->logger->warning(
                sprintf('Failed to downstream batch batch_id=%d. Unexpected batch status', $batch_id),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'actual_status' => $batch_data->getStatus(),
                    'expected_status' => [Batch::STATUS_MANUAL_QA_COMPLETE, Batch::STATUS_MANUAL_DOWNSTREAMED]
                ]
            );

            return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            if ($batch_data->getStatus() === Batch::STATUS_MANUAL_QA_COMPLETE) {
                $downstream_batch_service->downstream_batch($batch_id);
                $this->info(
                    sprintf('Batch batch_id=%s has been downstreamed', $batch_id),
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                    ]
                );
                $curation_only_batch_service->mark_as_set_downstream($batch_id);
                $this->info(
                    sprintf('Batch batch_id=%s marked as downstreamed', $batch_id),
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                    ]
                );
            }

            $production_tracking_service->downstream_projects($batch_id);
            $this->info(
                sprintf('Batch batch_id=%s downstreamed in Product Tracking', $batch_id),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                ]
            );
            return $this->json(['result' => true]);
        } catch (White_Label_Batch_Already_Exists_Exception $exception) {
            $this->logger->error(
                sprintf(
                    'Can not downstream batch. Batch already exists in White Labeling: %s',
                    $exception->getMessage()
                ),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception,
                ]
            );

            $curation_only_batch_service->mark_as_set_downstream($batch_id);
            $this->info(
                sprintf('Batch batch_id=%s marked as downstreamed', $batch_id),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                ]
            );
            $production_tracking_service->downstream_projects($batch_id);
            $this->info(
                sprintf('Batch batch_id=%s has been downstreamed', $batch_id),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                ]
            );

            return $this->json(['result' => true, 'message' => 'Batch already exists in Whitelabeling']);
        } catch (Throwable $exception) {
            $this->logger->error(
                sprintf(
                    'An error occurred during Batch downstream with batch_id=%s: %s',
                    $batch_id,
                    $exception->getMessage()
                ),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception,
                ]
            );

            return $this->json([
                'result' => false,
                'errorCode' => $exception->getCode(),
                'errorMessage' => $exception->getMessage()
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
