<?php

declare(strict_types=1);
/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\Curation;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;

/**
 * @todo refactor this part to use some kind of API which will provides such data instead of DB
 */
class Curation_Option_Combination_Loader_DAO {

  private ProductConnection $pdo;

  /**
    * Curation_Option_Combination_Loader_DAO constructor.
    *
    * @param ProductConnection $pdo PDO connection to SQLPRODUCT(aka BOSQLC6)
    */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * Return the option combinations for SKU
   *
   * @param string $sku SKU to search
   *
   * @return array[]
   * @throws \WF\Shared\DAOs\Exception\Execution_Exception
   */
  public function get_options_combinations(string $sku): array {
    if (empty($sku)) {
        return [];
    }

    $sql = '
              SELECT
                fo.OptionCombinationID AS OptionCombinationID,
                p.PrBclgID AS bclgID,
                fo.OptionID1 AS piid1,
                fo.OptionID2 AS piid2,
                fo.OptionID3 AS piid3,
                po1.PiCategory AS category1,
                po1.PiName AS name1,
                po2.PiCategory AS category2,
                po2.PiName AS name2,
                po3.PiCategory AS category3,
                po3.PiName AS name3
              FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
              JOIN csn_product.dbo.vwFlatProductOptionCombination fo WITH(NOLOCK) ON fo.PrSKU =  p.PrSKU
              JOIN csn_product.dbo.tblSupplierPart s WITH(NOLOCK)
                ON s.ManufacturerPartID = fo.ManufacturerPartID
              JOIN csn_product.dbo.tblSupplier su WITH (NOLOCK)
                ON su.SuID = s.SupplierID
              LEFT JOIN csn_product.dbo.tblProductOption po1 WITH (NOLOCK) ON po1.PiID = fo.OptionID1
              LEFT JOIN csn_product.dbo.tblProductOption po2 WITH (NOLOCK) ON po2.PiID = fo.OptionID2
              LEFT JOIN csn_product.dbo.tblProductOption po3 WITH (NOLOCK) ON po3.PiID = fo.OptionID3
              WHERE p.PrSKU = :sku_id';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sku_id', $sku);

    if (!$statement->execute()) {
        throw new ExecutionException('Failed to get option combinations - ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll();
  }
}
