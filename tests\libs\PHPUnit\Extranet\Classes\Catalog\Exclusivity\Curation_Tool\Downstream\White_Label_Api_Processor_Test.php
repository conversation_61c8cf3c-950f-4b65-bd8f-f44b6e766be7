<?php

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use ReflectionClass;
use WF\Curation\WhiteLabelApi\Client as White_Label_Client;
use WF\Curation\WhiteLabelApi\ClientConfig;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api\White_Label_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\Downstream_Fail_Exception;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_Api_Service;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Response;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch as Batch_Model;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use function get_class;

class White_Label_Api_Processor_Test extends TestCase
{
    use ProphecyTrait;

    private const NUMBER_OF_RETRIES = 1;
    /**
     * @var WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api\White_Label_Api_Processor
     */
    private $api_wl_processor;
    /**
     * @var WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Response
     */
    private $response;
    /**
     * @var WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Error_Response
     */
    private $error_response;
    /**
     * @var WF\Curation\WhiteLabelApi\Client as White_Label_Client
     */
    private $whitelabel_api_client;
    /**
     * @var WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_Api_Service
     */
    private $white_label_nextgen_api_service;
    /**
     * @var Psr\Log\LoggerInterface
     */
    private $logger;
    /**
     * @var WF\Curation\WhiteLabelApi\ClientConfig
     */
    private $clientConfig;

    /**
     * @var WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
     */
    private $mock_batch;

    /**
     *
     * @test
     *
     * @param array $verification_items verification_items value
     *
     * @dataProvider evaluate_sku_data_provider
     *
     * @return void
     */
    public function it_returns_true_when_partial_white_label_api_completed(array $verification_items)
    {
        $response_code_ok = 200;
        $batch_models = [];
        foreach ($verification_items as $vi_id => $item) {
            $verified_batch_sku = $this->prophesize(Verified_Batch_SKU::class);
            $verified_batch_sku->is_kit_parent()->willReturn($item['reason'] === Batched_SKU_Evaluation_Result::REASON_SKU_PARENT);
            $verified_batch_sku->get_id()->willReturn($vi_id);
            $verified_batch_sku->get_sku()->willReturn($item['sku']);
            $verified_batch_sku->get_target_ma_id()->willreturn(123);
            $verified_batch_sku->is_excluded_from_wl()->willReturn(false);
            $batch_models[$item['sku']] = $verified_batch_sku->reveal();
        }
        $this->mock_batch->models = $batch_models;
        $this->white_label_nextgen_api_service->post_request_to_white_label_nextgen_api(Argument::any())->willReturn(
            $response_code_ok
        );
        $result = $this->invokeMethod($this->api_wl_processor, 'process_white_label_next_gen', [$this->mock_batch, $response_code_ok]);
        $this->assertEquals($response_code_ok, $result);
    }

    /**
     * Call protected/private method of a class.
     *
     * @param object $object     Instantiated object that we will run method on.
     * @param string  $methodName Method name to call
     * @param array   $parameters Array of parameters to pass into method.
     *
     * @return mixed Method return.
     */
    public function invokeMethod(&$object, $methodName, array $parameters = [])
    {
        $reflection = new ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }

    /**
     * @return array
     */
    public function evaluate_sku_data_provider()
    {
        return
          [
              [
                  [
                      1 => ['sku' => 'ABC123', 'status' => QA_Status_Object::APPROVED, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING],
                      2 => ['sku' => 'ABC456', 'status' => QA_Status_Object::PENDING_APPROVAL, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING],
                      3 => ['sku' => 'ABC789', 'status' => QA_Status_Object::PENDING_APPROVAL, 'reason' => Batched_SKU_Evaluation_Result::REASON_SKU_PENDING],
                  ],
                  Batch_Evaluation_Status::partial(),
                  'If some SKUs have been rejected the batch evaluation should return partial',
              ]
          ];
    }

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $sku = 'ABC456';
        $batch_configuration = [];
        $batch_configuration['changeCollection'] = 1;
        $batch_configuration['changePartNumber'] = 0;
        $batch_configuration['changeEAN'] = 0;
        $batch_configuration['changeDisplaySku'] = 0;
        $batch_configuration['enableScrubbing'] = 0;
        $batch_configuration['brandCatalogId'] = 0;
        $this->response = new White_Label_Batch_Response('123-123', null, [$sku], $batch_configuration, true);
        $this->whitelabel_api_client = $this->prophesize(White_Label_Client::class);
        $this->white_label_nextgen_api_service = $this->prophesize(White_Label_Nextgen_Api_Service::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->clientConfig = new ClientConfig($this->logger->reveal(), self::NUMBER_OF_RETRIES);
        $this->mock_batch = new Batch_Model();

        $this->api_wl_processor = new White_Label_Api_Processor(
            $this->whitelabel_api_client->reveal(),
            $this->white_label_nextgen_api_service->reveal(),
            $this->clientConfig,
        );
    }

    /**
     * Test that send_downstream method calls process_white_label_next_gen when whiteLabelNextgenApiInCuration is true.
     */
    public function testSendDownstreamCallsProcessWhiteLabelNextGenWhenApiInCuration()
    {
        $mockBatchModel = $this->createMock(Batch_Model::class);
        $this->api_wl_processor->whiteLabelNextgenApiInCuration = true;

        $mockApiProcessor = $this->getMockBuilder(White_Label_Api_Processor::class)
            ->setConstructorArgs([$this->whitelabel_api_client->reveal(), $this->white_label_nextgen_api_service->reveal(), $this->clientConfig])
            ->onlyMethods(['process_white_label_next_gen'])
            ->getMock();

        $mockApiProcessor->expects($this->once())
            ->method('process_white_label_next_gen')
            ->with($mockBatchModel, false)
            ->willReturn(200); // You can set the expected return value accordingly

        $response = $mockApiProcessor->send_downstream($mockBatchModel);
        $this->assertEquals(200, $response);
    }

    /**
     * Test that send_downstream method calls process_white_label_next_gen when whiteLabelNextgenApiInCuration is false.
     */
    public function testSendDownstreamFailedToCallsProcessWhiteLabelNextGenWhenApiInCuration()
    {
        $mockBatchModel = $this->createMock(Batch_Model::class);
        $this->api_wl_processor->whiteLabelNextgenApiInCuration = false;

        $mockApiProcessor = $this->getMockBuilder(White_Label_Api_Processor::class)
            ->setConstructorArgs([$this->whitelabel_api_client->reveal(), $this->white_label_nextgen_api_service->reveal(), $this->clientConfig])
            ->onlyMethods(['process_white_label_next_gen'])
            ->getMock();

        $mockApiProcessor->expects($this->once())
            ->method('process_white_label_next_gen')
            ->with($mockBatchModel, false)
            ->willThrowException(new Downstream_Fail_Exception());

        try {
            $response = $mockApiProcessor->send_downstream($mockBatchModel);
        } catch (Downstream_Fail_Exception $e) {
            $this->assertInstanceOf(Downstream_Fail_Exception::class, $e);
            return;
        }

            $this->fail('Expected Downstream_Fail_Exception was not thrown.');
    }

    /**
     * Test
     */
    public function testProcessWhiteLabelNextGenReturnBadRequestWhenApiCallThrowsRuntimeException()
    {
        $mockBatchModel = $this->createMock(Batch_Model::class);
        $this->api_wl_processor->whiteLabelNextgenApiInCuration = false;

        $mockApiProcessor = $this->getMockBuilder(White_Label_Api_Processor::class)
            ->setConstructorArgs([$this->whitelabel_api_client->reveal(), $this->white_label_nextgen_api_service->reveal(), $this->clientConfig])
            ->onlyMethods(['process_white_label_next_gen'])
            ->getMock();

        $mockApiProcessor->expects($this->once())
            ->method('process_white_label_next_gen')
            ->with($mockBatchModel, false)
            ->willReturn(400); // You can set the expected return value accordingly


        $response = $mockApiProcessor->send_downstream($mockBatchModel);
        $this->assertEquals(400, $response);
    }

    /**
     * Test that send_downstream method throws Downstream_Fail_Exception when whiteLabelNextgenApiInCuration is false and process_white_label_next_gen throws Downstream_Fail_Exception.
     */
    public function testSendDownstreamThrowsDownstreamFailExceptionWhenApiInCurationIsFalse()
    {
        $mockBatchModel = $this->createMock(Batch_Model::class);
        $this->api_wl_processor->whiteLabelNextgenApiInCuration = false;

        $mockApiProcessor = $this->getMockBuilder(White_Label_Api_Processor::class)
            ->setConstructorArgs([$this->whitelabel_api_client->reveal(), $this->white_label_nextgen_api_service->reveal(), $this->clientConfig])
            ->onlyMethods(['process_white_label_next_gen'])
            ->getMock();

        $mockApiProcessor->expects($this->once())
            ->method('process_white_label_next_gen')
            ->with($mockBatchModel, false)
            ->willThrowException(new Downstream_Fail_Exception());
        $this->expectException(Downstream_Fail_Exception::class);
        $response = $mockApiProcessor->send_downstream($mockBatchModel);
    }
}
