<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status;

class Batch_Evaluation_Status_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @dataProvider get_it_returns_string_data
     *
     * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status $status          Batch status object
     * @param string                                                                                 $expected_result Expected string status
     *
     * @return void
     */
    public function it_returns_string(Batch_Evaluation_Status $status, string $expected_result)
    {
        $this->assertEquals($expected_result, (string)$status);
    }

    /**
     * @return array
     */
    public function get_it_returns_string_data(): array
    {
        return [
            [
                Batch_Evaluation_Status::ready(),
                Batch_Evaluation_Status::STATUS_READY,
            ],
            [
                Batch_Evaluation_Status::not_ready(),
                Batch_Evaluation_Status::STATUS_NOT_READY,
            ],
            [
                Batch_Evaluation_Status::partial(),
                Batch_Evaluation_Status::STATUS_PARTIAL_READY,
            ],
        ];
    }
}
