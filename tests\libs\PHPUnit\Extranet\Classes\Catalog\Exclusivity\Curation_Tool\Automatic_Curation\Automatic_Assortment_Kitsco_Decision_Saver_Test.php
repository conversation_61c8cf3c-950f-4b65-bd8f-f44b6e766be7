<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Matcher;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Move_To_Brand_Decision_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_No_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Storage\Assortment_Curation_Decision_Loader_Postgres_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Assortment_Kitsco_Decision_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer;

class Automatic_Assortment_Kitsco_Decision_Saver_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Curation_Decision_Service|\Prophecy\Prophecy\ObjectProphecy
     */
    private $assortmentDecisionLoader;

    /**
     * @var Assortment_Curation_Decision_Loader|\Prophecy\Prophecy\ObjectProphecy
     */
    private $curationDecisionService;

    /**
     * @var Automatic_Assortment_Kitsco_Decision_Saver
     */
    private $subject;

    /**
     * @var Assortment_Curation_Decision_Loader_Storage|\Prophecy\Prophecy\ObjectProphecy
     */
    private $assortmentDecisionStorage;

    /**
     * @var Assortment_Curation_Decision_Loader_Postgres_DAO |\Prophecy\Prophecy\ObjectProphecy
     */
    private $assortmentDecisionPgStorageDao;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->assortmentDecisionStorage = $this->prophesize(Assortment_Curation_Decision_Loader_Storage::class);
        $this->assortmentDecisionPgStorageDao = $this->prophesize(Assortment_Curation_Decision_Loader_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->assortmentDecisionLoader = $this->prophesize(Assortment_Curation_Decision_Loader::class);
        $this->assortmentDecisionLoader->willBeConstructedWith(
            [
                $this->prophesize(Assortment_Decision_DTO_Loader::class)->reveal(),
                $this->prophesize(Assortment_Curation_Decision_Matcher::class)->reveal(),
                $this->assortmentDecisionStorage->reveal(),
                $this->assortmentDecisionPgStorageDao->reveal(),
                $this->featureToggles->reveal()
            ]
        );

        $this->curationDecisionService = $this->prophesize(Curation_Decision_Service::class);

        $this->subject = new Automatic_Assortment_Kitsco_Decision_Saver(
            $this->assortmentDecisionLoader->reveal(),
            $this->curationDecisionService->reveal(),
            new Kitsco_Manufacturer()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function it_saves_sku_as_kitsco_with_move_to_kitsco_assortment_decision()
    {
        $batchId = 1;
        $sku = 'AAA';
        $employeeId = -1;

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn(1);
        $curationItem->get_price_tier()->willReturn(3);
        $curationItem->get_saved_at()->willReturn(null);
        $curationItem->get_final_granular_style_id()->willReturn(10);
        $curationItem->get_type()->willReturn(Curation_Item_Type::shared());
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);

        $sections = [
            $section->reveal()
        ];

        /** @var Assortment_Curation_Decision_Collection $assortmentDecisionCollection */
        $assortmentDecisionCollection = $this->prophesize(Assortment_Curation_Decision_Collection::class);

        /** @var Assortment_Curation_Move_To_Brand_Decision_Data $assortmentDecisionData */
        $assortmentDecisionData = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $assortmentDecisionData->get_target_manufacturer_id()->willReturn(Kitsco_Manufacturer::KITSCO_US);

        /** @var Assortment_Curation_Decision $assortmentDecision */
        $assortmentDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentDecision->get_sku()->willReturn($sku);
        $assortmentDecision->should_move_to_tail_brand()->willReturn(true);
        $assortmentDecision->get_move_to_brand_decision_data()->willReturn($assortmentDecisionData);

        $assortmentDecisionCollection->get_skus_with_decision()->willReturn([$sku]);
        $assortmentDecisionCollection->get_for_sku($sku)->willReturn($assortmentDecision);

        $this->assortmentDecisionLoader->assortment_decision_data_for_skus($batchId, [$sku])
            ->willReturn($assortmentDecisionCollection)
            ->shouldBeCalled();

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $sku,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function it_saves_sku_as_kitsco_with_shared_kit_child_decision()
    {
        $batchId = 1;
        $kitParentSKU = 'AAA';
        $sku = 'BBB';
        $employeeId = -1;

        $curationItem1 = $this->prophesize(Curation_Item::class);
        $curationItem1->get_sku()->willReturn($kitParentSKU);
        $curationItem1->get_saved_at()->willReturn(null);
        $curationItem1->get_type()->willReturn(Curation_Item_Type::kit_parent());

        $curationItem2 = $this->prophesize(Curation_Item::class);
        $curationItem2->get_sku()->willReturn($sku);
        $curationItem2->get_saved_at()->willReturn(null);
        $curationItem2->get_type()->willReturn(Curation_Item_Type::shared());

        $section = $this->prophesize(Section::class);
        $section->get_curation_items()->willReturn([$curationItem1->reveal(), $curationItem2->reveal()]);
        $sections = [
            $section->reveal()
        ];

        /** @var Assortment_Curation_Move_To_Brand_Decision_Data $assortmentDecisionMoveToHeaderData */
        $assortmentDecisionMoveToHeaderData = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $assortmentDecisionMoveToHeaderData->get_target_price_tier()->willReturn(1);
        $assortmentDecisionMoveToHeaderData->get_target_style_id()->willReturn(1);
        $assortmentDecisionMoveToHeaderData->get_target_substyle_id()->willReturn(2);
        $assortmentDecisionMoveToHeaderData->get_target_manufacturer_id()->willReturn('111');

        /** @var Assortment_Curation_Decision $assortmentMoveToHeaderDecision */
        $assortmentMoveToHeaderDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentMoveToHeaderDecision->get_sku()->willReturn($kitParentSKU);
        $assortmentMoveToHeaderDecision->should_move_to_header_brand()->willReturn(true);
        $assortmentMoveToHeaderDecision->should_move_to_tail_brand()->willReturn(false);
        $assortmentMoveToHeaderDecision->get_move_to_brand_decision_data()->willReturn($assortmentDecisionMoveToHeaderData);

        /** @var Assortment_Curation_Move_To_Brand_Decision_Data $assortmentDecisionMoveToKitscoData */
        $assortmentDecisionMoveToKitscoData = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $assortmentDecisionMoveToKitscoData->get_target_manufacturer_id()->willReturn(Kitsco_Manufacturer::KITSCO_US);

        /** @var Assortment_Curation_Decision $assortmentMoveToKitscoDecision */
        $assortmentMoveToKitscoDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentMoveToKitscoDecision->get_sku()->willReturn($sku);
        $assortmentMoveToKitscoDecision->should_move_to_tail_brand()->willReturn(true);
        $assortmentMoveToKitscoDecision->get_move_to_brand_decision_data()->willReturn($assortmentDecisionMoveToKitscoData);

        $this->assortmentDecisionStorage->get_child_skus([$kitParentSKU])->willReturn([$kitParentSKU => [$sku]]);

        $this->assortmentDecisionLoader->assortment_decision_data_for_skus($batchId, Argument::type('array'))
            ->willReturn(
                new Assortment_Curation_Decision_Collection(
                    [
                        $assortmentMoveToHeaderDecision->reveal(),
                        $assortmentMoveToKitscoDecision->reveal()
                    ]
                )
            );

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $kitParentSKU,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldNotBeCalled();

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $sku,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function it_saves_sku_as_kitsco_with_kit_child_decision()
    {
        $batchId = 1;
        $kitParentSKU = 'AAA';
        $childSKU = 'BBB';
        $employeeId = -1;

        $curationItem1 = $this->prophesize(Curation_Item::class);
        $curationItem1->get_sku()->willReturn($kitParentSKU);
        $curationItem1->get_saved_at()->willReturn(null);
        $curationItem1->get_type()->willReturn(Curation_Item_Type::kit_parent());

        $section = $this->prophesize(Section::class);
        $section->get_curation_items()->willReturn([$curationItem1->reveal()]);
        $sections = [
            $section->reveal()
        ];

        /** @var Assortment_Curation_Move_To_Brand_Decision_Data $assortmentDecisionMoveToHeaderData */
        $assortmentDecisionMoveToHeaderData = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $assortmentDecisionMoveToHeaderData->get_target_price_tier()->willReturn(1);
        $assortmentDecisionMoveToHeaderData->get_target_style_id()->willReturn(1);
        $assortmentDecisionMoveToHeaderData->get_target_substyle_id()->willReturn(2);
        $assortmentDecisionMoveToHeaderData->get_target_manufacturer_id()->willReturn('111');

        /** @var Assortment_Curation_Decision $assortmentMoveToHeaderDecision */
        $assortmentMoveToHeaderDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentMoveToHeaderDecision->get_sku()->willReturn($kitParentSKU);
        $assortmentMoveToHeaderDecision->should_move_to_header_brand()->willReturn(true);
        $assortmentMoveToHeaderDecision->should_move_to_tail_brand()->willReturn(false);
        $assortmentMoveToHeaderDecision->get_move_to_brand_decision_data()->willReturn($assortmentDecisionMoveToHeaderData);

        /** @var Assortment_Curation_Move_To_Brand_Decision_Data $assortmentDecisionMoveToKitscoData */
        $assortmentDecisionMoveToKitscoData = $this->prophesize(Assortment_Curation_Move_To_Brand_Decision_Data::class);
        $assortmentDecisionMoveToKitscoData->get_target_manufacturer_id()->willReturn(Kitsco_Manufacturer::KITSCO_US);

        /** @var Assortment_Curation_Decision $assortmentMoveToKitscoDecision */
        $assortmentMoveToKitscoDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentMoveToKitscoDecision->get_sku()->willReturn($childSKU);
        $assortmentMoveToKitscoDecision->should_move_to_tail_brand()->willReturn(true);
        $assortmentMoveToKitscoDecision->get_move_to_brand_decision_data()->willReturn($assortmentDecisionMoveToKitscoData);

        $this->assortmentDecisionStorage->get_child_skus([$kitParentSKU])->willReturn([$kitParentSKU => [$childSKU]]);

        /** @var Assortment_Curation_Decision_Collection $assortmentDecisionCollection */
        $assortmentDecisionCollection = $this->prophesize(Assortment_Curation_Decision_Collection::class);
        $assortmentDecisionCollection->get_skus_with_decision()->willReturn([$kitParentSKU, $childSKU]);

        $this->assortmentDecisionLoader->assortment_decision_data_for_skus($batchId, Argument::type('array'))
            ->willReturn(
                new Assortment_Curation_Decision_Collection(
                    [
                        $assortmentMoveToHeaderDecision->reveal(),
                        $assortmentMoveToKitscoDecision->reveal()
                    ]
                )
            );

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $kitParentSKU,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldNotBeCalled();

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $childSKU,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }


    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function it_does_not_save_sku_as_kitsco_with_move_to_tail_assortment_decision()
    {
        $batchId = 1;
        $sku = 'AAA';
        $employeeId = -1;

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn(1);
        $curationItem->get_price_tier()->willReturn(3);
        $curationItem->get_saved_at()->willReturn(null);
        $curationItem->get_final_granular_style_id()->willReturn(10);
        $curationItem->get_type()->willReturn(Curation_Item_Type::shared());
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);

        $sections = [
            $section->reveal()
        ];

        /** @var Assortment_Curation_Decision $assortmentDecision */
        $assortmentDecision = $this->prophesize(Assortment_Curation_Decision::class);
        $assortmentDecision->get_sku()->willReturn($sku);
        $assortmentDecision->should_move_to_tail_brand()->willReturn(true);
        $assortmentDecision->get_move_to_brand_decision_data()->willReturn(null);

        /** @var Assortment_Curation_Decision_Collection $assortmentDecisionCollection */
        $assortmentDecisionCollection = $this->prophesize(Assortment_Curation_Decision_Collection::class);
        $assortmentDecisionCollection->get_skus_with_decision()->willReturn([$sku]);
        $assortmentDecisionCollection->get_for_sku($sku)->willReturn($assortmentDecision);

        $this->assortmentDecisionLoader->assortment_decision_data_for_skus($batchId, [$sku])
            ->willReturn($assortmentDecisionCollection)
            ->shouldBeCalled();

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $sku,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function it_does_not_save_sku_as_kitsco_when_theres_no_assortment_decision()
    {
        $batchId = 1;
        $sku = 'AAA';
        $employeeId = -1;

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn(1);
        $curationItem->get_price_tier()->willReturn(3);
        $curationItem->get_saved_at()->willReturn(null);
        $curationItem->get_final_granular_style_id()->willReturn(10);
        $curationItem->get_type()->willReturn(Curation_Item_Type::shared());
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);

        $sections = [
            $section->reveal()
        ];

        /** @var Assortment_Curation_Decision_Collection $assortmentDecisionCollection */
        $assortmentDecisionCollection = $this->prophesize(Assortment_Curation_Decision_Collection::class);
        $assortmentDecisionCollection->get_for_sku($sku)->willReturn(new Assortment_Curation_No_Decision());
        $assortmentDecisionCollection->get_skus_with_decision()->willReturn([]);

        $this->assortmentDecisionLoader->assortment_decision_data_for_skus($batchId, [$sku])
            ->willReturn($assortmentDecisionCollection)
            ->shouldBeCalled();

        $this->curationDecisionService->save_as_kitsco(
            $batchId,
            $sku,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_assortment_decision()
        )->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }
}
