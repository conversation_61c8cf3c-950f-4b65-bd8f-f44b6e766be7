<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Contracts\Assortment_Decision;

interface Assortment_Decision_DTO_Loader {
  /**
   * @param string[] $skus skus to get decisions for
   *
   * @return \WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection
   */
  public function get_decisions_for_skus(array $skus) : Assortment_Decision_DTO_Collection;
}
