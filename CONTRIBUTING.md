# Pull Request and the Contributing Process

We encourage cross team collaboration via fast pull requests, thorough reviews, and knowledge sharing.

This document explains the process and best practices for submitting a pull request to our projects. 


- [Before You Submit a Pull Request](#before-you-submit-a-pull-request)
- [Reviewing Pull Requests](#reviewing-a-pull-request)

- [The Pull Request Process](#the-pull-request-submit-process)
  - [Pull Requests and the Release Cycle](#pull-requests-and-the-release-cycle)
  - [How the Automated Checks Work](#how-the-automated-checks-work)
  - [Marking Work In Progress Pull Requests](#marking-work-in-progress-pull-requests)
  - [Marking Collaboration Pull Requests](#marking-collab-pull-requests)

- [Best Practices for Faster Reviews](#best-practices-for-faster-reviews)
  - [1. Smaller Is Better: Small Commits, Small Pull Requests](#1-smaller-is-better-small-commits-small-pull-requests)
  - [2. WIP Pull Requests](#2-wip-pull-requests)
  - [3. COLLAB Pull Requests](#3-collab-pull-requests)
  - [4. Open Different Pull Requests for Hotfixes and Features](#4-open-different-pull-requesta-for-hotfixes-and-features)
  - [5. Comments Matter](#5-comments-matter)
  - [6. Test All the Things](#6-test-all-the-things)
  - [7. Squashing and PR Titles](#7-squashing-and-pr-titles)
  - [8. KISS, YAGNI, MVP, SRP, etc.](#8-kiss-yagni-mvp-srp-etc)
  - [9. It's OK to Push Back](#9-its-ok-to-push-back)
  - [10. Common Sense and Empathy](#10-common-sense-and-empathy)
  - [11. Trivial Edits](#11-trivial-edits)
  - [12. Follow the Scouts Rule](#12-follow-the-scouts-rule)
  
- [FAQs](#faqs)
    - [Why was my pull request closed?](#why-was-my-pull-request-closed)
    - [Why is my pull request not getting reviewed?](#why-is-my-pull-request-not-getting-reviewed)


# Before You Submit a Pull Request

This doc is for developers who have a pull request to submit or a pull request to review. If you're looking for information on setting up your development environment, see the [Webstack Environment Setup on InfoHub](https://infohub.corp.wayfair.com/display/DP/Webstack+Environment+Setup) for monolith development and [Set Up Docker on InfoHub](https://infohub.corp.wayfair.com/display/PPD/Set+Up+Docker) for separate repo development.

Make sure your pull request adheres to our best practices. These include 
    - Styling conventions
    - Making small pull requests
    - Commenting thoroughly
    - Ensure existing and new test cases pass locally
    
**Please read the more detailed section on [Best Practices for Faster Reviews](#best-practices-for-faster-reviews) at the end of this doc.**



# Reviewing a Pull Request

When reviewing and giving feedback on a pull request please keep in mind the following core values of code reviews:
    - Actionable Feedback - Avoid personal preference critiques. Leave actionable feedback for the submitter.
    - Empathetic Responses - Put yourself in the submitter's shoes when responding. Keep your tone in mind. Use emoji to convey emotion.
    - Thorough Review - The goal of the pull request process is not to simply add a check mark. The goal is to review well and provide helpful feedback through collaboration.

Pull requests should be viewed as a conversation and until approved, they're not final.  It is expected that you and the submitter, possibly along with other reviewers, will have several iterations of feedback -> implementation -> discussion.

Quick Collaboration > Thorough Review > Detailed Critiques.


# The Pull Request Submit Process

Submitting a pull request requires the following steps to be completed before the pull request will be approved and merged.

    - [Open a pull request](https://help.github.com/articles/about-pull-requests/) (include your tests)
    - Pass all tests and pipelines
    - Iterate on PR feedback
    - Obtain approvals from code owners
    - The team that is responsible will submit a deployment / release for your PR, if you are a part of that team feel free to handle this yourself
    
If the pull request is meant to change the project significantly (i.e. is bigger than a simple bugfix), open a GH issues or a WIP PR to discuss the changes before starting the work.  See [2. WIP Pull Requests](#2-wip-pull-requests) for more information.
    

## Pull Requests and the Deploy Process

If a pull request has been reviewed but held or not approved, it might be due to release freeze or competing priorities. Feel free to comment on the PR to request more information.

After approval you still need to deploy your code.  Once you receive all necessary approvals from code owners, submit a deployment via Integrator/Differentiator and then monitor your deployment in the #deploy and #deploy-integrator channels in Slack.


## How the Automated Checks Work

Pipelines will be triggered against your PR to verify code styling, compatibility, and that all tests are passing.


## Marking Work In Progress Pull Requests

If you want to solicit feedback in the early stages of your pull request, you should mark your PR as WIP when submitting:

1. Add "WIP" to the title of the PR 
2. Comment liberally on your code with questions within the PR to start the discussion
3. Request feedback from the team or engineers that you'd like a review from

Also mark your pull request as WIP if you have received feedback and need time to implement it.  WIP will ensure that teams know what to expect out of the pull request and how much scrutiny to give it.  It will also remove it from most automated PR bots/metrics.  Once you're ready for review again you can move to COLLAB or submit for a full review.


## Marking Collaboration Pull Requests

If you want to solicit reviews before the implementation of your pull request is complete, you should mark your PR as COLLAB when submitting:

1. Add "COLLAB" to the title of the PR 
2. Comment liberally on your code with questions within the PR to start the discussion


# Best Practices for Faster Reviews

Most of this section is not specific to Wayfair.  Most of these items will be helpful when approaching any code contribution (including open source contributions).

Let's talk about best practices so your pull request gets reviewed quickly.


## 1. Smaller Is Better: Small Commits, Small Pull Requests

Small commits and small pull requests get reviewed faster and are more likely to be correct than large ones.  Change as little as possible at one time.

Attention and time are both hard to come by.  There's a meme that a 10 line PR gets 5 comments but a 1,000 line PR gets an immediate LGTM.  This is not something we want to happen here at Wayfair.  We intend to give thorough reviews of all PRs, which means that long and complicated PRs can take a significant time to be reviewed.

Your goal should be to have a pull request that can be reviewed in 15 minutes or less.  This is not always possible, especially for new features, but nonetheless it is a good goal.


#### Breaking up commits

**NOTE: This does not apply to changes within the PHP monorepo since force pushing is not allowed there**

Making a series of discrete commits is a powerful way to express the evolution of an idea or the
different ideas that make up a single feature. Strive to group logically distinct ideas into separate commits.

For example, if you found that your feature needed some pre-factoring to fit in, make a commit that JUST does that pre-factoring. Then make a new commit for your feature.

Strike a balance with the number of commits. A pull request with 25 commits is still very cumbersome to review, so use
judgment.

Git allows you to [rewrite history](https://git-scm.com/book/en/v2/Git-Tools-Rewriting-History) so think about the pull request as something you can mold to present as a package you're proud of.


**Breaking up Pull Requests**

Going back to our prefactoring example, you could also fork a new branch, do the prefactoring there and send a pull request for that. If you can extract whole idea from your pull request and send those as separate pull requests of their own, you can avoid the painful problem of continually rebasing.  This will also increase the chances of your PR being reviewed in a timely manner.

Multiple small pull requests are often better than multiple commits. Don't worry about flooding teams with pull requests. We'd rather have 100 small, quick to review PRs than 10 monster, slow to review PRs.

We want every pull request to be useful on its own, so use your best judgment on what should be a pull request vs. a commit.

As a rule of thumb, if your pull request is directly related to a feature and nothing else, it should probably be part of the that feature's pull request. If you can explain why you are doing seemingly no-op work, "it makes this feature change easier", that's fine. If you can imagine someone finding value independently of your feature, try it as a pull request. 

In general, opt for too many PRs.  You'll get feedback if it gets excessive.


**Examples**
For example, here are some estimated pull request review times based on PR size:

    - +30 −5 across 2 files = 48 hours
    - +200 −500 across 1 file = 72 hours
    - +312 −110 across 10 files = 1 week
    - +5000 −1 across 100 files = multiple weeks (should likely be broken into multiple PRs)
    
 Smaller, more succinct PRs = Faster Reviews.
 
 
## 2. WIP Pull Requests

When starting work on a new feature/change it's a good idea to create the pull request early, marking it as WIP, instead of waiting until the entire feature is complete.  This allows you to discuss the change with the team and adds transparency to the development process.

This helps you ensure that you don't waste your time on unforeseen issues.  This also drives collaboration from the team you're contributing to so you don't have to work in a silo.

Feel free to create a WIP pull request as early as the planning process.  Even some basic plans or POC code changes can make the conversation clearer.
 

## 3. COLLAB Pull Requests

Once you're pull request has made progress and you'd like a review you can either remove all label and have it go through a full review or mark it as COLLAB to solicit collaboration from the team you're submitting to.

COLLAB is a good step if you would like the PR to fall in to the teams normal PR review workflow but also want them to know that you're more interested in discussion/feedback than you are an immediate approval.

NOTE: most PRs will not need a COLLAB step.
 

## 4. Open Different Pull Requests for Hotfixes and Features

**Put changes that are unrelated to your feature into a different pull request.**

Often, as you are implementing a feature, you will find typos in comments, poorly named variables, missing typehints, etc.

You absolutely should fix those things, but not in the same pull request as your feature. Otherwise, your PR will have way too many changes, and your reviewer won't have the time or the focus to review both together.

This also makes testing more difficult as it's harder to gauge the backwards compatibility impact that your changes may have.

Even PRs should follow the Single Responsibility Principle.


**Look for opportunities to decouple.**

For example, if you find yourself touching a lot of modules, think about the dependencies you are introducing between packages. Can some of what you're doing be made more generic and decoupled?

Likewise, if your feature is similar to another feature, and you're duplicating some complex logic from that feature, consider prefactoring the core logic out and using it in both features (Separate PRs FTW!).  Keep in mind though that code duplication is not always a bad thing.  For example, it might be best to duplicate code from the monolith's /includes so that you can completely decouple your application.  A general rule, is to only start looking to [DRY](https://en.wikipedia.org/wiki/Don%27t_repeat_yourself) up your code once you've duplicated the same logic in three places.


## 5. Comments Matter

In your code, if someone might not understand why you did something (or you won't remember why later), extract a method with self-describing name, or a constant for non-obvious values. If that's not possible - comment it. Many code-review comments are about this exact issue.

Make sure you're commenting the why and not the how.  For example, if you're added two numbers together you don't need to explain that you're adding numbers, you should explain why the numbers are being added.

If you think there's something pretty obvious that we could follow up on, add a TODO.

Use PR comments (instead of code comments) to ask questions and start the discussion on the changes that you may not want in the code itself.


## 6. Test All the Things

A huge waste of time is starting a review, only to find that the tests are inadequate or absent. Very few pull requests can modify code and NOT modify or create tests.

If you don't know how to test your change, please ask in the team that owns this code's Slack channel.  They'll be happy to help you design things for easy testing or to suggest test cases.

Be sure to test both happy paths and failure cases.  Neglecting to test failure cases is one of the biggest issues found with tests during code review.  


## 7. Squashing and PR Titles

When your pull request is merged, your commits will be squashed and named based on your PR title.  Make sure to use a descriptive PR title for later reference in git history.
 

## 8. KISS, YAGNI, MVP, SRP etc.

Sometimes we need to remind each other of core tenets of software design - Keep It Simple, You Aren't Gonna Need It, Minimum Viable Product, Single Responsibility Principle, and so on.  Be careful not to over-engineer your solutions.  Often, simpler code leads to easier testing and quicker PR review.  It's OK to develop a v1 even though you wish it was as polished as a v5. 


## 9. It's OK to Push Back

Sometimes reviewers make mistakes or don't have all the context that you do. It's OK to push back on changes your reviewer requested. If you have a good reason for doing something a certain way, you are encouraged to debate the merits of a requested change. Both the reviewers and submitter should strive to discuss these issues in a polite, respectful, and empathetic manner.

Pull requests are a collaboration between you and the team that owns that code. Requested changes are not personal attacks.  There is no need to be defensive.  Radical candor helps us to ship the best code possible.


## 10. Common Sense and Empathy

No document can take the place of common sense and empathy. Use your best judgment, while you put some thought into how your PR can be made easier to review. If you do these things your pull requests will get merged with a faster turn around time.


## 11. Trivial Edits

Each incoming pull requests needs to be reviewed, tested, and then merged/deployed.

While automation helps with this, each contribution also has an engineering cost. Not just on the person submitting the PR but on all of the reviewers.  Therefore it is appreciated if you do NOT make trivial edits on their own.  Review the entire file and update it at once.  If you find one grammatical or spelling error, it is likely there are more in that file, you can really make your pull request count by checking the formatting, checking for non-descriptive variable names, fixing errors and then submitting all the fixes at once to that file.  Do keep backward compatibility in mind for method name/API changes.

Please do not make edits solely based on personal style preferences unless the offending code fails to comply with existing styles and conventions used throughout the project.

**Some questions to consider:**

* Can the file be improved further?
* Does the trivial edit greatly improve the quality of the content?
* Is this change simply a personal preference I have?


## 12. Follow the Scouts Rule

Always leave the code you're editing a little better than you found it.  If you're making a change and see simple improvements you can make, go for it.  Just make sure it's somewhat related to the changes you're already making.  Again, rely on your best judgement.



# FAQs

## Why was my pull request closed?

Pull requests older than 30 days may be closed. Exceptions can be made for pull requests that have active review comments, or that are awaiting other dependent pull requests. Closed pull requests are easy to recreate, and little work is lost by closing a pull request that subsequently needs to be reopened. We want to limit the total number of pull requests in flight to:
* Maintain a clean project
* Remove old pull requests that would be difficult to merge with master
* Keep high priority PRs top of mind
* Encourage a quick feedback loop

Feel free to create a new PR from your branch and make your case for why the PR is still relevant.


## Why is my pull request not getting reviewed?

A few factors affect how long your pull request might wait for review.

If it's a few weeks away from Cyber5, we may need to stabilize the codebase.  If it's near the holidays, many team members may be OOTO.

Or, it could be related to the above best practices. One common issue is that the pull request is too large to review in a timely manner. Let's say you've touched 39 files and have 8657 insertions. When your would-be reviewers pull up the diffs, they run away - this pull request could take days to review across multiple teams.
There is a detailed rundown of best practices, including how to avoid lengthy pull requests, in the next section.

But, if you've already followed the best practices and you still aren't getting feedback, here are some things you can do to nudge the process along:

   * Post in a Slack channel associated with your change.  For example, #php-core-libraries for PRs against a PHP Core Library.
   
   * Ping individual team members in Slack from the Code Owners listed in the PR.

   * Start the discussion by commenting on the PR with questions/trade-offs.

   * If you have fixed all the issues from a review, and you haven't heard back within 5 business days, you should ping the reviewers on Slack directly.


Learn more about how to get faster reviews by following [best practices](#best-practices-for-faster-reviews).



---

Special thanks to the Kubernetes project for their [amazing documentation]((https://github.com/kubernetes/community/)) that served as a starting point for this document.
