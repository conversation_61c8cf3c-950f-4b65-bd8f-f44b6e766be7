<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\MerchConnection;
use App\Infrastructure\Exception\ExecutionException;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Api_Logger_Storage;
use PDO;

class Production_Tracking_Api_Logger_DAO implements Production_Tracking_Api_Logger_Storage {
  use LoggerTrait;
  use LoggerAwareTrait;

  private MerchConnection $pdo;

  /**
   * @param MerchConnection    $pdo    MERCH
   */
  public function __construct(MerchConnection $pdo) {
    $this->pdo    = $pdo;
  }

  /**
   * @param string $level       Level
   * @param string $message     Message
   * @param string $context     Context
   * @param string $application Application
   * @param int    $employeeId  Employee Id
   *
   * @return void
   */
  public function addLog(string $level, string $message, string $context, string $application, int $employeeId) : void {
    $this->info('Saving WhileLabel API log', ['message' => $message, 'context' => $context, 'employee_id' => $employeeId]);

    $sql       = 'INSERT csn_merch_tool.dbo.tblWhiteLabelApiLog (Application, Level, Message, Context, CreatedBy, CreatedAt)
            VALUES (:application, :level, :message, :context, :createdBy, GETDATE())';
    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':level', $level, PDO::PARAM_STR);
    $statement->bindValue(':application', $application, PDO::PARAM_STR);
    $statement->bindValue(':message', $message, PDO::PARAM_STR);
    $statement->bindValue(':context', $context, PDO::PARAM_STR);
    $statement->bindValue(':createdBy', $employeeId, PDO::PARAM_STR);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Could not save logging');
      $this->error(
          $exception->getMessage(),
          ['message' => $message, 'context' => $context, 'employee_id' => $employeeId, 'exception' => $exception]
      );
      throw $exception;
    }
  }
}
