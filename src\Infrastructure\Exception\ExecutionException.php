<?php
/**
 * Will be thrown when DAO is not able to execute a query against database.
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Exception;

use App\Infrastructure\Connection\Driver\StatementProxy;
use RuntimeException;
use function implode;
use function sprintf;

class ExecutionException extends RuntimeException
{
    /**
     * @param StatementProxy $statement The Statement that failed
     * @param string $message Main message
     *
     * @return ExecutionException
     */
    public static function forStatement(StatementProxy $statement, string $message): ExecutionException
    {
        return new ExecutionException(
            sprintf(
                '%s (Code: %s - %s)',
                $message,
                $statement->errorCode(),
                implode(';', $statement->errorInfo())
            )
        );
    }
}
