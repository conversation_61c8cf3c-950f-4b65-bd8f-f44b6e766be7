<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPunit\Shared\ProductManagement\Populator;

use Closure;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use stdClass;
use WF\Shared\ProductManagement\Populator\Closure_Cache;
use WF\Shared\ProductManagement\Populator\Closure_Generator;
use WF\Shared\ProductManagement\Populator\Populator;

class Populator_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\ProductManagement\Populator\Closure_Cache|\Prophecy\Prophecy\ObjectProphecy
     */
    private $cache;

    /**
     * @var \WF\Shared\ProductManagement\Populator\Closure_Generator|\Prophecy\Prophecy\ObjectProphecy
     */
    private $generator;

    /**
     * @var \WF\Shared\ProductManagement\Populator\Populator
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->cache = $this->prophesize(Closure_Cache::class);
        $this->generator = $this->prophesize(Closure_Generator::class);

        $this->subject = new Populator($this->cache->reveal(), $this->generator->reveal());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_populates_closure_cache_once_when_empty()
    {
        $populator_closure = static function () {
        };
        $this->generator->generate(stdClass::class)->willReturn($populator_closure);
        $this->cache->get(Argument::any())->willReturn(null);
        $this->cache->set(Argument::type('string'), Argument::type(Closure::class))->will(
            function ($args) {
                return $this->get($args[0])->willReturn($args[1]);
            }
        );

        $populated = new stdClass();

        $this->subject->populate($populated, []);
        $this->subject->populate($populated, []);
        $this->subject->populate($populated, []);

        $this->cache->set(stdClass::class, $populator_closure)->shouldHaveBeenCalledTimes(1);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_populates_with_generated_populator_closure()
    {
        $provided_subject = $provided_properties = null;
        $populator_closure = static function ($subject, $properties) use (&$provided_subject, &$provided_properties) {
            $provided_subject = $subject;
            $provided_properties = $properties;
        };
        $this->generator->generate(stdClass::class)->willReturn($populator_closure);

        $properties = ['some', 'thing'];
        $populated = new stdClass();
        $this->subject->populate($populated, $properties);

        $this->assertEquals($populated, $provided_subject);
        $this->assertEquals($provided_properties, $properties);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_same_object()
    {
        $this->cache->get(stdClass::class)->willReturn(static function () {
        });
        $populated = new stdClass();

        $this->assertSame($populated, $this->subject->populate($populated, []));
    }
}
