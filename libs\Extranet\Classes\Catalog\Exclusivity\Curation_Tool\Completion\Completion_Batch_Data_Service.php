<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Shared\Traits\Logging_Trait;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Completion_Batch_Data_Service implements LoggerAwareInterface {
  use <PERSON><PERSON><PERSON>wareTrait;
  use LoggerTrait;
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage
   */
  public $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;
  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage $dao    Completion_Batch_Data_Storage
   */
  public function __construct(Completion_Batch_Data_Storage $dao, Batch_Management_Postgres_DAO $dao_psql, FeatureTogglesInterface $featureToggles) {
    $this->dao = $dao;
    $this->dao_psql = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batchID Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data
   * @throws \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception
   */
  public function get(int $batchID) : Completion_Batch_Data {
    $this->info('Loading Batch by batch_id', ['batch_id' => $batchID]);

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $data = $this->dao_psql->get_batch_data($batchID);
    } else {
      $data = $this->dao->get_batch_data($batchID);
    }
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $types = $this->dao_psql->get_process_types();
    } else {
      $types = $this->dao->get_process_types();
    }

    if (empty($data)) {
      $exception = new Completion_Batch_Not_Found_Exception(sprintf('Batch with id %s could not be found', $batchID));
      $this->error(
          $exception->getMessage(),
          ['batch_id' => $batchID,  'exception' => $exception]
      );
      throw $exception;
    }

    $processTypeId = $data['CurationBatchProcessTypeID'];
    $processTypeName = $types[$processTypeId];

    return new Completion_Batch_Data(
        $batchID,
        $data['StatusID'],
        $data['BrandCatalogID'],
        $data['AssignedEmID'],
        $processTypeId,
        $processTypeName,
        $this->getDate($data['CreatedAt']),
        $data['ApprovedBy'],
        $this->getDate($data['ApprovedAt'])
    );
  }

  private function getDate(?string $date): ?\DateTime {
    if ($date === null || $date === '') {
      return null;
    }

    try {
      $result = new \DateTime($date);
    } catch(\Throwable $exception) {
      $result = null;
    }

    return $result;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel
   * @throws \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception
   */
  public function get_qa_batch_skus_with_exclude_reason(int $batch_id) : Curation_Automation_Unwhitelabel {
    $this->info('Loading Batch SKUS with exclude reason by batch_id', ['batch_id' => $batch_id]);

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $skus = $this->dao_psql->get_qa_batch_skus_with_exclude_reason($batch_id);
    } else {
      $skus = $this->dao->get_qa_batch_skus_with_exclude_reason($batch_id);
    }

    return new Curation_Automation_Unwhitelabel(
        $batch_id,
        $skus,
        true
    );
  }
}
