/**
 * Saving block of the Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Alert, Button, Box, FLEX_KEYWORDS, CELL_ALIGNMENT} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {
  QA_STATUS_ACCEPTED,
  QA_STATUS_REJECTED,
  QA_STATUS_UPDATED,
} from './curation_tool_shapes';
import {ALIGNMENT} from './common_layout_constants';

const CurrentQAStatus = ({isApproved, isAutomaticCurationPostQaEnabled}) => {
  if (isApproved) {
    return (
      <Alert variation="success">
        <Translation msgid="CurationTool.QASaveColumnApproved" />
      </Alert>
    );
  }
  return (
    <Alert variation="warning">
      <Translation msgid="CurationTool.QASaveColumnUpdated" />
    </Alert>
  );
};

CurrentQAStatus.propTypes = {
  isApproved: PropTypes.bool.isRequired,
  isAutomaticCurationPostQaEnabled: PropTypes.bool.isRequired,
};

const QASaveColumn = ({
  isSaveEnabled,
  isPending,
  isApproved,
  isUpdated,
  isChangeEnabled,
  onSave,
  onChange,
  isAutomaticCurationPostQaEnabled,
}) => {
  const shouldDisplayApprove = isPending || !isApproved;
  const shouldDisplayUpdate =
    (isPending || isApproved) &&
    isChangeEnabled;
  const shouldDisplayChange =
    (isPending || isApproved) &&
    !isChangeEnabled;
  const isApproveEnabled = !isChangeEnabled && !isSaveEnabled && !isUpdated;

  const approve = () => {
    onSave(QA_STATUS_ACCEPTED);
    onChange(false);
  };
  const reject = () => onSave(QA_STATUS_REJECTED);
  const update = () => {
    onSave(QA_STATUS_UPDATED);
    onChange(false);
  };
  const change = () => onChange(true);

  return (
    <Box
      display="flex"
      alignItems={ALIGNMENT.CENTER}
      flexDirection={FLEX_KEYWORDS.COLUMN}
      gridRowGap={2}
    >
      {!isPending && (
        <CurrentQAStatus
          isApproved={isApproved}
          isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
        />
      )}
      {shouldDisplayApprove && (
        <Button disabled={!isApproveEnabled} onClick={approve}>
            <Translation msgid="CurationTool.QASaveColumnApprove" />
        </Button>
      )}
      {shouldDisplayUpdate && (
        <Button
          disabled={!isChangeEnabled || !isSaveEnabled}
          onClick={update}
          secondary
        >
          <Translation msgid="CurationTool.QASaveColumnUpdate" />
        </Button>
      )}
      {shouldDisplayChange && (
        <Button onClick={change} secondary>
          <Translation msgid="CurationTool.QASaveColumnChange" />
        </Button>
      )}
    </Box>
  );
};

QASaveColumn.propTypes = {
  isPending: PropTypes.bool,
  isApproved: PropTypes.bool,
  isUpdated: PropTypes.bool,
  isSaveEnabled: PropTypes.bool,
  isChangeEnabled: PropTypes.bool,
  onSave: PropTypes.func,
  onChange: PropTypes.func,
  isAutomaticCurationPostQaEnabled: PropTypes.bool,
};

QASaveColumn.defaultProps = {
  isPending: true,
  isApproved: false,
  isUpdated: false,
  isSaveEnabled: false,
  isChangeEnabled: false,
  isAutomaticCurationPostQaEnabled: false,
  onSave() {},
  onChange() {},
};

export default QASaveColumn;
