<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;

class Curation_Save_Info implements \JsonSerializable {

  /**
   * @var string
   */
  private $savedAt;

  /**
   * @var string
   */
  private $savedBy;

  /**
   * @var Curation_Decision_Source
   */
  private $source;

  /**
  * @var array
  */
  private $skus;

  /**
   * @param string                   $savedAt Saved at date
   * @param string                   $savedBy Saved by
   * @param Curation_Decision_Source $source  Curation Decision Source
   */
  public function __construct(string $savedAt, string $savedBy, Curation_Decision_Source $source, array $skus) {
    $this->savedAt = $savedAt;
    $this->savedBy = $savedBy;
    $this->source  = $source;
    $this->skus    = $skus;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'savedAt'      => $this->savedAt,
        'savedBy'      => sprintf('%s (%s)', $this->savedBy, $this->source->label()),
        'skus'         => $this->skus,
        'rejectedAt'   => null,
        'rejectedBy'   => null,
        'rejectedNote' => null,
    ];
  }
}
