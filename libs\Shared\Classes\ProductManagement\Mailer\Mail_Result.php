<?php
/**
 * Represents result of sending an email
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

class Mail_Result {
  /**
   * @var bool
   */
  private $successful;
  /**
   * @var string
   */
  private $reason;

  /**
   * Mail_Result constructor.
   *
   * @param bool   $successful indicates if send was successful
   * @param string $reason     failure reason (if any)
   */
  public function __construct($successful, $reason = '') {
    $this->successful = (bool) $successful; /** @phpstan-ignore-line */
    $this->reason     = (string) $reason; /** @phpstan-ignore-line */
  }

  /**
   * @return bool
   */
  public function is_successful() {
    return $this->successful;
  }

  /**
   * @return string
   */
  public function get_reason() {
    return $this->reason;
  }
}
