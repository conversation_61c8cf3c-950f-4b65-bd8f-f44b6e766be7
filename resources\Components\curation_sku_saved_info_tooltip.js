/**
 * Tooltip component used to show saved at information
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {IconV2 as Icon, Tooltip} from '@wayfair/homebase-extranet';
import {faInfoCircle} from '@fortawesome/free-solid-svg-icons';

class CurationSkuSavedInfoTooltip extends React.Component {
  static propTypes = {
    savedBy: PropTypes.string,
    savedAt: PropTypes.string,
  };

  static defaultProps = {
    savedBy: null,
    savedAt: null,
  };

  state = {
    isOpen: false,
  };

  shouldComponentUpdate(nextProps, nextState) {
    return (
      this.state.isOpen !== nextState.isOpen ||
      this.props.savedAt !== nextProps.savedAt ||
      this.props.savedBy !== nextProps.savedBy
    );
  }

  open = () => this.setState({isOpen: true});
  close = () => this.setState({isOpen: false});

  render() {
    return (
      <div
        onMouseEnter={this.open}
        onMouseLeave={this.close}>
        <Tooltip
          isOpen={this.state.isOpen}
          onRequestClose={this.close}
          placement="left"
          showClose={false}
          content={
            <p>
              <Translation
                msgid="CurationTool.SaveAtXByY"
                params={{
                  savedAt: this.props.savedAt,
                  savedBy: this.props.savedBy,
                }}
              />
            </p>
          }
          target={
            <Icon
              icon={faInfoCircle}
              className="text_large"
            />
          }
        />
      </div>
    );
  }
}

export default CurationSkuSavedInfoTooltip;
