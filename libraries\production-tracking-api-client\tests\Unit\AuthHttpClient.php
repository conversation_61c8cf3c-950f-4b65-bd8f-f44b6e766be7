<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Tests\Unit;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Guz<PERSON>Http\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Request;

final class AuthHttpClient
{
    /**
     * @return Client
     */
    public static function createConnectionTimeoutClientMock(): Client {

        $responses = [
            new RequestException('Error Communicating with Server', new Request('POST', '/oauth/token')),
            new RequestException('Error Communicating with Server', new Request('POST', '/oauth/token')),
            new RequestException('Error Communicating with Server', new Request('POST', '/oauth/token')),
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createSuccessfulClientMock(): Client {
        $responseBody = <<<BODY

{"access_token":"abcd1234",
 "expires_in":86400,
 "token_type":"Bearer",
 "scope":"admin:api http:///wayfair.com/internal_app/"
 }
BODY;

        $responses = [
            new Response(200, [], $responseBody),
            new RequestException('Error Communicating with Server', new Request('POST', 'oauth/token')),
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createValidationErrorClientMock(): Client {
        $responses = [
            new Response(400, [], null),
            new RequestException('Error Communicating with Server', new Request('POST', 'oauth/token')),
        ];

        return self::createClient($responses);
    }

    /**
     * @param  array  $responses
     * @return Client
     */
    private static function createClient(array $responses): Client {
        $mock = new MockHandler($responses);

        $handlerStack = HandlerStack::create($mock);

        return new Client(['handler' => $handlerStack, 'http_errors' => false]);
    }
}
