<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Configuration;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Manufacturer_Service;

class Context_Data_Manufacturer_Service_Test extends TestCase
{
    public const CONTEXT_SKU_STANDARD_MA_IDS = [2576];
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Configuration
     */
    private $context_data_configuration;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Manufacturer_Service
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->context_data_configuration = $this->prophesize(Context_Data_Configuration::class);

        $this->subject = new Context_Data_Manufacturer_Service($this->context_data_configuration->reveal());
    }

    /**
     * @test
     * @dataProvider final_manufacturer_id_expectations
     *
     * @param array    $data     Data inputs
     * @param int|null $expected Expected result
     *
     * @return void
     */
    public function it_gets_final_manufacturer_id(array $data, int $expected = null)
    {
        $manufacturer_id = $data['manufacturer_id'];
        $manufacturer_brw_id = $data['manufacturer_brw_id'];
        $source_id = $data['source_id'];
        $recently_curated_manufacturer_id = $data['recently_curated_manufacturer_id'];

        $this->context_data_configuration->get_standard_brands()->willReturn(static::CONTEXT_SKU_STANDARD_MA_IDS);

        $this->assertEquals($expected, $this->subject->get_final_manufacturer_id($manufacturer_id, $manufacturer_brw_id, $source_id, $recently_curated_manufacturer_id));
    }

    /**
     * @return array
     */
    public function final_manufacturer_id_expectations()
    {
        return [
            'not wl brand with no recent' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 0,
                    'source_id' => null,
                    'recently_curated_manufacturer_id' => null
                ],
                null
            ],
            'wl brand with no recent' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => null,
                    'recently_curated_manufacturer_id' => null
                ],
                1
            ],
            'wl brand with no recent and with source id not collision' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => 1,
                    'recently_curated_manufacturer_id' => null
                ],
                null
            ],
            'wl brand with no recent and with collisions' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => 2,
                    'recently_curated_manufacturer_id' => null
                ],
                1
            ],
            'wl brand with recent and with empty source' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => null,
                    'recently_curated_manufacturer_id' => 2
                ],
                2
            ],
            'wl brand with recent, but without collision' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => 1,
                    'recently_curated_manufacturer_id' => 2
                ],
                null
            ],
            'wl brand with recent and collision' => [
                [
                    'manufacturer_id' => 1,
                    'manufacturer_brw_id' => 1,
                    'source_id' => 2,
                    'recently_curated_manufacturer_id' => 2
                ],
                2
            ],
            'standard manufactured configured as context data ' => [
                [
                    'manufacturer_id' => static::CONTEXT_SKU_STANDARD_MA_IDS[0],
                    'manufacturer_brw_id' => 0,
                    'source_id' => null,
                    'recently_curated_manufacturer_id' => null
                ],
                static::CONTEXT_SKU_STANDARD_MA_IDS[0]
            ],
        ];
    }
}
