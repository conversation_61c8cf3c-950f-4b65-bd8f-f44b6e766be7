<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result;

class Batch_Evaluator_Factory {

  /**
   * @param string $sku       SKU
   * @param string $reason    Reason
   * @param int    $qa_status QA Status
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result
   */
  public function create_batched_sku_evaluation_result(string $sku, string $reason, int $qa_status) : Batched_SKU_Evaluation_Result {
    return new Batched_SKU_Evaluation_Result($sku, $reason, $qa_status, new \DateTime());
  }
}
