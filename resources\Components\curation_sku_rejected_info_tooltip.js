/**
 * Tooltip component used to show rejected information
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {IconV2 as Icon, Tooltip} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import {faExclamationTriangle} from '@fortawesome/free-solid-svg-icons';

class CurationSkuRejectedInfoTooltip extends React.Component {
  static propTypes = {
    rejectedBy: PropTypes.string,
    rejectedAt: PropTypes.string,
    rejectedNote: PropTypes.string,
  };

  static defaultProps = {
    rejectedBy: null,
    rejectedAt: null,
    rejectedNote: null,
  };

  state = {
    isOpen: false,
  };

  shouldComponentUpdate(nextProps, nextState) {
    return (
      this.state.isOpen !== nextState.isOpen ||
      this.props.rejectedBy !== nextProps.rejectedBy ||
      this.props.rejectedAt !== nextProps.rejectedAt ||
      this.props.rejectedNote !== nextProps.rejectedNote
    );
  }

  open = () => this.setState({isOpen: true});
  close = () => this.setState({isOpen: false});

  render() {
    return (
      <div
        onMouseEnter={this.open}
        onMouseLeave={this.close}>
        <Tooltip
          isOpen={this.state.isOpen}
          onRequestClose={this.close}
          placement="left"
          showClose={false}
          content={
            <div>
              <p>
                <Translation
                  msgid="CurationTool.CurationSkuRejectedInfoTooltipRejectedAtXByY"
                  params={{
                    rejectedAt: this.props.rejectedAt,
                    rejectedBy: this.props.rejectedBy,
                  }}
                />
              </p>
              <p>
                <span className="text_bold margin_right_small">
                  <Translation msgid="CurationTool.CurationSkuRejectedInfoTooltipRejectedNote" />
                </span>
                {this.props.rejectedNote}
              </p>
            </div>
          }
          target={
            <Icon
              icon={faExclamationTriangle}
              className={['fa-2x', 'text_alert']}
            />
          }
        />
      </div>
    );
  }
}

export default CurationSkuRejectedInfoTooltip;
