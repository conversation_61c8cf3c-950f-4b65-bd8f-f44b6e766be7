<?php

declare(strict_types=1);

namespace App\Tests\Unit\libs\Shared\Curation\Api\Style_Suggestion;

use Mockery;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Style_Suggestion\Style_Suggestion_Api_Client;
use WF\Shared\Environment;
use function is_string;

class Style_Suggestion_Api_Client_Test extends TestCase
{
    private const STYLE_SUGGESTION_DEV_URL = 'https://kube-style-suggestion-api.service.intradsm1.sdeconsul.csnzoo.com';
    private ?HttpClientInterface $httpClient;
    private ?Style_Suggestion_Api_Client $client;
    private $logger;



    /**
     * @test
     * @return void
     * @throws API_Request_Exception
     */
    public function getSubStyleSuggestions_EmptyPayload()
    {
        $this->logger->expects('log')->with(
            LogLevel::INFO,
            Mockery::on(static fn ($arg) => is_string($arg)),
            []
        );
        $expected = [];
        $actual = $this->client->get_substyle_suggestion([], 10);
        self::assertEquals($expected, $actual);
    }

    /**
     * @test
     * @return void
     * @throws API_Request_Exception
     */
    public function getStyleSuggestions_EmptyPayload()
    {
        $this->logger->expects('log')->with(
            LogLevel::INFO,
            Mockery::on(static fn ($arg) => is_string($arg)),
            []
        );
        $expected = [];
        $actual = $this->client->get_style_suggestion([], 10);
        self::assertEquals($expected, $actual);
    }

    protected function setUp(): void
    {
        $this->httpClient = Mockery::mock(HttpClientInterface::class);
        $this->client = new Style_Suggestion_Api_Client(
            $this->httpClient,
            Environment::DEVELOPMENT
        );
        $this->logger = Mockery::mock(LoggerInterface::class);
        $this->client->setLogger($this->logger);

        parent::setUp();
    }
}
