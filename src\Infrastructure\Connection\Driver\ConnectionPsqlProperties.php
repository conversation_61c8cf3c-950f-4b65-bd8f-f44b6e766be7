<?php
/**
 * <AUTHOR> <<EMAIL>>
 * * @copyright 2023 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace App\Infrastructure\Connection\Driver;

class ConnectionPsqlProperties
{
    private $driver;
    private $username;
    private $credentialName;

    /**
     * @param driver                                 $driver
     * @param user                               $username to login as
     * @param credential                         $credentialName name - password will be decrypted internally
     */
    public function __construct(
        string $driver,
        string $username,
        string $credentialName
    ) {
        $this->driver = $driver;
        $this->username = $username;
        $this->credentialName = $credentialName;
    }

    public function toArray(): array
    {
        return [
            'driver' => $this->driver,
            'user' => $this->username,
            'password' => $this->credentialName
        ];
    }
}
