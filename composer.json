{"name": "wayfair/brand-workflows-curation-tool", "description": "Decoupled Curation Tool", "type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": "^7.4 || ^8.0", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-pdo": "*", "caseyamcl/guzzle_retry_middleware": "^2.7", "doctrine/doctrine-bundle": "^2.7", "google/cloud-bigquery": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "phpmailer/phpmailer": "^6.6", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "sensio/framework-extra-bundle": "^6.2", "symfony/asset": "^5.4", "symfony/cache": "^5.4", "symfony/console": "^5.4", "symfony/dotenv": "^5.4", "symfony/expression-language": "^5.4", "symfony/framework-bundle": "^5.4", "symfony/http-client": "^5.4", "symfony/http-foundation": "^5.4", "symfony/monolog-bundle": "^3.8", "symfony/security-bundle": "^5.4", "symfony/serializer-pack": "^1.1", "symfony/translation": "^5.4", "symfony/yaml": "^5.4", "wayfair/brand-workflows-assortment-library": "^1.10.4", "wayfair/brand-workflows-purest-client": "dev-master", "wayfair/curation-production-tracking-api-client": "dev-master", "wayfair/curation-white-label-api-client": "dev-master", "wayfair/frontend-webpack": "^0.7.0", "wayfair/php-core-db-symfony-bundle": "^1.3", "wayfair/php-core-envconfig-symfony-bundle": "^v2.1", "wayfair/php-core-feature-toggle": "^1.0", "wayfair/php-core-logging-symfony-bundle": "^2.5", "wayfair/php-core-metrics": "^3.2", "wayfair/php-core-secrets-symfony-bundle": "^2.1", "wayfair/php-partner-home-user-auth-bundle": "^2.2", "wayfair/php-slo": "^1.0@RC"}, "require-dev": {"infection/infection": "^0.26", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.3", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5", "mockery/mockery": "^1.5", "squizlabs/php_codesniffer": "^3.7", "symfony/web-server-bundle": "^4.4", "wayfair/php-platform-coding-standard": "^3.0"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true, "infection/extension-installer": true}, "platform": {"php": "7.4.27"}}, "autoload": {"psr-4": {"App\\": "src/", "WF\\Extranet\\": "libs/Extranet", "WF\\Shared\\": "libs/Shared"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "WF\\Tests\\": "tests/libs/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php56": "*"}, "conflict": {"symfony/symfony": "*"}, "repositories": {"local": {"type": "path", "url": "./libraries/*"}, "artifactory-github": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-github", "options": {"ssl": {"verify_peer": false}}}, "artifactory-wayfair": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-wayfair", "options": {"ssl": {"verify_peer": false}}}, "packagist": false}, "scripts": {"comment": ["The scripts here allow you to locally run the same checks that the pipelines will run.", "You are welcome to add your own, but please don't modify `phpunit`, `phpstan` or `ecs`.", "And please make sure the `verify` script includes at least those three.", "Note that we use the `php -d` format instead of calling the binary directly so the scripts", " won't fail on dev VMs, where auto_prepend.php will get run by default."], "verify": ["@lint", "@lint-container", "@php-versions-check", "@ecs", "@phpstan", "@phpunit", "@tests-mutation"], "lint": "php vendor/bin/parallel-lint . --exclude vendor --exclude var", "phpunit": "php vendor/bin/phpunit --verbose --coverage-clover phpunit.coverage.xml --log-junit phpunit.report.xml", "phpstan": "php vendor/bin/phpstan analyze -c phpstan.neon --memory-limit=-1", "ecs-fix": "php vendor/bin/ecs --fix check src tests", "ecs-fix-clear-cache": "php vendor/bin/ecs --clear-cache check src tests", "ecs": "php vendor/bin/ecs check src tests", "php-versions-check": ["@php74check", "@php81check"], "php74check": "vendor/bin/phpcs -p --standard=PHPCompatibility --runtime-set testVersion 7.4 src tests libs libraries", "php81check": "vendor/bin/phpcs -p --standard=PHPCompatibility --runtime-set testVersion 8.1 src tests libs libraries", "tests-mutation": "php -d memory_limit=-1 vendor/bin/infection --min-msi=0 --min-covered-msi=5 --test-framework-options=\"--testsuite=UnitTestSuite\"", "lint-container": "php bin/console lint:container --env=prod --no-interaction", "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}}}