<?php
/**
 *
 * Handles Talent API requests.
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Curation;

use App\Application\Logger\LoggerTrait;
use GuzzleHttp\Client;
use Psr\Cache\InvalidArgumentException;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Cache\Adapter\AdapterInterface;
use WF\Shared\Environment;

use function json_decode;
use function time;

/**
 * Class to handle our SQL queries.
 */
class Talent<PERSON>pi implements LoggerAwareInterface {

  use LoggerTrait;
  use LoggerAwareTrait;

  const PROD_TOKEN_ENDPOINT = 'https://auth.wayfair.io/auth/realms/Admin_Home/protocol';
  const DEV_TOKEN_ENDPOINT = 'https://authwayfairio.csnzoo.com/auth/realms/Admin_Home/protocol';

  const PROD_TALENT_ENDPOINT = 'https://kube-talent-api.service.intraiad1.consul.csnzoo.com/v1';
  const DEV_TALENT_ENDPOINT = 'https://kube-talent-api.service.intradsm1.sdeconsul.csnzoo.com/v1';
  public const CACHE_KEY = 'talent_api_access_token';
  public const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 1800; // 30 min

  public CacheItemPoolInterface $cache;



  /**
   * @var string the client secret.
   */
  public $clientSecret;

  /**
   * @var string the endpoint to hit for auth_identity
   */
  public $tokenEndpoint;

  /**
   * @var string the endpoint to hit for talent api
   */
  public $talentEndpoint;

  /**
   * @var string The Client ID.
   */
  public $clientID = '';

  /**
   * @var Client Client.
   */
  public Client $httpClient;

  /**
   * Talent API constructor.
   *
   * @param string $environment ENV
   * @param string $clientID Client ID
   * @param string $clientSecret Client Secret
   */
  public function __construct(
      string $environment,
      string $clientID,
      string $clientSecret,
      Client                 $httpClient,
      CacheItemPoolInterface $cache
   ) {
    $this->clientID       = $clientID;
    $this->clientSecret   = $clientSecret;
    $this->tokenEndpoint  = $environment === Environment::PRODUCTION ? self::PROD_TOKEN_ENDPOINT : self::DEV_TOKEN_ENDPOINT;
    $this->talentEndpoint = $environment === Environment::PRODUCTION ? self::PROD_TALENT_ENDPOINT : self::DEV_TALENT_ENDPOINT;
    $this->httpClient = $httpClient;
    $this->cache = $cache;
  }


  /**
   * Get an auth token
   *
   * @param integer $employeeId
   *
   * @return mixed Auth token
   * @throws \Exception
   */
  public function getAccessToken() : ?string {

      $accessToken = $this->getCachedAccessToken();
      if ($accessToken !== null) {
          return $accessToken;
      }
      $request = [
        'form_params' => [
            'client_id'     => $this->clientID,
            'client_secret' => $this->clientSecret,
            'grant_type'    => 'client_credentials',
            'scope'         => 'roles'
        ]
    ];
    $url     = $this->tokenEndpoint . '/openid-connect/token';
    try {
      $response = $this->httpClient->post($url, $request);
      $data = json_decode($response->getBody(), true);

        if (isset($data['access_token'])) {
            $accessToken = $data['access_token'];
            $this->cacheAccessToken($accessToken);
            return $accessToken;
        }
        $this->logger->error('Failed to get access token from response', ['response' => $data]);
    } catch (\Exception $exception) {
      $this->error(
          sprintf('Failed to get access token: %s', $exception->getMessage()));

    }
      return null;
  }

  public function getCachedAccessToken(): ?string {
        $cacheItem = $this->cache->getItem(self::CACHE_KEY);

        if ($cacheItem->isHit()) {
            $tokenData = $cacheItem->get();

            if (isset($tokenData['token']) && isset($tokenData['expires_at']) && $tokenData['expires_at'] > time()) {
                return $tokenData['token'];
            } else {
                $this->clearAccessToken(); // Clear the expired token from cache
            }
        }

        return null;
  }

  public function cacheAccessToken(string $accessToken): void {
        $tokenData = [
            'token' => $accessToken,
            'expires_at' => time() + self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS
        ];
        $cacheItem = $this->cache->getItem(self::CACHE_KEY);
        $cacheItem->set($tokenData)->expiresAfter(self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS);
        $this->cache->save($cacheItem);
  }

  /**
   * Get an employee details
   *
   * @param integer $employeeId
   *
   * @return mixed employee details
   * @throws \Exception
   */
  public function getEmployeeDetails($employeeId) {
      $url = $this->talentEndpoint . '/employees/' . $employeeId;
      $token = $this->getAccessToken($employeeId);

      if ($token !== null) {
          try {
              $response = $this->httpClient->request(
                  'GET',
                  $url,
                  [
                      'headers' => [
                          'Authorization' => "Bearer {$token}"
                      ],
                  ]
              );
              $this->info(
                  'Getting employee details from Talent Api',
                  ['employee_id' => $employeeId]
              );
              return json_decode($response->getBody());
          } catch (\Exception $exception) {
              if ($exception instanceof \GuzzleHttp\Exception\ClientException && $exception->getCode() === 401) {
                  // If 401 Unauthorized error occurs, the token may have expired, so refresh the token and try again
                  $this->logger->warning('Access token has expired. Refreshing the token and retrying...');
                  $this->clearAccessToken(); // Clear the expired token from cache
                  return $this->getEmployeeDetails($employeeId); // Retry with a fresh token
              } else {
                  $this->logger->error(
                      sprintf('Failed to load employee data: %s', $exception->getMessage()),
                      ['employee_id' => $employeeId, 'exception' => $exception]
                  );
              }
          }
      }
      return  null ;
  }

  public function clearAccessToken(): void
  {
        $this->cache->deleteItem(self::CACHE_KEY);
  }

  /**
   * Get an employee list
   *
   * @param array $employeeIds
   *
   * @return mixed employee lists
   * @throws \Exception
   */
  public function getEmployeeList(array $employeeIds) {
    $assigned_employee_id_str = implode(",", array_filter($employeeIds, 'strlen'));
    $size                     = count($employeeIds);
    $query_data               = [
        'ids'             => $assigned_employee_id_str,
        "includeinactive" => true,
        "size"            => $size
    ];
    $url                      = $this->talentEndpoint . '/employees/?' . http_build_query($query_data);
    $token                    = $this->getAccessToken($employeeIds[0]);
    if ($token !== null) {
      try {
      $response = $this->httpClient->request(
          'GET',
          $url,
          [
              'headers' => [
                  'Authorization' => "Bearer {$token}"
              ],
          ]
      );
      $this->info(
          'Getting employee List from Talent Api',
          ['employee_id' => $employeeIds]
      );
      return json_decode($response->getBody());
    } catch (\Exception $exception) {
      if ($exception instanceof \GuzzleHttp\Exception\ClientException && $exception->getCode() === 401) {
        // If 401 Unauthorized error occurs, the token may have expired, so refresh the token and try again
        $this->clearAccessToken(); // Clear the expired token from cache
        return $this->getEmployeeDetails($employeeIds[0]); // Retry with a fresh token
      } else {
        $this->logger->error(
            sprintf('Failed to load employee data: %s', $exception->getMessage()),
            ['employee_id' => $employeeIds[0], 'exception' => $exception]
        );
      }
    }
  }
    return  null;
  }
}