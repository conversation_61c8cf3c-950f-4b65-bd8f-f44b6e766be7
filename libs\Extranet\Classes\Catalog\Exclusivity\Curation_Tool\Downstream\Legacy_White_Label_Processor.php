<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use Psr\Log\LoggerInterface;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

class Legacy_White_Label_Processor implements Downstream_Processor {

  /**
   * @var White_Label_Batch_DAO
   */
  private $dao;

  /**
   * @var White_Label_Batch_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;

  private $logger;

  /**
   * @param White_Label_Batch_DAO $dao DAO class for white label tables
   */
  public function __construct(
      White_Label_Batch_DAO $dao,
      White_Label_Batch_Postgres_DAO $dao_psql,
      FeatureTogglesInterface $featureToggles,
      LoggerInterface $logger
  ) {
      $this->dao            = $dao;
      $this->dao_psql       = $dao_psql;
      $this->featureToggles = $featureToggles;
      $this->logger         = $logger;
  }

  /**
   * Loads curation requests with id and quckiform id
   *
   * @param Batch $batch The batch for which we create white label batch
   *
   * @return bool
   */
    public function send_downstream(Batch $batch): int {
        $response_code_ok = 200;
        $response_code_Bad_Request = 400;
        try {
            // Check if the batch already exists in the DAO
            if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                $batch_data = $this->dao_psql->get_batch($batch->get_id());
            } else {
                $batch_data = $this->dao->get_batch($batch->get_id());
            }
            if (!empty($batch_data)) {
                $this->logger->info("Batch with ID {$batch->get_id()} already exists. Skipping insertion.");
                return $response_code_ok;
            }
            // Insert the batch into the DAO
            $this->dao->insert_batch($batch);
            $this->logger->info("Batch with ID {$batch->get_id()} inserted successfully.");
            return $response_code_ok;
        } catch (\Exception $exception) {
            $this->logger->error("Error occurred while sending legacy downstream: " . $exception->getMessage());
            return $response_code_Bad_Request;
        }
    }

}
