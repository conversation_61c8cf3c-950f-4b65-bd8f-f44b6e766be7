# Useful Scripts

Some python scrips were created to deal with some prevalent/repetitive issues, even though the goal is to fix them from the root.
Almost of the scripts read a CSV file with rows to update and call [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/) to make the change.
Few call a certain service endpoint other than Uber Loader to achieve the goal.

!!! info
    This list of scripts is subject to change, and it's encouraged to add more scripts like these here when you find necessary.

## Downstream Manual Batches
**Frequency Rating: 10/10**

**Could we do better so the script is no longer needed?** Yes. We have to fix the bug to make sure the manual batches are actually downstreamed before. The `publish` or alike API can be leveraged to guarantee WL downstream is not missed.

**When and how to use this script:**

- Stakeholders complain that manual batches are not properly downstreamed even though it's QA Complete status
- A SQL to find out this kind of batches
```sql
select top 2000 * from csn_product.dbo.tblCurationBatch batch where CurationBatchProcessTypeID=1 and StatusID in (5,6) and VerificationID is null  ORDER by ID DESC
```
- Export the result to a CSV file and save it to the same path as the script with name `manual_batch_to_downstream.csv`
- Login to [Curation Tool](https://partners.wayfair.com/d/curation-tool/index) and open dev console to get the cookie (very long)
- Replace `COOKIE_FROM_DEV_CONSOLE_ON_CURATION_TOOL` with the cookie
- This script could run anywhere. Could be local/VDI or devbox wherever it can reach uber loader.

```py
import csv
import requests

header = {
    "cookie": "<COOKIE_FROM_DEV_CONSOLE_ON_CURATION_TOOL>"
}

def downstream_curation_batch(batchId: str) -> None:
    try:
        res = requests.get(url=f"https://partners.wayfair.com/d/curation-tool/downstream?batch_id={batchId}", headers=header, verify=True)
        if res.status_code >= 400:
            print(f"downstream failed with response code: {res.status_code}")
            return
        else:
            print(f"downstreamed with response code: {res.status_code}")
    except Exception as err:
        print(f"failed processing batch: {batchId}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./manual_batch_to_downstream.csv") as csv_file:
    csv_reader, line_count, curation_batch_ids = csv.DictReader(csv_file), 0, set()
    for row in csv_reader:
        curation_batch_ids.add(row['ID'])
        print(f"Processing Batch: {row['ID']}")
        downstream_curation_batch(row['ID'])
        line_count += 1
        if line_count % 100 == 0:
            print(f"{line_count} completed")
    print(f'Manual Batches Downstreamed. Processed {line_count} lines')
    print(f"All Batches in {curation_batch_ids} are processed")
```

## Delete Verification Items in Curation Batch
**Frequency Rating: 8/10**

**Could we do better so the script is no longer needed?** Yes. This problem is caused by large batches. In PA flow, we can change the logic to limit the batch side from import job side;
On Curation Loader flow, we could limit the number of rows to upload per request and update the import manual job to limit the batch size too;
On Curation Tool side, we could add pagination on BE and FE to be able to load larger batch with SKUs fetched in pages

**When and how to use this script:**

- The stakeholders complain about a batch is too large to open in curation tool and agree to drop it to start from scratch
- A SQL to find out all the verification items under the batch:
```sql
select top 10000 * from csn_product.dbo.tblVerificationItem vi WITH(NOLOCK) where ViBatchID in (<batch_id>)
```
- Export the result to a CSV file and save it to the same path as the script with name `verification_items_to_remove.csv`
- You need to login [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/) and open web dev console to get the cookie. 
Make sure you have `Uber Loader Access - Brand Workflow` ARA group on PROD (and DEV). You can file a ticket to get the PROD one.
- This script could run anywhere. Could be local/VDI or devbox wherever it can reach uber loader.

!!! warning
    This script runs very slowly. You need to be prepared to run it for a few days so find a reliable environment like devbox to run it.

```py
import csv
import requests

header = {
    "cookie": "<YOUR_COOKIE_FROM_UBER_LOADER_WEB_PAGE>"
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
}

def delete_verification_item(viId: str) -> None:
    try:
        res = requests.post(url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-item/delete/', headers=header, data="id="+viId)
        if res.status_code >= 400:
            print(f"delete failed with response code: {res.status_code}")
            return
        else:
            print(f"deleted with response code: {res.status_code}")
    except Exception as err:
        print(f"failed processing viId: {viId}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./verification_items_to_remove.csv") as csv_file:
    csv_reader, line_count, curation_batch_ids = csv.DictReader(csv_file), 0, set()
    for row in csv_reader:
        curation_batch_ids.add(row['ViBatchID'])
        print(f"Processing SKU: {row['ViSKU']} with ViID: {row['ViID']}")
        delete_verification_item(row['ViID'])
        line_count += 1
        if line_count % 100 == 0:
            print(f"{line_count} completed")
    print(f'Verification Item Delete Finished. Processed {line_count} lines')
    print(f"All verification items in {curation_batch_ids} are dropped")
```

## Update Batch Status

**Frequency Rating: 8/10**

**Could we do better so the script is no longer needed?** Yes and no. There are a lot of situations that could cause a batch to be in unexpected status.

**When and how to use this script:**

- When you or the stakeholders find that certain batches are in unexpected status to be corrected
- A SQL to find out this kind of batches
```sql
select top 2000 * from csn_product.dbo.tblCurationBatch where <YOUR_CONDITION>
```
- Export the result to a CSV file and save it to the same path as the script with name `curation_batches.csv`
- You need to login [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/) and open web dev console to get the cookie.
  Make sure you have `Uber Loader Access - Brand Workflow` ARA group on PROD (and DEV). You can file a ticket to get the PROD one.
- Update STATUS_TO_UPDATE_TO to you desired state number. Available statuses are:
```
1	Manual Not assigned
2	Manual Assigned
3	Manual In progress
4	Manual Curation complete
5	Manual QA complete
6	Manual Downstreamed
7	Automated Generated
8	Automated Downstreamed
9	Automated Post-Launch QA In Progress
10	Automated Post-Launch QA Complete
11	Automated Re-Downstreamed
12	Automated Post-Launch QA Assigned
13	Automated Re-Downstream In Progress
14	Automated Post-Launch QA In Progress
```
- This script could run anywhere. Could be local/VDI or devbox wherever it can reach uber loader.

```py
import csv
from http.client import RemoteDisconnected
import requests

header = {
    "cookie": "<YOUR_COOKIE_FROM_UBER_LOADER_WEB_PAGE>"
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
}

def updateBatchStatusId(id: str, newStatus: int) -> None:
    try:
        data = f"list_form_pk={id}&StatusID={newStatus}"
        print(f"Data to send: {data}")
        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/curation-batch/ajax/update/', headers=header, data=data)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for batch ID: {id} with response code: {res.status_code}")
            return
        else:
            print(f"updated for batch ID:  {id}  with response code: {res.status_code}")
    except RemoteDisconnected as err:
        print(f"failed processing batch id: {id}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./curation_batches.csv") as csv_file:
    csv_reader = csv.DictReader(csv_file)
    line_count = 0
    for row in csv_reader:
        print(f"Processing batch id: {row['ID']}")
        updateBatchStatusId(row['ID'], <STATUS_TO_UPDATE_TO>)
        line_count += 1
        if line_count % 100 == 0:
            print(f"{line_count} completed")
    print(f'Finished. Processed {line_count} lines')
```

## Update Logos For MaIDs

**Frequency Rating: 7/10**

**Could we do better so the script is no longer needed?** Yes and no. This is a regular request from stakeholders.
The only optimization is that we could create a UI for them to upload the logos to update so they self serve.

**When to use this script:**

- When the stakeholder (e.g. Sam Pavano) asks us to update with a list of MaIds and MaIreIds.

**Steps:**

1. Download the Excel file from as CSV from the Slack message from Sam
2. Place the file in your local folder for example C:\Users\<USER>\csv\logos.csv or any other folder and copy your csv file under this folder
3. Copy the below code and create a file with .py(example logos_update.py) extension and paste below code in it and save.
4. Update your folder path in the code `UPLOAD_FOLDER = os.path.join('<Your local CSV file folder path>', '<your csv file name>')`
5. Update the cookies in the code under header based Prod environment -> Login into -->> [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/)
      1. Go to-->> Manage Manufacturer and select Manufacturer option. 
      2. Open the developer tools and go to network tab. 
      3. You can use test data to update -->> Manufacturer Image Resource ID once you update it. 
      4. Go to -->> developer tools-->> Network -->> Name -->> click on /update -->> Go to Header -->> Request Headers -->> cookie= session= "It's value" (Key = Value)
      5. Paste the copied cookie in this code under header -->> cookie:"copied session=value"
      6. If you are using Dev environment the copy Dev cookie otherwise use the Prod cookie 
      7. If you are using Dev environment then Dev url or Prod environment then prod url
6. Once you are done with above code changes, you can go to your .py file location and open the command prompt to run this file.
7. After opening command prompt -->> python <your_python_code_file_name>.py 
8. You can verify your changes base on environment you used to update the Logos either Dev or Prod

```py
import os
import csv
import requests
from time import sleep

class Demo:
    UPLOAD_FOLDER = os.path.join('C:/Users/<USER>/csv', 'logos.csv')

    def read_logo_csv(self) -> None:
        data_lists = []
        count = 0
        with open(self.UPLOAD_FOLDER, 'r') as file:
            csvreader = csv.reader(file)
            next(csvreader)
            for row in csvreader:
                self.upload_manufacturer_logo({"list_form_pk": row[2], "MaIreID": row[6]})
                sleep(0.1)

    def upload_manufacturer_logo(self, data) -> None:

        header = {
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8;",
            "cookie": "session=.eJwdzrsRwzAIANBdVKcAIQTyMj4E6JLWjqtcds9ngXfvVfZ15Hkv2_O48lb2R5SthKu6Gw9A9-zkSEYKc1ku81ADk640PGVms9ZXYOiU2plmx0kSlTJTwVmsYThnpNUaUGsKhxCPpqtzhSlM4-cYq2RTMhjlG7nOPP4bRCDu8P4AIyMxSQ.ZEZqXQ.2imHlVKxA80Rf1oUiDHAO_43yVE"
        }

        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intradsm1.sdeconsul.csnzoo.com/manage-manufacturer/manufacturer/ajax/update/',
            headers=header,  data=data, verify=False)

        if res.status_code >= 200 and res.status_code < 300 :
            print(res.status_code)
            print("successfully uploaded the Manufacturer Logos for MaID: "+ data["list_form_pk"])
        elif res.status_code >=400 and res.status_code <=600:
            print(res.status_code)
            print("Failed to upload Manufacturer Logos for MaID: "+ data["list_form_pk"])

d = Demo();
d.read_logo_csv();
```

## Break a Large Batch Into Smaller Ones

**Frequency Rating: 5/10**

**Could we do better so the script is no longer needed?** Yes. Same reason with [above](#delete-verification-items-in-curation-batch)

**When to use this script:**

!!! warning
    Proceed with caution. Don't use it unless absolutely needed. The risk: it might break the skus in a single collection to multiple batches, where one single collection should be in only one single batch.

- Very large batches could be generated when there have been larger than usual curation projects from upstream or the job stopped running for a long time before it's fixed.
The stakeholders might reach out to us to break the larger batch to smaller ones. Usually a healthy batch should have less than **500** skus.
The goal is to break the larger batches to multiple ones with less than 500 skus each and make sure each of them could open in Curation Tool

**Steps:**

Say you are working on batchID: `12345` which has `5000` skus and cannot be opened in Curation Tool.
Since we'd like to limit it to 500, `9` more batches are to be created and each of them will have 500 skus at the end.

1. Run the SQL query to find the information about this batch and note it down:
```sql
select * from csn_product.dbo.tblCurationBatch batch with (nolock) where batch.ID in (12345)
```
2. Create `9` batches with the same information as batch 12345 using [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/curation-batch/) : Click `Insert New Row`.
Fill the information in step 1. Repeat until you have created the 9 batches. Run 
```sql
select top(100) * from csn_product.dbo.tblCurationBatch batch with (nolock) where batch.VerificationID in (@verificationId in step1)
``` 
to find out all the 9 + 1 batch. Suppose they are: 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009 and 12345
3. Now run this query to get all the 5000 skus and store them in a CSV:
```sql
select vi.* from csn_product.dbo.tblVerificationitem vi with (nolock) where vi.ViBatchID in (12345)
```
4. Now run the following py script to move `500` skus at a time to the 9 batches. You need to run 9 times exactly.
```py
import csv
from http.client import RemoteDisconnected
import requests

# login to https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/ and update the header cookie with yours from the browser

header = {
    "cookie": "<Your_Cookie>",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
}

def setBatchId(id: str, sku: str) -> None:
    try:
        data = f"list_form_pk={id}&ViBatchID={batch_id}"
        print(f"Data to send: {data}")
        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-item/ajax/update/', headers=header, data=data)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for SKU: {sku} with response code: {res.status_code}")
            return
        else:
            print(f"updated for SKU:  {sku}  with response code: {res.status_code}")
    except RemoteDisconnected as err:
        print(f"failed processing sku: {row['SKU']} id: {id}")
        print(f"unexpected {err=}, {type(err)=}")

batch_id = int(input("Enter destination batch id: "))
from_index = int(input("Enter from index (starting from 0): "))
offset = int(input("Enter offset: "))

with open("<Absolute_Path_To_The_CSV>") as csv_file:
    csv_reader = csv.DictReader(csv_file)
    line_count = 0
    for i, row in enumerate(csv_reader):
        if from_index <= i <= from_index+offset:
            print(f"Processing sku: {row['ViSKU']} at index {i}")
            setBatchId(row['ViID'], row['ViSKU'])
            line_count += 1
            if line_count % 100 == 0:
                print(f"{line_count} completed")
    print(f'Finished. Processed {line_count} lines')
```
This is a run example:
```shell
% /usr/bin/python3 /Users/<USER>/workspace/change_batch_id.py
Enter destination batch id: 20001
Enter from index (starting from 0): 0
Enter offset: 500

% /usr/bin/python3 /Users/<USER>/workspace/change_batch_id.py
Enter destination batch id: 20002
Enter from index (starting from 0): 500
Enter offset: 500

% /usr/bin/python3 /Users/<USER>/workspace/change_batch_id.py
Enter destination batch id: 20003
Enter from index (starting from 0): 1000
Enter offset: 500

...

% /usr/bin/python3 /Users/<USER>/workspace/change_batch_id.py
Enter destination batch id: 20009
Enter from index (starting from 0): 4000
Enter offset: 500
```

## Directly Send Manual Batches to WL API
**Frequency Rating: 7/10**

**Could we do better so the script is no longer needed?** Yes. This problem is cased by bug in the tool that was not able to send the batches to WL successfully. We need to fix the bugs.

**When and how to use this script:**

- The stakeholders complain about stuck batches that don't have ViVerificationID (is null)
- A SQL to find out all the manual verification items and batches that are in stuck state:
```sql
select vi.*, batch.BrandCatalogID from csn_product.dbo.tblVerificationitem vi with (nolock)
JOIN csn_product.dbo.tblCurationBatch batch WITH (NOLOCK) on vi.ViBatchID = batch.ID
where batch.CurationBatchProcessTypeID = 1 and batch.CurationRequestTypeID = 0 and batch.StatusID in (5,6) and batch.VerificationID is null and batch.CreatedAt > '2023-06-01' ORDER by batch.ID DESC
```
- Export the result to a CSV file and save it to the same path as the script with name `pa_manual_vi_to_wl.csv`
- You need to login [Vault](https://vault.service.csnzoo.com:8200/ui/vault/secrets) through Okta and open the [curation-job-consumer-client-secret-key](https://vault.service.csnzoo.com:8200/ui/vault/secrets/brand_workflows_assortment_platform%2Fprod%2Fkv%2Fsecrets/show/curation-job-consumer-client-secret-key)
- Note down its `value`
- Make a call to get a JWT to access the WL API
```shell
curl --location 'https://sso.auth.wayfair.com/oauth/token' \
--header 'Content-Type: application/json' \
--data '{
    "grant_type":"client_credentials",
    "client_id": "MBUZGSz2SWA16PP2nEPBBg",
    "client_secret": "<Secret_Value>"
}'
```
- Replace the `header` with the JWT
- This script could run anywhere that can reach out to https://kube-white-label-nextgen-api.service.intraiad1.consul.csnzoo.com/v1/batches/

```py
from collections import defaultdict
import csv
import json
import requests

# select vi.*, batch.BrandCatalogID from csn_product.dbo.tblVerificationitem vi with (nolock)
# JOIN csn_product.dbo.tblCurationBatch batch WITH (NOLOCK) on vi.ViBatchID = batch.ID
# where vi.ViExcludedReasonID = 0 and batch.CurationBatchProcessTypeID=1 and batch.CurationRequestTypeID =0 and batch.StatusID in (5,6) and batch.VerificationID is null and batch.CreatedAt > '2023-06-01' ORDER by batch.ID DESC

# {
#   "whiteLabelBatchId": 1577397,
#   "skus": [
#     {
#       "sku": "TSAG1462",
#       "targetManufacturerId": 44308,
#       "targetCollectionId": null,
#       "isExcluded": false
#     }
#   ],
#   "configuration": {
#     "changeProductName": true,
#     "changeCollection": true,
#     "changePartNumber": true,
#     "changeEAN": true,
#     "changeDisplaySku": true,
#     "enableScrubbing": true
#   }
# }


class VerficationItem:
    def __init__(self, sku, targetManufacturerId):
        self.sku = sku
        self.targetManufacturerId = targetManufacturerId
        self.isExcluded = False
        self.targetCollectionId = None


header = {
    "Authorization": "Bearer <JWT>",
    "Content-Type": "application/json",
}


def downstream_curation_batch(batch: dict) -> None:
    try:
        res = requests.post(
            url="https://kube-white-label-nextgen-api.service.intraiad1.consul.csnzoo.com/v1/batches/",
            headers=header,
            json=batch,
        )
        if res.status_code >= 400:
            print(f"downstream failed with response code: {res.status_code}; body: {res.json()}; request: {json.dumps(batch)}")
            return
        else:
            print(f"downstreamed with response code: {res.status_code}")
    except Exception as err:
        print(f"failed processing batch request: {json.dumps(batch)}")
        print(f"unexpected {err=}, {type(err)=}")


with open("./pa_manual_vi_to_wl.csv") as csv_file:
    csv_reader, curationBatchWithSkus = csv.DictReader(csv_file), defaultdict(list)
    for row in csv_reader:
        verficationItem = VerficationItem(row["ViSKU"], row["ViFinalBrandMaID"], False) if row["ViFinalBrandMaID"] != "NULL" else VerficationItem(row["ViSKU"], None, True)
        curationBatchWithSkus[row["ViBatchID"]].append(verficationItem)

    configuration = {
        "changeProductName": True,
        "changeCollection": True,
        "changePartNumber": True,
        "changeEAN": True,
        "changeDisplaySku": True,
        "enableScrubbing": True
    }

    counter = 0
    for batch, vis in curationBatchWithSkus.items():
        jsonTemplate = {}
        jsonTemplate["whiteLabelBatchId"] = batch
        jsonTemplate["configuration"] = configuration
        jsonTemplate["skus"] = [vi.__dict__ for vi in vis]
        downstream_curation_batch(jsonTemplate)
        counter += 1
    print(f"Processed all {counter} batches.")

```

## Update Project Stage
**Frequency Rating: 7/10**

**Could we do better so the script is no longer needed?** Yes. This problem is cased by bug in the tool that was not able to send the batches to WL successfully. We need to fix the bugs.

**When and how to use this script:**
!!! warning
        This script can ONLY be used after [Directly Send Manual Batches to WL API](#directly-send-manual-batches-to-wl-api) and share the same input csv with it.
- The stakeholders complain about stuck batches. 
- You have `pa_manual_vi_to_wl.csv` and ran [Directly Send Manual Batches to WL API](#directly-send-manual-batches-to-wl-api) already

```py
import csv
from http.client import RemoteDisconnected
import requests

def updateProjectStage(batchId: str) -> None:
    try:
        url = f"http://kube-brandworkflows-curation-service.service.intraiad1.consul.csnzoo.com/curation-batch/{batchId}/update-projects-to-white-label-stage"
        res = requests.post(url=url)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for batch ID: {id} with response code: {res.status_code}")
            return
        else:
            print(f"updated the project stage for batch ID: {batchId} with response code: {res.status_code} and content: {res.content}")
    except RemoteDisconnected as err:
        print(f"failed processing batch id: {id}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./pa_manual_vi_to_wl.csv") as csv_file:
    csv_reader, unique_batch_ids = csv.DictReader(csv_file), set()
    line_count = 0
    for row in csv_reader:
        unique_batch_ids.add(row["ViBatchID"])
    
    for batch_id in unique_batch_ids:
        updateProjectStage(batch_id)
    print(f"All {len(unique_batch_ids)} batch ids were processed")
```

## Set Verification ID

**Frequency Rating: 7/10**

**Could we do better so the script is no longer needed?** Yes. All the skus are WLed correctly.

**When and how to use this script:**

!!! warning
        This script can ONLY be used after [Directly Send Manual Batches to WL API](#directly-send-manual-batches-to-wl-api) and [Update Project Stage](#update-project-stage) and share the same input csv with it.

- There is some logic in the code that demands `ViVerificationID` to be NOT null. We can use this script to set it to -1 (or another value).
- You have `pa_manual_vi_to_wl.csv` and ran [Directly Send Manual Batches to WL API](#directly-send-manual-batches-to-wl-api) and [Update Project Stage](#update-project-stage) already
- You need to login [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/) and open web dev console to get the cookie.
  Make sure you have `Uber Loader Access - Brand Workflow` ARA group on PROD (and DEV). You can file a ticket to get the PROD one.
- This script could run anywhere. Could be local/VDI or devbox wherever it can reach uber loader.


```py
import csv
from http.client import RemoteDisconnected
import requests

header = {
    "cookie": "<COOKIE_FROM_DEV_CONSOLE_ON_UBER_LOADER>"
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
}

def setVerificationId(id: str, sku: str, verificationId: int) -> None:
    try:
        data = f"list_form_pk={id}&ViVerificationID={verificationId}"
        print(f"Data to send: {data}")
        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-item/ajax/update/', headers=header, data=data)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for SKU: {sku} with response code: {res.status_code}")
            return
        else:
            print(f"updated for SKU:  {sku}  with response code: {res.status_code}")
    except RemoteDisconnected as err:
        print(f"failed processing sku: {sku}")
        print(f"unexpected {err=}, {type(err)=}")

def setVerificationIdForBatch(id: str, verificationId: int) -> None:
    try:
        data = f"list_form_pk={id}&VerificationID={verificationId}"
        print(f"Data to send: {data}")
        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/curation-batch/ajax/update/', headers=header, data=data)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for batch: {id} with response code: {res.status_code}; body: {res.content}")
            return
        else:
            print(f"updated for batch: {id} with response code: {res.status_code}")
    except RemoteDisconnected as err:
        print(f"failed processing batch: {id}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./pa_manual_vi_to_wl.csv") as csv_file:
    csv_reader = csv.DictReader(csv_file)
    line_count = 0
    for row in csv_reader:
        print(f"Processing sku: {row['ViSKU']} and batch id: {row['ViBatchID']}")
        setVerificationId(row['ViID'], row['ViSKU'], -1)
        setVerificationIdForBatch(row['ViBatchID'], 986472)
        line_count += 1
        if line_count % 100 == 0:
            print(f"{line_count} completed")
    print(f'Finished. Processed {line_count} lines')
```

## Update Batch Status

**Frequency Rating: 8/10**

**Could we do better so the script is no longer needed?** Yes and no. There are a lot of situations that could cause a batch to be in unexpected status.

**When and how to use this script:**

- When you or the stakeholders find that certain batches are in unexpected status to be corrected
- Usually used after [Directly Send Manual Batches to WL API](#directly-send-manual-batches-to-wl-api), [Update Project Stage](#update-project-stage) and [Set Verification ID](#set-verification-id) already
- Assume that you have `pa_manual_vi_to_wl.csv` and ran the above 3 scripts 
- You need to login [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/) and open web dev console to get the cookie.
  Make sure you have `Uber Loader Access - Brand Workflow` ARA group on PROD (and DEV). You can file a ticket to get the PROD one.
- Update `STATUS_TO_UPDATE_TO` to you desired state number. Most of the time you need to use `6`. Available statuses are:
```
1	Manual Not assigned
2	Manual Assigned
3	Manual In progress
4	Manual Curation complete
5	Manual QA complete
6	Manual Downstreamed
7	Automated Generated
8	Automated Downstreamed
9	Automated Post-Launch QA In Progress
10	Automated Post-Launch QA Complete
11	Automated Re-Downstreamed
12	Automated Post-Launch QA Assigned
13	Automated Re-Downstream In Progress
14	Automated Post-Launch QA In Progress
```
- This script could run anywhere. Could be local/VDI or devbox wherever it can reach uber loader.

```py
import csv
from http.client import RemoteDisconnected
import requests

header = {
    "cookie": "<YOUR_COOKIE_FROM_UBER_LOADER_WEB_PAGE>"
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
}

def updateBatchStatusId(id: str, newStatus: int) -> None:
    try:
        data = f"list_form_pk={id}&StatusID={newStatus}"
        print(f"Data to send: {data}")
        res = requests.post(
            url='https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/curation-batch/ajax/update/', headers=header, data=data)  # verify=False - need for removing cert issues
        if res.status_code >= 400:
            print(f"update failed for batch ID: {id} with response code: {res.status_code}")
            return
        else:
            print(f"updated for batch ID:  {id}  with response code: {res.status_code}")
    except RemoteDisconnected as err:
        print(f"failed processing batch id: {id}")
        print(f"unexpected {err=}, {type(err)=}")

with open("./pa_manual_vi_to_wl.csv") as csv_file:
    csv_reader, unique_batch_ids = csv.DictReader(csv_file), set()
    line_count = 0

    for row in csv_reader:
        unique_batch_ids.add(row["ViBatchID"])
    
    for batch_id in unique_batch_ids:
        updateBatchStatusId(batch_id, <STATUS_TO_UPDATE_TO>)
        line_count += 1
        if line_count % 100 == 0:
            print(f"{line_count} completed")
    print(f'Finished. Processed {line_count} lines')
```
