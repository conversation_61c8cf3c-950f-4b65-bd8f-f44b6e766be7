<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment;

class Exclusivity_Assortment_Filter_Request_DTO implements Exclusivity_Assortment_Filter_Request {

  /**
   * @var bool
   */
  private $with_rebrand_project_only = false;

  /**
   * @var array
   */
  private $selected_skus = [];

  /**
   * @param Exclusivity_Assortment_Filter_Request $filter Filter
   *
   * @return Exclusivity_Assortment_Filter_Request_DTO
   */
  public static function from_filter_request(Exclusivity_Assortment_Filter_Request $filter) : self {
    $filter_dto = new Exclusivity_Assortment_Filter_Request_DTO();
    $filter_dto->set_with_rebrand_project_only($filter->get_with_rebrand_project_only());
    $filter_dto->set_selected_skus($filter->get_selected_skus());

    return $filter_dto;
  }

  /**
   * @var int|null
   */
  private $status = null;

  /**
   * @var int|null
   */
  private $investment_status = null;

  /**
   * @var int|null
   */
  private $whitelabel_status = null;

  /**
   * @param bool $fetch_only_rebrand_project_skus Flag
   *
   * @return void
   */
  public function set_with_rebrand_project_only(bool $fetch_only_rebrand_project_skus) : void {
    $this->with_rebrand_project_only = $fetch_only_rebrand_project_skus;
  }

  /**
   * @return bool
   */
  public function get_with_rebrand_project_only() : bool {
    return $this->with_rebrand_project_only;
  }

  /**
   * @return array
   */
  public function get_selected_skus() : array {
    return $this->selected_skus;
  }

  /**
   * @param array $selected_skus selected_skus
   *
   * @return void
   */
  public function set_selected_skus(array $selected_skus) {
    $this->selected_skus = $selected_skus;
  }

  /**
   * @return int|null
   */
  public function get_status() : ?int {
    return $this->status;
  }

  /**
   * @param int $status the status
   *
   * @return void
   */
  public function set_status(int $status) : void {
    $this->status = $status;
  }

  /**
   * @param int $investment_status investment status
   *
   * @return void
   */
  public function set_investment_status(int $investment_status) : void {
    $this->investment_status = $investment_status;
  }

  /**
   * @return int|null
   */
  public function get_investment_status() : ?int {
    return $this->investment_status;
  }

  /**
   * @param int $whitelabel_status whitelabel status
   *
   * @return void
   */
  public function set_whitelabel_status(int $whitelabel_status) : void {
    $this->whitelabel_status = $whitelabel_status;
  }

  /**
   * @return int|null
   */
  public function get_whitelabel_status() : ?int {
    return $this->whitelabel_status;
  }
}
