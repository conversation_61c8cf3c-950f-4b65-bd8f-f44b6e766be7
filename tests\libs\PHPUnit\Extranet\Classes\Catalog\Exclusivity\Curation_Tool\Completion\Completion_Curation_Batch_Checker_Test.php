<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;

class Completion_Curation_Batch_Checker_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface
     */
    private $sectionLoader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section
     */
    private $section;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->sectionLoader = $this->prophesize(Section_Loader_Interface::class);
        $this->section = $this->prophesize(Section::class);

        $this->subject = new Completion_Curation_Batch_Checker($this->sectionLoader->reveal());
    }

    /**
     * @param int|null $manufacturerId    Manufacturer ID
     * @param int|null $exclusionReasonId Exclusion Reason ID
     * @param bool     $expectedResult    Expected Result
     *
     * @test
     *
     * @dataProvider get_curation_item_data
     * @return void
     */
    public function it_checks_if_it_is_ready($manufacturerId, $exclusionReasonId, bool $expectedResult)
    {
        $batchId = 1;
        $this->section->get_title()->willReturn('Simple SKU');
        $this->section->get_curation_items()->willReturn([$this->get_curation_item_param($manufacturerId, $exclusionReasonId)]);

        $this->sectionLoader->getSections($batchId)->willReturn([$this->section]);
        $actualResult = $this->subject->isCurationCompleted($batchId);

        $this->assertEquals($expectedResult, $actualResult);
    }

    /**
     * @return array
     */
    public function get_curation_item_data(): array
    {
        return [
            ['manufacturerId' => null, 'exclusionReasonId' => null, 'expectedResult' => false],
            ['manufacturerId' => 0, 'exclusionReasonId' => 0, 'expectedResult' => false],
            ['manufacturerId' => 1, 'exclusionReasonId' => null, 'expectedResult' => true],
            ['manufacturerId' => null, 'exclusionReasonId' => 1, 'expectedResult' => true],
            ['manufacturerId' => 1, 'exclusionReasonId' => 0, 'expectedResult' => true],
            ['manufacturerId' => 0, 'exclusionReasonId' => 1, 'expectedResult' => true],
        ];
    }

    /**
     * @param int|null $manufacturerId    Manufacturer ID
     * @param int|null $exclusionReasonId Exclusion Reason Id
     *
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item
     */
    private function get_curation_item_param($manufacturerId, $exclusionReasonId)
    {
        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $curationItem */
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_final_brand_id()->willReturn($manufacturerId);
        $curationItem->get_excluded_reason_id()->willReturn($exclusionReasonId);
        $curationItem->get_sku()->willReturn('ICAL1381');
        $curationItem->jsonSerialize()->willReturn(
            [
                'sku' => 'ICAL1381',
                'name' => 'Smart Rods No Measuring Easy Install Adjustable Single Curtain Rod With Ball Finials, Black',
                'manufacturer' => 'Zenna Home',
                'manufacturerBrwId' => 0,
                'brandType' => 0,
                'class' => 'Curtain Hardware & Accessories',
                'suppliers' => [],
                'isCanadianSupplier' => false,
                'price' => 9999,
                'priceOptionsCount' => 2,
                'image' => null,
                'lastCloneDate' => null,
                'isRecentlyCloned' => false,
                'savedAt' => '2022-01-01 01:01:00',
                'savedBy' => 'Roman Tymoshyk',
                'rejectedAt' => null,
                'rejectedBy' => null,
                'rejectedNote' => null,
                'url' => null,
                'type' => 'simple',
                'isKitsco' => false,
                'kitParents' => [],
                'relatedKits' => [],
                'readonly' => false,
                'brandCatalogName' => 'Wayfair US',
                'isWrongContinent' => false,
                'decision' =>
                    [
                        'exclusionReasonId' => 0,
                        'priceTier' => 4,
                        'styleId' => null,
                        'substyleId' => null,
                        'manufacturerId' => null,
                        'granularStyleId' => null,
                        'isSuggestedStyleMatched' => false,
                        'styleDecisionNotes' => null
                    ],
                'suggestedStyles' => [],
                'suggestedSubstyles' => [],
                'shouldMoveToHeaderBrand' => false,
                'shouldMoveToTailBrand' => false,
                'isPredictedWinner' => false,
                'isHoldoutManufacturer' => false
            ]
        );

        return $curationItem;
    }
}
