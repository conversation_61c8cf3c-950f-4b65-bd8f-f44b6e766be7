<?php

declare(strict_types=1);

namespace App\Application\Service\Security;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Infrastructure\Helper\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

class ToolAvaila<PERSON><PERSON><PERSON><PERSON> implements ToolAvailabilityCheckerInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use LoggerTrait;

    protected const DISABLE_MERCH_ENG_CURATION_C6 = 'disable_merch_eng_curation_c6';
    protected const DISABLE_CURATION_TOOL = 'disable_merch_curation_tool';

    public const DEFAULT_FEATURE_TOGGLE_STATE = [
        self::DISABLE_MERCH_ENG_CURATION_C6 => false,
        self::DISABLE_CURATION_TOOL => false,
    ];

    private FeatureTogglesInterface $featureToggles;

    public function __construct(FeatureTogglesInterface $featureToggles)
    {
        $this->featureToggles = $featureToggles;
    }

    public function isToolDisabled(): bool
    {
        return $this->featureToggles->isEnabled(static::DISABLE_MERCH_ENG_CURATION_C6) ||
            $this->featureToggles->isEnabled(static::DISABLE_CURATION_TOOL);
    }
}
