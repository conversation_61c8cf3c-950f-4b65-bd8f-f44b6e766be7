<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\ProductManagement\Populator;

class Closure_Populator implements Populator_Interface {
  /**
   * @var \Closure
   */
  private $populator_closure;

  /**
   * @param \Closure $populator_closure closure which performs the population
   */
  public function __construct(\Closure $populator_closure) {
    $this->populator_closure = $populator_closure;
  }

  /**
   * @param object $subject subject for population
   * @param array  $map     property map
   *
   * @return object
   */
  public function populate($subject, $map) {
    ($this->populator_closure)($subject, $map);

    return $subject;
  }
}
