<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Batch_SKU_Service {
  /**
   * @var Batch_SKU_DAO
   */
  private $dao;

  /**
   * @var Batch_SKU_Postgresql_DAO
   */
  private $batch_sku_postgresql_dao;

  /**
   * @var \WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory
   */
  private $batch_factory;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;

  /**
   * Batch_SKU_Service constructor.
   *
   * @param Batch_SKU_DAO $dao           DAO
   * @param \WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory $batch_factory model factory
   * @param \WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO $psql_dao DAO
   * @param FeatureTogglesInterface $featureToggles  featureToggles
   */
  public function __construct(
      Batch_SKU_DAO $dao,
      Batch_Factory $batch_factory,
      Batch_SKU_Postgresql_DAO $psql_dao,
      FeatureTogglesInterface $featureToggles
  ) {
    $this->dao           = $dao;
    $this->batch_factory = $batch_factory;
    $this->batch_sku_postgresql_dao=$psql_dao;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch object
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function load_verified_skus(Batch $batch) : Batch {

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $verified_skus = $this->batch_sku_postgresql_dao->get_batch_skus($batch);
    }else{
      $verified_skus = $this->dao->get_batch_skus($batch);
    }

    foreach ($verified_skus as $sku_map) {
      $verified_sku = $this->batch_factory->create_verified_batch_sku($sku_map);
      $batch->push($verified_sku);
      if ($this->check_kit_child($verified_sku)) {
        $batch = $this->load_kit_parents($batch, $verified_sku);
      }

    }

    return $batch;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch              $batch              Batch object
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku Verified Batch SKU object
   *
   * @return \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
   */
  public function load_kit_parents(Batch $batch, Verified_Batch_SKU $verified_batch_sku) : Batch {


    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $kit_parent_skus = $this->batch_sku_postgresql_dao->get_kit_parent_skus($verified_batch_sku);
    }else{
      $kit_parent_skus = $this->dao->get_kit_parent_skus($verified_batch_sku);
    }

    foreach ($kit_parent_skus as $sku) {
      $parent_verified_batch_sku = $this->batch_factory->copy_verified_batch_sku($verified_batch_sku, $sku);
      $parent_verified_batch_sku->set_is_kit_parent(1);
      $verified_batch_sku->add_parent($sku);
      $batch->push($parent_verified_batch_sku);
    }

    return $batch;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku Verified Batch SKU object
   *
   * @return bool
   */
  public function check_kit_child($verified_batch_sku) : bool {
    if ($verified_batch_sku->is_excluded_from_wl()
        || $verified_batch_sku->is_kitsco()
        || !$verified_batch_sku->is_kit_component()
    ) {
      return false;
    }

    return true;
  }
}
