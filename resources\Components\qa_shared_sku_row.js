/**
 * Presents the shared sku between kits
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Column, Grid, IconV2 as Icon, Text, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import {faCheckCircle, faCircleNotch} from "@fortawesome/free-solid-svg-icons";
import CurationSkuImage from './curation_sku_image';
import CurationSkuSupplier from './curation_sku_supplier';
import CurationSkuManufacturer from './curation_sku_manufacturer';
import CurationSkuName from './curation_sku_name';
import './curation_shared_sku.scss';
import Translation from '@wayfair/translation';
import QASaveColumn from './qa_save_column';
import CurationToolShapes, {
  QA_STATUS_PENDING,
  QA_STATUS_ACCEPTED,
  QA_STATUS_UPDATED,
} from './curation_tool_shapes';

const QAKitComponentKitscoStatusLayout = ({iconName, message}) => (
  <Grid alignItems="center">
    <Column size={3}>
      <Icon icon={iconName} className="fa-3x text_center" />
    </Column>
    <Column size={9}>
      <Text fontStyle="bold">{message}</Text>
    </Column>
  </Grid>
);

QAKitComponentKitscoStatusLayout.propTypes = {
  iconName: PropTypes.string.isRequired,
  message: PropTypes.string.isRequired,
};

const QAKitComponentKitscoStatus = ({isKitsco}) => {
  let iconName = faCircleNotch;
  let message = (
    <Translation msgid="CurationTool.QAKitComponentKitscoStatusRemovedFromKitsco" />
  );

  if (isKitsco) {
    iconName = faCheckCircle;
    message = (
      <Translation msgid="CurationTool.QAKitComponentKitscoStatusSavedAsKitsco" />
    );
  }

  return (
    <QAKitComponentKitscoStatusLayout iconName={iconName} message={message} />
  );
};

QAKitComponentKitscoStatus.propTypes = {
  isKitsco: PropTypes.bool.isRequired,
};

class QASharedSkuRow extends React.Component {
  static propTypes = {
    curationItem: CurationToolShapes.curationItemShape.isRequired,
    onSave: PropTypes.func,
    isAutomaticCurationPostQaEnabled: PropTypes.bool,
  };

  static defaultProps = {
    curationItem: {},
    onSave() {},
    isAutomaticCurationPostQaEnabled: false,
  };

  shouldComponentUpdate(nextProps) {
    const item = this.props.curationItem;

    return (
      (item.qaStatus === QA_STATUS_ACCEPTED) !==
        (nextProps.curationItem.qaStatus === QA_STATUS_ACCEPTED) ||
      (item.qaStatus === QA_STATUS_PENDING) !==
        (nextProps.curationItem.qaStatus === QA_STATUS_PENDING)
    );
  }

  render() {
    return (
      <div className="QASharedSku">
        <Grid>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.sku}</Text>
          </Column>
          <Column size={1}>
            <CurationSkuName
              name={this.props.curationItem.name}
              url={this.props.curationItem.url}
            />
          </Column>
          <Column size={1}>
            <CurationSkuManufacturer
              name={this.props.curationItem.manufacturer}
              brwid={this.props.curationItem.manufacturerBrwId}
              brandType={this.props.curationItem.brandType}
              brandName={this.props.curationItem.brandName}
            />
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.class}</Text>
          </Column>
          <Column size={1}>
            <CurationSkuSupplier
              names={this.props.curationItem.suppliers}
              isCanadian={this.props.curationItem.isCanadianSupplier}
            />
          </Column>
          <Column size={1}>
            <div className="text_center">
              <p>
                <Translation msgid="CurationTool.QASharedSkuRowKitParents" />
              </p>
              <p>
                {this.props.curationItem.kitParents.map(sku => (
                  <span key={sku} className="display_block">
                    {sku}
                  </span>
                ))}
              </p>
            </div>
          </Column>
          <Column size={2}>
            <CurationSkuImage
              image={this.props.curationItem.image}
              sku={this.props.curationItem.sku}
            />
          </Column>
          <Column size={3}>
            {!this.props.curationItem.readonly && (
              <QAKitComponentKitscoStatus
                isKitsco={this.props.curationItem.isKitsco}
              />
            )}
          </Column>
          <Column size={1}>
            {!this.props.curationItem.readonly && (
              <QASaveColumn
                onSave={qaStatus =>
                  this.props.onSave({
                    sku: this.props.curationItem.sku,
                    qaStatus,
                  })
                }
                isApproved={
                  this.props.curationItem.qaStatus === QA_STATUS_ACCEPTED
                }
                isUpdated={
                  this.props.curationItem.qaStatus === QA_STATUS_UPDATED
                }
                isPending={
                  this.props.curationItem.qaStatus === QA_STATUS_PENDING
                }
                isAutomaticCurationPostQaEnabled={
                  this.props.isAutomaticCurationPostQaEnabled
                }
              />
            )}
          </Column>
        </Grid>
      </div>
    );
  }
}

export default QASharedSkuRow;
