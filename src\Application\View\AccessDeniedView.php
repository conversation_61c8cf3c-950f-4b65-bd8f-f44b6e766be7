<?php

declare(strict_types=1);

namespace App\Application\View;

class AccessDeniedView extends BaseView
{
    protected const COMPONENT_KEY = 'AccessDeniedEntry';

    protected string $errorMessage;

    public function __construct(string $errorMessage = 'Access Denied')
    {
        parent::__construct();
        $this->errorMessage = $errorMessage;
    }

    public function errorMessage(): string
    {
        return $this->errorMessage;
    }

    protected function componentKey(): string
    {
        return static::COMPONENT_KEY;
    }
}
