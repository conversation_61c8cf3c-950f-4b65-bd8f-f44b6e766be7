<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Entity;

class Historical_Suggested_Style_Collection {
  /**
   * @var Historical_Suggested_Style[]
   */
  private $sku_infos = [];

  /**
   * @param Historical_Suggested_Style ...$sku_infos sku infos
   */
  private function __construct(Historical_Suggested_Style ...$sku_infos) {
    foreach ($sku_infos as $info) {
      $this->sku_infos[$info->get_sku()] = $info;
    }
  }

  /**
   * @param string $sku sku
   *
   * @return Historical_Suggested_Style
   */
  public function get_for_sku(string $sku) : Historical_Suggested_Style {
    return $this->sku_infos[$sku] ?? Historical_Suggested_Style_Missing::for_sku($sku);
  }

  /**
   * @param Historical_Suggested_Style ...$sku_infos sku infos
   *
   * @return Historical_Suggested_Style_Collection
   */
  public static function create(Historical_Suggested_Style ...$sku_infos) : self {
    return new self(...$sku_infos);
  }
}
