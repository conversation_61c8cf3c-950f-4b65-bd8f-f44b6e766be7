<?php
/**
 * Tests for the \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;

class Curation_Request_Model_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * Test Add SKU
     *
     * @param int $number Number of objects
     *
     * @dataProvider add_sku_data_provider
     *
     * @return void
     */
    public function test_add_sku(int $number)
    {
        $subject = new Curation_Request_Model();

        for ($i = 0; $i < $number; $i++) {
            $sku_model = $this->prophesize(Curation_Request_SKU_Base_Model::class);
            $subject->add_sku($sku_model->reveal());
        }

        $this->assertCount($number, $subject->get_sku_list());
    }

    /**
     * @return array
     */
    public function add_sku_data_provider()
    {
        return [
            [0],
            [1],
            [99]
        ];
    }
}
