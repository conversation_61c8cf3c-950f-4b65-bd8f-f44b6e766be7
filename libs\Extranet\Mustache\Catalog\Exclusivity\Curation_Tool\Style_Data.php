<?php

declare (strict_types=1);

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool;


class Style_Data implements \JsonSerializable {

  /**
   * @var int
   */
  private $id;

  /**
   * @var string
   */
  private $name;

  /**
   * Style_Data constructor.
   *
   * @param int    $id   ID
   * @param string $name Name
   */
  public function __construct(int $id = null, string $name = '') {
    $this->id   = $id;
    $this->name = $name;
  }

  /**
   * @return int
   */
  public function value() {
    return $this->id;
  }

  /**
   * @return string
   */
  public function text() : string {
    return $this->name;
  }

    public function jsonSerialize()
    {
        return [
            'value' => $this->value(),
            'text' => $this->text(),
        ];
    }
}
