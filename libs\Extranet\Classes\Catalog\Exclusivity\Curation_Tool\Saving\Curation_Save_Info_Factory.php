<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;

class Curation_Save_Info_Factory {

  /**
   * @param string                   $savedAt Saved At Date
   * @param string                   $savedBy Saved by
   * @param Curation_Decision_Source $source  Decision Source
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Save_Info
   */
  public function getCurationSaveInfo(string $savedAt, string $savedBy, Curation_Decision_Source $source, array $skus) : Curation_Save_Info {
    return new Curation_Save_Info($savedAt, $savedBy, $source, $skus);
  }
}
