<?php

namespace App\Tests\libs\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Api\Production_Tracking_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Curation_Production_Tracking_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Legacy_Production_Tracking_Processor;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Process_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO;
use WF\Secrets\SecretProviderInterface;

class Curation_Production_Tracking_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Production_Tracking_Process_Factory
     */
    private $pt_factory;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage_production_tracking_sql_dao;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage_production_tracking_psql_dao;


    /**
     * @var LoggerAwareInterface|LoggerInterface
     */
    private $curation_production_tracking;




    /**
     * @var Legacy_Production_Tracking_Processor
     */
    private $legacy_pt_processor;

    /**
     * @var Production_Tracking_Api_Processor
     */
    private $pt_api_processor;

    private SecretProviderInterface $secretProvider;



    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->storage_production_tracking_sql_dao = $this->prophesize(Curation_Production_Tracking_DAO::class);
        $this->storage_production_tracking_psql_dao = $this->prophesize(Curation_Production_Tracking_PostgreSQL_DAO::class);

        $this->legacy_pt_processor = $this->prophesize(Legacy_Production_Tracking_Processor::class);
        $this->pt_api_processor = $this->prophesize(Production_Tracking_Api_Processor::class);
        $this->secretProvider = $this->createMock(SecretProviderInterface::class);

        $this->pt_factory = new Production_Tracking_Process_Factory(
            $this->legacy_pt_processor->reveal(),
            $this->pt_api_processor->reveal(),
            $this->secretProvider
        );

        $this->curation_production_tracking = new Curation_Production_Tracking_Service(
            $this->storage_production_tracking_sql_dao->reveal(),
            $this->pt_factory,
            $this->storage_production_tracking_psql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * Test
     */
    public function test_get_project_ids_feature_toggle_false()
    {
        $this->storage_production_tracking_sql_dao->get_project_ids(Argument::exact(101))->willReturn([]);
        $response = $this->curation_production_tracking->get_project_ids(101);
        $this->assertEquals([], $response);
    }

    /**
     * Test
     */
    public function test_get_project_ids_feature_toggle_true()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->storage_production_tracking_psql_dao->get_project_ids(Argument::exact(102))->willReturn([]);
        $response = $this->curation_production_tracking->get_project_ids(102);
        $this->assertEquals([], $response);
    }
}
