<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluator;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluator_Interface;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

final class Batch_Reevaluator {

  /**
   * @var Reevaluator_Interface
   */
  private $reevaluator;

  /**
   * @var Batch_Evaluator
   */
  private $batch_evaluator;

  /**
   * @var Batch_Evaluation_Tracker
   */
  private $batch_evaluation_tracker;

  /**
   * Batch_Reevalua<PERSON> constructor.
   *
   * @param Reevaluator_Interface    $reevaluator              Reevaluator
   * @param Batch_Evaluator          $batch_evaluator          Batch evaluator
   * @param Batch_Evaluation_Tracker $batch_evaluation_tracker Batch evaluation tracker
   */
  public function __construct(
      Reevaluator_Interface $reevaluator,
      Batch_Evaluator $batch_evaluator,
      Batch_Evaluation_Tracker $batch_evaluation_tracker
  ) {
    $this->reevaluator              = $reevaluator;
    $this->batch_evaluator          = $batch_evaluator;
    $this->batch_evaluation_tracker = $batch_evaluation_tracker;
  }

  /**
   * @param Batch $batch Batch to reevaluate
   *
   * @return void
   */
  public function reevaluate(Batch $batch) : void {
    $evaluation_of_batch = $this->batch_evaluator->evaluate_batch($batch);
    $this->batch_evaluation_tracker->save_batch_evaluation_result($evaluation_of_batch);

    if ($evaluation_of_batch->get_status() === Batch_Evaluation_Status::ready()) {
      $this->reevaluator->reevaluate($batch);
    }
  }
}
