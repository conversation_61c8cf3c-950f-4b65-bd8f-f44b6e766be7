<?php

declare(strict_types=1);

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use App\Application\DTO\SaveCurationDecision;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Suggestion_Info_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Save_Info_Factory;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function date;
use function sprintf;

class SaveController extends AbstractBaseController
{
    /**
     * @param Curation_Decision_Service $curation_decision_manager the decision manager
     * @param Automatic_Suggestion_Info_Saver $suggested_style_info_saver Suggested Style Info Saver
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param Curation_Save_Info_Factory $save_info_factory
     * @param SaveCurationDecision $curationDecision
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/save", methods={"POST"})
     */
    public function __invoke(
        Curation_Decision_Service $curation_decision_manager,
        Automatic_Suggestion_Info_Saver $suggested_style_info_saver,
        Completion_Batch_Data_Service $batch_data_service,
        Curation_Save_Info_Factory $save_info_factory,
        SaveCurationDecision $curationDecision
    ): JsonResponse {
        $batch_id = $curationDecision->getBatchId();
        $skus = $curationDecision->getSkus();

        try {
            if (!$this->isExpectedBatchStatus(
                $batch_id,
                [Batch::STATUS_MANUAL_UNASSIGNED, Batch::STATUS_MANUAL_ASSIGNED, Batch::STATUS_MANUAL_IN_PROGRESS],
                $batch_data_service,
            )) {
                $this->warning(
                    sprintf('Failed to save batch with id=%d. Unexpected batch status.', $batch_id),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'skus' => $skus]
                );

                return $this->json([], Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            $exclusion_reason_id = $curationDecision->getExclusionReasonId();
            $price_tier = $curationDecision->getPriceTier();
            $style_id = $curationDecision->getStyleId();
            $substyle_id = $curationDecision->getSubStyleId();
            $brand_id = $curationDecision->getBrandId();
            $suggested_notes = $curationDecision->getSuggestedNotes();
            $suggested_reason_in_url = $curationDecision->getSuggestedReason();
            $granular_style_id = $curationDecision->getGranularStyleId();

            $this->info(
                'Creating Suggested Reason from suggested_reason_in_url',
                [
                    'url' => $suggested_reason_in_url,
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'skus' => $skus
                ]
            );

            $suggested_reason = $suggested_reason_in_url !== '' ? Curation_Decision_Style_Rejection_Reason::create_from_label(
                $suggested_reason_in_url
            )->value() : null;

            $saved_at = date('Y-m-d H:i:s');

            if ($brand_id > 0 && $exclusion_reason_id === 0) {    // curated skus
                $this->info(
                    'Saving curated info',
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                        'skus' => $skus
                    ]
                );
                $curation_decision_manager->save_curated(
                    $batch_id,
                    $skus,
                    $price_tier,
                    $style_id,
                    $substyle_id,
                    $brand_id,
                    $granular_style_id,
                    $saved_at,
                    $this->getEmployeeId(),
                    Curation_Decision_Source::manual()
                );
                $this->info(
                    'Saving suggested info for curated SKUs',
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                        'skus' => $skus
                    ]
                );
                $suggested_style_info_saver->save_suggested_info_for_curated_skus(
                    $batch_id,
                    $skus,
                    $style_id,
                    $substyle_id,
                    $saved_at,
                    $suggested_notes,
                    $suggested_reason
                );
                $this->debug('Saved curated');
            } else {                // excluded skus
                $this->info(
                    'Saving excluded SKUs',
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                        'skus' => $skus
                    ]
                );
                $curation_decision_manager->save_excluded(
                    $batch_id,
                    $skus,
                    $exclusion_reason_id,
                    $saved_at,
                    $this->getEmployeeId(),
                    Curation_Decision_Source::manual(),
                    $style_id,
                    $substyle_id
                );
                $this->info(
                    'Removing suggested info for excluded SKUs',
                    [
                        'batch_id' => $batch_id,
                        'employee_id' => $this->getEmployeeId(),
                        'skus' => $skus
                    ]
                );
                $suggested_style_info_saver->remove_suggested_info_for_excluded_skus($batch_id, $skus);
                $this->debug('Saved excluded');
            }

            $this->info(
                'Loading save info',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId()
                ]
            );

            $employee = $this->getUser();
            $employee_name = sprintf('%s %s', $employee->firstName(), $employee->lastName());

            $result = $save_info_factory->getCurationSaveInfo(
                $saved_at,
                $employee_name,
                Curation_Decision_Source::manual(),
                $skus
            );

            return $this->json($result);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('Failed to save batch: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );
            throw $exception;
        }
    }
}
