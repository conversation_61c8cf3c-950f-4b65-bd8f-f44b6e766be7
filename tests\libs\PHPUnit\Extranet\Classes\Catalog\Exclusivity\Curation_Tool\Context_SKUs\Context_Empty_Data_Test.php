<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Empty_Data;

class Context_Empty_Data_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_is_not_considered_fully_filled()
    {
        $subject = new Context_Empty_Data();

        $this->assertFalse($subject->is_fully_filled());
    }
}
