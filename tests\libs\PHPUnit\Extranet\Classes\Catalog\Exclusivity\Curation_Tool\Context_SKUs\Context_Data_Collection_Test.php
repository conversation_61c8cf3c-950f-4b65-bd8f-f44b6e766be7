<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Empty_Data;

class Context_Data_Collection_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_returns_empty_data_when_sku_does_not_exist()
    {
        $data = $this->prophesize(Context_Data::class);

        $subject = new Context_Data_Collection();

        $subject->set_for_sku('SKU1', $data->reveal());
        $existing_data = $subject->get_for_sku('SKU1');
        $this->assertEquals($data->reveal(), $existing_data);

        $no_data_for_sku = $subject->get_for_sku('SKU2');
        $this->assertInstanceOf(Context_Empty_Data::class, $no_data_for_sku);
    }
}
