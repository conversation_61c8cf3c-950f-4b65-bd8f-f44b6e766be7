<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_Postgres_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Style_Suggestion_Storage;
use WF\Shared\Traits\Logging_Trait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Automatic_Suggestion_Info_Saver {
  use Logging_Trait;

  /**
   * @var Automatic_Suggestion_Info_Saver_Storage
   */
  private $dao;

  /**
   * @var Style_Suggestion_Storage
   */
  private $style_suggestion_storage;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */


  private FeatureTogglesInterface $featureToggles;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @param Automatic_Suggestion_Info_Saver_Storage $dao                      DAO
   * @param Style_Suggestion_Storage                $style_suggestion_storage Style_Suggestion_Storage
   * @param FeatureTogglesInterface                 $featureToggles           Feature toggle
   * @param Automatic_Suggestion_Postgres_DAO       $dao_psql Automatic_Suggestion_Postgres_DAO
   * @param LoggerInterface|null                    $logger                   Logger
   */
  public function __construct(Automatic_Suggestion_Info_Saver_Storage $dao, Style_Suggestion_Storage $style_suggestion_storage,FeatureTogglesInterface $featureToggles, Automatic_Suggestion_Postgres_DAO $dao_psql, ?LoggerInterface $logger = null) {
    $this->dao                      = $dao;
    $this->style_suggestion_storage = $style_suggestion_storage;
    $this->featureToggles           = $featureToggles;
    $this->dao_psql                 = $dao_psql;
    $this->logger                   = $logger;
  }

  /**
   * @param int      $batch_id    Batch ID
   * @param array    $skus        SKUs
   * @param int      $style_id    Style ID
   * @param int      $substyle_id Substyle ID
   * @param string   $saved_at    Saved At
   * @param string   $notes       Notes
   * @param int|null $reason      Reason
   *
   * @return void
   */
  public function save_suggested_info_for_curated_skus(
      int $batch_id,
      array $skus,
      int $style_id,
      int $substyle_id,
      string $saved_at,
      string $notes,
      ?int $reason
  ) {
    if (empty($skus)) {
      return;
    }

    try {
      $this->log_info('Loading style suggestions for SKUs', ['skus' => $skus]);
      $suggested_styles           = $this->style_suggestion_storage->get_style_suggestion($skus);
      $skus_with_suggested_styles = $suggested_styles->get_skus_with_styles();

      if (!empty($skus_with_suggested_styles)) {
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_psql->save_suggested_style_info($batch_id, $skus_with_suggested_styles, $suggested_styles->to_array(), $style_id, $saved_at, $notes, $reason);
        } else {
          $this->dao->save_suggested_style_info($batch_id, $skus_with_suggested_styles, $suggested_styles->to_array(), $style_id, $saved_at, $notes, $reason);
        }
        $this->log_info(
            sprintf('Style suggestion info saved for batch_id "%s"', $batch_id),
            [
                'suggested_styles'           => $suggested_styles->to_array(),
                'batch_id'                   => $batch_id,
                'skus_with_suggested_styles' => $skus_with_suggested_styles,
                'saved_at'                   => $saved_at,
                'style_id'                   => $style_id,
                'notes'                      => $notes,
                'reason'                     => $reason
            ]
        );
      }

      $this->log_info('Loading substyle suggestions for SKUs', ['skus' => $skus]);
      $suggested_substyles           = $this->style_suggestion_storage->get_substyle_suggestion($skus);
      $skus_with_suggested_substyles = $suggested_substyles->get_skus_with_substyles();

      if (!empty($skus_with_suggested_substyles)) {
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_psql->save_suggested_substyle_info($batch_id, $skus_with_suggested_substyles, $suggested_substyles->to_array(), $style_id, $substyle_id, $saved_at, $notes, $reason);
        } else {
          $this->dao->save_suggested_substyle_info($batch_id, $skus_with_suggested_substyles, $suggested_substyles->to_array(), $style_id, $substyle_id, $saved_at, $notes, $reason);
      }
        $this->log_info(
            sprintf('SubStyle suggestion info saved for batch_id "%s"', $batch_id),
            [
                'suggested_styles'              => $suggested_substyles->to_array(),
                'batch_id'                      => $batch_id,
                'skus_with_suggested_substyles' => $skus_with_suggested_substyles,
                'saved_at'                      => $saved_at,
                'style_id'                      => $style_id,
                'notes'                         => $notes,
                'reason'                        => $reason
            ]
        );
      }
    }
    catch (Automatic_Style_Curation_Timeout_Exception $api_exception) {
      // do nothing
      $this->log_throwable_error($api_exception, 'Automatic Style Curation Timeout Exception', ['skus' => $skus]);
    }
  }

  /**
   * @param int      $batch_id Batch ID
   * @param string[] $skus     SKUs
   *
   * @return void
   */
  public function remove_suggested_info_for_excluded_skus(int $batch_id, array $skus) {
    if (empty($skus)) {
      return;
    }

    try {
      $skus_having_suggested_styles = $this->get_skus_with_suggested_styles($skus);

      if (!empty($skus_having_suggested_styles)) {
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_psql->remove_suggested_style_info($batch_id, $skus_having_suggested_styles);
        } else {
          $this->dao->remove_suggested_style_info($batch_id, $skus_having_suggested_styles);
        }
        $this->log_info(
            sprintf('Removed suggested style info for batch_id "%s"', $batch_id),
            [
                'batch_id'                     => $batch_id,
                'skus_having_suggested_styles' => $skus_having_suggested_styles
            ]
        );
      }

      $skus_having_suggested_substyles = $this->get_skus_with_suggested_substyles($skus);

      if (!empty($skus_having_suggested_substyles)) {
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_psql->remove_suggested_substyle_info($batch_id, $skus_having_suggested_substyles);
        } else {
          $this->dao->remove_suggested_substyle_info($batch_id, $skus_having_suggested_substyles);
        }
        $this->log_info(
            sprintf('Removed suggested substyle info for batch_id "%s"', $batch_id),
            [
                'batch_id'                        => $batch_id,
                'skus_having_suggested_substyles' => $skus_having_suggested_substyles
            ]
        );
      }
    }
    catch (Automatic_Style_Curation_Timeout_Exception $api_exception) {
      // do nothing
      $this->log_throwable_error(
          $api_exception,
          'Automatic Style Curation Timeout Exception',
          ['batch_id' => $batch_id, 'skus' => $skus]
      );
    }
  }

  /**
   * @param string[] $skus Skus list
   *
   * @return string[]
   * @throws Automatic_Style_Curation_Exception
   */
  private function get_skus_with_suggested_styles(array $skus) : array {
    $this->log_info('Loading style suggestion for SKUs', ['skus' => $skus]);

    return $this->style_suggestion_storage->get_style_suggestion($skus)->get_skus_with_styles();
  }

  /**
   * @param string[] $skus Skus list
   *
   * @return string[]
   * @throws Automatic_Style_Curation_Exception
   */
  private function get_skus_with_suggested_substyles(array $skus) : array {
    $this->log_info('Loading subStyle suggestion for SKUs', ['skus' => $skus]);

    return $this->style_suggestion_storage->get_substyle_suggestion($skus)->get_skus_with_substyles();
  }
}
