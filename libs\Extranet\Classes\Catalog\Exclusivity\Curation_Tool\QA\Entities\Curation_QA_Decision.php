<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;

final class Curation_QA_Decision {
  /**
   * @var int
   */
  private $batch_id;

  /**
   * @var array
   */
  private $skus = [];

  /**
   * @var int
   */
  private $employee_id;

    /**
     * @var int
     */
    private $price_tier_override;

    /**
     * @var int
     */
    private $final_style_id;

    /**
     * @var int
     */
    private $final_substyle_id;

    /**
     * @var int
     */
    private $final_brand_id;

  /**
   * @var string|null
   */
  private $reason;

  /**
   * @var int|null
   */
  private $exclusion_reason;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  private $qa_status;

  /**
   * Curation_QA_Decision constructor
   */
  private function __construct() {
  }

  /**
   * @param int   $batch_id    the batch to generate decision for
   * @param array $skus        list of SKUs to approve
   * @param int   $employee_id identifier of the approver
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision
   */
  public static function approve(int $batch_id, array $skus, int $employee_id) : self {
    $approved              = new self();
    $approved->batch_id    = $batch_id;
    $approved->qa_status   = Curation_QA_Status::approved();
    $approved->skus        = $skus;
    $approved->employee_id = $employee_id;

    return $approved;
  }

  /**
   * @param int    $batch_id    the batch to generate decision for
   * @param array  $skus        list of SKUs to reject
   * @param int    $employee_id identifier of the rejecter
   * @param string $reason      the reason of rejection
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision
   */
  public static function reject(int $batch_id, array $skus, int $employee_id, string $reason) : self {
    $rejected              = new self();
    $rejected->batch_id    = $batch_id;
    $rejected->qa_status   = Curation_QA_Status::rejected();
    $rejected->skus        = $skus;
    $rejected->employee_id = $employee_id;
    $rejected->reason      = $reason;

    return $rejected;
  }

   /**
   * @param int   $batch_id            the batch to generate decision for
   * @param array $skus                list of SKUs to reject
   * @param int   $employee_id         identifier of the updater
   * @param int $price_tier_override new data set for sku
   * @param int $final_style_id      new data set for sku
   * @param int $final_substyle_id   new data set for sku
   * @param int $final_brand_id      new data set for sku
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision
   */
  public static function update(
      int $batch_id,
      array $skus,
      int $employee_id,
      int $price_tier_override,
      int $final_style_id,
      int $final_substyle_id,
      int $final_brand_id
  ) : self {
    $updated                      = new self();
    $updated->batch_id            = $batch_id;
    $updated->qa_status           = Curation_QA_Status::updated();
    $updated->skus                = $skus;
    $updated->employee_id         = $employee_id;
    $updated->price_tier_override = $price_tier_override> 0 ? $price_tier_override: 4 ;
    $updated->final_style_id      = $final_style_id;
    $updated->final_substyle_id   = $final_substyle_id;
    $updated->final_brand_id      = $final_brand_id;

    return $updated;
  }

  /**
   * @param int   $batch_id         the batch to generate decision for
   * @param array $skus             list of SKUs to exclude
   * @param int   $employee_id      identifier of the excluder
   * @param int   $exclusion_reason the reason of exclusion
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision
   */
  public static function excludeFromWL(int $batch_id, array $skus, int $employee_id, int $exclusion_reason) : self {
    $excluded                        = new self();
    $excluded->batch_id              = $batch_id;
    $excluded->qa_status             = Curation_QA_Status::updated();
    $excluded->skus                  = $skus;
    $excluded->employee_id           = $employee_id;
    $excluded->exclusion_reason      = $exclusion_reason;

    return $excluded;
  }

  /**
   * @return int
   */
  public function get_batch_id() : int {
    return $this->batch_id;
  }

  /**
   * @return array
   */
  public function get_skus() : array {
    return $this->skus;
  }

  /**
   * @return int
   */
  public function get_employee_id() : int {
    return $this->employee_id;
  }

  /**
   * @return string
   */
  public function get_reason() : string {
    return (string)$this->reason;
  }

  /**
   * @return int
   */
  public function get_exclusion_reason() : int {
    return (int) $this->exclusion_reason;
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status
   */
  public function get_qa_status() : Curation_QA_Status {
    return $this->qa_status;
  }

  /**
   * @return int
   */
  public function get_price_tier_override() : int {
    return $this->price_tier_override;
  }

  /**
   * @return int
   */
  public function get_final_style_id() : int {
    return $this->final_style_id;
  }

  /**
   * @return int
   */
  public function get_final_substyle_id() : int {
    return $this->final_substyle_id;
  }

  /**
   * @return int
   */
  public function get_final_brand_id() : int {
    return $this->final_brand_id;
  }
}
