<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface;
use WF\Shared\Exception\Invalid_Argument_Exception;

class Curation_Item_Group implements Mergeable_Interface {
  /**
   * @var array
   */
  private $skus = [];

  /**
   * @var array
   */
  private $collections = [];

  /**
   * @var array
   */
  private $items = [];

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $item Curation Item
   *
   * @return void
   */
  public function add(Curation_Item $item) {
    $this->items[$item->get_sku()] = $item;

    $this->skus = array_unique(array_merge($this->skus, [$item->get_sku()], $item->get_kit_parents()));

    if (!empty($item->get_context_xn_id()) && !in_array($item->get_context_xn_id(), $this->collections)) {
      $this->collections[] = $item->get_context_xn_id();
    }
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  public function getItems(): array {
    return array_values($this->items);
  }

  /**
   * @param string $sku SKU
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item
   */
  public function get(string $sku): Curation_Item {
    return $this->items[$sku];
  }

  /**
   * @param string $sku SKU
   *
   * @return void
   */
  public function remove(string $sku) {
    unset($this->items[$sku]);
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return bool
   */
  public function isDependent(Mergeable_Interface $item): bool {
    if (!$item instanceof Curation_Item_Group) {
      throw new Invalid_Argument_Exception('Argument type is not supported');
    }

    if (!empty(array_intersect($this->skus, $item->skus)) || !empty(array_intersect($this->collections, $item->collections))) {
      return true;
    }

    return false;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return void
   */
  public function merge(Mergeable_Interface $item) {
    if (!$item instanceof Curation_Item_Group) {
      throw new Invalid_Argument_Exception('Argument type is not supported');
    }

    foreach ($item->getItems() as $value) {
      $this->add($value);
    }
  }

  /**
   * @return bool
   */
  public function isCollectionGroup(): bool {
    foreach ($this->getItems() as $item) {
      if (!empty($item->get_context_xn_id())) {
        return true;
      }
    }

    return false;
  }

  /**
   * @return bool
   */
  public function isKitGroup(): bool {
    foreach ($this->getItems() as $item) {
      if (!empty($item->get_kit_parents())) {
        return true;
      }
    }

    return false;
  }

  /**
   * @return string[]
   */
  public function getSkus(): array {
    return array_keys($this->items);
  }

  /**
   * @return string[]
   */
  public function getParents(): array {
    $parents = [];
    foreach ($this->getItems() as $item) {
      $parents = array_merge($parents, $item->get_kit_parents());
    }

    return array_unique($parents);
  }
}