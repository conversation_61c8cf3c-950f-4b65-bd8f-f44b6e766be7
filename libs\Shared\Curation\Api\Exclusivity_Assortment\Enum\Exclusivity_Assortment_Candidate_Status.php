<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Exclusivity_Assortment_Candidate_Status extends AbstractEnumeration {
  /**
   * csn_merch_tool..tblplAssortmentCandidateStatus
   */

  /**
   * @var string|null
   */
  private $label;

  /**
   * @var array
   */
  private static $values = [
      'Pending QA'                    => 1,
      'Approved by QA'                => 2,
      'Rejected by QA'                => 3,
      'Invalid'                       => 4,
      'Imported'                      => 5,
      'In Cart'                       => 6,
      'Approved for Investment by QA' => 7,
  ];

  /**
   * @return self
   */
  public static function pending_qa() : self {
    return self::get_value_for_option(self::$values['Pending QA']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function approved_by_qa() : self {
    return self::get_value_for_option(self::$values['Approved by QA']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function rejected_by_qa() : self {
    return self::get_value_for_option(self::$values['Rejected by QA']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function invalid() : self {
    return self::get_value_for_option(self::$values['Invalid']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function imported() : self {
    return self::get_value_for_option(self::$values['Imported']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function in_cart() : self {
    return self::get_value_for_option(self::$values['In Cart']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function approved_for_investment_by_qa() : self {
    return self::get_value_for_option(self::$values['Approved for Investment by QA']); /** @phpstan-ignore-line */
  }

  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return self
   *
   * @throws \InvalidArgumentException
   */
  public static function create(int $option) : self {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $option);
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function label() : string {
    // if the label is requested for the first time, compute it and save the result
    if ($this->label === null) {
      $this->label = array_search((int)$this->value(), self::$values, true);
    }

    return $this->label;
  }
}
