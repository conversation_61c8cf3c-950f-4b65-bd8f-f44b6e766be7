<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision;

interface Assortment_Curation_Decision_Matcher {
  /**
   * Starting from the price tier and manufacturer, it tries to get the closest curation decision available
   *
   * @param int $batch_id        Batch ID
   * @param int $manufacturer_id Manufacturer ID
   * @param int $price_tier      Price Tier
   *
   * @return Assortment_Curation_Move_To_Brand_Decision_Data|null
   */
  public function find_closest_match(int $batch_id, int $manufacturer_id, int $price_tier) : ?Assortment_Curation_Move_To_Brand_Decision_Data;
}
