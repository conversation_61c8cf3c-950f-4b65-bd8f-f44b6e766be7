<?php
/**
 * Test for the \WF\Shared\Classes\ProductManagement\Curation\Curation_Price_Option_Loader
 *
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Curation;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Curation\ExclusivityAssortment\Domain\Price\Client\AletheiaApiClient;
use WF\Curation\ExclusivityAssortment\Domain\Price\DAO\CurationOptionCombinationLoaderDAO;
use WF\Curation\ExclusivityAssortment\Domain\Price\DTO\PriceOptionsCombinationDTO;
use WF\Curation\ExclusivityAssortment\Domain\Price\DTO\UnitPriceOptionsDTO;
use WF\Curation\ExclusivityAssortment\Domain\Price\Services\PriceOptionLoader;
use function json_encode;

class Curation_Price_Option_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Curation\ExclusivityAssortment\Domain\Price\DAO\CurationOptionCombinationLoaderDAO
     */
    private $dao;

    /**
     * @var \WF\Curation\ExclusivityAssortment\Domain\Price\Client\AletheiaApiClient
     */
    private $api_client;

    /**
     * @var \WF\Curation\ExclusivityAssortment\Domain\Price\Services\PriceOptionLoader
     */
    private $subject;

    /**
     * The Setup function of this test
     *
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(CurationOptionCombinationLoaderDao::class);
        $this->dao->getOptionsCombinations(Argument::any())->willReturn([]);

        $this->api_client = $this->prophesize(AletheiaApiClient::class);
        $this->api_client->getUnitPriceOptions(Argument::any())->willReturn([]);

        $this->subject = new PriceOptionLoader(
            $this->dao->reveal(),
            $this->api_client->reveal()
        );
    }

    /**
     * @param array $db_data  DB Data
     * @param array $api_data API Data
     * @param array $expected Expected
     *
     * @test
     * @dataProvider get_price_options_data_provider
     *
     * @return void
     */
    public function get_price_options(array $db_data, array $api_data, array $expected)
    {
        $sku = 'SKU123';
        $brand_catalog_id = 1;

        $this->dao->getOptionsCombinations($sku)->willReturn($db_data)->shouldBeCalledOnce();

        $api_result_item = new UnitPriceOptionsDTO($sku, $api_data);
        $this->api_client->getUnitPriceOptions([$sku], $brand_catalog_id)
            ->willReturn([$api_result_item])
            ->shouldBeCalledOnce();

        $this->assertSame(json_encode($expected), json_encode($this->subject->getPriceOptions($sku)));
    }

    /**
     * @return array
     */
    public function get_price_options_data_provider(): array
    {
        return [
            [
                'db_data' => [
                    [
                        'OptionCombinationID' => 1,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Sonoma Oak',
                        'piid1' => 11,
                        'category2' => 'Size',
                        'name2' => '181cm',
                        'piid2' => 21,
                        'category3' => 'Interior',
                        'name3' => 'Basic',
                        'piid3' => 31,
                    ],
                    [
                        'OptionCombinationID' => 2,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Alpine White',
                        'piid1' => 12,
                        'category2' => 'Size',
                        'name2' => '181cm',
                        'piid2' => 21,
                        'category3' => 'Interior',
                        'name3' => 'Basic',
                        'piid3' => 31,
                    ],
                    [
                        'OptionCombinationID' => 3,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Sonoma Oak',
                        'piid1' => 11,
                        'category2' => 'Size',
                        'name2' => '200cm',
                        'piid2' => 22,
                        'category3' => 'Interior',
                        'name3' => 'Basic',
                        'piid3' => 31,
                    ],
                    [
                        'OptionCombinationID' => 4,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Alpine White',
                        'piid1' => 12,
                        'category2' => 'Size',
                        'name2' => '200cm',
                        'piid2' => 22,
                        'category3' => 'Interior',
                        'name3' => 'Basic',
                        'piid3' => 31,
                    ],
                    [
                        'OptionCombinationID' => 5,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Sonoma Oak',
                        'piid1' => 11,
                        'category2' => 'Size',
                        'name2' => '181cm',
                        'piid2' => 21,
                        'category3' => 'Interior',
                        'name3' => 'Modern',
                        'piid3' => 32,
                    ],
                    [
                        'OptionCombinationID' => 6,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Alpine White',
                        'piid1' => 12,
                        'category2' => 'Size',
                        'name2' => '181cm',
                        'piid2' => 21,
                        'category3' => 'Interior',
                        'name3' => 'Modern',
                        'piid3' => 32,
                    ],
                    [
                        'OptionCombinationID' => 7,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Sonoma Oak',
                        'piid1' => 11,
                        'category2' => 'Size',
                        'name2' => '200cm',
                        'piid2' => 22,
                        'category3' => 'Interior',
                        'name3' => 'Modern',
                        'piid3' => 32,
                    ],
                    [
                        'OptionCombinationID' => 8,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Alpine White',
                        'piid1' => 12,
                        'category2' => 'Size',
                        'name2' => '200cm',
                        'piid2' => 22,
                        'category3' => 'Interior',
                        'name3' => 'Modern',
                        'piid3' => 32,
                    ],
                    // add the option again for a different supplier/combination
                    // (the first win approach check)
                    [
                        'OptionCombinationID' => 9,
                        'bclgID' => 1,
                        'category1' => 'Color',
                        'name1' => 'Alpine White',
                        'piid1' => 12,
                        'category2' => 'Size',
                        'name2' => '200cm',
                        'piid2' => 22,
                        'category3' => 'Interior',
                        'name3' => 'Modern',
                        'piid3' => 32,
                    ]
                ],

                'api_data' => [
                    new PriceOptionsCombinationDTO(1, 97),
                    new PriceOptionsCombinationDTO(2, 62),
                    new PriceOptionsCombinationDTO(3, 94),
                    new PriceOptionsCombinationDTO(4, 53),
                    new PriceOptionsCombinationDTO(5, 26),
                    new PriceOptionsCombinationDTO(6, 65),
                    new PriceOptionsCombinationDTO(7, 24),
                    new PriceOptionsCombinationDTO(8, 355),
                    // add the option again for a different supplier/combination
                    // (the first win approach check)
                    new PriceOptionsCombinationDTO(9, 377),
                ],

                'expected' => [
                    [
                        'Color' => 'Sonoma Oak',
                        'Size' => '181cm',
                        'Interior' => 'Basic',
                        'Price' => '97.0000',
                    ],
                    [
                        'Color' => 'Alpine White',
                        'Size' => '181cm',
                        'Interior' => 'Basic',
                        'Price' => '62.0000',
                    ],
                    [
                        'Color' => 'Sonoma Oak',
                        'Size' => '200cm',
                        'Interior' => 'Basic',
                        'Price' => '94.0000',
                    ],
                    [
                        'Color' => 'Alpine White',
                        'Size' => '200cm',
                        'Interior' => 'Basic',
                        'Price' => '53.0000',
                    ],
                    [
                        'Color' => 'Sonoma Oak',
                        'Size' => '181cm',
                        'Interior' => 'Modern',
                        'Price' => '26.0000',
                    ],
                    [
                        'Color' => 'Alpine White',
                        'Size' => '181cm',
                        'Interior' => 'Modern',
                        'Price' => '65.0000',
                    ],
                    [
                        'Color' => 'Sonoma Oak',
                        'Size' => '200cm',
                        'Interior' => 'Modern',
                        'Price' => '24.0000',
                    ],
                    [
                        'Color' => 'Alpine White',
                        'Size' => '200cm',
                        'Interior' => 'Modern',
                        'Price' => '355.0000',
                    ]
                ]
            ]
        ];
    }
}
