<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace App\Tests\libs\PHPUnit\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_SKU_Service;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Batch_SKU_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch|\Prophecy\Prophecy\ObjectProphecy
     */
    private $batch;

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU|\Prophecy\Prophecy\ObjectProphecy
     */
    private $verified_batch_sku;

    /**
     * @var Batch_SKU_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $dao;

    /**
     * @var Batch_SKU_Postgresql_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $batch_sku_postgresql_dao;

    /**
     * @var Batch_Factory
     */
    private $batch_factory;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;


    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->batch = $this->prophesize(Batch::class);
        $this->batch->set_id(1);
        $this->verified_batch_sku = $this->prophesize(Verified_Batch_SKU::class);

        $this->dao = $this->prophesize(Batch_SKU_DAO::class);
        $this->batch_factory = $this->prophesize(Batch_Factory::class);
        $this->batch_sku_postgresql_dao = $this->prophesize(Batch_SKU_Postgresql_DAO::class);

        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->subject = new Batch_SKU_Service(
            $this->dao->reveal(),
            $this->batch_factory->reveal(),
            $this->batch_sku_postgresql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_load_verified_skus_with_feature_toggle_off()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch_skus(Argument::any())->willReturn([]);
        $batch = $this->subject->load_verified_skus($this->batch->reveal());
        $this->assertEquals($batch->get_id(), null);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_load_verified_skus_with_feature_toggle_on()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->batch_sku_postgresql_dao->get_batch_skus(Argument::any())->willReturn([]);
        $batch = $this->subject->load_verified_skus($this->batch->reveal());
        $this->assertEquals($batch->get_id(), null);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_load_kit_parents_with_toggle_off()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_kit_parent_skus(Argument::any())->willReturn([]);
        $batch = $this->subject->load_kit_parents($this->batch->reveal(), $this->verified_batch_sku->reveal());
        $this->assertEquals($batch->get_id(), null);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_load_kit_parents_with_feature_toggle_on()
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->batch_sku_postgresql_dao->get_kit_parent_skus(Argument::any())->willReturn([]);
        $batch = $this->subject->load_kit_parents($this->batch->reveal(), $this->verified_batch_sku->reveal());
        $this->assertEquals($batch->get_id(), null);
    }
}
