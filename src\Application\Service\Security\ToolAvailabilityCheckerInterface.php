<?php

declare(strict_types=1);

namespace App\Application\Service\Security;

interface ToolAvailabilityCheckerInterface
{
    public const CURATION_TOOL_QA_RESOURCE = 'ARA:Curation QA';
    public const CURATION_US_OFFSHORE = 'ARA:Curation - US Offshore';
    public const CURATION_ASSORTMENT_WORKFLOW_BY_OFFSHORE = 'ARA:Curation Assortment Workflow by Offshore';
    public const CURATION_BATCH_MANAGEMENT_EU_NEARSHORE_CURATORS = 'ARA:Curation Batch Management EU Nearshore Curators';

    public function isToolDisabled(): bool;
}
