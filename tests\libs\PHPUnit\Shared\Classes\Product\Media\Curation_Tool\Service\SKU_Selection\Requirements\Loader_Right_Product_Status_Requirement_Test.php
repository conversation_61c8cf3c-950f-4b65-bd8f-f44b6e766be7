<?php
/**
 * Tests for the \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Right_Product_Status_Requirement
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Right_Product_Status_Requirement;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\Product_Model;

/**
 * @property \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Right_Product_Status_Requirement subject
 */
class Loader_Right_Product_Status_Requirement_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model
     */
    private $curation_request_sku_base_mock;

    /**
     * SetUp
     *
     * @return void
     */
    public function setUp(): void
    {
        $this->curation_request_sku_base_mock = $this->prophesize(Curation_Request_SKU_Base_Model::class);
        $this->subject = new Loader_Right_Product_Status_Requirement();
    }

    /**
     * @param int    $product_status product_status
     * @param bool   $expected       expected
     * @param string $message        message
     *
     * @dataProvider is_right_product_status_data_provider
     *
     * @return void
     */
    public function test_is_right_product_status($product_status, $expected, $message)
    {
        $this->curation_request_sku_base_mock->get_product_status()->willReturn($product_status);
        $this->assertEquals($expected, $this->subject->is_right_product_status($this->curation_request_sku_base_mock->reveal()), $message);
    }

    /**
     * @return array
     */
    public function is_right_product_status_data_provider()
    {
        return [
            [Product_Model::STATUS_BEING_ADDED, false, 'product status is ' . Product_Model::STATUS_BEING_ADDED],
            [Product_Model::STATUS_KIT_COMPONENT, true, 'product status is ' . Product_Model::STATUS_KIT_COMPONENT]
        ];
    }
}
