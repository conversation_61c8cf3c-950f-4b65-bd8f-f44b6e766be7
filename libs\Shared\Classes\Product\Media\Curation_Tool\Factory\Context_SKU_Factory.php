<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Factory;

use WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU;

class Context_SKU_Factory {
  /**
   * @param string   $target_sku  target sku
   * @param string   $context_sku context sku
   * @param int|null $xn_id       collection id
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\Context_SKU
   */
  public function create(string $target_sku, string $context_sku, int $xn_id = null) : Context_SKU {
    return new Context_SKU($target_sku, $context_sku, $xn_id);
  }
}
