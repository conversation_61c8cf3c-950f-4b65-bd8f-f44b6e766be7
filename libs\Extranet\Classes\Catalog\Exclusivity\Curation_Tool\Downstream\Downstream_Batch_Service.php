<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\Downstream_Fail_Exception;
use WF\FeatureToggle\FeatureTogglesInterface;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU;

final class Downstream_Batch_Service implements Downstream_Batch_Service_Interface, LoggerAwareInterface {
  use LoggerTrait;
  use LoggerAwareTrait;

  public const CURATION_TOOL_ALL_BATCHES_AS_DISPLAY_SKU_ELIGIBLE_TOGGLE = 'curation_tool_all_batch<PERSON>_as_display_sku_eligible';

  public const PARTIAL_WHITE_LABEL_REQUEST_TYPE = 2;

  public const CURATION_ONLY_MANUAL_REQUEST_TYPE = 7;

  /**
   * @var Downstream_Process_Factory
   */
  private $downstream_process_factory;

  /**
   * @var Batch_Data_Loader
   */
  private $batch_loader;

  /**
   * @var Batch_Reevaluator
   */
  private $reevaluator;

  /**
   * @var World_Region_EU
   */
  private $world_region_eu;

  private FeatureTogglesInterface $toggle;

  /**
   * Downstream_Batch_Service constructor.
   *
   * @param Downstream_Process_Factory $downstream_process_factory Process factory decider
   * @param Batch_Data_Loader          $batch_loader               Batch Loader
   * @param Batch_Reevaluator          $reevaluator                Batch Reevaluator service
   * @param World_Region_EU            $world_region_eu            World Region EU
   * @param FeatureTogglesInterface    $toggle                     Feature Toggle
   */
  public function __construct(
      Downstream_Process_Factory $downstream_process_factory,
      Batch_Data_Loader $batch_loader,
      Batch_Reevaluator $reevaluator,
      World_Region_EU $world_region_eu,
      FeatureTogglesInterface $toggle
  ) {
    $this->downstream_process_factory = $downstream_process_factory;
    $this->batch_loader               = $batch_loader;
    $this->reevaluator                = $reevaluator;
    $this->world_region_eu            = $world_region_eu;
    $this->toggle                     = $toggle;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return void
   * @throws Downstream_Fail_Exception
   */
  public function downstream_batch(int $batch_id) : void {
    $this->info(
        sprintf('Downstreaming BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );

    set_time_limit(5 * 60); // set page limit in 5 minutes to handle batches with 10k+ SKUs

    $processor = $this->downstream_process_factory->get_processor();

    $batch = $this->batch_loader->load_data($batch_id);
    if(count($batch->get_skus()) == 0){
      $exception = new Downstream_Fail_Exception();
      $this->error(
          sprintf('Downstream failed for BatchId Reason : Sku list can not be empty "%s"', $batch_id),
          ['batch_id' => $batch_id, 'exception' => $exception]
      );
      throw $exception;
    }
    $this->reevaluator->reevaluate($batch);

      if ($this->is_batch_ready_for_white_labeling($batch)) {
          $retryAttempts = 3;
          $retryDelayInSeconds = 1;
          $retryCount = 0;
          $success = false;
          while ($retryCount < $retryAttempts) {
              $retryCount++;
              try {
                  if ($processor->send_downstream($batch)) {
                      $success = true;
                      break;
                  }
              }catch (\Exception $exception){
                  $this->logger->Warning ("Error occured while sending to downstream: ".$exception->getMessage());
              }
              $this->logger->warning(
                  sprintf(' Downstream taking longer time than expected . Retrying in %s seconds...',
                      $retryDelayInSeconds),
                  ['batch_id' => $batch_id, 'retry_count' => $retryCount]
              );
              sleep($retryDelayInSeconds);
          }
          if (!$success) {
              $exception = new Downstream_Fail_Exception();
              $this->logger->error(
                  sprintf('Downstream failed for BatchId "%s" after %d retry attempts...',
                      $batch_id, $retryAttempts),
                  ['batch_id' => $batch_id, 'exception' => $exception]
              );
              throw $exception;
          }
    }

    $this->logger->info(
        sprintf('Downstream successful for batch_id=%s', $batch_id),
        ['batch_id' => $batch_id]
    );
  }

  /**
   * Checks if this batch can be downstream to white labeling
   * Another condition for white labeling is Batch_Evaluation_Status::ready() which is implemented outside of this method
   *
   * @param Batch $batch The batch that should be checked
   *
   * @return bool
   */
  private function is_batch_ready_for_white_labeling(Batch $batch) : bool {
    $this->info(
        'Examining batch readiness to downstream',
        ['batch_id' => $batch->get_id()]
    );

    if ($batch->get_curation_request_type() === self::PARTIAL_WHITE_LABEL_REQUEST_TYPE
        || $batch->get_curation_manual_request_type() === self::CURATION_ONLY_MANUAL_REQUEST_TYPE
    ) {
      $this->info(
          sprintf('Batch batch_id=%s is part of Partial WhiteLabeling or Curation-Only request type', $batch->get_id()),
          [
              'batch_id'                         => $batch->get_id(),
              'is_curation_only_request'         => $batch->get_curation_manual_request_type() === self::CURATION_ONLY_MANUAL_REQUEST_TYPE,
              'is_partial_whitelabeling_request' => $batch->get_curation_request_type() === self::PARTIAL_WHITE_LABEL_REQUEST_TYPE
          ]
      );

      return false;
    }
    if ($this->toggle->isEnabled(self::CURATION_TOOL_ALL_BATCHES_AS_DISPLAY_SKU_ELIGIBLE_TOGGLE)) {
      $this->info(
          sprintf('Batch batch_id=%s is ready for WhiteLabeling', $batch->get_id()),
          ['batch_id' => $batch->get_id()]
      );
      return true;
    }
    if ($batch->is_display_sku_eligible() || $this->is_batch_eu($batch)) {
      $this->info(
          sprintf('Batch batch_id=%s is ready for WhiteLabeling', $batch->get_id()),
          ['batch_id' => $batch->get_id()]
      );
      return true;
    }

    $this->info(
        sprintf('Batch batch_id=%s is not ready for WhiteLabeling', $batch->get_id()),
        ['batch_id' => $batch->get_id()]
    );
    return false;
  }

  /**
   * @param Batch $batch The batch that should be checked if its EU
   *
   * @return bool
   */
  private function is_batch_eu(Batch $batch) : bool {
    return in_array($batch->get_brand_catalog_id(), $this->world_region_eu->getBrandCatalogIds());
  }
}
