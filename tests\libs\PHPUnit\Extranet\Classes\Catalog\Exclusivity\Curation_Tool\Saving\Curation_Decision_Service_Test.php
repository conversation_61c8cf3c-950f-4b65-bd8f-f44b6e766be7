<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_US;

class Curation_Decision_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Curation_Decision_DAO
     */
    private $dao;

    /**
     * @var Curation_Decision_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var Context_Data_Service
     */
    private $context_data_service;

    /**
     * @var Curation_Kit_Children_Loader
     */
    private $curation_kit_children_loader;

    /**
     * @var Curation_Decision_Factory
     */
    private $factory;

    /**
     * @var Region_Service
     */
    private $region_service;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Decision_DAO::class);
        $this->dao_psql = $this->prophesize(Curation_Decision_Postgres_DAO::class);
        $this->context_data_service = $this->prophesize(Context_Data_Service::class);
        $this->curation_kit_children_loader = $this->prophesize(Curation_Kit_Children_Loader::class);
        $this->factory = $this->prophesize(Curation_Decision_Factory::class);
        $this->region_service = $this->prophesize(Region_Service::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->logger = $this->prophesize(LoggerInterface::class);

        $this->subject = new Curation_Decision_Service(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->context_data_service->reveal(),
            $this->curation_kit_children_loader->reveal(),
            $this->factory->reveal(),
            $this->region_service->reveal(),
            $this->featureToggles->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_excluded_feature_toggle_off(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            1,
            null,
            null,
            null,
            null,
            null,
            1,
            false,
            null,
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->curation_kit_children_loader->replaceKitParents(['SKU1'], 1)->willReturn(['SKU1']);
        $this->factory->create_excluded(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao->save($decisions)->shouldBeCalled();
        $this->subject->save_excluded(
            1,
            ['SKU1'],
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_excluded_feature_toggle_on(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            1,
            null,
            null,
            null,
            null,
            null,
            1,
            false,
            null,
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->curation_kit_children_loader->replaceKitParents(['SKU1'], 1)->willReturn(['SKU1']);
        $this->factory->create_excluded(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao_psql->save($decisions)->shouldBeCalled();
        $this->subject->save_excluded(
            1,
            ['SKU1'],
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_curated_feature_toggle_off(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            1,
            1,
            1,
            1,
            1,
            1,
            false,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $dataColl = new Context_Data_Collection();
        $cd = new Context_Data(2, 1, 1, 1);
        $dataColl->set_for_sku('SKU1', $cd);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->curation_kit_children_loader->replaceKitParents(['SKU1'], 1)->willReturn(['SKU1']);
        $this->context_data_service->context_data_for_skus(1, ['SKU1'])->willReturn($dataColl);
        $this->factory->create_curated(
            1,
            'SKU1',
            1,
            1,
            1,
            1,
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao->save($decisions)->shouldBeCalled();
        $this->subject->save_curated(
            1,
            ['SKU1'],
            1,
            1,
            1,
            1,
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_curated_feature_toggle_on(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            1,
            1,
            1,
            1,
            1,
            1,
            false,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $dataColl = new Context_Data_Collection();
        $cd = new Context_Data(2, 1, 1, 1);
        $dataColl->set_for_sku('SKU1', $cd);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->curation_kit_children_loader->replaceKitParents(['SKU1'], 1)->willReturn(['SKU1']);
        $this->context_data_service->context_data_for_skus(1, ['SKU1'])->willReturn($dataColl);
        $this->factory->create_curated(
            1,
            'SKU1',
            1,
            1,
            1,
            1,
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao_psql->save($decisions)->shouldBeCalled();
        $this->subject->save_curated(
            1,
            ['SKU1'],
            1,
            1,
            1,
            1,
            1,
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_as_kitsco_feature_toggle_off(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            null,
            null,
            null,
            1,
            null,
            1,
            true,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->region_service->get_region(1)->willReturn(new World_Region_US());
        $this->factory->create_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao->save($decisions)->shouldBeCalled();
        $this->subject->save_as_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_save_as_kitsco_feature_toggle_on(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            null,
            null,
            null,
            1,
            null,
            1,
            true,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->region_service->get_region(1)->willReturn(new World_Region_US());
        $this->factory->create_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao_psql->save($decisions)->shouldBeCalled();
        $this->subject->save_as_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_remove_kitsco_feature_toggle_off(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            null,
            null,
            null,
            null,
            null,
            1,
            false,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->curation_kit_children_loader->replaceKitParents(['SKU2'], 1)->willReturn([]);
        $this->factory->create_empty(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao->save($decisions)->shouldBeCalled();
        $this->subject->remove_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            ['SKU2'],
            Curation_Decision_Source::manual()
        );
    }

    /**
     * @test
     *
     * @return void
     * @throws \Exception
     */
    public function test_remove_kitsco_feature_toggle_on(): void
    {
        $decision = new Curation_Decision(
            1,
            'SKU1',
            1,
            '2023-08-23 20:06:02',
            0,
            null,
            null,
            null,
            null,
            null,
            1,
            false,
            Curation_QA_Status::pending(),
            Curation_Decision_Source::manual()
        );
        $decisions = [$decision];
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->curation_kit_children_loader->replaceKitParents(['SKU2'], 1)->willReturn([]);
        $this->factory->create_empty(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            Curation_Decision_Source::manual()
        )->willReturn($decision);
        $this->dao_psql->save($decisions)->shouldBeCalled();
        $this->subject->remove_kitsco(
            1,
            'SKU1',
            '2023-08-23 20:06:02',
            1,
            ['SKU2'],
            Curation_Decision_Source::manual()
        );
    }
}
