<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits;

use App\Domain\Service\Loading\CurationItemFactory;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory;
use WF\Shared\Classes\ProductManagement\Utils\Merger;

class Kit_Parent_Grouper_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper
     */
    private $subject;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory
     */
    private $factory;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $merger = new Merger();
        $this->subject = new Kit_Parent_Grouper($merger);

        $isPAAutomationEnabled = true;

        $this->factory = new Curation_Item_Group_Factory($merger, new CurationItemFactory($isPAAutomationEnabled), $isPAAutomationEnabled);
    }

    /**
     * @test
     *
     * @param array $sharedComponents Shared components
     * @param array $expectedResult   Expected Result
     *
     * @dataProvider getGrouperData
     * @return void
     */
    public function itReturnsGroupedParentKits(array $sharedComponents, array $expectedResult)
    {
        $group = $this->factory->createFromArray($sharedComponents);
        $actualResult = $this->subject->createKitParents($group);

        $actualResultArray = [];

        foreach ($actualResult as $parent) {
            $actualResultArray[] = $parent->jsonSerialize();
        }

        $this->assertEquals($expectedResult, $actualResultArray);
    }

    /**
     * The kit skus are referred as A,B,C,D
     * The shared component are referred as combination from the kit skus: AB, AC, BD ...
     *
     * @return array
     */
    public function getGrouperData(): array
    {
        return [
            'All shared components are not kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => false, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => false, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B', 'C', 'D']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'CD shared component is kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => false, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => true, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B', 'C', 'D']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AC shared component is kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => true, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => false, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => false, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B', 'C', 'D']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AC and CD shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => true, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => false, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => true, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B', 'D']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => ['C']],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'BD shared component is kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => true, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => false, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B', 'C', 'D']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AB and BD shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => true, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => false, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'C', 'D']],
                    ['sku' => 'B', 'related_kits' => ['B']],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AB, BD, CD shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => true, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => true, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'C']],
                    ['sku' => 'B', 'related_kits' => ['B']],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => ['D']],
                ],
            ],
            'AC and BD shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => true, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => true, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => false, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B']],
                    ['sku' => 'B', 'related_kits' => []],
                    ['sku' => 'C', 'related_kits' => ['C', 'D']],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AB and CD shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => false, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => false, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => true, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'C']],
                    ['sku' => 'B', 'related_kits' => ['B', 'D']],
                    ['sku' => 'C', 'related_kits' => []],
                    ['sku' => 'D', 'related_kits' => []],
                ],
            ],
            'AB has two shared components, one kitsco and one not kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AB2', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B']],
                    ['sku' => 'B', 'related_kits' => []],
                ],
            ],
            'AB has two shared components, one kitsco and one not kitsco - different order' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => false, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AB2', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A', 'B']],
                    ['sku' => 'B', 'related_kits' => []],
                ],
            ],
            'All shared components are kitsco' => [
                'shared_components' => [
                    ['sku' => 'AB', 'is_kitsco' => true, 'kit_parents' => ['A', 'B']],
                    ['sku' => 'AC', 'is_kitsco' => true, 'kit_parents' => ['A', 'C']],
                    ['sku' => 'BD', 'is_kitsco' => true, 'kit_parents' => ['B', 'D']],
                    ['sku' => 'CD', 'is_kitsco' => true, 'kit_parents' => ['C', 'D']],
                ],
                'result' => [
                    ['sku' => 'A', 'related_kits' => ['A']],
                    ['sku' => 'B', 'related_kits' => ['B']],
                    ['sku' => 'C', 'related_kits' => ['C']],
                    ['sku' => 'D', 'related_kits' => ['D']],
                ],
            ],
        ];
    }
}
