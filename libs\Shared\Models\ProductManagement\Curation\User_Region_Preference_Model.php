<?php
/**
 * Service of the user region preference
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\Curation;

use WF\Shared\Classes\ProductManagement\Curation\World_Regions;

class User_Region_Preference_Model {

  const USER_PREFERENCE_REGION_US = World_Regions::REGION_US;
  const USER_PREFERENCE_REGION_EU = World_Regions::REGION_EU;

  //The key of the array corresponds to the brand catalog and that's why it doesn't start with zero
  const REGIONS = [
      1 => 'US',
      2 => 'Europe',
  ];

  /**
   * @var int
   */
  private $current_choice = 0;

  /**
   * @var int
   */
  private $employee_id = 0;

  /**
   * @var int
   */
  private $preference = 0;

  /**
   * User_Region_Preference_Model constructor.
   *
   * @param int $employee_id id of the owner of this preference
   */
  public function __construct(int $employee_id) {
    $this->employee_id = $employee_id;
  }


  /**
   * @param int $current_choice Current choice
   *
   * @return void
   */
  public function set_current_choice(int $current_choice) {
    $this->current_choice = $current_choice;
  }

  /**
   * @return int
   */
  public function get_current_choice() :int {
    return $this->current_choice;
  }

  /**
   * @param int $employee_id Employee id
   *
   * @return void
   */
  public function set_employee_id(int $employee_id) {
    $this->employee_id = $employee_id;
  }

  /**
   * @return int
   */
  public function get_employee_id() {
    return $this->employee_id;
  }

  /**
   * @param int $preference Preference
   *
   * @return void
   */
  public function set_preference(int $preference) {
    $this->preference = $preference;
  }

  /**
   * @return int
   */
  public function get_preference() {
    return $this->preference;
  }
}
