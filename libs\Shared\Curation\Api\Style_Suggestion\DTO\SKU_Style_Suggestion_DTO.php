<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Style_Suggestion\DTO;

class SKU_Style_Suggestion_DTO {
  /**
   * @var string
   */
  private $sku;

  /**
   * @var Suggestion_Threshold|null
   */
  private $threshold;

  /**
   * @var Style_Suggestion[]
   */
  private $suggestions;

  /**
   * SKU_Style_Suggestion_DTO constructor.
   *
   * @param string                    $sku         SKU
   * @param Style_Suggestion[]        $suggestions Style Suggestions
   * @param Suggestion_Threshold|null $threshold   Threshold value
   */
  public function __construct(string $sku, array $suggestions, ?Suggestion_Threshold $threshold) {
    $this->sku         = $sku;
    $this->threshold   = $threshold;
    $this->suggestions = $suggestions;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }


  /**
   * @return Suggestion_Thr<PERSON>|null
   */
  public function get_threshold() : ?Suggestion_Thresh<PERSON> {
    return $this->threshold;
  }

  /**
   * @return Style_Suggestion[]
   */
  public function get_suggestions() : array {
    return $this->suggestions;
  }
}
