<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Tests\DTO;

use PHPUnit\Framework\TestCase;
use WF\Curation\WhiteLabelApi\DTO\RequestBuilder;
use WF\Curation\WhiteLabelApi\Entity\Batch;
use WF\Curation\WhiteLabelApi\Entity\SKU;

class RequestBuilderTest extends TestCase
{
    public function testBuildJsonRequest()
    {
        $batches = $this->getTestData();
        $requestJson = RequestBuilder::buildJsonRequest($batches);

        $this->assertCount(2, $requestJson);
    }

    public function testBuildBatchRequest()
    {
        $batches = $this->getTestData();
        $requestJson = RequestBuilder::buildJsonRequest($batches);

        $batch = current($requestJson);

        $this->assertArrayHasKey('whiteLabelBatchId', $batch);
        $this->assertA<PERSON>('brandCatalogId', $batch);
        $this->assertArrayHasKey('skus', $batch);
    }

    public function testBuildBatchSkusRequest()
    {
        $batches = $this->getTestData();
        $requestJson = RequestBuilder::buildJsonRequest($batches);

        $batch = current($requestJson);
        $skus = $batch['skus'];

        $this->assertCount(3, $skus);

        $sku = current($skus);
        $this->assertArrayHasKey('sku', $sku);
        $this->assertArrayHasKey('targetManufacturerId', $sku);
        $this->assertArrayHasKey('isExcluded', $sku);
    }

    public function testGetTargetManufacturerId()
    {
        $batches = $this->getTestData();
        $requestJson = RequestBuilder::buildJsonRequest($batches);

        $batch = current($requestJson);
        $skus = $batch['skus'];

        $this->assertCount(3, $skus);

        $sku = current($skus);
        $this->assertEquals($sku['targetManufacturerId'], null);

        $sku2 = next($skus);
        $this->assertEquals($sku2['targetManufacturerId'], 10);
    }

    /**
     * @return array
     */
    private function getTestData(): array {
        $skus = [
            new SKU('SKU1', 10, true),
            new SKU('SKU2', 10),
            new SKU('SKU3', 10),
        ];
        $batch = new Batch(1, 1, $skus);

        $skus = [
            new SKU('SKU4', 10, true),
            new SKU('SKU5', 10),
        ];
        $batch2 = new Batch(2, 1, $skus);

        return [$batch, $batch2];
    }
}
