<?php

declare(strict_types = 1);

/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type;

final class Curation_Automation_Item {
  /**
   * @var int
   */
  private int $curation_batch_id;

  /**
   * @var string
   */
  private string $vi_sku;

   /**
    *
    * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type
    */
  private Curation_Batch_Automation_Type $curation_batch_automation_type_id;

  /**
   * @var int
   */
  private int $ds_model_score;

  /**
   * @var int
   */
  private int $vs_predicted_style_id;

  /**
   * @var int
   */
  private int $vss_predicted_substyle_id;

  /**
   * @var bool
   */
  private bool $unwhitelabel;

    /**
     * Curation_Automation_item constructor
     *
     * @param int $curation_batch_id CurationBatchId
     * @param string $vi_sku ViSku
     * @param Curation_Batch_Automation_Type $curation_batch_automation_type_id CurationBatchAutomationTypeID
     * @param int $ds_model_score DSModelScore
     * @param int $vs_predicted_style_id VSPredictedStyleID
     * @param int $vss_predicted_substyle_id VSSPredictedSubstyleID
     * @param bool $unwhitelabel Unwhitelabel
     */
  public function __construct(
      int $curation_batch_id,
      string $vi_sku,
      Curation_Batch_Automation_Type $curation_batch_automation_type_id,
      int $ds_model_score,
      int $vs_predicted_style_id,
      int $vss_predicted_substyle_id,
      bool $unwhitelabel
  ) {
    $this->curation_batch_id = $curation_batch_id;
    $this->vi_sku = $vi_sku;
    $this->curation_batch_automation_type_id = $curation_batch_automation_type_id;
    $this->ds_model_score = $ds_model_score;
    $this->vs_predicted_style_id = $vs_predicted_style_id;
    $this->vss_predicted_substyle_id = $vss_predicted_substyle_id;
    $this->unwhitelabel = $unwhitelabel;
  }

  /**
   *
   * @return int
   */
  public function get_curation_batch_id(): int {
    return $this->curation_batch_id;
  }

  /**
   *
   * @return string
   */
  public function get_vi_sku(): string {
    return $this->vi_sku;
  }

  /**
   *
    * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type
   */
  public function get_curation_batch_automation_type_id(): Curation_Batch_Automation_Type {
    return $this->curation_batch_automation_type_id;
  }

  /**
   *
   * @return int
   */
  public function get_ds_model_score(): int {
    return $this->ds_model_score;
  }

  /**
   *
   * @return int
   */
  public function get_vs_predicted_style_id(): int {
    return $this->vs_predicted_style_id;
  }

  /**
   *
   * @return int
   */
  public function get_vss_predicted_substyle_id(): int {
    return $this->vss_predicted_substyle_id;
  }

  /**
   *
   * @return bool
   */
  public function get_unwhitelabel(): bool {
    return $this->unwhitelabel;
  }
}
