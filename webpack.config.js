/**
 * Curation Tool webpack config
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

const {createWebpackConfig} = require('@wayfair/app');

const wayfairWebpackConfig = createWebpackConfig({
  title: 'Curation Tool',
  flow: true,
  react: true,
  typescript: true,

  createOutputPath: ({pathJoin, cwd, brand, locale}) =>
    pathJoin(cwd, 'public/d/curation-tool/bundles', brand, locale),

  createPublicPath: ({urlJoin, publicPathRoot, brand, locale}) =>
    urlJoin(
      publicPathRoot,
      'd/curation-tool/bundles',
      brand,
      locale,
      '/'
    ),
});

// the workaround to build UI (load po translations)
// should be removed when we'll upgrade "@wayfair/app" to 11+ version
module.exports = env => {
  const finalConfig = wayfairWebpackConfig(env);

  finalConfig.module = {
    rules: [
      {
        test: /\.po$/,
        use: [require.resolve('json-loader'), require.resolve('po-loader')],
      },
      ...finalConfig.module.rules,
    ],
  };

  return finalConfig;
};
