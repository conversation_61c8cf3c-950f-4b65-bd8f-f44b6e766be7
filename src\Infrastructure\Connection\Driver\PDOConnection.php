<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace App\Infrastructure\Connection\Driver;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use WF\Db\ConnectionFactoryInterface;
use WF\Shared\Interfaces\PDO_Like;

use function mb_strtoupper;
use function sprintf;

class PDOConnection implements PDOProxyInterface, PDO_Like
{
    private const T_SQL_ARRAY_STR_POST_FIX = '_array';
    private ?Connection $dbalConnection = null;
    private ConnectionFactoryInterface $dbalConnectionFactory;
    private ConnectionProperties $connectionProperties;

    /**
     * PDOConnection constructor.
     *
     * @param ConnectionFactoryInterface $dbalConnectionFactory
     * @param ConnectionProperties $connectionProperties
     */
    public function __construct(
        ConnectionFactoryInterface $dbalConnectionFactory,
        ConnectionProperties $connectionProperties
    ) {
        $this->dbalConnectionFactory = $dbalConnectionFactory;
        $this->connectionProperties = $connectionProperties;
    }

    /**
     * Lazy connection initializing
     *
     * @return Connection
     */
    private function getConnection(): Connection
    {
        if ($this->dbalConnection === null) {
            $this->dbalConnection = $this->dbalConnectionFactory->getConnectionByCode(
                ...$this->connectionProperties->toArray()
            );
        }

        return $this->dbalConnection;
    }

    /**
     * @param int $count
     * @param string $prefix
     * @param string $dataType
     * @return string
     */
    public function paramsForList(int $count, string $prefix = '?', string $dataType): string
    {
        $prefix = $prefix === '?' ? 'json' : $prefix;
        $arrayStr = $prefix . self::T_SQL_ARRAY_STR_POST_FIX;

        $upperDataType = mb_strtoupper($dataType);

        return sprintf('(SELECT CAST(value as %s) AS value FROM OPENJSON(:%s))', $upperDataType, $arrayStr);
    }

    /**
     * @param string $statement
     * @param array $driver_options
     * @return StatementProxy
     *
     * @throws Exception
     */
    public function prepare(string $statement, array $driver_options = []): StatementProxy
    {
        $statementProxy = $this->getConnection()->prepare($statement);

        return new StatementProxy($statementProxy);
    }

    /**
     * @return bool
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * @return bool
     * @throws \Doctrine\DBAL\ConnectionException
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * @return bool
     * @throws \Doctrine\DBAL\ConnectionException
     */
    public function rollBack(): bool
    {
        return $this->getConnection()->rollBack();
    }
}
