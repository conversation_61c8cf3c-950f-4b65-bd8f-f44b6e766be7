<?php declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model;

final class White_Label_Batch_Response implements White_Label_Batch_Base_Response {
  /**
   * @var string
   */
  private $whiteLabelRequestId;

  /**
   * @var int|null
   */
  private $whiteLabelBatchId;

  /**
   * @var array
   */
  private $skus;

  /**
   * @var array
   */
  private $configuration;

  /**
   * @var bool
   */
  private $brandCatalogIdPresent;

  /**
   * @param string   $whiteLabelRequestId   White Label Request ID
   * @param int|null $whiteLabelBatchId     White Label Batch ID
   * @param array    $skus                  SKUs
   * @param array    $configuration         Configuration
   * @param bool     $brandCatalogIdPresent Brand Catalog ID present?
   */
  public function __construct(
      string $whiteLabelRequestId,
      ?int $whiteLabelBatchId,
      array $skus,
      array $configuration,
      bool $brandCatalogIdPresent
  ) {
    $this->whiteLabelRequestId   = $whiteLabelRequestId;
    $this->whiteLabelBatchId     = $whiteLabelBatchId;
    $this->skus                  = $skus;
    $this->configuration         = $configuration;
    $this->brandCatalogIdPresent = $brandCatalogIdPresent;
  }

  /**
   * @return string
   */
  public function getWhiteLabelRequestId() : string {
    return $this->whiteLabelRequestId;
  }

  /**
   * @return int|null
   */
  public function getWhiteLabelBatchId() : ?int {
    return $this->whiteLabelBatchId;
  }

  /**
   * @return array
   */
  public function getSkus() : array {
    return $this->skus;
  }

  /**
   * @return array
   */
  public function getConfiguration() : array {
    return $this->configuration;
  }

  /**
   * @return bool
   */
  public function isBrandCatalogIdPresent() : bool {
    return $this->brandCatalogIdPresent;
  }

  /**
   * @return bool
   */
  public function isRequestSuccessful() : int {
    return 200;
  }
}
