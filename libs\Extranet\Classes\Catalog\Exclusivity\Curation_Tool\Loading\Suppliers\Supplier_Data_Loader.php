<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Shared\Traits\Logging_Trait;

class Supplier_Data_Loader {
    use Logging_Trait;
  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Factory
   */
  private $factory;

  /**
   * Data_Store constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                          $dao     Curation_Tool_DAO
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Factory $factory Supplier_Data_Factory
   */
  public function __construct(Curation_Tool_DAO $dao, Supplier_Data_Factory $factory) {
    $this->dao     = $dao;
    $this->factory = $factory;
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Collection
   */
  public function get_for_skus(array $skus) : Supplier_Data_Collection {
    $rows = $this->dao->get_skus_suppliers($skus);
    $collection = $this->factory->create_supplier_collection();

    $data = [];

    // group data by skus
    foreach ($rows as $row) {
      $data[$row['sku']][] = $row;
    }

    //
    foreach ($data as $sku => $sku_data) {
      $collection->set_for_sku($sku, $this->factory->create_supplier_data($sku_data));
    }

    return $collection;
  }
}