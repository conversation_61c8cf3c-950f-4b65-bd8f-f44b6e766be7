<?php
/**
 * The DAO for the Batch Verification tool
 *
 * PHP version 5
 *
 * <AUTHOR> <m<PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\Product\Media;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQL;
use App\Infrastructure\Helper\SQLBulkHelper;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Shared\Models\Product\Media\Batch_Verification_Item_Model;
use PDO;

class Batch_Verification_Postgres_DAO implements LoggerAwareInterface {
  use LoggerTrait;
  use LoggerAwareTrait;
  
  private PostgresConnection $pdo_psql;

  /**
   * Batch_Verification_MSSQL_DAO constructor.
   *
   * @param PostgresConnection $pdo_psql PDO Psql
   */
  public function __construct(PostgresConnection $pdo_psql) {
    $this->pdo_psql = $pdo_psql;
  }

  /**
   * @param int    $reason_id         Reason ID
   * @param string $excluded_reason   Excluded Reason
   * @param int    $sku_id            SKU ID
   *
   * @return void
   */
  public function update_items_exclude_reason(int $reason_id, string $excluded_reason, int $sku_id) {
    $sql = "UPDATE \"tblVerificationItem\" 
            SET \"ViExcludedReasonID\" = ?, \"ViRemark\" = ? 
            WHERE \"ViID\" = ?";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute([$reason_id, $excluded_reason, $sku_id])) {
      throw new ExecutionException(sprintf('Failed to update exclude reason: %s', implode(', ', $statement->errorInfo()) . $sql));
    }
  }

}
