<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

class Rebrand_Project implements \JsonSerializable {
  /**
   * @var int
   */
  private $id; /** @phpstan-ignore-line */

  /**
   * @var string
   */
  private $name; /** @phpstan-ignore-line */

  /**
   * Curation_Item constructor.
   */
  public function __construct() {
    // created only by PDO
  }

  /**
   * @param int $id
   */
  public function setId(int $id): void {
    $this->id = $id;
  }

  /**
   * @return int
   */
  public function get_id() : int {
    return $this->id;
  }

  /**
   * @param string $name
   */
  public function setName(string $name): void {
    $this->name = $name;
  }

  /**
   * @return string
   */
  public function name() {
    return $this->name;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'id'   => $this->id,
        'name' => $this->name
    ];
  }
}