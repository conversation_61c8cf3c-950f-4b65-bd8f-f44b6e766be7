<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Curation_Saver;
use Psr\Log\LoggerInterface;
use WF\Shared\SLO\SLOFactory;
use WF\Shared\Traits\Logging_Trait;

class Curation_Section_Service {
  use Logging_Trait;

  const SLO_SERVICE_NAME = 'brand-workflows-curation-tool.section-service.';
  /**
   * @var Section_Loader_Interface
   */
  private $sectionLoader;

  /**
   * @var Section_Additional_Data_Loader
   */
  private $sectionAdditionalDataLoader;

  /**
   * @var Automatic_Curation_Saver[]|iterable
   */
  private $automaticCurationSavers;

  private SLOFactory $slo;

  /**
   * @param Section_Loader_Interface       $sectionLoader               Section Loader
   * @param Section_Additional_Data_Loader $sectionAdditionalDataLoader Section Additional Data Loader
   * @param Automatic_Curation_Saver[]|iterable     $automaticCurationSavers     Automatic_Curation_Saver
   * @param LoggerInterface|null                    $logger                      Logger
   */
  public function __construct(
      Section_Loader_Interface $sectionLoader,
      Section_Additional_Data_Loader $sectionAdditionalDataLoader,
      iterable $automaticCurationSavers,
      ?LoggerInterface $logger = null
  ) {
    $this->sectionLoader               = $sectionLoader;
    $this->sectionAdditionalDataLoader = $sectionAdditionalDataLoader;
    $this->automaticCurationSavers     = $automaticCurationSavers;
    $this->slo                         = new SLOFactory();
    $this->logger                      = $logger;
  }

  /**
   * @param int $batchID Batch ID
   *
   * @return Section[]
   * @throws \Exception
   */
  public function load(int $batchID) : array {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    $slo->withTags(['batchId' => $batchID]);
    $sections = $this->sectionLoader->getSections($batchID);
    $this->log_info(
        'Loading section for batch',
        ['batch_id' => $batchID]
    );

    $this->sectionAdditionalDataLoader->populate($batchID, $sections);

    $slo->stop();
    return $sections;
  }

  /**
   * @param int $batchID    Batch ID
   * @param int $employeeID Employee ID
   *
   * @return Section[]
   * @throws \Exception
   */
  public function loadWithAutomaticCurationSaving(int $batchID, int $employeeID) : array {
    /**
     * @TODO improve performance for loading - we don't need all details and all groupings to apply automatic saving
     */
    if($batchID===0){
      return [];
    }

    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    $slo->withTags(['batchId' => $batchID]);
    $sections      = $this->sectionLoader->getSections($batchID);

    foreach ($this->automaticCurationSavers as $automaticCurationSaver) {
      $this->log_info(
          sprintf('Running Automatic Curation Saver "%s"', get_class($automaticCurationSaver)),
          ['batch_id' => $batchID, 'saver' => get_class($automaticCurationSaver)]
      );
      $affectedRows = $automaticCurationSaver->execute($batchID, $sections, $employeeID);
      $this->log_info(
          'Loading sections with automatic curation saving for batch',
          ['batch_id' => $batchID, 'employeeId' => $employeeID, 'sections' => $sections, 'affected_rows' => $affectedRows]
      );

      // if items were curated automatically, reload sections
      if ($affectedRows > 0) {
        $sections = $this->sectionLoader->getSections($batchID);
      }
    }

    $this->log_info(
        'Population additional data for sections of batch',
        ['batch_id' => $batchID]
    );
    $this->sectionAdditionalDataLoader->populate($batchID, $sections);
    $slo->stop();

    return $sections;
  }
}
