/**
 * Section header of the curation page sections
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import CurationSkuDecision from './curation_sku_decision';
import {Button, Checkbox, IconV2 as Icon, Grid, Column, Text, TEXT_ALIGNMENTS, TEXT_STYLE, withStickyHeader} from '@wayfair/homebase-extranet';
import './curation_page_header.scss';
import CurationToolShapes from './curation_tool_shapes';
import CurationToolProgressBar from './curation_tool_progress_bar';
import {faPlus, faMinus} from '@fortawesome/free-solid-svg-icons';

const isButtonSaveAllEnabled = (
  checkedAll,
  checkedIndeterminate,
  decision,
  curationItems,
  selectedRows
) => {
  if (decision.exclusionReasonId !== null && selectedRows.length > 0) {
    return true;
  }

  let needSelectStyleRejectedReason = false;
  for (let i = 0; i < curationItems.length; i++) {
    if (!selectedRows.includes(curationItems[i].sku)) {
      return;
    }
    if (
        (curationItems[i].suggestedStyles.length > 0 &&
            !curationItems[i].suggestedStyles.includes(decision.styleId)) ||
        (curationItems[i].suggestedSubstyles.length > 0 &&
            !curationItems[i].suggestedSubstyles.includes(decision.substyleId))
    ) {
      needSelectStyleRejectedReason = true;
    }
  }


  if (needSelectStyleRejectedReason) {
    if (decision.suggestedStyleRejectionReasonId !== null) {
      return true;
    }
    return false;
  }

  return (
    (checkedAll || checkedIndeterminate) &&
    (decision.exclusionReasonId || decision.manufacturerId)
  );
};

const CurationPageHeader = ({
  title,
  onSelectAll,
  checkedAll,
  checkedIndeterminate,
  decision,
  onDecisionChange,
  curationConfig,
  onSaveAllClick,
  onExpandClick,
  isExpanded,
  skusSavedCount,
  skusTotalCount,
  curationItems,
  selectedRows,
  suggestedStyleRejectionReasons,
}) => (
  <header className="curation_header">
    {!isExpanded && (
      <Grid alignItems="flex-start">
        <Column size={3} resetChildStyles>
          <div>
            <Button
              onClick={() => onExpandClick(!isExpanded)}
              title={title}
              text
              fullWidth
            >
              <div className="text_xxl text_bold text_overflow">
                <Icon icon={faPlus} /> {title}
              </div>
            </Button>
          </div>
        </Column>
        <Column size={1}>
          <CurationToolProgressBar
            savedCount={skusSavedCount}
            totalCount={skusTotalCount}
          />
        </Column>
      </Grid>
    )}
    {isExpanded && (
      <React.Fragment>
        <Grid alignItems="flex-start">
          <Column size={3} resetChildStyles>
            <Button
              onClick={() => onExpandClick(!isExpanded)}
              title={title}
              text
              fullWidth
            >
              <div className="text_xxl text_bold text_overflow">
                <Icon name={faMinus} /> {title}
              </div>
            </Button>
          </Column>
          <Column size={1}>
            <CurationToolProgressBar
              savedCount={skusSavedCount}
              totalCount={skusTotalCount}
            />
          </Column>
          <Column size={8}>
            <CurationSkuDecision
              displayHorizontal
              curationConfig={curationConfig}
              decision={decision}
              onDecisionChange={onDecisionChange}
              suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
              locatedArea={'header'}
              changeCheckedElement={checkedAll || checkedIndeterminate}
              curationItems={curationItems}
              selectedRows={selectedRows}
            />
          </Column>
          <Column size={1}>
            <Button
              disabled={
                !isButtonSaveAllEnabled(
                  checkedAll,
                  checkedIndeterminate,
                  decision,
                  curationItems,
                  selectedRows
                )
              }
              onClick={onSaveAllClick}
            >
              <Translation msgid="CurationTool.SaveAll" />
            </Button>
          </Column>
        </Grid>
        <Grid>
          <Column size={1}>
            <div className="text_bold">
              <Checkbox
                label={Translation({msgid: 'CurationTool.Sku'})}
                onChange={e => onSelectAll(e.target.checked)}
                checked={checkedAll}
                indeterminate={checkedIndeterminate}
              />
            </div>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Name" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Manufacturer" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Class" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Supplier" />
            </Text>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Price" />
            </Text>
          </Column>
          <Column size={2}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Image" />
            </Text>
          </Column>
          <Column size={3}>
            <Grid>
              <Column size={6}>
                <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.ExclusionReason" />
                </Text>
              </Column>
              <Column size={6}>
                <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.FinalStyle" />
                </Text>
              </Column>
            </Grid>
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Actions" />
            </Text>
          </Column>
        </Grid>
      </React.Fragment>
    )}
  </header>
);

CurationPageHeader.propTypes = {
  title: PropTypes.string.isRequired,
  curationConfig: CurationToolShapes.curationConfigShape.isRequired,
  decision: CurationToolShapes.decisionShape.isRequired,
  checkedIndeterminate: PropTypes.bool,
  checkedAll: PropTypes.bool,
  isExpanded: PropTypes.bool,
  skusSavedCount: PropTypes.number,
  skusTotalCount: PropTypes.number,
  onDecisionChange: PropTypes.func.isRequired,
  onSelectAll: PropTypes.func.isRequired,
  onSaveAllClick: PropTypes.func.isRequired,
  onExpandClick: PropTypes.func.isRequired,
  curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  suggestedStyleRejectionReasons: PropTypes.arrayOf(
    CurationToolShapes.reasonShape
  ).isRequired,
};

CurationPageHeader.defaultProps = {
  checkedIndeterminate: false,
  checkedAll: false,
  isExpanded: true,
  skusSavedCount: 0,
  skusTotalCount: 0,
};

export default withStickyHeader(CurationPageHeader);
