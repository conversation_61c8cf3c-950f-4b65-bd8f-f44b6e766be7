/**
 * Shared constants for curation tool index and qa
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import Translation from '@wayfair/translation';

const TOAST_ERROR_TYPE = 'error';
const TOAST_SUCCESS_TYPE = 'success';

const SAVE_ERROR_MESSAGE = (
  <Translation msgid="CurationTool.FailedToSaveDataExclamationPoint" />
);

const TIME_BEFORE_PAGE_REFRESH = 5000;

const INDEX_STATUS = 0;
const QA_STATUS = 1;
const COMPLETE_STATUS = 2;

const STATUSES = [INDEX_STATUS, QA_STATUS, COMPLETE_STATUS];

const STATUS_LABEL_MAP = {
  [INDEX_STATUS]: <Translation msgid="CurationTool.Curation" />,
  [QA_STATUS]: <Translation msgid="CurationTool.QA" />,
  [COMPLETE_STATUS]: <Translation msgid="CurationTool.Complete" />,
};

export {
  TOAST_ERROR_TYPE,
  TOAST_SUCCESS_TYPE,
  SAVE_ERROR_MESSAGE,
  TIME_BEFORE_PAGE_REFRESH,
  STATUSES,
  STATUS_LABEL_MAP,
  INDEX_STATUS,
  QA_STATUS,
  COMPLETE_STATUS,
};
