<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Tool_Curation_Decision\External;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Prophecy\Prophecy\ObjectProphecy;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Move_To_Brand_Decision_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\External\Context_Data_Decision_Matcher;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service;

class Context_Data_Decision_Matcher_Test extends TestCase
{
    use Prophe<PERSON><PERSON><PERSON><PERSON>;
    /**
     * @var Context_Data_Decision_Service|ObjectProphecy
     */
    private $context_decision_service;

    /**
     * @var Context_Data_Decision_Matcher
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->context_decision_service = $this->prophesize(Context_Data_Decision_Service::class);

        $this->subject = new Context_Data_Decision_Matcher($this->context_decision_service->reveal());
    }

    /**
     * @param bool $is_fully_filled      is fully filled
     * @param bool $is_same_manufacturer is same manufacturer
     *
     * @test
     * @dataProvider not_able_to_match_data
     *
     * @return void
     */
    public function it_returns_null_when_not_able_to_match(bool $is_fully_filled, bool $is_same_manufacturer)
    {
        $decision = $this->prophesize(Context_Data::class);
        $decision->is_fully_filled()->willReturn($is_fully_filled);
        $decision->has_same_manufacturer(Argument::any())->willReturn($is_same_manufacturer);

        $this->context_decision_service->find_closest_match(Argument::cetera())->willReturn($decision);

        $this->assertEquals(null, $this->subject->find_closest_match(11, 22, 1));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_decision_data_when_able_to_match()
    {
        $decision = $this->prophesize(Context_Data::class);
        $decision->is_fully_filled()->willReturn(true);
        $decision->has_same_manufacturer(Argument::any())->willReturn(true);
        $decision->get_manufacturer_id()->willReturn(11);
        $decision->get_style_id()->willReturn(2);
        $decision->get_substyle_id()->willReturn(3);
        $decision->get_price_tier()->willReturn(1);

        $this->context_decision_service->find_closest_match(Argument::cetera())->willReturn($decision);

        $result = $this->subject->find_closest_match(11, 22, 33);

        $this->assertInstanceOf(Assortment_Curation_Move_To_Brand_Decision_Data::class, $result);
        $this->assertEquals(22, $result->get_target_manufacturer_id());
        $this->assertEquals(2, $result->get_target_style_id());
        $this->assertEquals(3, $result->get_target_substyle_id());
        $this->assertEquals(1, $result->get_target_price_tier());
    }

    /**
     * @return array
     */
    public function not_able_to_match_data(): array
    {
        return [
            'is not fully filled, has same manufacturer' => [false, true],
            'is not fully filled, has other manufacturer' => [false, false],
            'is fully filled, has other manufacturer' => [true, false],
        ];
    }
}
