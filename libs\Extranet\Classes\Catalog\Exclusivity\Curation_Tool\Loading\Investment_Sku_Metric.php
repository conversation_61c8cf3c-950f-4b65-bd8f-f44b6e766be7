<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

class Investment_Sku_Metric {

  /**
   * @var string
   */
  private $sku;

  /**
   * @var bool
   */
  private $is_predicted_winner;

  /**
   *
   * @param string $sku                 the SkU code
   * @param bool   $is_predicted_winner this SKU is a Predicted Winner?
   */
  public function __construct(string $sku, bool $is_predicted_winner) {
    $this->sku                 = $sku;
    $this->is_predicted_winner = $is_predicted_winner;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return bool
   */
  public function is_predicted_winner() : bool {
    return $this->is_predicted_winner;
  }
}
