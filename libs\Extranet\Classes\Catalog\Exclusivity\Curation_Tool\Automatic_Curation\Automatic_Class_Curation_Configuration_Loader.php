<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface;

class Automatic_Class_Curation_Configuration_Loader {
  /**
   * @var Automatic_Class_Curation_Configuration_Storage
   */
  private $dao;

  /**
   * @param Automatic_Class_Curation_Configuration_Storage $dao Class_Curation_Automatic_Configuration_Storage
   */
  public function __construct(Automatic_Class_Curation_Configuration_Storage $dao) {
    $this->dao = $dao;
  }

  /**
   * @param World_Region_Interface $region Region
   *
   * @return Automatic_Class_Curation_Configuration
   */
  public function get_configuration(World_Region_Interface $region) : Automatic_Class_Curation_Configuration {
    $items = $this->dao->get_configuration_items($region->getId());

    return new Automatic_Class_Curation_Configuration($items);
  }
}