/**
 * curation_tool_shapes react.js component
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import PropTypes from 'prop-types';

export const BRAND_TYPE_TAIL = 3;

export const QA_STATUS_CURATION_SAVED_DECISION = 1;
export const QA_STATUS_PENDING = 1;
export const QA_STATUS_ACCEPTED = 2;
export const QA_STATUS_REJECTED = 3;
export const QA_STATUS_UPDATED = 5;

export const QA_FILTER_PENDING = 'pending';
export const QA_FILTER_PENDING_WITH_SUGGESTED_STYLES =
    'pending_with_style_suggestions';
export const QA_FILTER_PENDING_WITHOUT_SUGGESTED_STYLES =
    'pending_without_style_suggestions';
export const QA_FILTER_APPROVED = 'approved';
export const QA_FILTER_REJECTED = 'rejected';
export const QA_FILTER_UPDATED = 'updated';
export const CURATION_SAVED_DECISION = 'saved_decision';


export const BATCH_PROCESS_MANUAL = 1;
export const BATCH_PROCESS_AUTOMATED = 2;

export const PAGE_LIMIT = 300;

export const COLLECTION_LIMIT = 10;

const curationStatusMap = [
    {
      key:QA_FILTER_PENDING,
      value: 0
    },
  {
    key:CURATION_SAVED_DECISION,
    value: QA_STATUS_CURATION_SAVED_DECISION
  },
  {
    key:QA_FILTER_APPROVED,
    value: QA_STATUS_ACCEPTED
  },
  {
    key:QA_FILTER_REJECTED,
    value: QA_STATUS_REJECTED
  },
  {
    key:QA_FILTER_UPDATED,
    value: QA_STATUS_UPDATED
  }
  ];

export const getQaCodeByCurationStatus = (curationStatus) =>{
  let result = -1;
  const length = curationStatusMap.length;
  for (let i = 0; i < length; i++) {
    if(curationStatus === curationStatusMap[i].key) {
      result = curationStatusMap[i].value;
    }
  }
  return result;
}

export const getSuppliersString = (suppliers) =>{
  let supplier;
  if(suppliers && suppliers.length > 0) {
    let length = suppliers.length
    supplier = "";
    for (let i = 0; i < length - 1; i++) {
      supplier = supplier + suppliers[i].value +"|"
    }
    supplier = supplier + suppliers[length-1].value
  }
  return supplier;
}


const exclusionReasonShape = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.number,
});

const priceTierShape = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.number,
});

const styleShape = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.number,
});

const substyleShape = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.number,
  styleId: PropTypes.number,
});

const manufacturerShape = PropTypes.shape({
  label: PropTypes.string,
  value: PropTypes.number,
});

const styleManufacturerShape = PropTypes.shape({
  manufacturerId: PropTypes.number,
  substyleId: PropTypes.number,
  priceTier: PropTypes.number,
  granularStyleId: PropTypes.number,
});

const curationConfigShape = PropTypes.shape({
  exclusionReasons: PropTypes.arrayOf(exclusionReasonShape).isRequired,
  priceTiers: PropTypes.arrayOf(priceTierShape).isRequired,
  styles: PropTypes.arrayOf(styleShape).isRequired,
  substyles: PropTypes.arrayOf(substyleShape).isRequired,
  granularStyles: PropTypes.arrayOf(substyleShape),
  manufacturers: PropTypes.arrayOf(manufacturerShape).isRequired,
  styleManufacturers: PropTypes.arrayOf(styleManufacturerShape).isRequired,
});

const decisionShape = PropTypes.shape({
  styleId: PropTypes.number,
  substyleId: PropTypes.number,
  priceTier: PropTypes.number,
  manufacturerId: PropTypes.number,
  granularStyleId: PropTypes.number,
  exclusionReasonId: PropTypes.number,
  isSuggestedStyleMatched: PropTypes.bool,
  suggestedStyleRejectionReasonId: PropTypes.number,
});

const curationItemShape = PropTypes.shape({
  sku: PropTypes.string,
  name: PropTypes.string,
  class: PropTypes.string,
  suppliers: PropTypes.array,
  isCanadianSupplier: PropTypes.bool,
  isPerigoldSupplier: PropTypes.bool,
  manufacturer: PropTypes.string,
  manufacturerBrwId: PropTypes.number,
  price: PropTypes.number,
  priceOptionsCount: PropTypes.number,
  image: PropTypes.string,
  lastCloneDate: PropTypes.string,
  isRecentlyCloned: PropTypes.bool,
  savedAt: PropTypes.string,
  savedBy: PropTypes.string,
  rejectedAt: PropTypes.string,
  rejectedBy: PropTypes.string,
  rejectedNote: PropTypes.string,
  url: PropTypes.string,
  type: PropTypes.string,
  decision: decisionShape,
  isKitsco: PropTypes.bool,
  kitParents: PropTypes.arrayOf(PropTypes.string),
  relatedKits: PropTypes.arrayOf(PropTypes.string),
  readonly: PropTypes.bool,
  qaStatus: PropTypes.int,
  brandCatalogName: PropTypes.string,
  suggestedStyles: PropTypes.arrayOf(PropTypes.number),
  suggestedSubstyles: PropTypes.arrayOf(PropTypes.number),
  isWhitelabelDownstream: PropTypes.bool,
});

const curationSectionShape = PropTypes.shape({
  title: PropTypes.string,
  curationItems: PropTypes.arrayOf(curationItemShape),
  selectedRows: PropTypes.arrayOf(PropTypes.string),
  changedRows: PropTypes.arrayOf(PropTypes.string),
  decision: decisionShape,
  isExpanded: PropTypes.bool,
});

const reasonShape = PropTypes.shape({
  id: PropTypes.number,
  name: PropTypes.string,
});

const batchShape = PropTypes.shape({
  curator: PropTypes.string,
  status: PropTypes.number,
  created_at: PropTypes.string,
  process_type: PropTypes.string,
});

const shapes = {
  decisionShape,
  curationConfigShape,
  curationItemShape,
  curationSectionShape,
  reasonShape,
  batchShape,
};

export default shapes;
