/**
 * Presents the shared sku between kits
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Grid, Column, Text, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import CurationSkuImage from './curation_sku_image';
import CurationSkuSupplier from './curation_sku_supplier';
import CurationSkuManufacturer from './curation_sku_manufacturer';
import CurationSkuName from './curation_sku_name';
import './curation_shared_sku.scss';
import CurationToolShapes from './curation_tool_shapes';
import Translation from '@wayfair/translation';
import CurationSharedSkuSave from './curation_shared_sku_save';

class CurationSharedSku extends React.Component {
  static propTypes = {
    curationItem: CurationToolShapes.curationItemShape.isRequired,
    onSave: PropTypes.func,
  };

  static defaultProps = {
    curationItem: {},
    onSave() {},
  };

  shouldComponentUpdate(nextProps) {
    return (
      this.props.curationItem.savedAt !== nextProps.curationItem.savedAt ||
      this.props.curationItem.savedBy !== nextProps.curationItem.savedBy ||
      this.props.curationItem.rejectedAt !==
        nextProps.curationItem.rejectedAt ||
      this.props.curationItem.rejectedBy !==
        nextProps.curationItem.rejectedBy ||
      this.props.curationItem.rejectedNote !==
        nextProps.curationItem.rejectedNote ||
      this.props.curationItem.isKitsco !== nextProps.curationItem.isKitsco
    );
  }

  render() {
    return (
      <div className="CurationSharedSku">
        <Grid>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.sku}</Text>
          </Column>
          <Column size={1}>
            <CurationSkuName
              name={this.props.curationItem.name}
              url={this.props.curationItem.url}
            />
          </Column>
          <Column size={1}>
            <CurationSkuManufacturer
              name={this.props.curationItem.manufacturer}
              brwid={this.props.curationItem.manufacturerBrwId}
            />
          </Column>
          <Column size={1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.class}</Text>
          </Column>
          <Column size={1}>
            <CurationSkuSupplier
              names={this.props.curationItem.suppliers}
              isCanadian={this.props.curationItem.isCanadianSupplier}
            />
          </Column>
          <Column size={1}>
            <div className="text_center">
              <p>
                <Translation msgid="CurationTool.KitParents" />
              </p>
              <p>
                {this.props.curationItem.kitParents.map(sku => (
                  <span key={sku} className="display_block">
                    {sku}
                  </span>
                ))}
              </p>
            </div>
          </Column>
          <Column size={2}>
            <CurationSkuImage
              image={this.props.curationItem.image}
              sku={this.props.curationItem.sku}
            />
          </Column>
          <Column size={3}>
            {!this.props.curationItem.readonly && (
              <CurationSharedSkuSave
                isKitsco={this.props.curationItem.isKitsco}
                savedAt={this.props.curationItem.savedAt}
                savedBy={this.props.curationItem.savedBy}
                rejectedBy={this.props.curationItem.rejectedBy}
                rejectedAt={this.props.curationItem.rejectedAt}
                rejectedNote={this.props.curationItem.rejectedNote}
                onSave={() => this.props.onSave(this.props.curationItem.sku)}
                readonly={this.props.curationItem.readonly}
              />
            )}
          </Column>
        </Grid>
      </div>
    );
  }
}

export default CurationSharedSku;
