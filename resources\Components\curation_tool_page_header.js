/**
 *
 * Curation Tool Page Header
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React,{useEffect,useState} from 'react';
import Translation from '@wayfair/translation';
import {Button, Accordion, Collapsible} from '@wayfair/homebase-extranet';
import {Column, Grid} from '@wayfair/homebase-extranet';
import PropTypes from 'prop-types';
import {IconV2 as Icon} from '@wayfair/homebase-extranet';
import './curation_tool_page_header.scss';
import BatchDetails from './curation_tool_batch_details';
import CurationToolProgressBar from './curation_tool_progress_bar';
import CurationFilterToolbar from './curation_filter_toolbar';
import {CurationPageBatchSearch} from './curation_page_batch_search';
import {CurationPageBatchSearchWithIcon} from './curation_page_batch_search_with_icon';
import {WIDTHS} from './common_layout_constants';
import {Box} from '@wayfair/homebase-extranet';
import {Block, FLEX_KEYWORDS, SPACING} from '@wayfair/homebase-extranet';
import {Text,
  TEXT_ALIGNMENTS,
  TEXT_STYLE,
  TEXT_VARIATIONS,
} from '@wayfair/homebase-extranet';
import {Alert} from '@wayfair/homebase-extranet';
import {Breadcrumbs} from '@wayfair/homebase-extranet';
import {getStatusTitle, getPageByStatus} from './curation_tool_utils';
import CurationToolShapes from './curation_tool_shapes';
import {QA_STATUS} from './curation_tool_constants';
import {faPlus, faMinus, faCheck} from "@fortawesome/free-solid-svg-icons";
import {
  CURRENT_PAGE_URL,
  getNextBatchId,
} from './curation_tool_services';
import {Loading} from "@homebase/core";
const URL_CURATION_BATCH_MANAGEMENT_REBRAND_PROJECT =
  '/v/catalog/curation_batch/index?rebrand_project_id=';
const INDEX_PAGE_URL = '/v/catalog/curation_batch/index';
const EMPTY_ANCHOR   = 'javascript:void(0);';
const ALERT_TYPE = 'warning';

const isCompleteButtonEnabled = (skusSavedCount, skusTotalCount) => {
  return skusSavedCount === skusTotalCount;
};

const CurationToolPageHeader = ({
  batchId,
  batchData,
  rebrandProject,
  skusSavedCount,
  skusTotalCount,
  onCompleteClick,
  onExpandAllClick,
  onCollapseAllClick,
  selectedFilters,
  filterOptions,
  onChangeFilter,
  isExpandedAll,
  labelComplete,
  progressLabel,
  isReadOnlyMode,
  isAutomaticCurationPostQaEnabled,
  warnings,
  batchDataLoading,
}) => {
  const isCompleteButtonDisabled = !isCompleteButtonEnabled(
    skusSavedCount,
    skusTotalCount
  );

  const [nextBatchID,setNextBatchID] = useState(batchId)
  const openBreadCrumb = () => {
    window.open(INDEX_PAGE_URL, '_blank');
  }

  useEffect(async  ()=>{
    const nextIDS = await getNextBatchId(batchId)

    if(nextIDS.length > 0) {
      setNextBatchID(nextIDS[0].batch_id)
      return;
    }
    setNextBatchID(null)
  },[])


  const goToNextBatchId = () =>{
    window.location = CURRENT_PAGE_URL +`?batch_id=${nextBatchID}`
  }



  return (
      <Block mb={SPACING.SPACE_LARGE}>
      {warnings && warnings.length>0 && (
        <Accordion defaultActivePanels={[0]}>
        <Collapsible title={`Warnings (${warnings.length})`}>
        <Block mb={SPACING.SPACE_LARGE}>
          {warnings.map(warning => (
            <Alert key={warning} variation={ALERT_TYPE}>
              {warning}
            </Alert>
          ))}
        </Block>
        </Collapsible>
    </Accordion>
      )}
      {batchId > 0 && (
        <Grid>
          <Breadcrumbs>
            <Breadcrumbs.Crumb onClick={openBreadCrumb} href="#">
              <Translation msgid="CurationTool.CurationBatchManagementPageTitle" />
            </Breadcrumbs.Crumb>
            {batchData?.status && (
              <Breadcrumbs.Crumb  testIds={{breadcrumb: 'child'}}>
                {getStatusTitle(batchData.status)}
              </Breadcrumbs.Crumb>
            )}
          </Breadcrumbs>
        </Grid>
      )}

      {(batchId === null || batchId === 0) && (
        <Grid>
          <Column size={2}>
            <CurationPageBatchSearch />
          </Column>
        </Grid>
      )}

      {batchId > 0 && (
        <>
          <Grid alignItems={FLEX_KEYWORDS.CENTER}>
              <Column size={WIDTHS.WIDTH_6} alignSelf={FLEX_KEYWORDS.BASELINE}>
                <BatchDetails
                  id={batchId}
                  status={batchData?.status}
                  createdAt={batchData?.created_at}
                  processType={batchData?.process_type}
                  curator={batchData?.curator}
                  rebrandProject={rebrandProject}
                />
              </Column>
              <Column size={WIDTHS.WIDTH_6} alignSelf={FLEX_KEYWORDS.BASELINE}>
                <Box
                  display="flex"
                  mb={4}
                  p={4}
                  flexDirection={FLEX_KEYWORDS.COLUMN}
                  gridRowGap={4}
                >

                  <div style={{justifyContent:'flex-end',display:'flex'}}>
                      <Button disabled={nextBatchID === null} onClick={goToNextBatchId}>Next</Button>
                  </div>
                  <Text fontStyle={TEXT_STYLE.BOLD}>
                    <Translation msgid="CurationTool.batchProgressTitle" />
                  </Text>
                  {batchDataLoading ? <CurationToolProgressBar
                      savedCount={skusSavedCount}
                      totalCount={skusTotalCount}
                      className="progress-bar"
                      label={
                        <Translation
                            msgid="CurationTool.XOutYSkusStatus"
                            params={{
                              skusSavedCount,
                              skusTotalCount,
                              status: progressLabel,
                            }}
                        />
                      }
                  /> : <span><Loading/></span>}
                  {!isReadOnlyMode ? (
                    <Text align={TEXT_ALIGNMENTS.CENTER}>
                      <Button
                        onClick={onCompleteClick}
                        disabled={isCompleteButtonDisabled}
                      >
                        <Block mr="small">
                          <Icon icon={faCheck} />{' '}
                        </Block>
                        {labelComplete}
                      </Button>
                    </Text>
                  ) : (
                    <Box
                      display="flex"
                      mb={4}
                      p={4}
                      flexDirection={FLEX_KEYWORDS.COLUMN}
                      gridRowGap={4}
                    >
                      <Box
                        display="flex"
                        justifyContent={FLEX_KEYWORDS.CENTER}
                        alignItems={FLEX_KEYWORDS.CENTER}
                        flexDirection={FLEX_KEYWORDS.ROW}
                        gridColumnGap={1}
                      >
                        <Box size="26px">
                          <Icon icon={faCheck} />
                        </Box>
                        <Text fontStyle={TEXT_STYLE.BOLD}>
                          <Translation msgid="CurationTool.batchCompleted" />
                          :
                        </Text>
                        <Text>{batchData?.approved_at}</Text>
                      </Box>
                      <Box
                        display="flex"
                        justifyContent={FLEX_KEYWORDS.CENTER}
                        alignItems={FLEX_KEYWORDS.CENTER}
                        flexDirection={FLEX_KEYWORDS.ROW}
                        gridColumnGap={1}
                      >
                        <Text fontStyle={TEXT_STYLE.BOLD}>
                          <Translation msgid="CurationTool.qaApprovedBy" />
                          :
                        </Text>
                        <Text>{batchData?.approved_by}</Text>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Column>
            </Grid>
          <Grid>
            <Column size={1} alignSelf="center">
              {!isExpandedAll ? (
                <Button onClick={onExpandAllClick} text>
                  <Text variation={TEXT_VARIATIONS.BOLD}>
                    <Icon icon={faPlus} />{' '}
                    <Translation msgid="CurationTool.CurationPageHeaderExpandAll" />
                  </Text>
                </Button>
              ) : (
                <Button onClick={onCollapseAllClick} text>
                  <Text variation={TEXT_VARIATIONS.BOLD}>
                    <Icon icon={faMinus} />{' '}
                    <Translation msgid="CurationTool.CurationPageHeaderCollapseAll" />
                  </Text>
                </Button>
              )}
            </Column>
            <Column size={8}>
              <CurationFilterToolbar
                onChangeFilter={onChangeFilter}
                selectedFilters={selectedFilters}
                filterOptions={filterOptions}
                isAutomaticCurationPostQaEnabled={
                  isAutomaticCurationPostQaEnabled
                }
                isQAPage={getPageByStatus(batchData.status) === QA_STATUS}
                showFilterOptionPendingWithStyles
              />
            </Column>
               <Column size={3}>
                <CurationPageBatchSearchWithIcon />
              </Column>
          </Grid>
        </>
      )}
    </Block>
  );
};

CurationToolPageHeader.propTypes = {
  batchId: PropTypes.number,
  batchData: CurationToolShapes.batchShape,
  rebrandProject: PropTypes.number,
  skusSavedCount: PropTypes.number,
  skusTotalCount: PropTypes.number,
  onCompleteClick: PropTypes.func,
  onExpandAllClick: PropTypes.func,
  onCollapseAllClick: PropTypes.func,
  isExpandedAll: PropTypes.bool,
  filterToolbarData: PropTypes.array,
  selectedFilters: PropTypes.object,
  filterOptions: PropTypes.object,
  onChangeFilter: PropTypes.func,
  labelComplete: PropTypes.node,
  progressLabel: PropTypes.node,
  isReadOnlyMode: PropTypes.bool,
  isAutomaticCurationPostQaEnabled: PropTypes.bool,
  warnings: PropTypes.arrayOf(PropTypes.string),
  batchDataLoading: PropTypes.bool,
};

CurationToolPageHeader.defaultProps = {
  batchId: 0,
  batchData: {},
  rebrandProject: 0,
  skusSavedCount: 0,
  skusTotalCount: 0,
  onCompleteClick: {},
  onExpandAllClick: {},
  onCollapseAllClick: {},
  filterToolbarData: [],
  isExpandedAll: false,
  selectedFilters: [],
  filterOptions: [],
  onChangeFilter: {},
  labelComplete: <Translation msgid="CurationTool.Complete" />,
  progressLabel: <Translation msgid="CurationTool.Reviewed" />,
  isReadOnlyMode: false,
  isAutomaticCurationPostQaEnabled: false,
  warnings: [],
  batchDataLoading: false,
};

export default CurationToolPageHeader;
