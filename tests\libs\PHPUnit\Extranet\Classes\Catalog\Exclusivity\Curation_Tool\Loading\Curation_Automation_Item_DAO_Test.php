<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_DAO;

class Curation_Automation_Item_DAO_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_qa_automation_item_storage()
    {
        $rc = new ReflectionClass(Curation_Automation_Item_DAO::class);

        $this->assertTrue($rc->implementsInterface(Curation_Automation_Item_Storage::class));
    }
}
