<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Tests\Unit;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use GuzzleHttp\Psr7\Response;
use WF\Curation\ProductionTrackingApi\Api\ApiClient;
use WF\Curation\ProductionTrackingApi\ClientConfig;

class ApiClientTest extends TestCase
{
    private const NUMBER_OF_RETRIES = 1;
    private const CONNECTION_TIMEOUT = 1;
    private const CLIENT_ID = 'client_id';
    private const CLIENT_SECRET = 'client_secret';
    private const API_URL = 'https://apiwayfaircom.csnzoo.com';
    private const AUTH_URL = 'https://ssoauthwayfaircom.csnzoo.com';

    /**
     * @var ApiClient
     */
    private $subject;

    /**
     * @var ClientConfig
     */
    private $clientConfig;

    /**
     * @var MockObject|LoggerInterface
     */
    private $logger;

    protected function setUp(): void
    {
        parent::setUp();

        /* @var LoggerInterface $logger */
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->clientConfig = new ClientConfig(
            $this->logger,
            self::NUMBER_OF_RETRIES,
            self::CONNECTION_TIMEOUT,
            self::CLIENT_ID,
            self::CLIENT_SECRET,
            self::AUTH_URL,
            self::API_URL
        );
        $this->subject = new ApiClient($this->clientConfig);
    }

    /**
     * @param array $input Input
     *
     * @test         Test - Log Error After Connection Failure
     *
     * @dataProvider getConnectionFailureParams
     * @return void
     */
    public function itShouldLogErrorAfterConnectionFailure(array $input): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = ApiHttpClient::createConnectionTimeoutClientMock();
        $this->clientConfig->setApiHttpClient(self::API_URL, $httpClient);

        $response = $this->subject->sendRequest($input['token'], $input['query'], $input['variable']);

        $this->assertNull($response);
    }

    /**
     * @param array $input Input
     * @param string|null $expectationDate Expectations
     *
     * @test         Test - Successful Response
     *
     * @dataProvider getSuccessfulResponseParams
     * @return void
     */
    public function itShouldReturnASuccessfulResponse($input): void
    {
        $this->logger->expects($this->once())
            ->method('info');

        $httpClient = ApiHttpClient::createSuccessfulClientMock();

        $this->clientConfig->setApiHttpClient(self::API_URL, $httpClient);

        $response = $this->subject->sendRequest($input['token'], $input['query'], $input['variable']);

        $responseBody = \json_decode((string)$response->getBody(), true);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals('200', $response->getStatusCode());
        $this->assertNotEmpty($responseBody['data']);
    }

    /**
     * @param array $input Input
     * @param string|null $expectationDate Expectations
     *
     * @test         Test - Successful Response with Error
     *
     * @dataProvider getSuccessfulResponseErrorParams
     * @return void
     */
    public function itShouldReturnASuccessfulResponseWithError($input): void
    {
        $this->logger->expects($this->once())
            ->method('info');

        $httpClient = ApiHttpClient::createSuccessfulClientWithErrorParamsMock();

        $this->clientConfig->setApiHttpClient(self::API_URL, $httpClient);

        $response = $this->subject->sendRequest($input['token'], $input['query'], $input['variable']);

        $responseBody = \json_decode((string)$response->getBody(), true);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals('200', $response->getStatusCode());
        $this->assertNotEmpty($responseBody['errors']);
    }

    /**
     * @param array $input Input
     * @param string|null $expectationDate Expectations
     *
     * @test         Test - Return an Error
     *
     * @dataProvider getErrenousResponseParams
     * @return void
     */
    public function itShouldReturnAnError($input): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = ApiHttpClient::createErrenousClientMock();

        $this->clientConfig->setApiHttpClient(self::API_URL, $httpClient);

        $response = $this->subject->sendRequest($input['token'], $input['query'], $input['variable']);

        $this->assertNull($response);
    }

    /**
     * @return array
     */
    public function getConnectionFailureParams(): array
    {
        return [
            '# Sample Token, Query and Variable' => [
                [
                    'token' => '1234',
                    'query' => 'query',
                    'variable' => [],
                ],
            ]
        ];
    }

    /**
     * @return array
     */
    public function getSuccessfulResponseParams(): array
    {
        return [
            '# Sample Token, Query and Variable' => [
                [
                    'token' => '1234',
                    'query' => '
                        mutation update($projectId: Int32!, $stage: String!){
                          productTracking {
                            update(
                              projectEntry: [
                                {
                                  projectId: $projectId,
                                  stage: $stage
                                }
                              ]
                            )
                          }
                        }',
                    'variable' => [
                        'projectId' => 1,
                        'stage' => '(E5) Extranet White Label'
                    ],
                ],
            ]
        ];
    }

    /**
     * @return array
     */
    public function getSuccessfulResponseErrorParams(): array
    {
        return [
            '# Sample Token, Query and Variable' => [
                [
                    'token' => '1234',
                    'query' => '
                        mutation update($projectId: Int32!, $stage: String!){
                          productTracking {
                            update(
                              projectEntry: [
                                {
                                  stage: $stage
                                }
                              ]
                            )
                          }
                        }',
                    'variable' => [
                        'stage' => '(E5) Extranet White Label'
                    ],
                ],
            ]
        ];
    }


    /**
     * @return array
     */
    public function getErrenousResponseParams(): array
    {
        return [
            '# Request with Invalid Token' => [
                [
                    'token' => '1234',
                    'query' => '
                        mutation update($projectId: Int32!, $stage: String!){
                          productTracking {
                            update(
                              projectEntry: [
                                {
                                  projectId: $projectId,
                                  stage: $stage
                                }
                              ]
                            )
                          }
                        }',
                    'variable' => [
                        'projectId' => 1,
                        'stage' => '(E5) Extranet White Label'
                    ],
                ],
            ]
        ];
    }
}
