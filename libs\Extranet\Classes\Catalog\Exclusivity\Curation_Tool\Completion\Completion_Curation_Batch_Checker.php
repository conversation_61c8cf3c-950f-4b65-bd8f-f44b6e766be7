<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface;
use WF\Shared\Traits\Logging_Trait;

class Completion_Curation_Batch_Checker {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface
   */
  private $sectionLoader;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader_Interface $sectionLoader Section Loader
   * @param LoggerInterface|null                                                                    $logger        Logger
   */
  public function __construct(Section_Loader_Interface $sectionLoader, ?LoggerInterface $logger = null) {
    $this->sectionLoader = $sectionLoader;
    $this->logger        = $logger;
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return bool
   */
  public function isCurationCompleted(int $batchId): bool {
    $sections = $this->sectionLoader->getSections($batchId);

    foreach ($sections as $section) {
      if ($this->isSectionCurated($section, $batchId)) {
        continue;
      }

      $this->log_warning(
          sprintf(
              'Curation is not completed in section "%s" for batch_id=%s',
              $section->get_title(),
              $batchId
          ),
          [
              'batch_id' => $batchId,
              'section'  => $section->get_title()
          ]
      );

      return false;
    }

    return true;
  }


  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section $section Section data with curation items
   * @param int                                                                    $batchId Batch ID
   *
   * @return bool
   */
  private function isSectionCurated(Section $section, int $batchId): bool {
    foreach ($section->get_curation_items() as $curationItem) {
      if ($this->isItemCurated($curationItem)) {
        continue;
      }

      $this->log_warning(
          sprintf(
              'Curation is not completed for SKU "%s" in section "%s" of batch_id=%s',
              $curationItem->get_sku(),
              $section->get_title(),
              $batchId
          ),
          [
              'batch_id' => $batchId,
              'section'  => json_encode($curationItem),
              'sku'      => $curationItem->get_sku()
          ]
      );

      return false;
    }

    return true;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $curationItem Curation item data
   *
   * @return bool
   */
  private function isItemCurated(Curation_Item $curationItem): bool {
    return ($curationItem->get_final_brand_id() !== null && $curationItem->get_final_brand_id() > 0)
        || ($curationItem->get_excluded_reason_id() !== null && $curationItem->get_excluded_reason_id() > 0);
  }
}
