<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Tests\Unit\Infrastructure;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Prophecy\Prophecy\ObjectProphecy;
use Psr\Http\Message\RequestFactoryInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\StreamFactoryInterface;
use Psr\Http\Message\StreamInterface;
use WF\BrandWorkflows\PuREST\Contract\UrlBuilderInterface;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTUrlBuilderException;
use WF\BrandWorkflows\PuREST\Infrastructure\RequestFactory as PuRESTRequestFactory;

final class RequestBuilderTest extends TestCase
{
    use ProphecyTrait;

    private const HTTP_METHOD_POST = 'POST';

    private const PUREST_SERVICE = 'service';
    private const PUREST_RESOURCE = 'resource';
    private const PUREST_URL = 'http://localhost/service/resource';

    private const REQUEST_BODY = 'body';
    private const EXTRA_HEADERS = ['Content-Type' => 'application/json'];

    /**
     * @var UrlBuilderInterface|ObjectProphecy
     */
    private $purestUrlBuilder;

    /**
     * @var PuRESTRequestFactory
     */
    private $puRESTRequestFactory;

    /**
     * @var RequestFactoryInterface|ObjectProphecy
     */
    private $requestFactory;

    /**
     * @var StreamFactoryInterface|ObjectProphecy
     */
    private $streamFactory;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->purestUrlBuilder = $this->prophesize(UrlBuilderInterface::class);
        $this->requestFactory = $this->prophesize(RequestFactoryInterface::class);
        $this->streamFactory = $this->prophesize(StreamFactoryInterface::class);

        $this->puRESTRequestFactory = new PuRESTRequestFactory(
            $this->purestUrlBuilder->reveal(),
            $this->requestFactory->reveal(),
            $this->streamFactory->reveal()
        );
    }

    public function testPostSuccess(): void
    {
        /** @var RequestInterface|ObjectProphecy $request */
        $request = $this->prophesize(RequestInterface::class);
        /** @var StreamInterface|ObjectProphecy $stream */
        $stream = $this->prophesize(StreamInterface::class);

        $this->purestUrlBuilder->buildUrl(self::PUREST_SERVICE, self::PUREST_RESOURCE)->willReturn(self::PUREST_URL);

        $this->streamFactory->createStream(self::REQUEST_BODY)
            ->shouldBeCalled()
            ->willReturn($stream);

        $request->withBody($stream)
            ->shouldBeCalled()
            ->willReturn($request);

        $request->withHeader(Argument::any(), Argument::any())
            ->shouldBeCalledOnce()
            ->willReturn($request);

        $request->withAddedHeader(Argument::any(), Argument::any())
            ->shouldBeCalled()
            ->willReturn($request);

        $this->requestFactory->createRequest(
            self::HTTP_METHOD_POST,
            self::PUREST_URL
        )
            ->shouldBeCalled()
            ->willReturn($request);

        $this->puRESTRequestFactory->createRequest(
            self::PUREST_SERVICE,
            self::PUREST_RESOURCE,
            self::REQUEST_BODY,
            self::EXTRA_HEADERS
        );
    }

    public function testPostPuRESTUrlBuilderException(): void
    {
        $this->purestUrlBuilder->buildUrl(Argument::cetera())->willThrow(PuRESTUrlBuilderException::class);
        $this->expectException(PuRESTUrlBuilderException::class);

        $this->puRESTRequestFactory->createRequest(
            self::PUREST_SERVICE,
            self::PUREST_RESOURCE,
            self::REQUEST_BODY,
            self::EXTRA_HEADERS
        );
    }
}
