<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Contracts\Assortment_Decision;

class Assortment_Decision_DTO_Collection {
  /**
   * @var Assortment_Decision_DTO[]
   */
  private $decisions;

  /**
   * @var Assortment_No_Decision_DTO
   */
  private $no_decision_dto;

  /**
   * @param Assortment_Decision_DTO[] $decisions decisions
   */
  public function __construct(array $decisions) {
    $decisions_by_sku = [];

    foreach ($decisions as $decision) {
      $decisions_by_sku[$decision->get_sku()] = $decision;
    }

    $this->decisions = $decisions_by_sku;

    $this->no_decision_dto = new Assortment_No_Decision_DTO();
  }

  /**
   * @param string $sku sku
   *
   * @return Assortment_Decision_DTO
   */
  public function get_for_sku(string $sku) : Assortment_Decision_DTO {
    return $this->decisions[$sku] ?? $this->no_decision_dto;
  }
}
