<?php
/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Historical_Suggested_Style_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Historical_Suggested_Style_Loader_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Historical_Suggested_Style_Postgres_DAO;

class Historical_Suggested_Style_Loader_Test extends TestCase
{
    use ProphecyTrait;
    // Mock objects for dependencies

    /**
     * @var Historical_Suggested_Style_Loader_Storage|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var Historical_Suggested_Style_Postgres_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $dao_psql;


    // Object under test
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        // Create mocks for dependencies
        $this->storage = $this->prophesize(Historical_Suggested_Style_Loader_Storage::class);
        $this->dao_psql = $this->prophesize(Historical_Suggested_Style_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        // Create an instance of the class under test
        $this->subject = new Historical_Suggested_Style_Loader(
            $this->storage->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_historical_suggested_style_info_for_batch_feature_toggle_off()
    {
        $batchId = 1;
        $this->storage->get_cached_suggested_style_info_for_batch(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->dao_psql->get_cached_suggested_style_info_for_batch(Argument::cetera())->willReturn([]);
        $this->subject->get_historical_suggested_style_info_for_batch($batchId);
    }


    /**
     * @test
     *
     * @return void
     */
    public function get_historical_suggested_style_info_for_batch_feature_toggle_on()
    {
        $batchId = 1;
        $this->storage->get_cached_suggested_style_info_for_batch(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->dao_psql->get_cached_suggested_style_info_for_batch(Argument::cetera())->willReturn([]);
        $this->subject->get_historical_suggested_style_info_for_batch($batchId);
    }
}
