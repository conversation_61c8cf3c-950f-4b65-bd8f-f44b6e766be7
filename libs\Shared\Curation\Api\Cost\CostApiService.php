<?php

namespace WF\Shared\Curation\Api\Cost;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use GuzzleHttp\Client;
use Psr\Cache\InvalidArgumentException;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Cache\Adapter\AdapterInterface;
use WF\Shared\Environment;

use WF\Shared\Logging\Logger;
use function json_decode;
use function time;

class CostApiService
{
    use LoggerTrait;
    use LoggerAwareTrait;

    public const FALLBACK_PRICE = 9999.00;

    private const BASE_URL_DEV = 'https://kube-cost-service-api.service.intradsm1.sdeconsul.csnzoo.com/api/v1/graphql';
    private const BASE_URL_PROD = 'https://kube-cost-service-api.service.intraiad1.consul.csnzoo.com/api/v1/graphql';

    private const BASE_AUTH_URL_DEV = 'https://ssoauthwayfaircom.csnzoo.com/oauth/token';
    private const BASE_AUTH_URL_PROD = 'https://sso.auth.wayfair.com/oauth/token';
    public const CACHE_KEY = 'cost_api_access_token';
    public const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 86400; // 24 hr
    public CacheItemPoolInterface $cache;

    /**
     * @var string The Client ID.
     */
    public $clientID = '';

    /**
     * @var Client Client.
     */
    public Client $httpClient;
    private CurationGraphApi $curationGraphApi;

    /**
     * Talent API constructor.
     *
     * @param string $environment ENV
     * @param string $clientID Client ID
     * @param string $clientSecret Client Secret
     */
    public function __construct(
        Logger $logger,
        string $environment,
        string $clientID,
        string $clientSecret,
        Client                 $httpClient,
        CacheItemPoolInterface $cache,
        CurationGraphApi $curationGraphApi
    ) {
        $this->logger   = $logger;
        $this->clientID       = $clientID;
        $this->clientSecret   = $clientSecret;
        $this->authEndpoint  = $environment === Environment::PRODUCTION ? self::BASE_AUTH_URL_PROD : self::BASE_AUTH_URL_DEV;
        $this->costApiEndpoint = $environment === Environment::PRODUCTION ? self::BASE_URL_PROD : self::BASE_URL_DEV;
        $this->httpClient = $httpClient;
        $this->cache = $cache;
        $this->curationGraphApi = $curationGraphApi;
    }


    /**
     * Get base cost details
     *
     * @param string $supplier_part_id
     *
     * @return float base cost
     * @throws \Exception
     */
    public function get_part_cost(int $supplier_part_id) : float { // need to pass region_id as parameter to get baseCost

        $this->logger->warn('Coming into the get_part_cost func... ');
        $token = $this->getAuthorizationToken();
        if($token !== null) {
          $url = $this->costApiEndpoint;
          try {
            $responseData = $this->curationGraphApi->get_part_cost_by_supplier_part_id_using_graphql_api($supplier_part_id, $token, $url);
            $supplierPartCostData = $responseData['supplierPartCostData'];
            $baseCostTerms = $supplierPartCostData[0]['baseCostTerms'];
            $baseCost = [];
            foreach ($baseCostTerms as $baseCostTerm) {
              $baseCost[] = $baseCostTerm['baseCost'];
            }
            //average the baseCost from multiple regions
            if (count($baseCost) > 1) {
              $this->info('costApiClient get_part_cost called average baseCost for multiple regions by supplierPartID ', [$supplier_part_id => $baseCost]);
              $avgBaseCost = floatval(array_sum($baseCost) / count($baseCost));
            } else {
              $avgBaseCost = floatval($baseCost[0]);
            }

            return $avgBaseCost != 0.0 ? $avgBaseCost : self::FALLBACK_PRICE;
          }
          catch (\Exception $exception) {
            $this->logger->error('Failed to load cost api data, returning FALLBACK_PRICE and continue flow');
            return self::FALLBACK_PRICE;
          }

        }

        return  self::FALLBACK_PRICE;

    }


    public function getAuthorizationToken() : ?string {
        $this->logger->warn('Coming into the Access token code ======================== ');
        $authToken = $this->getTokenFromCache();
        if ($authToken !== null) {
            return $authToken;
        }
        $request = [
            'form_params' => [
                'client_id'     => $this->clientID,
                'client_secret' => $this->clientSecret,
                'grant_type'    => 'client_credentials'
            ]
        ];
        $url     = $this->authEndpoint;
        try {
            $response = $this->httpClient->post($url, $request);
            $data = json_decode($response->getBody(), true);
            if (isset($data['access_token'])) {
                $authToken = $data['access_token'];
                $expDate = $data['expires_in'];
                $this->setTokenInCache($authToken, $expDate);
                return $authToken;
            }
            $this->logger->error('Failed to get authentication token from response', ['response' => $data]);
        } catch (\Exception $exception) {
            $this->error(
                sprintf('Failed to get access token 1: %s', $exception->getMessage()));

        }
        return null;
    }

    public function getTokenFromCache(): ?string {
      $cacheItem = $this->cache->getItem( self::CACHE_KEY);

        if ($cacheItem->isHit()) {
            $tokenData = $cacheItem->get();

            if (isset($tokenData['token']) && isset($tokenData['expires_at']) && $tokenData['expires_at'] > time()) {
                return $tokenData['token'];
            } else {
                $this->clearAccessToken(); // Clear the expired token from cache
            }
        }

        return null;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function setTokenInCache(string $authToken, string $expiryDate): void {
        $tokenData = [
            'token' => $authToken,
            'expires_at' => time() + $expiryDate
        ];
        $cacheItem = $this->cache->getItem(self::CACHE_KEY);
        $cacheItem->set($tokenData)->expiresAfter(self::CACHE_EXPIRY_THRESHOLD_IN_SECONDS);
        $this->cache->save($cacheItem);
    }

    public function clearAccessToken(): void
    {
        $this->cache->deleteItem(self::CACHE_KEY);
    }
}