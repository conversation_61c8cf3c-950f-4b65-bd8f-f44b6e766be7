<?php

namespace WF\Tests\PHPunit\Shared\ProductManagement\Curation\Collider;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Kit_Collider;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Postgres_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Kit_Collision_SKU_DAO;

class Curation_Kit_Collider_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Curation_Collider_DAO
     */
    private $dao;

    /**
     * @var Curation_Collider_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var Kit_Collision_SKU_DAO
     */
    private $kit_logic_dao;

    /**
     * @var FeatureTogglesInterface
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Collider_DAO::class);
        $this->dao_psql = $this->prophesize(Curation_Collider_Postgres_DAO::class);
        $this->kit_logic_dao = $this->prophesize(Kit_Collision_SKU_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Curation_Kit_Collider(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->kit_logic_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }


    /**
     * @test
     *
     * @return void
     */
    public function test_get_collisions_for_skus_feature_toggle_off(): void
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->getExistingCurationSKUS(['SKU1'])->willReturn(['SKU1']);
        $this->dao->getKitCollisions(['SKU1'])->willReturn(['SKU1']);
        $response = $this->subject->getCollisionsForSKUS(['SKU1']);
        $this->assertEquals([], $response);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_get_collisions_for_skus_feature_toggle_on(): void
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->getExistingCurationSKUS(['SKU1'])->willReturn(['SKU1']);
        $this->dao->getKitCollisions(['SKU1'])->willReturn(['SKU1']);
        $response = $this->subject->getCollisionsForSKUS(['SKU1']);
        $this->assertEquals([], $response);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_get_collisions_for_kit_feature_toggle_off(): void
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->kit_logic_dao->get_kit_components('SKU1')->willReturn(['SKU1']);
        $this->dao->getExistingCurationSKUS(['SKU1'], false)->willReturn(['SKU1']);
        $this->dao->getExistingCurationSKUS(['SKU1'])->willReturn(['SKU1']);
        $this->dao->getKitCollisions(['SKU1'])->willReturn(['SKU1']);
        $this->subject->getCollisionsForSKUS(['SKU1']);
        $response = $this->subject->getCollisionsForKit('SKU1');
        $this->assertEquals(['SKU1'], $response);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_get_collisions_for_kit_feature_toggle_on(): void
    {
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->kit_logic_dao->get_kit_components('SKU1')->willReturn(['SKU1']);
        $this->dao_psql->getExistingCurationSKUS(['SKU1'], false)->willReturn(['SKU1']);
        $this->dao_psql->getExistingCurationSKUS(['SKU1'])->willReturn(['SKU1']);
        $this->dao->getKitCollisions(['SKU1'])->willReturn(['SKU1']);
        $this->subject->getCollisionsForSKUS(['SKU1']);
        $response = $this->subject->getCollisionsForKit('SKU1');
        $this->assertEquals(['SKU1'], $response);
    }
}
