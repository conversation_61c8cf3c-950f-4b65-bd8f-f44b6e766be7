<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace App\Infrastructure\Connection;

interface DatabaseConstantsInterface
{
    public const QUERY_TIMEOUT = 2001;
    // Pass this during statement creation to transform into sp_executesql calls.
    public const EXECUTESQL_PARAMS = 2017;

    public const PDO_ATTR_DRIVER_NAME = 16;
    public const PDO_DRIVER_NAME_SQLSRV = 'sqlsrv';
    public const ORDER_READ_ONLY = 'OTRO'; //The DB code/handle used to connect to the orders *read-only* database
    public const EXTRANET = 'EXT';
    public const ORDER = 'OT'; // The DB code/handle used to connect to the orders database (C4)
    public const INTRANET = 'INTRA';
    public const INTERNAL_TOOLS = 'INTT';
    public const OMS = 'OMS';
    public const EMAIL = 'EMAIL';

    // Common cache time constants
    public const CACHE_TIME_ONE_WEEK = 604800;
    public const CACHE_TIME_ONE_DAY = 86400;
    public const CACHE_TIME_TWELVE_HOURS = 43200;
    public const CACHE_TIME_SIX_HOURS = 21600;
    public const CACHE_TIME_FOUR_HOURS = 14400;
    public const CACHE_TIME_THREE_HOURS = 10800;
    public const CACHE_TIME_TWO_HOURS = 7200;
    public const CACHE_TIME_ONE_HOUR = 3600;
    public const CACHE_TIME_THIRTY_MINUTES = 1800;
    public const CACHE_TIME_FIFTEEN_MINUTES = 900;
    public const CACHE_TIME_TEN_MINUTES = 600;
    public const CACHE_TIME_FIVE_MINUTES = 300;
    public const CACHE_TIME_THREE_MINUTES = 180;
    public const CACHE_TIME_ONE_MINUTE = 60;
    public const CACHE_TIME_THIRTY_SECONDS = 30;
    public const CACHE_TIME_ONE_SECOND = 1;

    public const TIMEOUT_ONE_DAY = self::CACHE_TIME_ONE_DAY;
    public const TIMEOUT_TWELVE_HOURS = self::CACHE_TIME_TWELVE_HOURS;
    public const TIMEOUT_SIX_HOURS = self::CACHE_TIME_SIX_HOURS;
    public const TIMEOUT_THREE_HOURS = self::CACHE_TIME_THREE_HOURS;
    public const TIMEOUT_ONE_HOUR = self::CACHE_TIME_ONE_HOUR;
    public const TIMEOUT_THIRTY_MINUTES = self::CACHE_TIME_THIRTY_MINUTES;
    public const TIMEOUT_FIFTEEN_MINUTES = self::CACHE_TIME_FIFTEEN_MINUTES;
    public const TIMEOUT_TEN_MINUTES = self::CACHE_TIME_TEN_MINUTES;
    public const TIMEOUT_FIVE_MINUTES = self::CACHE_TIME_FIVE_MINUTES;
    public const TIMEOUT_THREE_MINUTES = self::CACHE_TIME_THREE_MINUTES;
    public const TIMEOUT_THIRTY_SECONDS = self::CACHE_TIME_THIRTY_SECONDS;
    public const TIMEOUT_ONE_SECOND = self::CACHE_TIME_ONE_SECOND;

    // These WF ATTR constants start at 2000 in order to be out of the range
    // of all extension defined driver-specific constants.
    // They should be similar to includes/pdo.php constants. In case of difference the includes/pdo.php is the source of truth.
    public const WF_ATTR_QUERY_TIMEOUT = 2001;
    public const WF_ATTR_EXECUTESQL_PARAMS = 2017;
}
