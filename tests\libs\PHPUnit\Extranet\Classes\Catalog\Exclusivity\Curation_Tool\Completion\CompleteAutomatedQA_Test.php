<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Application\Controller\QA\CompleteAutomatedQA;
use App\Application\DTO\QARequest;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Symfony\Component\HttpFoundation\JsonResponse;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api\Partial_White_Label_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream as Downstream;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Factory;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_SKU_Service;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch as WhiteLabelBatch;

use function call_user_func;
use function json_decode;

class TestCompleteAutomatedQA extends CompleteAutomatedQA
{
    /**
     * Returns a JsonResponse that uses the serializer component if enabled, or json_encode.
     */
    protected function json($data, int $status = 200, array $headers = [], array $context = []): JsonResponse
    {
        return new JsonResponse($data, $status, $headers);
    }
    /**
     * @return int|null Employee ID of current user
     */
    protected function getEmployeeId(): ?int
    {
        return 1;
    }
}
class CompleteAutomatedQA_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Completion_QA_Automated_Service
     */
    private $qa_service;
    /**
     * @var Completion_Batch_Data_Service
     */
    private $batch_data_service;
    /**
     * @var App\Application\DTO\QARequest
     */
    private $request;
    /**
     * @var Partial_White_Label_Api_Processor
     */
    private $api_partial_wl_processor;
    /**
     * @var Downstream\Batch_Data_Loader
     */
    private $batch_loader;
    /**
     * @var Completion_Verification_Item_Service
     */
    private $vi_service;
    /**
     * @var Completion_Batch_Status_Updater
     */
    private $batch_status_updated;
    /**
     * @var Object
     */
    public $Object;

    /**
     * @var Curation_Batch_DAO
     */
    private $dao;

    /**
     * @var Curation_Batch_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var Batch_SKU_Service
     */
    private $batch_sku_service;

    /**
     * @var Curation_Request_Factory
     */
    private $curation_request_factory;

    /**
     * @var Batch_Factory
     */
    private $batch_factory;

    /**
     * @var Completion_QA_Automated_Batch_Checker
     */
    private $qa_batch_checker;

    /**
     * @var Completion_QA_Notify_Curation_Rejected
     */
    private $notify_curation_rejected;

    /**
     * @var Curation_Automation_Item_Storage
     */
    private $automation_item_storage;

    /**
     * @var batchId
     */
    private $batchId;

    /**
     * @var employeeId
     */
    private $employeeId;

    /**
     * @var brandCatalogId
     */
    private $brandCatalogId;

    /**
     * @var storage
     */
    private $storage;

    /**
     * @var Curation_Only_Batch_Service_Postgres_Storage
     */
    private $postgres_storage;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO Curation_Automation_Item_PostgreSQL_DAO
     */
    private $curation_automation_item_postgres_dao;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
       * @return void
       */
    protected function setUp(): void
    {
        $this->batchId = 1;
        $this->employeeId = 1;
        $this->brandCatalogId = 1;
        $this->batch_data_service = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->vi_service = $this->prophesize(Completion_Verification_Item_Service::class);
        $this->batch_status_updated = $this->prophesize(Completion_Batch_Status_Updater::class);
        $this->qa_batch_checker = $this->prophesize(Completion_QA_Automated_Batch_Checker::class);
        $this->notify_curation_rejected = $this->prophesize(Completion_QA_Notify_Curation_Rejected::class);
        $this->automation_item_storage = $this->prophesize(Curation_Automation_Item_Storage::class);
        $this->storage = $this->prophesize(Curation_Only_Batch_Service_Storage::class);
        $this->postgres_storage = $this->prophesize(Curation_Only_Batch_Service_Postgres_Storage::class);
        $this->curation_automation_item_postgres_dao = $this->prophesize(Curation_Automation_Item_PostgreSQL_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $batch = $this->prophesize(Curation_Automation_Unwhitelabel::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $batch->get_batch_id()->willReturn(1);
        $batch->get_skus()->willReturn([]);
        $batch->get_unwhitelabel()->willReturn(true);
        $this->batch_data_service->get_qa_batch_skus_with_exclude_reason($this->batchId)->willReturn($batch);
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->set_verification_id_for_skus(1, 1);
        $this->storage->mark_batch_as_sent(1, 1, true);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgres_storage->create_verification_id()->willReturn(1);
        $this->postgres_storage->set_verification_id_for_skus(1, 1);
        $this->postgres_storage->mark_batch_as_sent(1, 1, true);

        $this->qa_service = new Completion_QA_Automated_Service(
            $this->batch_data_service->reveal(),
            $this->vi_service->reveal(),
            $this->batch_status_updated->reveal(),
            $this->qa_batch_checker->reveal(),
            $this->notify_curation_rejected->reveal(),
            $this->automation_item_storage->reveal(),
            $this->storage->reveal(),
            $this->postgres_storage->reveal(),
            $this->curation_automation_item_postgres_dao->reveal(),
            $this->featureToggles->reveal()
        );

        $this->request = new QARequest('1');


        $this->api_partial_wl_processor = $this->prophesize(Downstream\Api\Partial_White_Label_Api_Processor::class);
        $this->dao = $this->prophesize(Curation_Batch_DAO::class);
        $this->dao_psql = $this->prophesize(Curation_Batch_Postgres_DAO::class);
        $this->batch_sku_service = $this->prophesize(Batch_SKU_Service::class);
        $this->curation_request_factory = $this->prophesize(Curation_Request_Factory::class);
        $this->batch_factory = $this->prophesize(Batch_Factory::class);

        $this->batch_loader = new Downstream\Batch_Data_Loader(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->batch_sku_service->reveal(),
            $this->curation_request_factory->reveal(),
            $this->batch_factory->reveal(),
            $this->featureToggles->reveal()
        );
    }
    /**
     * @test
     *
     * @return void
     */
    public function it_returns_true_when_it_is_completed()
    {
        $processTypeName = 'Auto';
        $createdAt = new DateTime();
        $batchData = new Completion_Batch_Data(
            $this->batchId,
            Batch::STATUS_AUTOMATED_QA_IN_PROGRESS,
            $this->brandCatalogId,
            $this->employeeId,
            Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
            $processTypeName,
            $createdAt,
            null,
            null
        );
        $this->batch_data_service->get($this->batchId)->willReturn($batchData);


        $result = $this->prophesize(Completion_QA_Batch_Status::class);
        $result->is_pending()->willReturn(false);
        $result->is_approved()->willReturn(true);
        $this->qa_batch_checker->getCompletionQaStatus($this->batchId)->willReturn($result->reveal());

        $batch = $this->prophesize(Completion_Batch_Data::class);
        $batch->getStatus()->willReturn($batchData->getStatus());
        $batch->getAssignedEmployeeId()->willReturn($batchData->getAssignedEmployeeId());

        $this->vi_service->update_locked_data_if_null($this->batchId, $this->employeeId)->willReturn(true);
        $this->qa_batch_checker->batchHasUpdatedSkus($this->batchId)->willReturn(true);

        $this->Object = new TestCompleteAutomatedQA();

        $data = ['id' => '1', 'supplier_id' => 2, 'brand_catalog_id' => 2, 'curation_request_type' => Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED];
        $this->dao->get_batch_info($this->batchId)->willReturn($data);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledTimes(2);
        $this->dao_psql->get_batch_info($this->batchId)->willReturn($data);
        $batchDataResponse = new WhiteLabelBatch();
        $this->batch_factory->create($data)->willReturn($batchDataResponse);
        $this->batch_sku_service->load_verified_skus($batchDataResponse)->willReturn($batchDataResponse);
        $this->dao->get_manual_curation_type_for_batch($this->batchId)->willReturn(true);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledTimes(3);
        $this->dao_psql->get_manual_curation_type_for_batch($this->batchId)->willReturn(true);
        $this->api_partial_wl_processor->processPartialWhiteLabel($batchDataResponse)->willReturn(200);
        $result = call_user_func(
            $this->Object,
            $this->qa_service,
            $this->batch_data_service->reveal(),
            $this->request,
            $this->api_partial_wl_processor->reveal(),
            $this->batch_loader
        );
        $asertArr = json_decode($result->getContent(), true);
        $this->assertTrue(true, $asertArr['result']);
        $this->assertFalse(false, $asertArr['result']);
    }
}
