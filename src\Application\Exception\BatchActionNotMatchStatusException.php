<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\Exception;

use RuntimeException;
use Throwable;

class BatchActionNotMatchStatusException extends RuntimeException
{
    private const MESSAGE = 'Action failed. Failed to load batch data';

    private string $expectedAction;

    private int $batchId;

    /**
     * @param Throwable|null $previous The previous exception
     * @param string $expectedAction
     * @param int $batchId
     */
    public function __construct(
        ?Throwable $previous,
        string $expectedAction,
        int $batchId
    ) {
        parent::__construct(self::MESSAGE, 0, $previous);

        $this->expectedAction = $expectedAction;
        $this->batchId = $batchId;
    }

    /**
     * @return int
     */
    public function getBatchId(): int
    {
        return $this->batchId;
    }

    /**
     * @return string
     */
    public function getExpectedAction(): string
    {
        return $this->expectedAction;
    }
}
