<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Loading;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Kit_Parent_Replacer;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader;

class Curation_QA_Replacer implements Curation_QA_Kit_Parent_Replacer {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader
   */
  private $curation_kit_children_loader;

  /**
   * Curation_QA_Replacer constructor.
   *
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader $curation_kit_children_loader kit children loader
   */
  public function __construct(Curation_Kit_Children_Loader $curation_kit_children_loader) {
    $this->curation_kit_children_loader = $curation_kit_children_loader;
  }

  /**
   * @param int   $batch_id the batch id
   * @param array $skus     the list of skus
   *
   * @return array
   */
  public function replace_kit_parents_with_children(int $batch_id, array $skus) : array {
    return $this->curation_kit_children_loader->replaceKitParents($skus, $batch_id, false);
  }
}