<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\DTO;

class CurationRequest extends AbstractCurationRequest
{
    protected const FIELD_SECTION_NAME = 'section_name';
    protected const FIELD_PAGE_NUMBER = 'page_number';
    protected const FIELD_LIMIT = 'limit';

    protected const FIELD_COUNT_BASE = 'count_base';

    protected const FIELD_SUPPLIER = 'supplier';

    protected const PAGE_TYPE = 'page_type';

    protected const XN_ID = 'xn_id';

    protected string $sectionName;
    protected int $pageNumber;
    protected string $countBase;
    protected string $supplier;
    protected int $limit;
    protected string $pageType;
    protected int $xnId;

    public function __construct(
        int    $batchId = 0,
        int    $pageNumber = 0,
        int    $limit = 0,
        string $sectionName = '',
        string $countBase = '',
        string $supplier = '',
        string $pageType = '',
        int $xnId = 0
    ) {
        parent::__construct($batchId);

        $this->pageNumber = $pageNumber;
        $this->limit = $limit;
        $this->sectionName = $sectionName;
        $this->countBase = $countBase;
        $this->supplier = $supplier;
        $this->pageType = $pageType;
        $this->xnId = $xnId;
    }

    /**
     * @return int
     */
    public function getXnId(): int
    {
        return $this->xnId;
    }

    /**
     * @return string
     */
    public function getPageType(): string
    {
        return $this->pageType;
    }

    /**
     * @return string
     */
    public function getSectionName(): string
    {
        return $this->sectionName;
    }

    /**
     * @param string $sectionName
     */
    public function setSectionName(string $sectionName): void
    {
        $this->sectionName = $sectionName;
    }

    /**
     * @return int
     */
    public function getPageNumber(): int
    {
        return $this->pageNumber;
    }

    /**
     * @param int $pageNumber
     */
    public function setPageNumber(int $pageNumber): void
    {
        $this->pageNumber = $pageNumber;
    }

    /**
     * @return int
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * @param int $limit
     */
    public function setLimit(int $limit): void
    {
        $this->limit = $limit;
    }

    /**
     * @return String
     */
    public function getCountBase(): string
    {
        return $this->countBase;
    }

    /**
     * @param String $countBase
     */
    public function setCountBase(string $countBase): void
    {
        $this->countBase = $countBase;
    }

    /**
     * @return string
     */
    public function getSupplier(): string
    {
        return $this->supplier;
    }

    /**
     * @param string $supplier
     */
    public function setSupplier(string $supplier): void
    {
        $this->supplier = $supplier;
    }

    /**
     * @param array $params
     * @return self
     */
    public static function fromArray(array $params): self
    {
        return new self(
            (int)($params[self::FIELD_BATCH_ID] ?? 0),
            (int)($params[self::FIELD_PAGE_NUMBER] ?? 0),
            (int)($params[self::FIELD_LIMIT] ?? 0),
            (string)($params[self::FIELD_SECTION_NAME] ?? ''),
            (string)($params[self::FIELD_COUNT_BASE] ?? ''),
            (string)($params[self::FIELD_SUPPLIER] ?? ''),
            (string)($params[self::PAGE_TYPE] ?? ''),
            (int)($params[self::XN_ID] ?? '')
        );
    }
}
