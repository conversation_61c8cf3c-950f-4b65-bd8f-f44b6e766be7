<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\Product\Media\Batch_Verification_Item_Model;
use WF\Shared\Models\Product_Model;

class Verified_Batch_SKU implements Batch_SKU, Downstreamable_SKU {

  /**
   * @var int
   */
  private $id;

  /**
   * @var string
   */
  private $sku;

  /**
   * @var int
   */
  private $target_ma_id;

  /**
   * @var int
   */
  private $is_excluded_from_wl;

  /**
   * written during fetching DB results
   * @var int
   */
  private $is_kitsco; /** @phpstan-ignore-line */

  /**
   * written during fetching DB results
   * @var int
   */
  private $is_kit_component; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $is_kit_parent = 0;

  /**
   * @var bool
   */
  private $is_eligible;

  /**
   * @var int
   */
  private $source_id;

  /**
   * written during fetching DB results
   * @var int|null
   */
  private $collection_id; /** @phpstan-ignore-line */

  /**
   * @var array
   */
  private $parent_skus = [];

  /**
   * @var int
   */
  private $status;

  /**
   * @var string
   */
  private $excluded_reason;


  /**
   * @return int|null
   */
  public function get_id() {
    return $this->id;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return int
   */
  public function get_target_ma_id() {
    return $this->target_ma_id;
  }

  /**
   * @return int
   */
  public function get_is_excluded_from_wl() : int {
    return $this->is_excluded_from_wl;
  }

  /**
   * @return string
   */
  public function get_excluded_reason() : string {
    return $this->excluded_reason;
  }


  /**
   * @return int
   */
  public function get_source_id() {
    return $this->source_id;
  }

  /**
   * @return bool
   */
  public function is_eligible() {
    return $this->is_eligible;
  }

  /**
   * @return int|null
   */
  public function get_collection_id() {
    return $this->collection_id;
  }

  /**
   * @return bool
   */
  public function is_excluded_from_wl() : bool {
    return (bool)$this->is_excluded_from_wl;
  }

  /**
   * @return bool
   */
  public function is_kitsco() : bool {
    return (bool)$this->is_kitsco;
  }

  /**
   * @return bool
   */
  public function is_kit_component() : bool {
    return (bool)$this->is_kit_component;
  }

  /**
   * @return bool
   */
  public function is_kit_parent() : bool {
    return (bool)$this->is_kit_parent;
  }

  /**
   * @param int $id Id value
   *
   * @return void
   */
  public function set_id(int $id) {
    $this->id = $id;
  }

  /**
   * @param string $sku Verified SKU value
   *
   * @return void
   */
  public function set_sku(string $sku) {
    $this->sku = $sku;
  }

  /**
   * @param int $target_ma_id Target MaID
   *
   * @return void
   */
  public function set_target_ma_id($target_ma_id) {
    $this->target_ma_id = $target_ma_id;
  }

  /**
   * @param int $is_excluded_from_wl Is excluded from WL value
   *
   * @return void
   */
  public function set_is_excluded_from_wl(int $is_excluded_from_wl) {
    $this->is_excluded_from_wl = $is_excluded_from_wl;
  }

  /**
   * @param string $excluded_reason Excluded Reason
   *
   * @return void
   */
  public function set_excluded_reason(string $excluded_reason) {
    $this->excluded_reason = $excluded_reason;
  }


  /**
   * @param int $is_kit_parent Indicates whatever the verified batch sku is kit parent or not
   *
   * @return void
   */
  public function set_is_kit_parent(int $is_kit_parent) {
    $this->is_kit_parent = $is_kit_parent;
  }

  /**
   * @param int $source_id Source ID
   *
   * @return void
   */
  public function set_source_id($source_id) {
    $this->source_id = $source_id;
  }

  /**
   * @param bool $is_eligible Is elibible
   *
   * @return void
   */
  public function set_eligible(bool $is_eligible) {
    $this->is_eligible = $is_eligible;
  }

  /**
   * @param int $status status
   *
   * @return void
   */
  public function set_status(int $status) {
    $this->status = $status;
  }

  /**
   * @param string $parent_sku Parent sku value
   *
   * @return void
   */
  public function add_parent(string $parent_sku) {
    $this->parent_skus[] = $parent_sku;
  }

  /**
   * @return array
   */
  public function get_parents() {
    return $this->parent_skus;
  }

  /**
   * @return int
   */
  public function get_status() : int {
    return $this->status;
  }

  /**
   * @return bool
   */
  public function is_collision() : bool {
    return $this->get_source_id() === Batch_Verification_Item_Model::SOURCE_COLLISION;
  }

  /**
   * @return bool
   */
  public function blocks_display_sku_eligibility() : bool {
    if ($this->is_excluded_from_wl()) {
      return false;
    }

    if ($this->target_ma_id > 0 && in_array($this->status, [Product_Model::STATUS_BEING_ADDED, Product_Model::STATUS_KIT_COMPONENT])) {
      return false;
    }

    return true;
  }
}
