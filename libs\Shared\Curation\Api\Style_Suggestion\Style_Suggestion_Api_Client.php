<?php

declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Style_Suggestion;

use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use WF\Shared\Curation\Api\Api_Style_Suggestion;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\SKU_Style_Suggestion_DTO;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\Style_Suggestion;
use WF\Shared\Curation\Api\Style_Suggestion\DTO\Suggestion_Threshold;
use WF\Shared\Environment;
use Symfony\Component\HttpClient\RetryableHttpClient;
use function array_values;
use function is_null;
use function json_decode;
use function sprintf;

class Style_Suggestion_Api_Client implements Api_Style_Suggestion, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use LoggerTrait;

  private const BASE_URL_GBQ_DS_PROD = 'http://kube-btbb-bc-curation-style-suggestion-api.service.intraiad1.consul.csnzoo.com';
  private const BASE_URL_GBQ_DS_DEV = 'http://kube-btbb-bc-curation-style-suggestion-api.service.intradsm1.sdeconsul.csnzoo.com';

    /**
     * @var string
     */
    private string $base_gbq_ds_url;

    /**
     * @var HttpClientInterface
     */
    private HttpClientInterface $client;


    /**
     * Style_Suggestion_API_Client constructor.
     *
     * @param HttpClientInterface $client
     * @param string $environment Environment
     */
    public function __construct(HttpClientInterface $client, string $environment)
    {
        $this->base_gbq_ds_url = $environment === Environment::DEVELOPMENT ? self::BASE_URL_GBQ_DS_DEV : self::BASE_URL_GBQ_DS_PROD;
        $this->client = new RetryableHttpClient($client);
    }

    /**
     * @param string[] $skus SKU list
     * @param int $limit Max number of suggestions to return
     *
     * @return SKU_Style_Suggestion_DTO[]
     * @throws API_Request_Exception
     */
    public function get_style_suggestion(array $skus, int $limit): array
    {
        if (empty($skus)) {
            $this->info('get_style_suggestion skus array is empty');
            return [];
        }

        return $this->make_request('/api/masterstyle', ['skus' => array_values($skus), 'limit' => $limit]);
    }

    /**
     * @param string $path The request endpoint
     * @param array|null $payload Optional payload to send
     *
     * @return array array<string, SKU_Style_Suggestion_DTO>
     * @throws API_Request_Exception
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    private function make_request(string $path, ?array $payload = null): array
    {
        $url = $this->base_gbq_ds_url;
        $endpoint = $url . $path;

        $options = [
            'headers' => [
                'Accept: application/json',
            ],
        ];

        if (!empty($payload)) {
            $options['json'] = $payload;
        }

        $response = $this->client->request(
            Request::METHOD_POST,
            $endpoint,
            $options,
        );

        $content = $response->getContent(false);
        $statusCode = $response->getStatusCode();
        $context = [
            'message' => $content,
            'statusCode' => $statusCode,
            'payload' => $payload
        ];

        if ($statusCode >= 300) {
            $this->error($content, $context);
            throw new API_Request_Exception($content, $statusCode);
        }

        $this->info(
            sprintf('RESPONSE: [%s] %s', $statusCode, $content),
            [
                'endpoint' => $endpoint,
                'payload' => $payload
            ]
        );
        $this->logToStdOutForLocalDevelopment("DS Response", $content, __LINE__, __FILE__);
        return $this->decode_response($content, $statusCode);
    }

    /**
     * @param string $response The HTTP response body
     * @param int $response_code The HTTP response code
     *
     * @return array array<string, SKU_Style_Suggestion_DTO>
     * @throws API_Request_Exception
     */
    private function decode_response(string $response, int $response_code): array
    {
        $decoded_response = json_decode($response);
        if (is_null($decoded_response)) {
            $this->warning(sprintf('Response data is an invalid JSON: %s', $response));

            return [];
        }

        if ($response_code !== Response::HTTP_OK) {
            $error_message = sprintf('Response error: %s - %s', $decoded_response->code, $decoded_response->message);
            $this->warning($error_message, [$decoded_response]);
            throw new API_Request_Exception($error_message);
        }

        $sku_style_suggestion = [];

        foreach ($decoded_response as $item) {
            $suggestions = [];
            foreach ($item->suggestions as $suggestion) {
                $suggestions[] = new Style_Suggestion(
                    $suggestion->styleId,
                    $suggestion->styleName,
                    $suggestion->probability,
                    $suggestion->rank
                );
            }

            $threshold = null;
            if (!empty($item->threshold)) {
                $threshold = new Suggestion_Threshold(
                    $item->threshold->classId,
                    $item->threshold->className,
                    $item->threshold->value
                );
            }
            $sku_style_suggestion[$item->sku] = new SKU_Style_Suggestion_DTO($item->sku, $suggestions, $threshold);
        }

        return $sku_style_suggestion;
    }

    /**
     * @param string[] $skus SKU list
     * @param int $limit Max number of suggestions to return
     *
     * @return SKU_Style_Suggestion_DTO[]
     * @throws API_Request_Exception
     */
    public function get_substyle_suggestion(array $skus, int $limit): array
    {
        if (empty($skus)) {
            $this->info('get_substyle_suggestion skus array is empty');
            return [];
        }

        return $this->make_request('/api/substyle', ['skus' => array_values($skus), 'limit' => $limit]);
    }
}
