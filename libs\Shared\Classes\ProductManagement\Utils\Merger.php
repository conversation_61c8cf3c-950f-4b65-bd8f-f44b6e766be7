<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Utils;

class Merger {
  /**
   * Merges together groups that are dependent
   *
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface[] $mergeable_groups Groupable Interface
   *
   * @return \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface[]
   */
  public function merge(array $mergeable_groups) : array {
    /**
     * @var \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface[] $results
     */
    $results = [];

    while (!empty($mergeable_groups)) {
      $current = array_shift($mergeable_groups);

      $current_grouping = [];

      foreach ($mergeable_groups as $group) {
        if ($group->isDependent($current)) {
          $current->merge($group);
          continue;
        }

        $current_grouping[] = $group;
      }

      // try to merge current group to the results
      $merged = false;
      foreach ($results as $result_grouping) {
        if ($result_grouping->isDependent($current)) {
          $result_grouping->merge($current);
          $merged = true;
          break;
        }
      }

      // if it wasn't merged into some existing group of the results, add it to the list
      if (!$merged) {
        $results[] = $current;
      }

      $mergeable_groups = $current_grouping;
    }

    return $results;
  }
}