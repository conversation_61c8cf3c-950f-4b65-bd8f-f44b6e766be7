<?php

declare(strict_types=1);

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Decision_Logging_Service;

class CurationDecisionLoggingController extends AbstractBaseController
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Constructor
     *
     * @param LoggerInterface $logger Logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    /**
     * Insert the curation decision log into BigQuery
     * @param Curation_Decision_Logging_Service $curation_decision_logging_service
     * @return JsonResponse
     * @Route(path="/insert_data_big_query", methods={"GET"})
     */
    public function insertDataBigQuery(Curation_Decision_Logging_Service $curation_decision_logging_service): JsonResponse
    {
        // Insert the curation decision log into BigQuery
        $this->logger->info('Inside the controller to insert the curation decision log into BigQuery');
        return $this->json($curation_decision_logging_service->insertDataBigQuery());
    }
}
