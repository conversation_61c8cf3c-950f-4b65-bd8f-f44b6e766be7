/**
 * CurationSkuSave
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Button} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import CurationSkuSavedInfoTooltip from './curation_sku_saved_info_tooltip';
import CurationSkuRejectedInfoTooltip from './curation_sku_rejected_info_tooltip';
import {Loading} from "@homebase/core";

class CurationSkuSave extends React.Component {
  static propTypes = {
    isSaveEnabled: PropTypes.bool,
    isSaving: PropTypes.bool,
    savedBy: PropTypes.string,
    savedAt: PropTypes.string,
    rejectedBy: PropTypes.string,
    rejectedAt: PropTypes.string,
    rejectedNote: PropTypes.string,
    onSaveClick: PropTypes.func,
  };

  static defaultProps = {
    isSaveEnabled: false,
    isSaving: false,
    savedBy: null,
    savedAt: null,
    rejectedBy: null,
    rejectedAt: null,
    rejectedNote: null,
    onSaveClick() {},
  };

  shouldComponentUpdate(nextProps) {
    return (this.props.isSaveEnabled !== nextProps.isSaveEnabled) || (this.props.savedBy !== nextProps.savedBy) || (this.props.isSaving !== nextProps.isSaving);
  }

  isSaved = () => this.props.savedAt !== null;

  isSaving = () => this.props.isSaving;
  isRejected = () => this.props.rejectedAt !== null;

  render() {
    return (
       <div className="text_center">
         {this.isSaving() ? <Loading/> : <Button
             onClick={async () => {
               await this.props.onSaveClick()
             }}
             disabled={!this.props.isSaveEnabled}
         >
           {<Translation msgid="CurationTool.Save"/>}
         </Button>}
        {this.isSaved() && (
          <CurationSkuSavedInfoTooltip
            savedAt={this.props.savedAt}
            savedBy={this.props.savedBy}
          />
        )}
        {this.isRejected() && (
          <CurationSkuRejectedInfoTooltip
            rejectedAt={this.props.rejectedAt}
            rejectedBy={this.props.rejectedBy}
            rejectedNote={this.props.rejectedNote}
          />
        )}
      </div>
    );
  }
}

export default CurationSkuSave;
