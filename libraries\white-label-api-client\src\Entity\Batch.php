<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Entity;

final class Batch
{
    /**
     * @var int
     */
    private $batchId;

    /**
     * @var int
     */
    private $brandCatalogId;

    /**
     * @var SKU[]
     */
    private $skus;

    /**
     * Batch constructor.
     *
     * @param int   $batchId
     * @param int   $brandCatalogId
     * @param SKU[] $skus
     */
    public function __construct(int $batchId, int $brandCatalogId, array $skus = [])
    {
        $this->batchId = $batchId;
        $this->brandCatalogId = $brandCatalogId;
        $this->skus = $skus;
    }

    /**
     * @return int
     */
    public function getBatchId(): int
    {
        return $this->batchId;
    }

    /**
     * @return int
     */
    public function getBrandCatalogId(): int
    {
        return $this->brandCatalogId;
    }

    /**
     * @return SKU[]
     */
    public function getSkus(): array
    {
        return $this->skus;
    }

    /**
     * @param SKU $sku
     * @return void
     */
    public function addSku(SKU $sku)
    {
        $this->skus[] = $sku;
    }
}
