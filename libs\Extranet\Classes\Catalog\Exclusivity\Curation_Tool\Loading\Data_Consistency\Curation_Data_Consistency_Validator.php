<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_Postgres_DAO;

class Curation_Data_Consistency_Validator {

  use Logging_Trait;

  /**
   * @var iterable<Curation_Data_Consistency_Validation>
   */
  private $validations;

  /**
   * @var Curation_Data_Consistency_Validator_Storage
   */
  private $dao;


  /**
   * @var Curation_Data_Consistency_Validation_Postgres_DAO
   */
  private Curation_Data_Consistency_Validation_Postgres_DAO $dao_postgres;

  /**
   * @var FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;



  /**
   * @param Curation_Data_Consistency_Validator_Storage    $dao         DAO
   * @param Curation_Data_Consistency_Validation_Postgres_DAO    $dao_postgres         DAO Postgres
   * @param FeatureTogglesInterface                             $featureToggles     FeatureTogglesInterface
   * @param iterable<Curation_Data_Consistency_Validation> $validations Validations
   * @param LoggerInterface|null                                    $logger      Logger
   */
  public function __construct(Curation_Data_Consistency_Validator_Storage $dao, Curation_Data_Consistency_Validation_Postgres_DAO $dao_postgres, FeatureTogglesInterface $featureToggles, iterable $validations, ?LoggerInterface $logger = null) {
    $this->dao         = $dao;
    $this->dao_postgres         = $dao_postgres;
    $this->featureToggles = $featureToggles;
    $this->validations = $validations;
    $this->logger      = $logger;
  }

  /**
   * @param int       $batch_id Batch ID
   * @param Section[] $sections Sections
   *
   * @return string[]
   */
  public function run_validations(int $batch_id, array $sections): array {
    $results = [];

    if ($batch_id===0){
      return [];
    }

    foreach ($this->validations as $validation) {
      $this->log_info(
          'Running validator', [
          'batch_id'   => $batch_id,
          'validation' => get_class($validation),
          ]
      );
      $results[] = $validation->validate($batch_id, $sections);
    }

    $errors = $this->extract_errors($results);

    $this->log_info(
        'Saving validation results in tblVerificationItemDataConsistencyLog', [
        'batch_id' => $batch_id,
        'results'  => $errors
        ]
    );
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $this->dao_postgres->save_results($batch_id, $results);
    } else {
      $this->dao->save_results($batch_id, $results);
    }

    return $errors;
  }

  /**
   * @param Curation_Data_Consistency_Validation_Result[] $results Results
   *
   * @return string[]
   */
  private function extract_errors(array $results) : array {
    $errors = [];

    foreach ($results as $result) {
      $errors = array_merge($errors, $result->get_errors());
    }

    return $errors;
  }
}
