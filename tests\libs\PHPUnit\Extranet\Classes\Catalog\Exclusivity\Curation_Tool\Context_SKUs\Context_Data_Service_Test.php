<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Collection;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Factory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Manufacturer_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_Postgresql_DAO;

class Context_Data_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_Data_Factory|\Prophecy\Prophecy\ObjectProphecy
     */
    private $factory;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $dao;


    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Context_Data_Postgresql_DAO|\Prophecy\Prophecy\ObjectProphecy
     */
    private $postgresql_dao;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_Data_Collection|\Prophecy\Prophecy\ObjectProphecy
     */
    private $collection;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_Data_Manufacturer_Service|\Prophecy\Prophecy\ObjectProphecy
     */
    private $context_data_manufacturer_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_Data_Decision_Service|\Prophecy\Prophecy\ObjectProphecy
     */
    private $context_data_decision_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_Data_Service
     */
    private $subject;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->factory = $this->prophesize(Context_Data_Factory::class);
        $this->dao = $this->prophesize(Context_Data_DAO::class);
        $this->postgresql_dao = $this->prophesize(Context_Data_Postgresql_DAO::class);
        $this->context_data_manufacturer_service = $this->prophesize(Context_Data_Manufacturer_Service::class);
        $this->context_data_decision_service = $this->prophesize(Context_Data_Decision_Service::class);
        $this->collection = $this->prophesize(Context_Data_Collection::class);
        $this->factory->create_collection()->willReturn($this->collection);

        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();

        $this->subject = new Context_Data_Service(
            $this->factory->reveal(),
            $this->dao->reveal(),
            $this->postgresql_dao->reveal(),
            $this->context_data_manufacturer_service->reveal(),
            $this->context_data_decision_service->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_gets_manufacturer_ids_only_for_context_skus()
    {
        $this->dao->get_context_skus_manufacturer_data(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_context_skus_manufacturer_data(Argument::cetera())->willReturn([]);
        $context_data = $this->prophesize(Context_Data::class);

        // gets manufacturer for the sku and calculates closest decision match
        $this->context_data_manufacturer_service->get_final_manufacturer_id(Argument::cetera())->willReturn(1)->shouldNotBeCalled();
        $this->context_data_decision_service->find_closest_match(Argument::cetera())->willReturn($context_data->reveal())->shouldNotBeCalled();

        $this->subject->context_data_for_skus(1, ['SKU1', 'SKU2', 'SKU3']);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_gets_manufacturer_ids_for_context_skus_and_gets_closest_match()
    {
        $data = [
            'SKU1' => ['manufacturer_id' => 1, 'ma_brw_id' => 1, 'source_id' => null, 'recently_curated_manufacturer_id' => null, 'price_tier' => 1]
        ];
        $this->dao->get_context_skus_manufacturer_data(Argument::cetera())->willReturn($data)->shouldBeCalled();
        $this->postgresql_dao->get_context_skus_manufacturer_data(Argument::cetera())->willReturn([]);
        $context_data = $this->prophesize(Context_Data::class);

        // gets manufacturer for the sku and calculates closest decision match
        $this->context_data_manufacturer_service->get_final_manufacturer_id(1, 1, null, null)->willReturn(1)->shouldBeCalled();
        $this->context_data_decision_service->find_closest_match(1, 1, 1)->willReturn($context_data->reveal())->shouldBeCalled();

        // it adds to the collection
        $this->collection->set_for_sku('SKU1', $context_data)->shouldBeCalled();

        $this->subject->context_data_for_skus(1, ['SKU1', 'SKU2', 'SKU3']);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_gets_manufacturer_ids_for_context_skus_and_does_not_get_closest_match()
    {
        $data = [
            'SKU1' => ['manufacturer_id' => 1, 'ma_brw_id' => 1, 'source_id' => null, 'recently_curated_manufacturer_id' => null, 'price_tier' => 1]
        ];
        $this->dao->get_context_skus_manufacturer_data(Argument::cetera())->willReturn($data)->shouldBeCalled();
        $context_data = $this->prophesize(Context_Data::class);

        // gets manufacturer for the sku and calculates closest decision match
        $this->context_data_manufacturer_service->get_final_manufacturer_id(1, 1, null, null)->willReturn(null)->shouldBeCalled();
        $this->context_data_decision_service->find_closest_match(1, 1, 1)->willReturn($context_data->reveal())->shouldNotBeCalled();

        $this->subject->context_data_for_skus(1, ['SKU1', 'SKU2', 'SKU3']);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_looks_up_data_by_sku()
    {
        $this->dao->get_context_skus_manufacturer_data(1, ['SKU1', 'SKU2', 'SKU3'], Argument::any())->willReturn([])->shouldBeCalled();
        $this->subject->context_data_for_skus(1, ['SKU1', 'SKU2', 'SKU3']);
    }
}
