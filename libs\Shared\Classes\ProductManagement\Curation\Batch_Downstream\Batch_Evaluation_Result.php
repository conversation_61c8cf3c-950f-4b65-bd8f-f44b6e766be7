<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

class Batch_Evaluation_Result {

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result[]
   */
  private $batched_skus_evaluation_results;

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private $batch_evaluation_status;

  /**
   * Batch_Evaluation_Result constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result[] $batched_skus_evaluation_results batched_skus_evaluation_results
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status         $batch_evaluation_status         batch_evaluation_status
   */
  public function __construct(array $batched_skus_evaluation_results, <PERSON>ch_Evaluation_Status $batch_evaluation_status) {
    $this->batched_skus_evaluation_results = $batched_skus_evaluation_results;
    $this->batch_evaluation_status         = $batch_evaluation_status;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result[]
   */
  public function get_batched_skus_evaluation_result() {
    return $this->batched_skus_evaluation_results;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  public function get_status() : Batch_Evaluation_Status {
    return $this->batch_evaluation_status;
  }
}
