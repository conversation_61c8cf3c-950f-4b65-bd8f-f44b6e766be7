<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use App\Application\DTO\CurationRequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;

class DebugController extends AbstractBaseController
{
    /**
     * @Route(path="/debug_groups", methods={"GET"})
     * @param Curation_Section_Service $curation_section_service_with_collisions
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws \Exception
     */
    public function debug_groups(
        Curation_Section_Service $curation_section_service_with_collisions,
        CurationRequest $curationRequest
    ): JsonResponse {
        $batchId = $curationRequest->getBatchId();

        $this->info('Loading Curation sections with collisions', ['batch_id' => $batchId]);
        $groups = $curation_section_service_with_collisions->load($batchId);

        return $this->json($groups);
    }
}
