<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Collider;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Postgres_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Kit_Collision_SKU_DAO;

class Curation_Kit_Collider {
  const ITERATION_LIMIT = 500;

  /**
   * @var \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO
   */
  private $dao;

  /**
   * @var \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Postgres_DAO
   */
  private $dao_psql;

  /**
   * @var \WF\Shared\DAOs\ProductManagement\WhiteLabel\Kit_Collision_SKU_DAO
   */
  private $kit_logic_dao;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @param \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO   $dao           collider dao
   * @param \WF\Shared\DAOs\ProductManagement\WhiteLabel\Kit_Collision_SKU_DAO $kit_logic_dao kit collision dao
   */
  public function __construct(Curation_Collider_DAO $dao, Curation_Collider_Postgres_DAO $dao_psql, Kit_Collision_SKU_DAO $kit_logic_dao, FeatureTogglesInterface $featureToggles) {
    $this->dao            = $dao;
    $this->kit_logic_dao  = $kit_logic_dao;
    $this->dao_psql       = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * Only returns the collisions which are not in the provided list.
   * Due to the requirements, collisions are only calculated for the skus which are already exist in verification items table
   *
   * @param array $skus list of skus
   *
   * @return array
   */
  public function getCollisionsForSKUS(array $skus) : array {
    $remaining_iterations = static::ITERATION_LIMIT;

    $collisions = $to_collide_next = $skus;
    do {
      if ($remaining_iterations < 0) {
        throw new \LogicException('Too many collision iterations. Probably something is not right:' . implode(',', $skus));
      }

      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $existing_curation_items = $this->dao_psql->getExistingCurationSKUS($to_collide_next);
      } else {
        $existing_curation_items = $this->dao->getExistingCurationSKUS($to_collide_next);
      }

      $found_collisions = [];
      if (!empty($existing_curation_items)) {
        $found_collisions = $this->dao->getKitCollisions($existing_curation_items);
      }

      //Important assumption: all the collisions are returned on the first run, so we don't have to run collisions for the same sku twice
      //calculates which of skus are not yet in the list of collisions
      $to_collide_next = array_diff($found_collisions, $collisions);

      //Add found collisions to the result so we don't process them anymore
      $collisions = array_merge($collisions, $to_collide_next);

      $remaining_iterations--;
    } while (!empty($to_collide_next));

    return array_diff($collisions, $skus);
  }

  /**
   * @param string $kit_sku kit sku
   *
   * @return array
   */
  public function getCollisionsForKit(string $kit_sku) : array {
    $kit_components = $this->kit_logic_dao->get_kit_components($kit_sku);

    //The only case we should consider kitsco skus is here as they influence if we should display the whole kit or not
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $displayable_children = $this->dao_psql->getExistingCurationSKUS($kit_components, false);
    } else {
      $displayable_children = $this->dao->getExistingCurationSKUS($kit_components, false);
    }

    $collisions = $this->getCollisionsForSKUS($displayable_children);

    $components_and_collisions = array_merge($displayable_children, $collisions);

    return array_unique($components_and_collisions);
  }
}
