<?php

namespace WF\Shared\Logging;

use Psr\Log\LoggerAwareTrait;
use Psr\Log\LoggerInterface;
use Psr\Log\LoggerTrait;
use Psr\Log\LogLevel;

/**
 * @deprecated Use `Psr\Log\LoggerInterface` instead
 */
class Logger implements LoggerInterface
{
    use LoggerAwareTrait;
    use LoggerTrait;

    /**
     * Adapter from WF to PSR. Consider to be removed after replacement all mentions with PSR implementation
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;

        @trigger_error(
            'This class should be replace with LoggerInteface',
            E_USER_DEPRECATED
        );
    }

    /**
     * Logs with an arbitrary level.
     *
     * @param mixed $level
     * @param string $message
     * @param mixed[] $context
     *
     * @return void
     */
    public function log($level, $message, array $context = []): void
    {
        @trigger_error(
            'This class should be replace with LoggerInterface',
            E_USER_DEPRECATED
        );

        if ($this->logger instanceof LoggerInterface) {
            $this->logger->log($level, $message, $context);
        }
    }

    /**
     * Logs with an arbitrary level.
     *
     * @param string $message
     * @param mixed[] $context
     *
     * @return void
     */
    public function warn($message, array $context = []): void
    {
        $this->log(LogLevel::WARNING, $message, $context);
    }
}
