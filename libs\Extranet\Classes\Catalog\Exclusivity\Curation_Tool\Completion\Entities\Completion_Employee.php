<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities;

class Completion_Employee {
  /**
   * @var string
   */
  private $name;

  /**
   * @var string
   */
  private $email;

  /**
   * @param string $name  Name
   * @param string $email Email
   */
  public function __construct(string $name, string $email) {
    $this->name  = $name;
    $this->email = $email;
  }

  /**
   * @return string
   */
  public function getName() : string {
    return $this->name;
  }

  /**
   * @return string
   */
  public function getEmail() : string {
    return $this->email;
   // return "<EMAIL>";
  }
}
