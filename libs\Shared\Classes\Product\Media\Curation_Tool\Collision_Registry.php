<?php
/**
 * Collision Registry - Should be Factory, but it's a temporary solution.
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool;

class Collision_Registry {

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Collision_Registry
   */
  private static $instance;

  /**
   * @var array
   */
  private $stored_collections = [];

  /**
   * @var array
   */
  private $stored_kits = [];

  /**
   * @var array
   */
  private $stored_kit_children = [];


  /**
   * Get Instance
   *
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Collision_Registry
   */
  public static function get_instance() {
    if (null === self::$instance) {
      self::$instance = new self();
    }

    return self::$instance;
  }

  /**
   * Get Collection
   *
   * @param int $collection_id Collection ID
   *
   * @return array|null
   */
  public function get_collection($collection_id) {
    if (isset($this->stored_collections[$collection_id])) {
      return $this->stored_collections[$collection_id];
    }

    return null;
  }

  /**
   * Set Collection
   *
   * @param int   $collection_id Collection ID
   * @param array $collection    Information about collection items
   *
   * @return void
   */
  public function set_collection($collection_id, $collection) {
    $this->stored_collections[$collection_id] = $collection;
  }

  /**
   * Get Kit
   *
   * @param string $kit_sku Kit SKU
   *
   * @return array|null
   */
  public function get_kit($kit_sku) {
    if (isset($this->stored_kits[$kit_sku])) {
      return $this->stored_kits[$kit_sku];
    }

    return null;
  }

  /**
   * Set Kit
   *
   * @param string $kit_sku        Kit SKU
   * @param array  $kit_components Information about kit components
   *
   * @return void
   */
  public function set_kit($kit_sku, $kit_components) {
    $this->stored_kits[$kit_sku] = $kit_components;
  }

  /**
   * Get Kit
   *
   * @param string $kit_sku Kit SKU
   *
   * @return array|null
   */
  public function get_kit_children($kit_sku) {
    if (isset($this->stored_kit_children[$kit_sku])) {
      return $this->stored_kit_children[$kit_sku];
    }

    return null;
  }

  /**
   * Set Kit
   *
   * @param string $kit_sku      Kit SKU
   * @param array  $kit_children Information about kit components
   *
   * @return void
   */
  public function set_kit_children($kit_sku, $kit_children) {
    $this->stored_kit_children[$kit_sku] = $kit_children;
  }

  /**
   * Collision_Registry constructor.
   */
  protected function __construct() {
  }
}
