<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_SKU;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Completion_QA_Batch_SKU_Service {

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;
  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage $dao Completion_QA_Rejected_SKU_Storage
   */
  public function __construct(Completion_QA_Batch_SKU_Storage $dao, Batch_Management_Postgres_DAO $dao_psql, FeatureTogglesInterface $featureToggles) {
    $this->dao = $dao;
    $this->dao_psql = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_QA_Batch_SKU[]
   */
  public function getRejectedSkus(int $batch_id) : array {
    $skus = [];
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      foreach ($this->dao_psql->get_qa_batch_skus($batch_id, Curation_QA_Status::rejected()->value()) as $row) {
        $skus[] = new Completion_QA_Batch_SKU($row['sku'], $row['message'] ?? '');
      }
    } else {
      foreach ($this->dao->get_qa_batch_skus($batch_id, Curation_QA_Status::rejected()->value()) as $row) {
        $skus[] = new Completion_QA_Batch_SKU($row['sku'], $row['message'] ?? '');
      }
    }
    return $skus;
  }
}
