<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO;
class Completion_QA_Automated_Service {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
   */
  private $batch_data_service;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service
   */
  private $viService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
   */
  private $batch_status_updater;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker
   */
  private $qa_batch_checker;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected $notify_curation_rejected Completion_QA_Email_Notifier
   */
  private $notify_curation_rejected;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage $curation_automation_storage Curation_Automation_Item_Storage
   */
  private $curation_automation_storage;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage $storage Curation_Only_Batch_Service_Storage
   */
  private $storage;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage $postgres_storage Curation_Only_Batch_Service_Postgres_Storage
   */
  private $postgres_storage;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface  $featureToggles;


  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_PostgreSQL_DAO $curation_automation_item_postgres_dao Curation_Automation_Item_PostgreSQL_DAO
   */
  private $curation_automation_item_postgres_dao;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service          $batch_data_service          Completion_Batch_Data_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Service   $viService                   Completion_Verification_Item_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater        $batch_status_updater        Completion_Batch_Status_Updater
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Automated_Batch_Checker  $qa_batch_checker            QA Batch Checker
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected $notify_curation_rejected    Completion_QA_Notify_Curation_Rejected
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage          $curation_automation_storage Curation_Automation_Item_Storage
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage $storage Curation_Only_Batch_Service_Storage
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface  $featureToggles FeatureTogglesInterface
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage\Curation_Only_Batches_Postgres_DAO $storage_psql Curation_Only_Batches_Postgres_DAO
   */
  public function __construct(
      Completion_Batch_Data_Service $batch_data_service,
      Completion_Verification_Item_Service $viService,
      Completion_Batch_Status_Updater $batch_status_updater,
      Completion_QA_Automated_Batch_Checker $qa_batch_checker,
      Completion_QA_Notify_Curation_Rejected $notify_curation_rejected,
      Curation_Automation_Item_Storage $curation_automation_storage,
      Curation_Only_Batch_Service_Storage $storage,
      Curation_Only_Batch_Service_Postgres_Storage $postgres_storage,
      Curation_Automation_Item_PostgreSQL_DAO $curation_automation_item_postgres_dao,
      FeatureTogglesInterface $featureToggles
  ) {
    $this->batch_data_service             = $batch_data_service;
    $this->viService                      = $viService;
    $this->batch_status_updater           = $batch_status_updater;
    $this->qa_batch_checker               = $qa_batch_checker;
    $this->notify_curation_rejected       = $notify_curation_rejected;
    $this->curation_automation_storage    = $curation_automation_storage;
    $this->storage    = $storage;
    $this->postgres_storage    = $postgres_storage;
    $this->curation_automation_item_postgres_dao  = $curation_automation_item_postgres_dao;
    $this->featureToggles    = $featureToggles;
  }

  /**
   * @param int $batch_id    Batch Id
   * @param int $employee_id Employee Id
   *
   * @return bool
   */
  public function complete(int $batch_id, int $employee_id) : bool {
    $check_result = $this->qa_batch_checker->getCompletionQaStatus($batch_id);

    if ($check_result->is_pending()) {
      return false;
    }

    $batch_data = $this->batch_data_service->get($batch_id);

    // if the batch is not in correct curation status, then don't change it
    if ($batch_data->getStatus() !== Batch::STATUS_AUTOMATED_QA_IN_PROGRESS && $batch_data->getStatus() !== Batch::STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS) {
      return true;
    }

    if (!$check_result->is_approved()) {
      $this->markBatchInProgress($batch_id, $employee_id, $batch_data->getAssignedEmployeeId());
      return true;
    }

    $this->markBatchCompleted($batch_id, $employee_id);

    // Get fresh batch data and check its status
    $batch_data = $this->batch_data_service->get($batch_id);
    if ($batch_data->getStatus() === Batch::STATUS_AUTOMATED_QA_COMPLETE) {
      $this->viService->update_locked_data_if_null($batch_id, $employee_id);
    }

    return true;
  }

  /**
   * @param int $batch_id Batch Id
   *
   * @return bool
   */
  public function unwhitelabelExcludedSkus(int $batch_id) : bool {
    $skus_with_excluded_reason = $this
        ->batch_data_service
        ->get_qa_batch_skus_with_exclude_reason($batch_id);

    // No SKUS so do not update
    if (count($skus_with_excluded_reason->get_skus()) === 0) {
      return true;
    }

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      $this->curation_automation_item_postgres_dao->unwhitelabel($skus_with_excluded_reason);
    } else {
      $this->curation_automation_storage->unwhitelabel($skus_with_excluded_reason);
    }
    return true;
  }

  /**
   * @param int $batch_id             Batch Id
   * @param int $employee_id          Employee Id
   * @param int $assigned_employee_id Assigned Employee ID
   *
   * @return void
   */
  private function markBatchInProgress(int $batch_id, int $employee_id, int $assigned_employee_id = null) {
    $this->batch_status_updater->changeStatus($batch_id, Batch::STATUS_AUTOMATED_QA_IN_PROGRESS, $employee_id);

    if (!empty($assigned_employee_id)) {
      $this->notify_curation_rejected->send($batch_id, $assigned_employee_id, $employee_id);
    }
  }

  /**
   * @param int $batch_id    Batch Id
   * @param int $employee_id Employee Id
   *
   * @return void
   */
  private function markBatchCompleted(int $batch_id, int $employee_id) {
    $this->batch_status_updater->changeStatus($batch_id, Batch::STATUS_AUTOMATED_QA_COMPLETE, $employee_id);
  }

  /**
   *
   * @param int $batch_id batch_id
   *
   * @return bool true if all skus are approved
   */
  public function isAllSkuApproved(int $batch_id): bool {
    $check_result = $this->qa_batch_checker->getCompletionQaStatus($batch_id);

    return $check_result->is_approved();
  }

  /**
   *
   * @param int $batch_id batch_id
   *
   * @return bool true if all skus are approved
   */
  public function hasSkusWithUpdated(int $batch_id): bool {
    return $this->qa_batch_checker->batchHasUpdatedSkus($batch_id);
  }
  /**
   * @param int $batch_id    Batch Id
   * @param int $employee_id Employee Id
   *
   * @return void
   */
  public function markPartialWhiteLabelRequest(int $batch_id, int $employee_id) {
    $this->batch_status_updater->changeStatus($batch_id, Batch::STATUS_AUTOMATED_RE_DOWNSTREAMED, $employee_id);
  }
  /**
   * @param int $batch_id Batch ID
   *
   * @return void
   */
  public function markPartialWhiteLabelSentWLAt(int $batch_id) : void {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      $verification_id = $this->postgres_storage->create_verification_id();
      $this->postgres_storage->mark_batch_as_sent($batch_id, $verification_id, true);
      $this->postgres_storage->set_verification_id_for_skus($batch_id, $verification_id);
    } else {
      $verification_id = $this->storage->create_verification_id();
      $this->storage->mark_batch_as_sent($batch_id, $verification_id, true);
      $this->storage->set_verification_id_for_skus($batch_id, $verification_id);
    }
  }
}
