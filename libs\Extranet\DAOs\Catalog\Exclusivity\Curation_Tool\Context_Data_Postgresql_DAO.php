<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;

class Context_Data_Postgresql_DAO {
  use Logging_Trait;

  private ProductConnection $pdo;
  private PostgresConnection $pdo_psql;

  /**
   * @param ProductConnection $pdo PDO
   * @param PostgresConnection $pdo_psql PDO_PSL
   * @param LoggerInterface|null $logger Logger
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql, ?LoggerInterface $logger = null) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
    $this->logger = $logger;
  }


  /**
   * @return  array
   * only queries the curation table
   * to get source-id from tblVerificationItem based on the sku & batch id
   */
  public function get_verification_source_id_by_sku_and_batch(array $skus, int $batch_id, int $curated_max_days_ago): array {
    if (empty($skus)) {
      return [];
    }
    $sql = 'SELECT
                  vi."ViSKU" AS sku,
                  vi."ViSourceID" AS source_id,
                  recently_curated."ViFinalBrandMaID" AS recently_curated_manufacturer_id
              FROM "tblVerificationItem" vi
              LEFT JOIN LATERAL (
                  SELECT "ViFinalBrandMaID"
                  FROM "tblVerificationItem" viRecent
                  WHERE viRecent."ViSKU" = vi."ViSKU"
                      AND viRecent."ViFinalBrandMaID" > 0
                      AND (CURRENT_DATE - viRecent."ViLockedDate") < :curated_input_max_days_ago
                      AND viRecent."ViBatchID" != :batch_id_1
                  ORDER BY "ViID" DESC
                  LIMIT 1
              ) AS recently_curated ON true
              WHERE vi."ViBatchID" = :batch_id_2
                  AND vi."ViSKU" IN (
                      SELECT value::text
                      FROM jsonb_array_elements_text(:skus_array))';

    $prepared_statement = $this->pdo_psql->prepare($sql);
    $prepared_statement->bindValue(':batch_id_1', $batch_id, PDO::PARAM_INT);
    $prepared_statement->bindValue(':batch_id_2', $batch_id, PDO::PARAM_INT);
    $prepared_statement->bindValue(':curated_input_max_days_ago', $curated_max_days_ago, PDO::PARAM_INT);
    $prepared_statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

    if (!$prepared_statement->execute()) {
      $query_exception = ExecutionException::forStatement($prepared_statement, 'Failed to load context data for these skus');
      $this->log_throwable_error($query_exception, $query_exception->getMessage(), ['batch_id' => $batch_id, 'skus' => $skus]);
      throw $query_exception;
    }
    return $prepared_statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_UNIQUE);
  }


  /**
   * @param int $batch_id Batch ID
   * @param array $skus the SKUs to look data for
   * @param int $curated_max_days_ago Curated Max Days Ago
   *
   * @return array
   * Merges the results from non-curation tables & curation tables
   */
  public function get_context_skus_manufacturer_data(int $batch_id, array $skus, int $curated_max_days_ago): array {
    if (empty($skus)) {
      return [];
    }
    $this->log_info('Loading context manufacturers data input', ['batch_id' => $batch_id, 'skus' => $skus]);
    // calling query on  non-Curation  tables
    $context_skus = $this->get_context_skus_manufacturer_data_ext($batch_id, $skus, $curated_max_days_ago);
    // calling query on curation tables
    $src_skus = $this->get_verification_source_id_by_sku_and_batch($skus, $batch_id, $curated_max_days_ago);

    $context_skus_1 = array();
    foreach ($context_skus as $row) {
      if (count($src_skus) > 0) {
        foreach ($src_skus as $sku => $row1) {
          if ($row['sku'] == $sku) {
            $context_skus_1[$sku]['manufacturer_id'] = $row['manufacturer_id'];
            $context_skus_1[$sku]['ma_brw_id'] = $row['ma_brw_id'];
            $context_skus_1[$sku]['source_id'] = $row1['source_id'];
            $context_skus_1[$sku]['recently_curated_manufacturer_id'] = $row1['recently_curated_manufacturer_id'];
            $context_skus_1[$sku]['price_tier'] = $row['price_tier'];
          } else {
            $context_skus_1[$row['sku']]['manufacturer_id'] = $row['manufacturer_id'];
            $context_skus_1[$row['sku']]['ma_brw_id'] = $row['ma_brw_id'];
            $context_skus_1[$row['sku']]['source_id'] = null;
            $context_skus_1[$row['sku']]['recently_curated_manufacturer_id'] = null;
            $context_skus_1[$row['sku']]['price_tier'] = $row['price_tier'];
          }
        }
      } else {
        $context_skus_1[$row['sku']]['manufacturer_id'] = $row['manufacturer_id'];
        $context_skus_1[$row['sku']]['ma_brw_id'] = $row['ma_brw_id'];
        $context_skus_1[$row['sku']]['source_id'] = null;
        $context_skus_1[$row['sku']]['recently_curated_manufacturer_id'] = null;
        $context_skus_1[$row['sku']]['price_tier'] = $row['price_tier'];
      }
    }
    return $context_skus_1;
  }


  /**
   * @param int $batch_id Batch ID
   * @param array $skus the SKUs to look data for
   * @param int $curated_max_days_ago Curated Max Days Ago
   * @return array
   * only queries non-curation tables
   */
  public function get_context_skus_manufacturer_data_ext(int $batch_id, array $skus, int $curated_max_days_ago): array {
    if (empty($skus)) {
      return [];
    }
    $this->log_info('Loading context manufacturers data', ['batch_id' => $batch_id, 'skus' => $skus]);
    $sql_query = 'SELECT 
                   p.PrMaID AS manufacturer_id,
                   m.MaBrwId AS ma_brw_id,p.PrSKU AS sku,
                   p.PrSalePrice as sale_price,cl.ClID as cl_id,p.PrBclgID as pr_bclg_id
            FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
            JOIN csn_product.dbo.tblManufacturer m WITH (NOLOCK) ON m.MaID = p.PrMaID
            INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
            INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID         
            WHERE p.PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));

    $prepared_statement = $this->pdo->prepare($sql_query);

    $prepared_statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

    if (!$prepared_statement->execute()) {
      $query_exception = ExecutionException::forStatement($prepared_statement, 'Failed to load context data for these skus');
      $this->log_throwable_error($query_exception, $query_exception->getMessage(), ['batch_id' => $batch_id, 'skus' => $skus]);
      throw $query_exception;
    }

    $result = $prepared_statement->fetchAll();
    $final_result = [];
    foreach ($result as $row) {
      $price_tier = $this->get_price_tier(floatval($row["sale_price"]), $row["cl_id"], $row["pr_bclg_id"]);
      $row["price_tier"] = $price_tier;
      $final_result[] = $row;
    }
    return $final_result;
  }


  /**
   * @param float $sale_price Sale Price
   * @param int $cl_id the cl Id
   * @param int $b_clg_id b_clg_id
   * @return int
   * only queries non-curation tables
   */
  public function get_price_tier(float $sale_price, int $cl_id, int $b_clg_id): int {

    $this->log_info('Loading context manufacturers data', ['SalePrice' => $sale_price, 'clID' => $cl_id, 'bclgId' => $b_clg_id]);

    $sql = '
        SELECT COALESCE(
            CASE WHEN MAX("CptPriceTier") = 4 THEN 4 ELSE MAX("CptPriceTier") + 1 END, 1
            ) AS CptPriceTier
        FROM "tblClassPriceTier" cpt
        WHERE "CptPriceTierCeiling" < :salePrice
            AND "CptClID" = :clID
            AND "CptBclgID" = :bclgId';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':salePrice', $sale_price, PDO::PARAM_STR);
    $statement->bindValue(':clID', $cl_id, PDO::PARAM_INT);
    $statement->bindValue(':bclgId', $b_clg_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to load context data for skus');
      $this->log($exception, $exception->getMessage(), ['SalePrice' => $sale_price, 'clID' => $cl_id, 'bclgId' => $b_clg_id]);
      throw $exception;
    }
    return $statement->fetch(PDO::FETCH_COLUMN);
  }
}