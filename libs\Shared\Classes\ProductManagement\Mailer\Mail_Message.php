<?php
/**
 * Mail message representation which is finally clear from sending logic
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Mailer;

class Mail_Message {
  /**
   * @var array
   */
  private $recipients = [];
  /**
   * @var array
   */
  private $sender = [];
  /**
   * @var string
   */
  private $subject = '';
  /**
   * @var string
   */
  private $text_body = '';
  /**
   * @var string
   */
  private $html_body = '';
  /**
   * @var array
   */
  private $attachments = [];

  /**
   * @param string $email email of recipient
   * @param string $name  name of recipient
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function add_recipient($email, $name = '') {
    $this->recipients[] = [$email, $name];

    return $this;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Recipient $recipient Recipient
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function add_recipient_address(Mail_Recipient $recipient) : Mail_Message {
    return $this->add_recipient($recipient->get_email(), $recipient->get_name());
  }

  /**
   * @param array $recipients recipients
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_recipients(array $recipients) {
    $this->recipients = $recipients;

    return $this;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Recipient[] ...$recipients Recipients
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_recipient_addresses(Mail_Recipient ...$recipients) : Mail_Message {
    $this->reset_recipients();

    foreach ($recipients as $recipient) {
      $this->add_recipient_address($recipient);
    }

    return $this;
  }

  /**
   * @return array
   */
  public function get_sender() {
    return $this->sender;
  }

  /**
   * @param string $email email of sender
   * @param string $name  name of sender
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_sender($email, $name = '') {
    $this->sender = [$email, $name];

    return $this;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Sender $sender Sender
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_sender_address(Mail_Sender $sender) : Mail_Message {
    return $this->set_sender($sender->get_email(), $sender->get_name());
  }

  /**
   * @return string
   */
  public function get_subject() {
    return $this->subject;
  }

  /**
   * @param string $subject message subject
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_subject($subject) {
    $this->subject = $subject;

    return $this;
  }

  /**
   * @return string
   */
  public function get_text_body() {
    return $this->text_body;
  }

  /**
   * Due to specific PHPMailer implementation, setting text body without html will not work.
   *
   * @param string $body message text body
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_text_body($body) {
    $this->text_body = $body;

    return $this;
  }

  /**
   * @param string $body message text body
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function set_html_body($body) {
    $this->html_body = $body;

    return $this;
  }

  /**
   * @return string
   */
  public function get_html_body() {
    return $this->html_body;
  }

  /**
   * @return array
   */
  public function get_recipients() {
    return $this->recipients;
  }

  /**
   * @param string $content     raw file content
   * @param string $filename    file name as it appears in mail client
   * @param string $mime        attachment mime type
   * @param string $encoding    attachment encoding ("8bit", "7bit", "binary", "base64", and "quoted-printable")
   * @param string $disposition content disposition (attachment in most of cases)
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function add_attachment($content, $filename, $mime = '', $encoding = 'base64', $disposition = 'attachment') {
    $this->attachments[] = [$content, $filename, $mime, $encoding, $disposition];

    return $this;
  }

  /**
   * @return array
   */
  public function get_attachments() {
    return $this->attachments;
  }

  /**
   * @return array
   */
  public function to_array() {
    return get_object_vars($this);
  }

  /**
   * @return void
   */
  private function reset_recipients() {
    $this->set_recipients([]);
  }
}
