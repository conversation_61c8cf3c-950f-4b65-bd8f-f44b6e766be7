<?php
/**
 * Tests for the \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Eligibility_Requirement
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Eligibility_Requirement;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;

/**
 * @property \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Eligibility_Requirement subject
 */
class Loader_Eligibility_Requirement_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model
     */
    private $curation_request_sku_base_mock;

    /**
     * SetUp
     *
     * @return void
     */
    public function setUp(): void
    {
        $this->curation_request_sku_base_mock = $this->prophesize(Curation_Request_SKU_Base_Model::class);
        $this->subject = new Loader_Eligibility_Requirement();
    }

    /**
     *
     * @param bool   $is_master_core_class              is_master_core_class
     * @param bool   $is_holdout_manufacturer           is_holdout_manufacturer
     * @param bool   $is_wayfair_channel                is_wayfair_channel
     * @param bool   $is_active_join_supplier           is_active_join_supplier
     * @param bool   $is_right_assigned_supplier_method is_right_assigned_supplier_method
     * @param bool   $is_kit_pr_sku                     is_kit_pr_sku
     * @param bool   $is_perigold_only                  is_perigold_only
     * @param bool   $has_holdout_manufacturer_part     has_holdout_manufacturer_part
     * @param bool   $expected_results                  expected_results
     * @param string $message                           error message
     *
     * @dataProvider is_eligible_data_provider
     *
     * @return void
     */
    public function test_is_eligible(
        $is_master_core_class,
        $is_holdout_manufacturer,
        $is_wayfair_channel,
        $is_active_join_supplier,
        $is_right_assigned_supplier_method,
        $is_perigold_only,
        $is_standard_brand,
        $has_images,
        $has_holdout_manufacturer_part,
        $expected_results,
        $message
    ) {
        $this->curation_request_sku_base_mock->is_master_core_class()->willReturn($is_master_core_class);
        $this->curation_request_sku_base_mock->is_holdout_manufacturer()->willReturn($is_holdout_manufacturer);
        $this->curation_request_sku_base_mock->is_wayfair_channel()->willReturn($is_wayfair_channel);
        $this->curation_request_sku_base_mock->is_active_join_supplier()->willReturn($is_active_join_supplier);
        $this->curation_request_sku_base_mock->is_right_assigned_supplier_method()->willReturn($is_right_assigned_supplier_method);
        $this->curation_request_sku_base_mock->is_standard_brand()->willReturn($is_standard_brand);
        $this->curation_request_sku_base_mock->has_images()->willReturn($has_images);
        $this->curation_request_sku_base_mock->is_perigold_only()->willReturn($is_perigold_only);
        $this->curation_request_sku_base_mock->has_holdout_manufacturer_part()->willReturn($has_holdout_manufacturer_part);

        $this->assertEquals($expected_results, $this->subject->is_eligible($this->curation_request_sku_base_mock->reveal()), $message);
    }

    /**
     * @return array
     */
    public function is_eligible_data_provider()
    {
        return [
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'expected_results' => true,
                'has_holdout_manufacturer_part' => false,
                'message' => '',
            ],
            [
                'is_master_core_class' => false,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_master_core_class = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => true,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_holdout_manufacturer = true',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => false,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_wayfair_channel = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => false,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_active_join_supplier = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => false,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_right_assigned_supplier_method = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => false,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_standard_brand = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => false,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'has_images = false',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => true,
                'has_holdout_manufacturer_part' => false,
                'expected_results' => false,
                'message' => 'is_perigold_only = true',
            ],
            [
                'is_master_core_class' => true,
                'is_holdout_manufacturer' => false,
                'is_wayfair_channel' => true,
                'is_active_join_supplier' => true,
                'is_right_assigned_supplier_method' => true,
                'is_standard_brand' => true,
                'has_images' => true,
                'is_perigold_only' => false,
                'has_holdout_manufacturer_part' => true,
                'expected_results' => false,
                'message' => 'has_holdout_manufacturer_part = true',
            ],
        ];
    }
}
