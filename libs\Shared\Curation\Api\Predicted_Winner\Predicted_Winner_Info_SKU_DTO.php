<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Predicted_Winner;

class Predicted_Winner_Info_SKU_DTO {

  /**
   * @var string
   */
  private $sku;

  /**
   * @var int|null
   */
  private $total_orders;

  /**
   * @var bool
   */
  private $is_predicted_winner;

  /**
   * Assortment_Predicted_Winner_SKU constructor.
   *
   * @param string   $sku                 SKU
   * @param bool     $is_predicted_winner Is predicted winner?
   * @param int|null $total_orders        number of orders
   */
  public function __construct(string $sku, bool $is_predicted_winner, ?int $total_orders) {
    $this->sku                 = $sku;
    $this->total_orders        = $total_orders;
    $this->is_predicted_winner = $is_predicted_winner;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return int|null
   */
  public function get_total_orders() : ?int {
    return $this->total_orders;
  }

  /**
   * @return bool
   */
  public function is_predicted_winner() : bool {
    return $this->is_predicted_winner;
  }
}
