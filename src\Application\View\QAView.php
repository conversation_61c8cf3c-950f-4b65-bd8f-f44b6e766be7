<?php

declare(strict_types=1);

namespace App\Application\View;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data;

use function array_merge;
use function array_unique;
use function sort;

class QAView extends BaseView
{
    private const COMPONENT_KEY = 'QAPage';

    private Completion_Batch_Data $batch_data;

    private ?Curator $batch_curator;

    private ?Curator $batch_approver;

    private Config_Data $config;

    private int $batchId;

    private ?int $batchProcessType;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    private array $sections;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[]
     */
    private array $rejectionReasons;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
     */
    private array $suggestedStyleRejectionReasons;

    private ?Rebrand_Project $rebrandProject;

    /**
     * @var bool Read only mode. All action buttons are disabled
     */
    private bool $isReadOnlyMode;

    /**
     * @var string[]
     */
    private array $warnings;

    /**
     * @var bool
     */
    private bool $isAssortmentWorkflowOffshoreUser;

    /**
     * QAView constructor.
     *
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data $batch_data Batch Data
     * @param \WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator $batch_curator Batch Curator
     * @param \WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator $batch_approver Batch Approver
     * @param \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data $config Config
     * @param int $batchId Batch ID
     * @param int|null $batchProcessType Batch Process Type ID
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Sections
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[] $rejectionReasons Rejection Reasons
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[] $suggestedStyleRejectionReasons Suggested reasons pick list
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project|null $rebrandProject Rebrand_Project
     * @param bool $isReadOnlyMode Read only mode (curation completed)
     * @param string[] $warnings Warnings
     * @param bool $isAssortmentWorkflowOffshoreUser Is Assortment Workflow Offshore User
     */
    public function __construct(
        Completion_Batch_Data $batch_data,
        ?Curator $batch_curator,
        ?Curator $batch_approver,
        Config_Data $config,
        int $batchId,
        ?int $batchProcessType,
        array $sections,
        array $rejectionReasons,
        array $suggestedStyleRejectionReasons,
        Rebrand_Project $rebrandProject = null,
        bool $isReadOnlyMode = false,
        array $warnings = [],
        bool $isAssortmentWorkflowOffshoreUser = false
    ) {
        parent::__construct();
        $this->batch_data = $batch_data;
        $this->batch_curator = $batch_curator;
        $this->batch_approver = $batch_approver;
        $this->config = $config;
        $this->batchId = $batchId;
        $this->batchProcessType = $batchProcessType;
        $this->sections = $sections;
        $this->rejectionReasons = $rejectionReasons;
        $this->suggestedStyleRejectionReasons = $suggestedStyleRejectionReasons;
        $this->rebrandProject = $rebrandProject;
        $this->isReadOnlyMode = $isReadOnlyMode;
        $this->warnings = $warnings;
        $this->isAssortmentWorkflowOffshoreUser = $isAssortmentWorkflowOffshoreUser;

        if ($this->isReadOnlyMode) {
            $this->markAllItemsAsReadOnly();
        }
    }

    /**
     * Gets the value passed to the bootstrap script as 'key'. This can be used
     * by apps to determine which component to load.
     *
     * @return string component key
     */
    protected function componentKey(): string
    {
        return self::COMPONENT_KEY;
    }

    /**
     * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data
     */
    public function curationConfig(): Config_Data
    {
        return $this->config;
    }

    /**
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    public function sections(): array
    {
        return $this->sections;
    }

    /**
     * @return string[]
     */
    public function suppliers(): array
    {
        $suppliers = [];

        foreach ($this->sections as $section) {
            foreach ($section->get_curation_items() as $item) {
                $suppliers = array_merge($item->get_suppliers(), $suppliers);
            }
        }

        $suppliers = array_unique($suppliers);
        sort($suppliers);

        return $suppliers;
    }

    /**
     * @return int
     */
    public function batchId(): int
    {
        return $this->batchId;
    }

    /**
     * @return int
     */
    public function batchProcessType(): ?int
    {
        return $this->batchProcessType;
    }

    /**
     * @return array
     */
    public function batchData(): array
    {
        return [
            'curator' => $this->batch_curator !== null
                ? $this->batch_curator->get_full_name()
                : 'Not Assigned',
            'status' => $this->batch_data->getStatus(),
            'process_type' => $this->batch_data->getProcessTypeName(),
            'created_at' => $this->batch_data->getCreatedAt() !== null
                ? $this->batch_data->getCreatedAt()->format('M d, Y \a\t g:iA T')
                : null,
            'approved_by' => $this->batch_approver !== null ? $this->batch_approver->get_full_name() : '',
            'approved_at' => $this->batch_data->getApprovedAt() !== null
                ? $this->batch_data->getApprovedAt()->format('M d, Y \a\t g:iA T')
                : null
        ];
    }

    /**
     * @return null|Rebrand_Project
     */
    public function rebrandProject(): ?Rebrand_Project
    {
        return $this->rebrandProject;
    }

    /**
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[]
     */
    public function rejectionReasons(): array
    {
        return $this->rejectionReasons;
    }

    /**
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
     */
    public function suggestedStyleRejectionReasons(): array
    {
        return $this->suggestedStyleRejectionReasons;
    }

    /**
     * @return bool
     */
    public function isReadOnlyMode(): bool
    {
        return $this->isReadOnlyMode;
    }


    /**
     * @return bool
     */
    public function isAssortmentWorkflowOffshoreUser(): bool
    {
        return $this->isAssortmentWorkflowOffshoreUser;
    }

    /**
     * @return void
     */
    private function markAllItemsAsReadOnly(): void
    {
        foreach ($this->sections as $section) {
            foreach ($section->get_curation_items() as $curationItem) {
                $curationItem->set_readonly(true);
            }
        }
    }

    /**
     * @return string[]
     */
    public function warnings(): array
    {
        return $this->warnings;
    }
}
