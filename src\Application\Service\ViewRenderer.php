<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\Service;

use WF\Frontend\Webpack\Webpack_Config;
use WF\Frontend\Webpack\Webpack_Layout_View;
use WF\Frontend\Webpack\Webpack_React_View;
use WF\Shared\Environment;
use function sprintf;

class ViewRenderer
{
    private bool $isProduction;

    /**
     * @param string $env 'prod', 'can' or 'dev'
     */
    public function __construct(string $env)
    {
        $this->isProduction = $env === Environment::PRODUCTION;
    }

    public function render(Webpack_React_View $view, string $webpackPublicPathRoot, string $webpackOutputPathRoot): string
    {
        $webpackPublicPathRoot = $this->isProduction
            ? '/' // in case of PROD env, it will be always "/"
            : $webpackPublicPathRoot;

        $webpackBrand = Webpack_Config::BRAND_EXTRANET;
        $webpackLocale = Webpack_Config::LOCALE_ENGLISH_US;

        $webpackOutputPath = sprintf(
            '%s/d/curation-tool/bundles/%s/%s',
            $webpackOutputPathRoot,
            $webpackBrand,
            $webpackLocale
        );
        $webpackPublicPath = sprintf(
            '%sd/curation-tool/bundles/%s/%s/',
            $webpackPublicPathRoot,
            $webpackBrand,
            $webpackLocale
        );

        $webpackLayout = new Webpack_Layout_View(
            new Webpack_Config(
                $webpackOutputPath,
                $webpackPublicPath
            )
        );

        return $webpackLayout->render($view);
    }
}
