<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */
namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\External;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Matcher;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Move_To_Brand_Decision_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Decision_Service;

class Context_Data_Decision_Matcher implements Assortment_Curation_Decision_Matcher {
  /**
   * @var Context_Data_Decision_Service
   */
  private $context_decision_service;

  /**
   * @param Context_Data_Decision_Service $context_decision_service context decision service
   */
  public function __construct(Context_Data_Decision_Service $context_decision_service) {
    $this->context_decision_service = $context_decision_service;
  }


  /**
   * Starting from the price tier and manufacturer, it tries to get the closest curation decision available
   *
   * @param int $batch_id        Batch ID
   * @param int $manufacturer_id Manufacturer ID
   * @param int $price_tier      Price Tier
   *
   * @return Assortment_Curation_Move_To_Brand_Decision_Data
   */
  public function find_closest_match(int $batch_id, int $manufacturer_id, int $price_tier) : ?Assortment_Curation_Move_To_Brand_Decision_Data {
    $match = $this->context_decision_service->find_closest_match($batch_id, $manufacturer_id, $price_tier);

    if ($match->is_fully_filled() && $match->has_same_manufacturer($manufacturer_id)) {
      return new Assortment_Curation_Move_To_Brand_Decision_Data(
          $manufacturer_id,
          $match->get_style_id(),
          $match->get_substyle_id(),
          $match->get_price_tier()
      );
    }

    return null;
  }
}
