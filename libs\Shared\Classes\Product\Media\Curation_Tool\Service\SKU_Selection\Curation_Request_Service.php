<?php
/**
 * Curation Request Service
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection;

use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model;
use WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_DAO;
use WF\Shared\Models\Product_Model;

class Curation_Request_Service {

  private Curation_Request_DAO $dao;

  private Curation_Request_Factory $factory;

  private Requirement_Container $requirement_container_product_addition;

  private Requirement_Container $requirement_container_manual;

  /**
   * Constructor
   *
   * @param Curation_Request_DAO     $dao                                    Curation Request DAO
   * @param Curation_Request_Factory $factory                                Curation Request Factory
   * @param Requirement_Container    $requirement_container_product_addition Curation Requirements Container PA
   * @param Requirement_Container    $requirement_container_manual           Curation Requirements Container manual
   */
  public function __construct(
      Curation_Request_DAO $dao,
      Curation_Request_Factory $factory,
      Requirement_Container $requirement_container_product_addition,
      Requirement_Container $requirement_container_manual
  ) {
    $this->dao                                    = $dao;
    $this->factory                                = $factory;
    $this->requirement_container_product_addition = $requirement_container_product_addition;
    $this->requirement_container_manual           = $requirement_container_manual;
  }

  /**
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model $curation_request Curation Request Model
   * @param array                                                                              $sku_list         SKU List
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model
   */
  public function load_skus_by_list(Curation_Request_Model $curation_request, array $sku_list) : Curation_Request_Model {
    $sku_list              = $this->dao->get_skus_data($sku_list);
    $requirement_container = $this->get_requirements_container_by_type($curation_request->type_id);

    foreach ($sku_list as $sku_data) {
      if ($this->should_skip_by_status($sku_data['product_status'])) {
        continue;
      }

      $sku_model = $this->factory->create_request_sku_source_model(
          $requirement_container,
          $sku_data['sku'],
          $sku_data
      );

      $curation_request->add_sku($sku_model);
    }

    return $curation_request;
  }

  /**
   * Initial validation on product status
   *
   * @param int $product_status product status
   *
   * @return bool
   */
  private function should_skip_by_status(int $product_status) : bool {
    // PT:15513361 skip SKUs with PrStatus = 0 or 7 in order to reduce the number of skus needed to be reevaludated
    return in_array($product_status, [Product_Model::STATUS_CAN_BE_DELETED, Product_Model::STATUS_INTERNAL_DISCONTINUED]);
  }

  /**
   * @param int $type_id type id
   *
   * @return Requirement_Container
   */
  private function get_requirements_container_by_type(int $type_id) {
    return $type_id === Curation_Request_Model::TYPE_PRODUCT_ADDITION ?
        $this->requirement_container_product_addition :
        $this->requirement_container_manual;
  }

}
