<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Kit_Collider;
use WF\Shared\Curation\Api\Api_Curation;
use WF\Shared\Curation\Api\ApiProductContextCollectionNextgen;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use Psr\Log\LoggerInterface;
use WF\Shared\SLO\SLOFactory;
use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

use function array_diff;
use function array_keys;
use function array_merge;
use function array_values;
use function count;
use function json_encode;
use function in_array;

class Curation_Item_Collision_Loader extends Curation_Item_Loader {

  public const ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION = 'enable_curation_collision_calculation_optimization';

  private const SLO_SERVICE_NAME = 'brand-workflows-curation-tool-collision-loader.';

  private Api_Curation $curationApi;

  private Curation_Collection_Collider $collectionCollider;

  private Curation_Kit_Collider $kitCollider;

  private ApiProductContextCollectionNextgen $pccNextgenApi;

  private SLOFactory $slo;

  private CurationItemFactory $curationItemFactory;

  private bool $useContextCollectionApiForCollisions;

  private bool $useCollisionCalculationOptimization;

  private bool $useEligibilityFilterCollision;

  /**
   * @param Curation_Tool_DAO                   $dao                                  An ability to perform DB queries
   * @param CurationItemFactory                 $curationItemFactory                  Factory for Curation_Item
   * @param Curation_Collection_Collider        $collectionCollider                   Loading Collection collision
   * @param Curation_Kit_Collider               $kitCollider                          Loading Kit collision
   * @param Api_Curation                        $curationApi                          Curation API client
   * @param SLOFactory                          $sloFactory                           An ability to get and track SLO
   * @param LoggerInterface                     $logger                               Logger
   * @param bool                                $useContextCollectionApiForCollisions FT `enable_context_collection_api_for_collisions`
   * @param bool                                $useCollisionCalculationOptimization  FT `enable_curation_collision_calculation_optimization`
   * @param bool                                $useEligibilityFilterCollision        FT `merch_curation_use_eligibility_filter_collision`
   * @param ApiProductContextCollectionNextgen  $pccNextgenApi                    ApiProductContextCollectionNextgen
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface $featureToggles        Feature toggle
   * @param Curation_Tool_Postgres_DAO          $postgres_dao                          Curation Tool Postgres DAO
   */
  public function __construct(
      Curation_Tool_DAO $dao,
      CurationItemFactory $curationItemFactory,
      Curation_Collection_Collider $collectionCollider,
      Curation_Kit_Collider $kitCollider,
      Api_Curation $curationApi,
      SLOFactory $sloFactory,
      LoggerInterface $logger,
      bool $useContextCollectionApiForCollisions,
      bool $useCollisionCalculationOptimization,
      bool $useEligibilityFilterCollision,
      ApiProductContextCollectionNextgen $pccNextgenApi,
      FeatureTogglesInterface $featureToggles,
      Curation_Tool_Postgres_DAO $postgres_dao
  ) {
    parent::__construct($dao, $curationItemFactory, $featureToggles, $postgres_dao);

    $this->curationApi = $curationApi;
    $this->collectionCollider = $collectionCollider;
    $this->kitCollider = $kitCollider;
    $this->pccNextgenApi = $pccNextgenApi;
    $this->slo = $sloFactory;
    $this->logger = $logger;
    $this->curationItemFactory = $curationItemFactory;
    $this->useContextCollectionApiForCollisions = $useContextCollectionApiForCollisions;
    $this->useCollisionCalculationOptimization = $useCollisionCalculationOptimization;
    $this->useEligibilityFilterCollision = $useEligibilityFilterCollision;
  }

  /**
   * @param int $batchId BatchID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  public function load(int $batchId, int $qaStatus) : array {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    $slo->withTags(['batchId' => $batchId]);

    $curationItems = parent::load($batchId, $qaStatus);

    $skus         = array_keys($curationItems);
    $contextXnIDs = $this->getContextXnIDs($curationItems);

    $curationItems = array_merge($curationItems, $this->loadCollisions($skus, $contextXnIDs));

    $slo->stop();

    return $curationItems;
  }

  /**
   * @param string[] $skus         SKUs
   * @param int[]    $contextXnIDs Context XnIDs
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[]
   */
  private function loadCollisions(array $skus, array $contextXnIDs) : array {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    try {
      $slo->withTags(['skus' => json_encode($skus), 'contextXnIDs' => json_encode($contextXnIDs)]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }
    $curationItems = [];

    // load all kits collections
    $kitsCollisions = $this->kitCollider->getCollisionsForSKUS($skus);
    foreach ($kitsCollisions as $kitsCollision) {
      $item = $this->curationItemFactory->create();
      $item->set_sku($kitsCollision);

      $curationItems[$kitsCollision] = $item;
    }

    if (empty($contextXnIDs)) {
      $slo->stop();
      return $curationItems;
    }

    if ($this->useContextCollectionApiForCollisions) {
      $sloCollisions  = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-api');
      $collisionItems = $this->loadAllCollisionsApi($skus, $contextXnIDs);
      $sloCollisions->stop();
    } elseif ($this->useCollisionCalculationOptimization) {
      $sloCollisions  = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-new');
      $collisionItems = $this->loadAllCollisionsOnce($skus, $contextXnIDs);
      $sloCollisions->stop();
    } else {
      $sloCollisions  = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-old');
      $collisionItems = $this->loadAllCollisionsInteratable($skus, $contextXnIDs);
      $sloCollisions->stop();
    }
    $collisionItems = $this->filterNonEligibleForCurationItems($collisionItems);

    try {
      $slo->stop();
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    return array_merge($curationItems, $collisionItems);
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item[] $curationItems Curation Items
   *
   * @return int[]
   */
  private function getContextXnIDs(array $curationItems) : array {
    $contextXnIDs = [];
    foreach ($curationItems as $curationItem) {
      if (empty($curationItem->get_context_xn_id()) || in_array($curationItem->get_context_xn_id(), $contextXnIDs)) {
        continue;
      }

      $contextXnIDs[] = $curationItem->get_context_xn_id();
    }

    return $contextXnIDs;
  }

  /**
   * @param string[] $skus         SKUs
   * @param int[]    $contextXnIDs collection contexts
   *
   * @return array<string, Curation_Item>
   */
  private function loadAllCollisionsOnce(array $skus, array $contextXnIDs) : array {
    $items = [];

    $this->logger->info(
        sprintf(
            'Feature "%s" enabled, so retrieving collection collision SKUs at once',
            self::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION
        ),
        [
            'skus' => $skus,
            'context_xn_IDs' => $contextXnIDs,
        ]
    );

    $sloQuery   = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-query');
    $collisions = $this->collectionCollider->get_all_collisions_collections($contextXnIDs, $skus);
    try {
      $sloQuery->stop();
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    $sloPop = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-populate');
    try {
      $sloPop->withTags(['collisionsNumber' => count(array_keys($collisions))]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }
    foreach ($collisions as $collision => $contextXnID) {
      $item = $this->curationItemFactory->create();
      $item->set_sku($collision);
      $item->set_context_xn_id($contextXnID);

      $items[$collision] = $item;
    }
    try {
      $sloPop->stop();
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    return $items;
  }

  /**
   * @param string[] $skus         SKUs
   * @param int[]    $contextXnIDs collection contexts
   *
   * @return array<string, Curation_Item>
   */
  private function loadAllCollisionsInteratable(array $skus, array $contextXnIDs) : array {
    $items = [];

    $this->logger->info(
        sprintf(
            'Feature "%s" disabled, so retrieving collection collision SKUs iterable',
            self::ENABLE_CURATION_COLLISIONS_CALCULATION_OPTIMIZATION
        ),
        [
            'skus' => $skus,
            'context_xn_IDs' => $contextXnIDs,
        ]
    );

    foreach ($contextXnIDs as $contextXnID) {
      $slo        = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-query');
      $collisions = $this->collectionCollider->get_collisions_for_collection($contextXnID, $skus);
      try {
        $slo->stop();
      } catch (\Exception $e) {
        // Don't break the execution if the monitor fails
      }

      $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-populate');
      try {
        $slo->withTags(['collisionsNumber' => count($collisions)]);
      } catch (\Exception $e) {
        // Don't break the execution if the monitor fails
      }
      foreach ($collisions as $collision) {
        $item = $this->curationItemFactory->create();
        $item->set_sku($collision);
        $item->set_context_xn_id($contextXnID);

        $items[$collision] = $item;
      }
      try {
        $slo->stop();
      } catch (\Exception $e) {
        // Don't break the execution if the monitor fails
      }
    }

    return $items;
  }

  /**
   * @param string[] $skus         SKUs
   * @param int[]    $contextXnIDs collection contexts - might be used in the future
   *
   * @return array<string, Curation_Item>
   */
  private function loadAllCollisionsApi(array $skus, array $contextXnIDs) : array {
    $items = [];

    $this->logger->info(
        sprintf(
            'Retrieving collection collision SKUs from PCC API'
        ),
        [
            'skus' => $skus,
            'context_xn_IDs' => $contextXnIDs,
        ]
    );
    $sloQuery = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-query');
    $collisions = [];
    try {
      $collisions = $this->pccNextgenApi->findSkuCollisions($skus);
    } catch (API_Request_Exception $exception) {
      $this->logger->error(
          sprintf(
              'An Exception (%s) thrown during PCC API call, retrieving collection collision SKUs from DB',
              $exception->getMessage()
          ),
          [
              'skus'              => $skus,
              'exception_message' => $exception->getMessage(),
              'exception_trace'   => $exception->getTraceAsString(),
          ]
      );

      // fallback to the previous approach
      $items = $this->loadAllCollisionsInteratable($skus, $contextXnIDs);
    } finally {
      try {
        $sloQuery->stop();
      } catch (\Exception $e) {
        // Don't break the execution if the monitor fails
      }
    }
    if (!empty($items)) {
      return $items; // fallback case
    }

    // verify that all collections are present in collisions
    $diffXnIDs = array_diff($contextXnIDs, array_values($collisions));
    if (count($diffXnIDs) > 0) {
      // most likely we're working with PA Flow when collection collision are not present at API
      // in such case the PCC API will return NULL as collection ID
      // fallback to legacy approach

      $items = $this->loadAllCollisionsInteratable(array_keys($collisions), $diffXnIDs);
    }

    $sloPop = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-populate');
    try {
      $sloPop->withTags(['collisionsNumber' => count(array_keys($collisions))]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }
    foreach ($collisions as $collision => $contextXnID) {
      if (in_array($collision, $skus)) {
        continue; // we should return only collisions
      }
      $item = $this->curationItemFactory->create();
      $item->set_sku($collision);
      if (is_integer($contextXnID)) {
        $item->set_context_xn_id($contextXnID);
      }

      $items[$collision] = $item;
    }
    try {
      $sloPop->stop();
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    return $items;
  }

  /**
   * @param Curation_Item[] $items A Curation items to be filtered
   *
   * @return Curation_Item[]
   */
  private function filterNonEligibleForCurationItems(array $items) : array {
    if (!$this->useEligibilityFilterCollision) {
      return $items;
    }

    $sloFilter = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__ . '-filter');

    $skus = array_values(array_map(
        function (Curation_Item $item) {
          return $item->get_sku();
        }, $items
    ));

    $skuEligibleMapping = [];
    try {
      if (count($skus) > 0) {
        $skuEligibleMapping = $this->curationApi->is_eligible_batch($skus);
      }
    } catch (API_Request_Exception $exception) {
      // use default behavior(treat sku as eligible ) if service API isn't available
      $this->logger->warning('Curation service is not available', ['exception' => $exception]);
    }

    foreach ($skuEligibleMapping as $sku => $isEligible) {
      if (!$isEligible) {
        unset($items[$sku]);
      }
    }

    try {
      $sloFilter->stop();
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    return $items;
  }
}
