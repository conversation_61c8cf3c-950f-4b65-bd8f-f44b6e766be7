<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Contracts\SKU_Collisions;

interface Collection_SKU_Collider {

  /**
   * @param int   $collection    Collection ID
   * @param array $excluded_skus Excluded SKUs
   *
   * @return string[]
   */
  public function get_colliding_collection_skus(int $collection, array $excluded_skus) : array;
}
