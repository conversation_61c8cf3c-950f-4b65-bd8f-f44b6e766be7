<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Candidate_DTO;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Filter_Request;

interface Api_Exclusivity_Assortment {

  /**
   * @param Exclusivity_Assortment_Filter_Request $filter Filter
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_sku_list(Exclusivity_Assortment_Filter_Request $filter);

  /**
   * @param Exclusivity_Assortment_Filter_Request $params filters for the approved sku list
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_for_investment_sku_list(Exclusivity_Assortment_Filter_Request $params);

  /**
   * @param string[] $skus SKUs to check
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_for_whitelabel_sku_list(array $skus);
}
