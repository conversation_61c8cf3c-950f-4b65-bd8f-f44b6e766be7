<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Auth;

use WF\Curation\ProductionTrackingApi\ClientConfig;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;

class AuthClient
{

    private const TOKEN_ENDPOINT     = 'oauth/token';
    private const CLIENT_CREDENTIALS = 'client_credentials';
    private const TIMEOUT            = 60;

    /**
     * @var ClientConfig
     */
    private $config;

    /**
     * Authentication constructor.
     */
    public function __construct(ClientConfig $config)
    {
        $this->config = $config;
    }

    /**
     * @return string|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function retrieveToken(): ?string
    {
        $httpClient = $this->config->getAuthHttpClient();

        $logger = $this->config->getLogger();

        $headers = ['Content-Type' => 'application/json'];

        $body = [
            'client_id'    => $this->config->getClientId(),
            'client_secret' => $this->config->getClientSecret(),
            'grant_type'    => self::CLIENT_CREDENTIALS
        ];

        $request = new Request('POST', self::TOKEN_ENDPOINT, $headers, \GuzzleHttp\json_encode($body));

        $options = [
            'connection_timeout' => self::TIMEOUT,
            'http_errors'        => true,
        ];

        $responseBody = [];
        try {
            $response = $httpClient->send($request, $options);

            /** @phpstan-ignore-next-line */
            if ($response->getBody() !== null && mb_strlen($response->getBody()->getContents()) > 0) {
                $responseBody = \json_decode($response->getBody()->getContents(), true);
            }

            $context = [
                'request'  => \json_encode($body),
                'response' => \json_encode($responseBody),
            ];

            $logger->info("POST " . self::TOKEN_ENDPOINT, $context);

            return $responseBody['access_token'];
        } catch (RequestException $exception) {
            $context = [
                'request'  => \json_encode($body),
                'response' => $exception->getMessage(),
            ];

            $logger->error('POST ' . self::TOKEN_ENDPOINT, $context);

            return null;
        }
    }
}
