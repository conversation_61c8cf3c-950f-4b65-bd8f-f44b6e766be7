<?php

declare (strict_types=1);

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool;


class Manufacturer_Data implements \JsonSerializable {

  /**
   * @var int
   */
  private $id;

  /**
   * @var string
   */
  private $brandClass;

  /**
   * @var string
   */
  private $name;

  /**
   * Manufacturer_Data constructor.
   *
   * @param int    $id         ID
   * @param string $name       Name
   * @param string $brandClass Brand Class
   */
  public function __construct(int $id = null, string $name = '', string $brandClass = '') {
    $this->id         = $id;
    $this->name       = $name;
    $this->brandClass = $brandClass;
  }

  /**
   * @return int
   */
  public function value() {
    return $this->id;
  }

  /**
   * @return string
   */
  public function text() : string {
    return sprintf('%s (%s)', $this->name, $this->brandClass);
  }

  /**
   * @return string
   */
  public function name() : string {
    return $this->name;
  }

  /**
   * @return string
   */
  public function brandClass() {
    return $this->brandClass;
  }

    /**
     * @return array
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
        return [
            'value' => $this->value(),
            'text' => $this->text(),
            'name' => $this->name(),
            'brandClass' => $this->brandClass(),
        ];
    }
}
