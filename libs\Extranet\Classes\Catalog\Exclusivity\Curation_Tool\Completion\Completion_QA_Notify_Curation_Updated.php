<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use App\Application\Controller\AbstractBaseController;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer;
use WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface;

class Completion_QA_Notify_Curation_Updated extends AbstractBaseController {
    private const FROM_EMAIL_ADDRESS = '<EMAIL>';
    private const FROM_NAME          = 'Batch QA';

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service
     */
    private $employeeService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service
     */
    private $qaBatchSkuService;

    private string $baseUri;

    /**
     * Email_Notification constructor.
     *
     * @param \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface                                      $mailer               Mailer Interface
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service     $employeeService      Completion_Employee_Service
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service $qaRejectedSkuService Completion_QA_Rejected_SKU_Service
     * @param string                                                                                            $baseUri              Product Base URI
     */
    public function __construct(
        Mailer_Interface $mailer,
        Completion_Employee_Service $employeeService,
        Completion_QA_Batch_SKU_Service $qaRejectedSkuService,
        string $baseUri
    ) {


        $this->mailer            = new Mailer();
        $this->employeeService   = $employeeService;
        $this->qaBatchSkuService = $qaRejectedSkuService;
        $this->baseUri           = $baseUri;
    }

    /**
     * @param int $batchId            Batch ID
     * @param int $assignedEmployeeId Assigned Employee ID
     * @param int $qaEmployeeId       QA Employee ID
     *
     * @return void
     */
    public function send(int $batchId, $Styles,$SubStyles,$updatedStyle,$updatedSubStyle,$sku, int $assignedEmployeeId, int $qaEmployeeId) {

        $email = $this->mailer->create_message();
        $assigned = $this->employeeService->get($assignedEmployeeId);
        $qa       = $this->employeeService->get($qaEmployeeId);
        $email
            ->set_html_body($this->get_body($batchId,$Styles,$SubStyles,$updatedStyle,$updatedSubStyle ,$sku,$assigned->getName(), $qa->getName()))
            ->set_subject($this->get_subject($batchId))
            ->set_sender(self::FROM_EMAIL_ADDRESS, self::FROM_NAME)
            ->add_recipient($assigned->getEmail(), $assigned->getName());
        $this->info('sending mail', [$email]);
        $this->mailer->send($email, true);

    }

    /**
     * @param int $batchId Batch ID
     *
     * @return string
     */
    private function get_subject(int $batchId) : string {

        return sprintf('Batch-Id %d has updated SKUs at QA', $batchId);
    }

    /**
     * @param int    $batchId      Batch ID
     * @param string $assignedName Assigned Name
     * @param string $qaName       QA Name
     *
     * @return string
     */


    private function get_bodyForBulkInfo(int $batchId, $oldSkusInfo, $updatedStyle, $updatedSubStyle, $skus, string $assignedName, string $qaName): string {
        $body = '<p>Hi %s,</p>
             <p>Curation decisions on Batch %d for the following SKUs were updated  by QA  %s. Please review your Style,SubStyle & do any changes if Required</p>
             <p>%s</p>
             <p><b>Updated Style</b></p>
             <p><b>Style</b></p>
             <p>%s</p>
             <p><b>Updated Sub-Style</b></p>
             <p><b>Substyle</b></p>
             <p>%s</p>
             <p>Link: %s</p>';
        $skusList = implode("<br>", $skus);
        $link = sprintf('%s/d/curation-tool/index?batch_id=%s', $this->baseUri, $batchId);
        return sprintf(
            $body,
            $assignedName,
            $batchId,
            $qaName,
            $skusList,
            $updatedStyle->VsName ?? null,
            $updatedSubStyle->VssName ?? null,
            $link
        );
    }


    private function get_body(int $batchId,$Styles,$SubStyles,$updatedStyle,$updatedSubStyle,$sku ,string $assignedName, string $qaName) : string {

        $body = '<p>Hi %s,</p>
             <p>Curation decisions for Batch %d (SKU <b>%s</b> ),has been updated by QA %s. Please review and make any necessary changes to the Style & SubStyle if Required</p>
             <p>%s</p>
              <p><b>Style</b></p>
              <p> %s is updated to %s</p>
              <p><b>Substyle</b></p>
              <p> %s is updated to %s</p>



             <p>Link: %s</p>';
        $content = vsprintf("%s", $sku);
        $link = sprintf('%sd/curation-tool/index?batch_id=%s', $this->baseUri, $batchId);

        return sprintf(
            $body,
            $assignedName,
            $batchId,
            $content,
            $qaName,
            $this->get_rejected_skus_message($batchId),
            $Styles->VsName??null,
            $updatedStyle->VsName??null,
            $SubStyles->VssName??null,
            $updatedSubStyle->VssName??null,
            $link
        );
    }
    /**
     * @param int $batchId Batch ID
     *
     * @return string
     */
    private function get_rejected_skus_message(int $batchId) : string {

        $skus         = $this->qaBatchSkuService->getRejectedSkus($batchId);
        $rowDelimiter = '<br />';

        $message = '';
        foreach ($skus as $sku) {
            $message .= sprintf('%s: %s%s', $sku->get_sku(), $sku->get_message(), $rowDelimiter);
        }

        return $message;
    }

    public function sendMailForBulkData(int $batchId, $oldSkusInfo, $updatedStyle, $updatedSubStyle, $skus, int $assignedEmployeeId, int $qaEmployeeId) {

        $email = $this->mailer->create_message();
        $assigned = $this->employeeService->get($assignedEmployeeId);
        $qa = $this->employeeService->get($qaEmployeeId);
        $email
            ->set_html_body($this->get_bodyForBulkInfo($batchId, $oldSkusInfo, $updatedStyle, $updatedSubStyle, $skus, $assigned->getName(), $qa->getName()))
            ->set_subject($this->get_subject($batchId))
            ->set_sender(self::FROM_EMAIL_ADDRESS, self::FROM_NAME)
            ->add_recipient($assigned->getEmail(), $assigned->getName());
        $this->info('sending mail', [$email]);
        $this->mailer->send($email, true);
    }

}