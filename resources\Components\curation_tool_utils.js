/**
 * Shared utils for curation tool index and qa
 *
 * <AUTHOR> <<PERSON><PERSON><PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

import {
  INDEX_STATUS,
  QA_STATUS,
  COMPLETE_STATUS,
  STATUS_LABEL_MAP,
} from './curation_tool_constants';

const STATUS_MANUAL_UNASSIGNED = 1;
const STATUS_MANUAL_ASSIGNED = 2;
const STATUS_MANUAL_IN_PROGRESS = 3;
const STATUS_MANUAL_CURATION_COMPLETE = 4;
const STATUS_MANUAL_QA_COMPLETE = 5;
const STATUS_MANUAL_DOWNSTREAMED = 6;
const STATUS_AUTOMATED_GENERATED = 7;
const STATUS_AUTOMATED_DOWNSTREAMED = 8;
const STATUS_AUTOMATED_QA_IN_PROGRESS = 9;
const STATUS_AUTOMATED_QA_COMPLETE = 10;
const STATUS_AUTOMATED_R<PERSON>_DOWNSTREAMED = 11;
const STATUS_AUTOMATED_POST_LAUNCH_QA_ASSIGNED = 12;
const STATUS_AUTOMATED_RE_DOWNSTREAM_INPROGRESS = 13;
const STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS = 14;

const INDEX_STATUSES = [
  STATUS_MANUAL_UNASSIGNED,
  STATUS_MANUAL_ASSIGNED,
  STATUS_MANUAL_IN_PROGRESS,
  STATUS_AUTOMATED_GENERATED,
];

const QA_STATUSES = [
  STATUS_MANUAL_CURATION_COMPLETE,
  STATUS_AUTOMATED_DOWNSTREAMED,
  STATUS_AUTOMATED_QA_IN_PROGRESS,
  STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS,
];

const COMPLETED_STATUSES = [
  STATUS_MANUAL_QA_COMPLETE,
  STATUS_MANUAL_DOWNSTREAMED,
  STATUS_AUTOMATED_QA_COMPLETE,
  STATUS_AUTOMATED_RE_DOWNSTREAMED,
];

export const getPageByStatus = status => {
  if (INDEX_STATUSES.includes(status)) {
    return INDEX_STATUS;
  } else if (QA_STATUSES.includes(status)) {
    return QA_STATUS;
  } else if (COMPLETED_STATUSES.includes(status)) {
    return COMPLETE_STATUS;
  }
  return INDEX_STATUS;
};

export const getStatusTitle = status => {
  const pageNumber = getPageByStatus(status);
  return STATUS_LABEL_MAP[pageNumber];
};
