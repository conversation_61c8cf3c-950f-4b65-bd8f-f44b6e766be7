<?php

declare(strict_types=1);

namespace App\Infrastructure\Connection;

use App\Infrastructure\Connection\Driver\StatementProxy;
use WF\Curation\ExclusivityAssortment\Infrastructure\PDOProxy;

use function call_user_func;

/**
 * This is adapter for PDOProxy interface from wayfair/brand-workflows-assortment-library
 *
 * @method StatementProxy|false prepare(string $statement, array $driver_options = [])
 * @method bool                 beginTransaction()
 * @method bool                 commit()
 * @method bool                 rollBack()
 */
class ProductConnectionAdapter implements PDOProxy
{
    private ProductConnection $connection;

    public function __construct(ProductConnection $connection)
    {
        $this->connection = $connection;
    }

    /**
     * @param int $count
     * @param string $prefix
     * @param mixed $dataType
     * @return mixed
     */
    public function paramsForList($count, $prefix = '?', $dataType = null)
    {
        return $this->connection->paramsForList($count, $prefix, $dataType);
    }

    /**
     * @param string $name
     * @param array $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments)
    {
        return call_user_func([$this->connection, $name], ...$arguments);
    }
}
