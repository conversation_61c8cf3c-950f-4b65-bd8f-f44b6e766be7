<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface;

class Automatic_Class_Curation_Saver_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Curation_Decision_Service
     */
    private $curationDecisionService;

    /**
     * @var Automatic_Class_Curation_Configuration_Loader
     */
    private $configurationLoader;

    /**
     * @var Region_Service
     */
    private $regionService;

    /**
     * @var Automatic_Class_Curation_Saver
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->curationDecisionService = $this->prophesize(Curation_Decision_Service::class);
        $this->configurationLoader = $this->prophesize(Automatic_Class_Curation_Configuration_Loader::class);
        $this->regionService = $this->prophesize(Region_Service::class);

        $this->subject = new Automatic_Class_Curation_Saver(
            $this->curationDecisionService->reveal(),
            $this->configurationLoader->reveal(),
            $this->regionService->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_saves_eligible_skus()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = 12;
        $granularStyleId = 10;
        $employeeId = 10;

        $region = $this->prophesize(World_Region_Interface::class);
        $this->regionService->get_region($batchId)->willReturn($region->reveal());

        $config = $this->prophesize(Automatic_Class_Curation_Configuration::class);
        $configItem = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $configItem->get_style_id()->willReturn($styleId);
        $configItem->get_substyle_id()->willReturn($substyleId);
        $configItem->get_manufacturer_id()->willReturn($manufacturerId);
        $config->get_for_class_with_price_tier($classId, $priceTier)->willReturn($configItem);
        $this->configurationLoader->get_configuration($region)->willReturn($config->reveal());

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $curationItem->get_final_granular_style_id()->willReturn($granularStyleId);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(
            $batchId,
            [$sku],
            $priceTier,
            $styleId,
            $substyleId,
            $manufacturerId,
            $granularStyleId,
            Argument::any(),
            $employeeId,
            Curation_Decision_Source::automatic_by_class()
        )->shouldBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_already_saved()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = 'today';
        $sku = 'AA123456';
        $styleId = 10;
        $substyleId = 11;
        $manufacturerId = 12;
        $employeeId = 10;

        $region = $this->prophesize(World_Region_Interface::class);
        $this->regionService->get_region($batchId)->willReturn($region->reveal());

        $config = $this->prophesize(Automatic_Class_Curation_Configuration::class);
        $configItem = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $configItem->get_style_id()->willReturn($styleId);
        $configItem->get_substyle_id()->willReturn($substyleId);
        $configItem->get_manufacturer_id()->willReturn($manufacturerId);
        $config->get_for_class_with_price_tier($classId, $priceTier)->willReturn($configItem);
        $this->configurationLoader->get_configuration($region)->willReturn($config->reveal());

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_in_class_without_configuration()
    {
        $batchId = 1;
        $classId = 112;
        $priceTier = 3;
        $savedAt = null;
        $sku = 'AA123456';
        $employeeId = 10;

        $region = $this->prophesize(World_Region_Interface::class);
        $this->regionService->get_region($batchId)->willReturn($region->reveal());

        $config = $this->prophesize(Automatic_Class_Curation_Configuration::class);
        $config->get_for_class_with_price_tier($classId, $priceTier)->willReturn(null);
        $this->configurationLoader->get_configuration($region)->willReturn($config->reveal());

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_skips_sku_without_class()
    {
        $batchId = 1;
        $classId = null;
        $priceTier = null;
        $savedAt = null;
        $sku = 'AA123456';
        $employeeId = 10;

        $region = $this->prophesize(World_Region_Interface::class);
        $this->regionService->get_region($batchId)->willReturn($region->reveal());

        $config = $this->prophesize(Automatic_Class_Curation_Configuration::class);
        $config->get_for_class_with_price_tier(Argument::any())->shouldNotBeCalled();
        $this->configurationLoader->get_configuration($region)->willReturn($config->reveal());

        $section = $this->prophesize(Section::class);
        $curationItem = $this->prophesize(Curation_Item::class);
        $curationItem->get_sku()->willReturn($sku);
        $curationItem->get_class_id()->willReturn($classId);
        $curationItem->get_price_tier()->willReturn($priceTier);
        $curationItem->get_saved_at()->willReturn($savedAt);
        $section->get_curation_items()->willReturn([$curationItem->reveal()]);
        $sections = [
            $section->reveal()
        ];

        $this->curationDecisionService->save_curated(Argument::cetera())->shouldNotBeCalled();

        $this->subject->execute($batchId, $sections, $employeeId);
    }
}
