<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

class Investment_Sku_Metric_Collection {

  /**
   * @var array
   */
  private $skus = [];

  /**
   * Investment_Sku_Metric_Collection constructor.
   */
  public function __construct() {
  }

  /**
   * @param string                $sku    the sku
   * @param Investment_Sku_Metric $metric the Investment_Sku_Metric to be included on collection
   *
   * @return void
   */
  public function add(string $sku, Investment_Sku_Metric $metric) {
    $this->skus[$sku] = $metric;
  }

  /**
   * @param string $sku the SKU to search
   *
   * @return Investment_Sku_Metric|null
   */
  public function get(string $sku) : ?Investment_Sku_Metric {
    return $this->skus[$sku] ?? null;
  }
}
