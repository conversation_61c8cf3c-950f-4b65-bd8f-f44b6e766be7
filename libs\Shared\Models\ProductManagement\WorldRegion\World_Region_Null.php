<?php
declare(strict_types=1);
/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WorldRegion;

class World_Region_Null implements World_Region_Interface {

  const REGION_ID = 0;

  /**
   * @return int
   */
  public function getId() : int {
    return self::REGION_ID;
  }

  /**
   * @return string
   */
  public function getName() : string {
    return 'All';
  }

  /**
   * @return string
   */
  public function getShortName() : string {
    return 'All';
  }

  /**
   * @return array
   */
  public function getBrandCatalogIds() : array {
    return [];
  }

  /**
   * @return string
   */
  public function getCuratorListAra() : string {
    return '';
  }

  /**
   * @return bool
   */
  public function isAllowedToSwitchRegion() : bool {
    return true;
  }
}
