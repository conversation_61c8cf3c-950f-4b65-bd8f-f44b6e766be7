parameters:

services:
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    WF\Shared\Curation\Api\Curation\Curation_Api_Client:
        arguments:
            - '@WF\Shared\Logging\Logger'
            - '%wf_env%'

    WF\Shared\Curation\Api\Api_Curation: '@WF\Shared\Curation\Api\Curation\Curation_Api_Client'

    WF\Shared\Curation\Api\Product_Context_Collection\Product_Context_Collection_Api_Client:
        arguments:
            - '@WF\Shared\Logging\Logger'
            - '%wf_env%'

    WF\Shared\Curation\Api\Api_Product_Context_Collection:  '@WF\Shared\Curation\Api\Product_Context_Collection\Product_Context_Collection_Api_Client'

    WF\Shared\Curation\Api\Product_Context_Collection\ProductContextCollectionNextgenApiClient:
        arguments:
            - '@WF\Shared\Logging\Logger'
            - '%wf_env%'

    WF\Shared\Curation\Api\ApiProductContextCollectionNextgen:  '@WF\Shared\Curation\Api\Product_Context_Collection\ProductContextCollectionNextgenApiClient'

    WF\Shared\Curation\Api\Cost\Cost_Api_Client:
        arguments:
            - '%wf_env%'
            - '@WF\Shared\Logging\Logger'
            - '%cost-service-api-client-id%'
            - '%cost-service-api-client-secret%'

    WF\Shared\Curation\Api\Cost\CostApiService:
        arguments:
            - '@WF\Shared\Logging\Logger'
            - '%wf_env%'
            - '%cost-service-api-client-id%'
            - '%cost-service-api-client-secret%'