<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use PDO;
use PHPUnit\Exception;
use Psr\Log\LogLevel;
use WF\Shared\Helpers\SQL;
use Psr\Log\LoggerInterface;
use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;
use WF\Shared\Traits\Logging_Trait;

class Curation_Decision_Postgres_DAO {
    use Logging_Trait;

    private ProductConnection $pdo;

    private PostgresConnection $pdo_psql;

    /**
     * Curation_Decision_DAO constructor.
     *
     * @param ProductConnection $pdo    pdo to use
     * @param \Psr\Log\LoggerInterface      $logger Logger
     */
    public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql, ?LoggerInterface $logger = null) {
        $this->pdo          = $pdo;
        $this->pdo_psql     = $pdo_psql;
        $this->logger       = $logger;
    }

    /**
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision[] $items Items to save
     *
     * @return void
     * @throws \Exception
     */
    public function save(array $items): void {
        $tblName = sprintf('_verification_item_%s', $this->createRandomCode());
        $total_skus = array_map(function($o) { return $o->get_sku();}, $items);
        $data = $this->get_temp_table_data($items);
        try {
            $this->drop_pg_temp_table($tblName);
            $this->create_temp_pg_table($tblName);
            $this->insert_into_temp_pg_table($tblName, $data);
            $available_skus = $this->get_data_available_from_verification_item_table($total_skus);
            $remaining_skus = array_diff($total_skus, $available_skus);
            if (sizeof($available_skus) > 0) {
                $this->update_pg_verification_item_table($tblName, $available_skus);
            }
            if (sizeof($remaining_skus) > 0) {
                $this->insert_into_pg_verification_item_table($tblName, $remaining_skus);
            }
            $this->drop_pg_temp_table($tblName);
        } catch (\Exception $e){
            $this->log(LogLevel::INFO, $e);
        } finally {
            $this->drop_pg_temp_table($tblName);
        }

    }

    /**
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision[] $items Items to save
     * @description Used to store, manipulate and retrieve the temporary data in sql db
     * @return array
     */
    private function get_temp_table_data(array $items): array {
        $this->log_info('Saving Curation decision', ['total' => count($items)]);

        $column_map = [
            'ViBatchID'              => 'INT NOT NULL',
            'ViSKU'                  => 'NVARCHAR(8) NOT NULL',
            'ViExcludedReasonID'     => 'INT NULL',
            'ViFinalStyleID'         => 'INT NULL',
            'ViFinalSubStyleID'      => 'INT NULL',
            'ViFinalBrandMaID'       => 'INT NULL',
            'ViLockedEmID'           => 'INT NULL',
            'ViLockedDate'           => 'DATETIMEOFFSET(4) NULL',
            'ViPriceTierOverride'    => 'INT NULL',
            'ViSourceID'             => 'INT NOT NULL',
            'ViIsKitsco'             => 'BIT NOT NULL',
            'ViIsKitComponent'       => 'BIT NOT NULL DEFAULT(0)',
            'ViContextXnID'          => 'INT NULL',
            'ViQAStatusID'           => 'INT NOT NULL',
            'ViDecisionSourceID'     => 'INT NOT NULL',
            'ViFinalGranularStyleID' => 'SMALLINT NULL'
        ];

        $sql = SQLBulkHelper::getTempTableJsonSql(
          $column_map,
          'VerificationItems',
            'verification_items_json'
        );

        $sql .= '
          UPDATE vis
          SET vis.ViIsKitComponent = 1
          FROM #VerificationItems vis
          JOIN csn_product.dbo.vwExclusivityKitComposition ekc WITH(NOLOCK) ON ekc.ChildSKU = vis.ViSKU
          WHERE
            EXISTS(
              ' . $this->get_active_product_query('vis.ViSKU') . '
            )
            AND EXISTS(
              ' . $this->get_active_product_query('ekc.ParentSKU') . '
            )
            DECLARE @ViID TABLE(ID INT)
            SELECT * FROM #VerificationItems;
        ';

        $statement = $this->pdo->prepare($sql);

        $params = [];
        foreach ($items as $item) {
          $params[] = [
              'ViSKU'                  => $item->get_sku(),
              'ViExcludedReasonID'     => $item->get_excluded_reason_id(),
              'ViFinalStyleID'         => $item->get_style_id(),
              'ViFinalSubStyleID'      => $item->get_substyle_id(),
              'ViFinalBrandMaID'       => $item->get_brand_id(),
              'ViLockedEmID'           => $item->get_locked_em_id(),
              'ViLockedDate'           => $item->get_locked_date(),
              'ViPriceTierOverride'    => $item->get_price_tier(),
              'ViSourceID'             => Curation_Item_Model::SOURCE_COLLISION,
              'ViIsKitsco'             => $item->is_kitsco(),
              'ViIsKitComponent'       => 0,
              'ViContextXnID'          => $item->get_context_xn_id(),
              'ViBatchID'              => $item->get_batch_id(),
              'ViQAStatusID'           => $item->get_qa_status()->value(),
              'ViDecisionSourceID'     => $item->get_decision_source()->value(),
              'ViFinalGranularStyleID' => $item->get_granular_style_id()
          ];
        }

        $statement->bindValue(':verification_items_json', \json_encode($params), PDO::PARAM_STR);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }

        return $statement->fetchAll();
    }

    /**
     * @param string $sku_column sku column
     *
     * @return string
     */
    private function get_active_product_query(string $sku_column) : string {
        $sql = '
          SELECT TOP 1 1
          FROM csn_product.dbo.tblProduct p WITH(NOLOCK)
          LEFT JOIN csn_product.dbo.tblProductAdditional pra WITH(NOLOCK) ON pra.PrSKU = p.PrSKU
          WHERE
            p.PrSKU = ' . $sku_column . '
            AND (
              p.PrStatus IN (2, 4, 6, 13, 18)
              OR (p.PrStatus = 3 AND pra.PrAcsID in (1, 2, 4, 7, 12))
              OR (p.PrStatus = 7 AND pra.PrDrID in (22, 23))
              OR (
                  p.PrStatus = 1 AND EXISTS (
                      SELECT TOP 1 1
                      FROM csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                      INNER JOIN csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                      ON pt2.QuickformID = qfl.QflQfpID
                      INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                      ON qfl.QflID = qfb.QfbQflID
                      WHERE qfb.QfbPrSKU = p.PrSKU
                      AND pt2.ExecutionCompleteDate IS NULL
                  )
              )
            )
        ';

        return $sql;
    }

    /**
     * @param string $tblName
     * @description Used to create temporary table in postgres db
     * @return void
     */
    private function create_temp_pg_table(string $tblName) : void {
        $sql = sprintf('
            CREATE TABLE IF NOT EXISTS "temp%s" (
              "ViBatchID"               INTEGER NOT NULL,
              "ViSKU"                   VARCHAR(8) NOT NULL,
              "ViExcludedReasonID"      INTEGER NULL,
              "ViFinalStyleID"          INTEGER NULL,
              "ViFinalSubStyleID"       INTEGER NULL,
              "ViFinalBrandMaID"        INTEGER NULL,
              "ViLockedEmID"            INTEGER NULL,
              "ViLockedDate"            TIMESTAMP(4) WITH TIME ZONE NULL,
              "ViPriceTierOverride"     INTEGER NULL,
              "ViSourceID"              INTEGER NOT NULL,
              "ViIsKitsco"              BOOLEAN NOT NULL,
              "ViIsKitComponent"        BOOLEAN NOT NULL DEFAULT false,
              "ViContextXnID"           INTEGER NULL,
              "ViQAStatusID"            INTEGER NOT NULL,
              "ViDecisionSourceID"      INTEGER NOT NULL,
              "ViFinalGranularStyleID"  SMALLINT NULL
          )', $tblName);

        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }
    }

    /**
     * @param string $tblName
     * @param array $items
     * @description Used to insert data into temporary table in postgres db
     * @return void
     */
    private function insert_into_temp_pg_table(string $tblName, array $items) : void {
        $sql = sprintf(' 
            INSERT INTO "temp%s" 
                ("ViSKU", "ViExcludedReasonID", "ViFinalStyleID", "ViFinalSubStyleID", "ViFinalBrandMaID",      
                "ViLockedEmID", "ViLockedDate", "ViPriceTierOverride", "ViSourceID", "ViIsKitsco",            
                "ViIsKitComponent", "ViContextXnID", "ViBatchID", "ViQAStatusID", "ViDecisionSourceID",     
                "ViFinalGranularStyleID") VALUES 
          ', $tblName);
        $values_list = array();
        foreach ($items as $item) {
          $values_list[] = "(
              '". $item['ViSKU'] ."' , 
              NULLIF('". $item['ViExcludedReasonID'] ."', '')::integer,
              NULLIF('". $item['ViFinalStyleID'] ."', '')::integer,
              NULLIF('". $item['ViFinalSubStyleID'] ."', '')::integer,
              NULLIF('".  $item['ViFinalBrandMaID'] ."', '')::integer,
              NULLIF('".  $item['ViLockedEmID'] ."', '')::integer,
              NULLIF('". $item['ViLockedDate'] ."', '')::date,
              NULLIF('". $item['ViPriceTierOverride'] ."', '')::integer,
              ". $item['ViSourceID'] .",
              ". ($item['ViIsKitsco'] == 0 ? 'false' : 'true') .",
              ". ($item['ViIsKitComponent'] == 0 ? 'false' : 'true') .",
              NULLIF('". $item['ViContextXnID'] ."', '')::integer,
              ". $item['ViBatchID'] .",
              ". $item['ViQAStatusID'] .",
              ". $item['ViDecisionSourceID'] .",
              NULLIF('". $item['ViFinalGranularStyleID'] ."', '')::integer
            )";
        }
        $sql .= implode(',', $values_list);

        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }
    }

    /**
     * @param array $skus
     * @description Used to get skus available in tblVerificationItem table in postgres db
     * @return array
     */
    private function get_data_available_from_verification_item_table(array $skus) : array {
        $in  = $this->pdo_psql->paramsForLists($skus);
        $sql = "SELECT \"ViSKU\" FROM \"tblVerificationItem\" WHERE \"ViSKU\" IN ($in)";
        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }

        $data = $statement->fetchAll();
        return array_column($data, 'ViSKU');
    }

    /**
     * @param string $tblName
     * @param array $skus
     * @description Used to update in tblVerificationItem table in postgres db
     * @return array
     */
    private function update_pg_verification_item_table(string $tblName, array $skus) : void {
        $in  = $this->pdo_psql->paramsForLists($skus);
        $sql = sprintf("UPDATE \"tblVerificationItem\" AS db SET
              \"ViExcludedReasonID\"      = model.\"ViExcludedReasonID\",
              \"ViFinalStyleID\"          = model.\"ViFinalStyleID\",
              \"ViFinalSubStyleID\"       = model.\"ViFinalSubStyleID\",
              \"ViFinalBrandMaID\"        = model.\"ViFinalBrandMaID\",
              \"ViLockedEmID\"            = model.\"ViLockedEmID\",
              \"ViLockedDate\"            = model.\"ViLockedDate\",
              \"ViPriceTierOverride\"     = model.\"ViPriceTierOverride\",
              \"ViIsKitsco\"              = model.\"ViIsKitsco\",
              \"ViQAStatusID\"            = model.\"ViQAStatusID\",
              \"ViDecisionSourceID\"      = model.\"ViDecisionSourceID\",
              \"ViFinalGranularStyleID\"  = model.\"ViFinalGranularStyleID\"
          FROM \"temp%s\" AS model
          WHERE db.\"ViBatchID\" = model.\"ViBatchID\" 
          AND db.\"ViSKU\" = model.\"ViSKU\"
          AND model.\"ViSKU\" IN ($in)", $tblName);
        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }
    }

    /**
     * @param string $tblName
     * @param array $skus
     * @description Used to insert in tblVerificationItem table in postgres db
     * @return array
     */
    private function insert_into_pg_verification_item_table(string $tblName, array $skus) : void {
        $in  = $this->pdo_psql->paramsForLists($skus);
        $sql = sprintf("INSERT INTO \"tblVerificationItem\" (\"ViBatchID\", \"ViSKU\", \"ViExcludedReasonID\", \"ViFinalStyleID\", \"ViFinalSubStyleID\", 
                    \"ViFinalBrandMaID\", \"ViLockedEmID\", \"ViLockedDate\", \"ViPriceTierOverride\", \"ViSourceID\", 
                    \"ViIsKitsco\", \"ViIsKitComponent\", \"ViContextXnID\", \"ViQAStatusID\", \"ViDecisionSourceID\", 
                    \"ViFinalGranularStyleID\")
                SELECT \"ViBatchID\", \"ViSKU\", \"ViExcludedReasonID\", \"ViFinalStyleID\", \"ViFinalSubStyleID\", \"ViFinalBrandMaID\", 
                       \"ViLockedEmID\", \"ViLockedDate\", \"ViPriceTierOverride\", \"ViSourceID\", \"ViIsKitsco\", \"ViIsKitComponent\", 
                       \"ViContextXnID\", \"ViQAStatusID\", \"ViDecisionSourceID\", \"ViFinalGranularStyleID\" 
                FROM \"temp%s\" AS model WHERE model.\"ViSKU\" IN ($in)", $tblName);
        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }
    }

    /**
     * @param string $tblName
     * @description Used to drop the temporary table in postgres db
     * @return array
     */
    private function drop_pg_temp_table(string $tblName): void {
        $sql = sprintf('DROP TABLE IF EXISTS "temp%s"', $tblName);
        $statement = $this->pdo_psql->prepare($sql);

        if (!$statement->execute()) {
          $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
          $this->log_throwable_error($exception, $exception->getMessage());

          throw $exception;
        }
    }

    /**
     * @param array $skus     Array of SKUs
     * @param int   $batch_id Batch ID Value
     *
     * @return array
     */
    public function get_kits_with_active_children(array $skus, int $batch_id) : array {
      $this->log_info('Loading kits with active children combined method', ['skus' => $skus, 'batch_id' => $batch_id]);
      $data = [];
      $internal_data = $this->get_kits_with_active_children_internal($batch_id);
      $external_data = $this->get_kits_with_active_children_external($skus);

      foreach ($external_data as $row) {
        $index = array_search($row["ChildSKU"], array_column($internal_data, "ViSKU"));
        if ( gettype($index) != 'boolean' ) {
          $int_data = $internal_data[$index];
          unset($int_data["ViSKU"]);
          $data[] = array_merge($row, $int_data);
        } else {
          $int_data["IsKitsco"] = null;
          $data[] = array_merge($row, $int_data);
        }
      }
      $group = array();

      foreach ( $data as $value ) {
        $temp = $value;
        unset($temp["ParentSKU"]);
        $group[$value['ParentSKU']][] = $temp;
      }
      return $group;
    }

    private function get_kits_with_active_children_internal(int $batch_id) : array {
      $this->log_info('Loading kits with active children internal method', ['batch_id' => $batch_id]);

      $sql = 'SELECT "verificationItem"."ViSKU", "verificationItem"."ViIsKitsco" AS "IsKitsco"
             FROM "tblVerificationItem" "verificationItem" 
             WHERE "verificationItem"."ViBatchID" = :batch_id';

      $statement = $this->pdo_psql->prepare($sql);
      $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

      if (!$statement->execute()) {
        $exception = ExecutionException::forStatement($statement, 'Cannot load KITs with active children');
        $this->log_throwable_error($exception, $exception->getMessage());

        throw $exception;
      }

      return $statement->fetchAll();
    }

    private function get_kits_with_active_children_external(array $skus) : array {
      $this->log_info('Loading kits with active children external method', ['skus' => $skus]);

      $sql = 'SELECT        
                activeKitComposition.ParentSKU,
                activeKitComposition.ChildSKU
              FROM csn_product.dbo.vwExclusivityKitCompositionActive activeKitComposition 
              WHERE activeKitComposition.ParentSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));

      $statement = $this->pdo->prepare($sql);
      $statement->bindValuesList(':skus', $skus, SQL::nvarchar(8));

      if (!$statement->execute()) {
        $exception = ExecutionException::forStatement($statement, 'Cannot load KITs with active children');
        $this->log_throwable_error($exception, $exception->getMessage());

        throw $exception;
      }
      return $statement->fetchAll();
    }

    public function getBatchinfo(array $skus, int $batch_id){
        $this->log_info('fetching batch info');
        $in  = $this->pdo_psql->paramsForLists($skus);
        $sql = "SELECT * FROM \"tblVerificationItem\" WHERE \"ViSKU\" IN ($in) AND \"ViBatchID\"=$batch_id";
        $statement = $this->pdo_psql->prepare($sql);
        $statement->execute();
        $statement->setFetchMode(PDO::FETCH_OBJ);
        $row = $statement->fetch();
        $this->log_info(print_r($row, true));
        $this->log_info(print_r($row->ViFinalStyleID, true));
        return $row;
    }


    public function getBatchinfoList(array $skus, int $batch_id){
        $this->log_info('fetching batch info');
        $in  = $this->pdo_psql->paramsForLists($skus);
        $sql = "SELECT * FROM \"tblVerificationItem\" WHERE \"ViSKU\" IN ($in) AND \"ViBatchID\" = $batch_id";
        $statement = $this->pdo_psql->prepare($sql);
        $statement->execute();
        $data = $statement->fetchAll();
        $this->log_info(print_r($data, true));
        return $data;
    }

    public function getStleByeId(int $style_id){

        $statement = $this->pdo_psql->prepare("SELECT * FROM \"tblVerificationStyle\" WHERE \"VsID\"=:style_id");
        $statement->execute([':style_id' => $style_id]);
        $statement->setFetchMode(PDO::FETCH_OBJ);
        $user = $statement->fetch();
        $this->log_info('fetching style info',['',$user]);
        return $user;
    }

    public function getSubStleByID(int $style_id){
        $statement = $this->pdo_psql->prepare("SELECT * FROM  \"tblVerificationSubStyle\" WHERE \"VssID\"=:style_id");
        $statement->execute([':style_id' => $style_id]);
        $statement->setFetchMode(PDO::FETCH_OBJ);
        $user = $statement->fetch();
        $this->log_info('fetching sub style info',['',$user]);
        return $user;
    }

  private function createRandomCode(): string
  {
    $chars = "abcdefghijkmnopqrstuvwxyz023456789";
    srand((double) microtime() * 1000000);

    $i = 0; $pass = '';

    while ($i <= 10)
    {
      $num = rand() % 33;
      $tmp = substr($chars, $num, 1);
      $pass = $pass . $tmp;
      $i++;
    }
    return $pass;
  }
}
