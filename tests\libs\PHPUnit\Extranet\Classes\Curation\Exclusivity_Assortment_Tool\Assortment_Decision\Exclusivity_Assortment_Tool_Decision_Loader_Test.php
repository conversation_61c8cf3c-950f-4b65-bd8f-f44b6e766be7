<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision;

use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Prophecy\Prophecy\ObjectProphecy;
use WF\Curation\ExclusivityAssortment\Domain\Enum\ExclusivityAssortmentCurationStatus;
use WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader;
use WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader_Storage;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_No_Decision_DTO;

class Exclusivity_Assortment_Tool_Decision_Loader_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var Exclusivity_Assortment_Tool_Decision_Loader_Storage|ObjectProphecy
     */
    private $storage;

    /**
     * @var Exclusivity_Assortment_Tool_Decision_Loader
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->storage = $this->prophesize(Exclusivity_Assortment_Tool_Decision_Loader_Storage::class);

        $this->subject = new Exclusivity_Assortment_Tool_Decision_Loader($this->storage->reveal());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_is_decision_dto_loader()
    {
        $this->assertInstanceOf(Assortment_Decision_DTO_Loader::class, $this->subject);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_wraps_decisions_in_collection()
    {
        $this->storage->get_decisions_for_skus(Argument::cetera())->willReturn([]);

        $decisions = $this->subject->get_decisions_for_skus(['SKU1', 'SKU2']);

        $this->assertInstanceOf(Assortment_Decision_DTO_Collection::class, $decisions);
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_requests_qa_approved_decisions()
    {
        $this->storage->get_decisions_for_skus(Argument::cetera())->willReturn([]);

        $this->subject->get_decisions_for_skus(['SKU1', 'SKU2']);

        $this->storage->get_decisions_for_skus(['SKU1', 'SKU2'], ExclusivityAssortmentCurationStatus::imported())->shouldHaveBeenCalled();
    }

    /**
     * @test
     * @depends it_wraps_decisions_in_collection
     *
     * @return void
     */
    public function it_returns_result_containing_all_skus_from_storage()
    {
        $decision_dto_first = $this->prophesize(Assortment_Decision_DTO::class);
        $decision_dto_first->get_sku()->willReturn('SKU1');

        $decision_dto_second = $this->prophesize(Assortment_Decision_DTO::class);
        $decision_dto_second->get_sku()->willReturn('SKU2');

        $this->storage->get_decisions_for_skus(Argument::cetera())->willReturn([$decision_dto_first, $decision_dto_second]);

        $decisions = $this->subject->get_decisions_for_skus(['SKU1', 'SKU2']);

        $this->assertEquals($decision_dto_first->reveal(), $decisions->get_for_sku('SKU1'));
        $this->assertEquals($decision_dto_second->reveal(), $decisions->get_for_sku('SKU2'));
        $this->assertInstanceOf(Assortment_No_Decision_DTO::class, $decisions->get_for_sku('SKU3'));
    }
}
