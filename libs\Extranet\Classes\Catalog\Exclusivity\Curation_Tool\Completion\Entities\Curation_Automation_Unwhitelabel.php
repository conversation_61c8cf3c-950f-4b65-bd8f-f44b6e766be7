<?php

declare(strict_types = 1);

/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities;

class Curation_Automation_Unwhitelabel {
  /**
   * @var int
   */
  private int $batch_id;

  /**
   * @var string[]
   */
  private array $skus = [];

  /**
   * @var bool
   */
  private bool $unwhitelabel;

   /**
    * Curation_Automation_Unwhitelabel constructor
    *
    * @param int      $batch_id     BatchID
    * @param string[] $skus         SKUS
    * @param bool     $unwhitelabel Unwhitelabel
    */
  public function __construct(
      int $batch_id,
      array $skus,
      bool $unwhitelabel = true
  ) {
    $this->batch_id = $batch_id;
    $this->skus = $skus;
    $this->unwhitelabel = $unwhitelabel;
  }

  /**
   * @return int
   */
  public function get_batch_id(): int {
    return $this->batch_id;
  }

  /**
   * @return string[]
   */
  public function get_skus(): array {
    return $this->skus;
  }

  /**
   * @return bool
   */
  public function get_unwhitelabel(): bool {
    return $this->unwhitelabel;
  }
}
