<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage;
use WF\Shared\Helpers\SQL;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;

class Curation_QA_DAO implements Curation_QA_Decision_Storage {
  use Logging_Trait;

  private ProductConnection $pdo;

  /**
   * @param ProductConnection                 $pdo    the PDO
   * @param \Psr\Log\LoggerInterface|null $logger Logger
   */
  public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null) {
    $this->pdo    = $pdo;
    $this->logger = $logger;
  }

  /**
   * @param array $skus     skus to check
   * @param int   $batch_id batch id
   *
   * @return bool true if all skus have either MAID or Excluded reason are not empty
   */
  public function check_if_ready_to_save(array $skus, int $batch_id) : bool {
    $this->info(
        sprintf('Checking readiness to save skus for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id, 'skus' => $skus]
    );

    $sql = '
      SELECT TOP 1 1 as count
      FROM csn_product.dbo.tblVerificationItem WITH ( NOLOCK )
      WHERE  
        ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '
        AND ViFinalBrandMaID IS NULL 
        AND ViExcludedReasonID = 0
        AND ViBatchID = :batch_id
     ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to check for not ready to save skus');
      $this->log_throwable_error(
          $exception,
          sprintf('Failed to check for not ready to save skus for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id, 'skus' => implode(",", $skus), 'sql' => $sql]
      );
      throw  $exception;
    }

    $row = $statement->fetch();

    return ($row === false);
  }


  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision(Curation_QA_Decision $decision): void {
    $this->info(
        sprintf('Saving curation decision for batchId "%s"', $decision->get_batch_id()),
        [
            'batch_id'    => $decision->get_batch_id(),
            'status'      => $decision->get_qa_status()->value(),
            'employee_id' => $decision->get_employee_id(),
            'reason'      => $decision->get_reason()
        ]
    );

    $skus = $decision->get_skus();

    $sql = '
        DECLARE @statusValue INT = :status_id
        DECLARE @employeeId INT = :employee_id
        DECLARE @UpdatedSKUs TABLE (ViID INT NOT NULL, ViSKU NVARCHAR(8) NOT NULL)

        UPDATE csn_product.dbo.tblVerificationItem
        SET 
          ViLockedEmID = CASE WHEN ViLockedDate IS NULL THEN @employeeId ELSE ViLockedEmID END,
          ViLockedDate = CASE WHEN ViLockedDate IS NULL THEN GETDATE() ELSE ViLockedDate END,
          ViQAStatusID = @statusValue
        OUTPUT inserted.ViID, inserted.ViSKU INTO @UpdatedSKUs
        WHERE
          ViBatchID = :batch_id
          AND ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '

        INSERT INTO csn_product.dbo.tblVerificationItemQAStatusHistory
        (VerificationItemID, SKU, StatusID, EmployeeID, Date, Message)
        SELECT ViID, ViSKU, @statusValue, @employeeId, GETDATE(), :message
        FROM @UpdatedSKUs
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('status_id', $decision->get_qa_status()->value(), PDO::PARAM_INT);
    $statement->bindValue('batch_id', $decision->get_batch_id(), PDO::PARAM_INT);
    $statement->bindValue('message', $decision->get_reason(), PDO::PARAM_STR);
    $statement->bindValue('employee_id', $decision->get_employee_id(), PDO::PARAM_INT);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision');
      $this->log_throwable_error(
          $exception,
          sprintf('Failed to save curation decision for batchId "%s"', $decision->get_batch_id()),
          [
              'skus'        => implode(",", $skus),
              'batch_id'    => $decision->get_batch_id(),
              'status'      => $decision->get_qa_status()->value(),
              'employee_id' => $decision->get_employee_id(),
              'reason'      => $decision->get_reason(),
              'sql'         => $sql
          ]
      );
      throw $exception;
    }
  }

    /**
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
     *
     * @return void
     */
    public function save_decision_and_styles(Curation_QA_Decision $decision): void {
        $this->info(
            sprintf('Saving curation decision and styles for batchId "%s"', $decision->get_batch_id()),
            [
                'batch_id'                 => $decision->get_batch_id(),
                'status'                   => $decision->get_qa_status()->value(),
                'employee_id'              => $decision->get_employee_id(),
                'reason'                   => $decision->get_reason(),
                'price_tier_override'      => $decision->get_price_tier_override(),
                'final_style_id'           => $decision->get_final_style_id(),
                'final_substyle_id'        => $decision->get_final_substyle_id(),
                'final_brand_id'           => $decision->get_final_brand_id()
            ]
        );

        $skus = $decision->get_skus();

        $sql = '
        DECLARE @statusValue INT = :status_id
        DECLARE @PriceTierOverride INT = :price_tier_override
        DECLARE @employeeId INT = :employee_id
        DECLARE @FinalStyleId INT = :final_style_id
        DECLARE @FinalSubStyleId INT = :final_substyle_id
        DECLARE @FinalBrandId INT = :final_brand_id
        DECLARE @UpdatedSKUs TABLE (ViID INT NOT NULL, ViSKU NVARCHAR(8) NOT NULL)

        UPDATE csn_product.dbo.tblVerificationItem
        SET
          ViLockedEmID = CASE WHEN ViLockedDate IS NULL THEN @employeeId ELSE ViLockedEmID END,
          ViLockedDate = CASE WHEN ViLockedDate IS NULL THEN GETDATE() ELSE ViLockedDate END,
          ViQAStatusID = @statusValue,
          ViPriceTierOverride = @PriceTierOverride,
          ViFinalStyleID = @FinalStyleId,
          ViFinalSubStyleID = @FinalSubStyleId,
          ViFinalBrandMaID = @FinalBrandId
        OUTPUT inserted.ViID, inserted.ViSKU INTO @UpdatedSKUs
        WHERE
          ViBatchID = :batch_id
          AND ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '

        INSERT INTO csn_product.dbo.tblVerificationItemQAStatusHistory
        (
          VerificationItemID,
          SKU,
          StatusID,
          EmployeeID,
          Date
        )
        SELECT
          ViID,
          ViSKU,
          @statusValue,
          @employeeId,
          GETDATE()
        FROM @UpdatedSKUs
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('status_id', $decision->get_qa_status()->value(), PDO::PARAM_INT);
    $statement->bindValue('batch_id', $decision->get_batch_id(), PDO::PARAM_INT);
    $statement->bindValue('employee_id', $decision->get_employee_id(), PDO::PARAM_INT);
    $statement->bindValue('price_tier_override', $decision->get_price_tier_override(), PDO::PARAM_INT);
    $statement->bindValue('final_style_id', $decision->get_final_style_id(), PDO::PARAM_INT);
    $statement->bindValue('final_substyle_id', $decision->get_final_substyle_id(), PDO::PARAM_INT);
    $statement->bindValue('final_brand_id', $decision->get_final_brand_id(), PDO::PARAM_INT);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
            $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision and styles');

      $this->log_throwable_error(
          $exception,
          sprintf('Failed to save curation decision and styles for batchId "%s"', $decision->get_batch_id()),
          [
              'skus'                => implode(",", $skus),
              'batch_id'            => $decision->get_batch_id(),
              'status'              => $decision->get_qa_status()->value(),
              'employee_id'         => $decision->get_employee_id(),
              'reason'              => $decision->get_reason(),
              'price_tier_override' => $decision->get_price_tier_override(),
              'final_style_id'      => $decision->get_final_style_id(),
              'final_substyle_id'   => $decision->get_final_substyle_id(),
              'final_brand_id'      => $decision->get_final_brand_id(),
              'sql'                 => $sql
          ]
      );

      throw $exception;
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision_and_exclude_from_wl(Curation_QA_Decision $decision) : void {
    $this->info(
        sprintf('Saving curation decision for batchId "%s"', $decision->get_batch_id()),
        [
            'batch_id'              => $decision->get_batch_id(),
            'status'                => $decision->get_qa_status()->value(),
            'employee_id'           => $decision->get_employee_id(),
            'exclusion_reason'      => $decision->get_exclusion_reason()
        ]
    );

    $skus = $decision->get_skus();

    $sql = '
        DECLARE @statusValue INT = :status_id
        DECLARE @UpdatedSKUs TABLE (ViID INT NOT NULL, ViSKU NVARCHAR(8) NOT NULL)

        UPDATE csn_product.dbo.tblVerificationItem
        SET
          ViQAStatusID = @statusValue,
          ViExcludedReasonID = :exclusion_reason
        OUTPUT inserted.ViID, inserted.ViSKU INTO @UpdatedSKUs
        WHERE
          ViBatchID = :batch_id
          AND ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '

        INSERT INTO csn_product.dbo.tblVerificationItemQAStatusHistory
        (VerificationItemID, SKU, StatusID, EmployeeID, Date)
        SELECT ViID, ViSKU, @statusValue, :employee_id, GETDATE()
        FROM @UpdatedSKUs
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('status_id', $decision->get_qa_status()->value(), PDO::PARAM_INT);
    $statement->bindValue('batch_id', $decision->get_batch_id(), PDO::PARAM_INT);
    $statement->bindValue('exclusion_reason', $decision->get_exclusion_reason(), PDO::PARAM_INT);
    $statement->bindValue('employee_id', $decision->get_employee_id(), PDO::PARAM_INT);
    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision');

      $this->error(
          sprintf('Failed to save curation decision for batchId "%s"', $decision->get_batch_id()),
          [
              'skus'                  => implode(",", $skus),
              'batch_id'              => $decision->get_batch_id(),
              'status'                => $decision->get_qa_status()->value(),
              'employee_id'           => $decision->get_employee_id(),
              'exclusion_reason'      => $decision->get_exclusion_reason(),
              'sql'                   => $sql,
              'exception'             => $exception,
          ]
      );
      throw $exception;
    }
  }
}
