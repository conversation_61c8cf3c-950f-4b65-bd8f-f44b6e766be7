<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Curation\ExclusivityAssortment\Infrastructure\Helper\SQLBulkHelper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Brand_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Granular_Style_Data;
use WF\Shared\Helpers\SQL;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Curation\Api\Cost\CostApiService;

class Curation_Tool_DAO {
  use Logging_Trait;
  private const REJECTION_REASON_ACTIVE = true;
  private const DEFAULT_MARGIN = 0.49;
  private const ROWS_COUNT = 5;
  private ProductConnection $pdo;

  private bool $useStrictMode;
    private CostApiService $costApiClient;
  /**
   * Curation_Tool_DAO constructor.
   *
   * @param ProductConnection $pdo           PDO
   * @param LoggerInterface   $logger        logger
   * @param bool              $useStrictMode In strict mode we exclude SKUs which have no assigned class via JOIN
   */
  public function __construct(ProductConnection $pdo, LoggerInterface $logger, bool $useStrictMode = false, CostApiService $costApiClient) {
    $this->pdo           = $pdo;
    $this->logger        = $logger;
    $this->useStrictMode = $useStrictMode;
    $this->costApiClient = $costApiClient;
  }

  /**
   * @param int   $batch_id Batch ID
   * @param array $skus     SKUs
   *
   * @return array
   */
  public function get_sku_data(int $batch_id, array $skus) : array {
    $this->info(
        'Loading information about SKUs',
        ['skus' => $skus, 'batch_id' => $batch_id]
    );

    $sql = 'SELECT
                 pr.PrSKU AS sku,
                 pr.prname AS product_name,
                 cl.ClID AS class_id,
                 cl.ClName AS class_name,
                 maOriginal.maname AS manufacturer_name,
                 maOriginal.maBrwID AS manufacturer_brw_id,
                 CASE WHEN maOriginal.MaEBHeaderBrand = 1 AND maOriginal.MaBrwID = 1 THEN :eb_flagship WHEN maOriginal.MaBrwID = 1 THEN :eb_tail ELSE :non_eb END AS brand_type,
                 vi.ViExcludedReasonID AS excluded_reason_id,
                 vi.ViPriceTierOverride AS price_tier,
                 vi.viFinalStyleID AS final_style_id,
                 vi.viFinalSubStyleID AS final_sub_style_id,
                 vi.ViFinalBrandMaID AS final_brand_id,
                 vi.ViFinalGranularStyleID AS final_granular_style_id,
                 vi.viLockedDate AS locked_date,
                 vi.ViIsKitsco AS is_kitsco,
                 bc.BclgID AS brand_catalog_id,
                 bc.BclgName AS brand_catalog_name,
                 em.EmFirstName + \' \' + em.EmLastName AS locked_employee_name,
                 vi.ViDecisionSourceID AS decision_source_id,
                 2 AS price_options_count,
                 clone_log.LastCloneDate AS last_clone_date,
                 vi.ViQaStatusID AS qa_status_id, 
                 history.EmFullName as last_qa_by,
                 history.Date as last_qa_at,
                 history.Message as last_qa_note,
                 holdOutManufacturer.NoWhiteLabel AS is_hold_out_manufacturer
            FROM tblProduct pr WITH (NOLOCK) 
            JOIN tblBrandCatalog bc WITH (NOLOCK) ON bc.BclgID = pr.PrBclgID
            LEFT JOIN tblVerificationItem vi WITH (NOLOCK) ON PrSKU = ViSKU AND vi.ViBatchID = :batch_id
            ' . $this->get_strict_class_join() . ' JOIN tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = pr.PrSKU AND PcMasterClass = 1
            LEFT JOIN tblplProductWhiteLabelExcludedReason pwler WITH (NOLOCK) ON PwlerID = ViExcludedReasonID
            ' . $this->get_strict_class_join() . ' JOIN tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
            LEFT JOIN tblManufacturer maOriginal WITH (NOLOCK) ON maOriginal.MaID = pr.PrMaID
            LEFT JOIN tblEmployee em WITH (NOLOCK) ON em.EmID = ViLockedEmID
            OUTER APPLY (
                   SELECT MAX(SclgCloneDate) AS LastCloneDate
                   FROM csn_product.dbo.tblSKUCloneLog WITH(NOLOCK)
                   WHERE SclgClonedPrSku = pr.PrSKU AND SclgSuccessfullyCloned = 1
            ) clone_log
            OUTER APPLY (
                   SELECT TOP (1)
                      (emp.EmFirstName + \' \' + emp.EmLastName) AS EmFullName,
                      qh.Date,
                      qh.Message
                   FROM csn_product.dbo.tblVerificationItemQAStatusHistory AS qh WITH (NOLOCK)
                   LEFT JOIN csn_hr.dbo.tblEmployee emp WITH (NOLOCK) ON qh.EmployeeID = emp.EmID
                   WHERE qh.SKU = pr.PrSKU
                   ORDER BY qh.Date DESC
            ) history
            LEFT JOIN csn_product.dbo.tblManufacturerWhiteLabel holdOutManufacturer WITH (NOLOCK)
                ON pr.PrMaID = holdOutManufacturer.MaID
            WHERE pr.PrSKU IN (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')
            ORDER BY pr.PrSKU';

    /**
     * @TODO
     *
     * for performance consideration, we skip load the price options count
     *
     * OUTER APPLY (
     * SELECT 2 AS price_options_count /* -- temporary disable for performance -- COUNT(DISTINCT ProductOptionID) AS price_options_count
     * FROM csn_product.dbo.tblOptionCombination o WITH(NOLOCK)
     * JOIN csn_product.dbo.tblJoinOptionCombinationOption j WITH(NOLOCK) ON j.OptionCombinationID = o.ID
     * WHERE o.PrSKU = PrSKU
     * ) poc
     */

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValue(':eb_flagship', Curation_Item_Brand_Type::eb_flagship()->value(), PDO::PARAM_INT);
    $statement->bindValue(':eb_tail', Curation_Item_Brand_Type::eb_tail()->value(), PDO::PARAM_INT);
    $statement->bindValue(':non_eb', Curation_Item_Brand_Type::non_eb()->value(), PDO::PARAM_INT);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load sku data');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load data for SKUs "%s"', implode(",", $skus)),
          ['skus' => $skus, 'batch_id' => $batch_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array
   */
  public function get_skus_suppliers(array $skus) : array {
    $this->info(
        'Loading Suppliers for SKUs',
        ['skus' => $skus]
    );

    $sql = 'SELECT DISTINCT o.PrSKU AS sku, sp.SupplierID AS id, s.SuName AS name, s.SuClgID AS clg_id
            FROM csn_product.dbo.tblOptionCombination o WITH (NOLOCK)
            JOIN csn_product.dbo.tblSupplierPart sp WITH(NOLOCK) ON sp.ManufacturerPartID = o.ManufacturerPartID
            JOIN csn_product.dbo.tblSupplier s WITH(NOLOCK) ON s.SuID = sp.SupplierID

            WHERE o.PrSKU IN  (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load skus suppliers');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load suppliers for SKUs "%s"', implode(",", $skus)),
          ['skus' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * Load predefined exclusion reasons from pick list
   *
   * @return array excluded reasons
   */
  public function get_exclusion_reasons() : array {
    $this->info("Loading exclusion reasons from pick list");

    $sql = '
      SELECT PwlerID                AS id,
             PwlerReasonName        AS name
      FROM tblplProductWhiteLabelExcludedReason WITH (NOLOCK)
      WHERE PwlerIsAutomatedReason = 0
      ORDER BY PwlerReasonName ASC
    ';

    $statement = $this->pdo->prepare($sql);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load exclusion reasons');
      $this->log_throwable_error($exception, $exception->getMessage());
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * Get styles
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_styles(int $region_id) : array {
    $this->info(
        sprintf('Loading Primary Styles for Region "%s"', $region_id),
        ['region_id' => $region_id]
    );


    $sql = '
      SELECT 
         DISTINCT VsID   AS id,
         VsName          AS name
      FROM        tblVerificationStyle style WITH (NOLOCK)
      INNER JOIN  tbljoinStylesPriceTierManufacturer sptm WITH (NOLOCK)
      ON  SptmStyleID = style.VsID
      WHERE sptm.SptmBclgID = :region_id
      ORDER BY VsName ASC
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load Primary Styles');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load Primary Styles for Region "%s"', $region_id),
          ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * Get substyles
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_substyles(int $region_id) : array {
    $this->info(
        sprintf('Loading substyles for Region "%s"', $region_id),
        ['region_id' => $region_id]
    );

    $sql = '
      SELECT      DISTINCT VsID   AS style_id,
                  VssID           AS substyle_id,
                  VssName         AS name
      FROM        tblVerificationStyle style WITH (NOLOCK)
      INNER JOIN  tbljoinStylesPriceTierManufacturer sptm WITH (NOLOCK)
              ON  SptmStyleID = style.VsID
      INNER JOIN  tblVerificationSubStyle sub WITH (NOLOCK)
              ON  SptmSubStyleID = sub.VssID
      WHERE sptm.SptmBclgID = :region_id
      ORDER BY VssName ASC
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load Substyles');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load Substyles for Region "%s"', $region_id),
          ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * Get Granular Styles
   *
   * @param int $region_id Region ID
   *
   * @return Granular_Style_Data[]
   */
  public function get_granular_styles(int $region_id) : array {
    $this->info(
        sprintf('Loading Granular Styles for Region "%s"', $region_id),
        ['region_id' => $region_id]
    );

    $sql = '
      SELECT      VgsID   AS id,
                  VssID   AS substyle_id,
                  VgsName AS name
      FROM        csn_product.dbo.tblplVerificationGranularStyle granular_style WITH (NOLOCK)
      JOIN  csn_product.dbo.tbljoinSubStyleGranularStyle ssgs WITH (NOLOCK)
              ON  ssgs.SsgsGranularStyleID = granular_style.VgsID
      JOIN  csn_product.dbo.tblVerificationSubStyle sub_style WITH (NOLOCK)
              ON  ssgs.SsgsSubStyleID = sub_style.VssID
      WHERE granular_style.VgsBclgID = :region_id
      ORDER BY VgsName ASC
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load Granular Styles');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load Granular Styles for Region "%s"', $region_id),
          ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Granular_Style_Data::class);
  }

  /**
   * Get Manufacturers for Region
   *
   * @param int $region_id Region ID
   *
   * @return array
   */
  public function get_manufacturers(int $region_id) : array {
    $this->info(
        sprintf('Loading manufacturers for Region "%s"', $region_id),
        ['region_id' => $region_id]
    );

    $sql = '
        SELECT 
            style.VsID         AS style_id,
            sub.VssID          AS substyle_id,
            sptm.SptmPriceTier AS price_tier,
            sptm.SptmMaID      AS manufacturer_id,
            ma.MaName          AS name,
            bt.Name            AS brand_class
        FROM 
            tblVerificationStyle style WITH (NOLOCK)
        INNER JOIN 
            tbljoinStylesPriceTierManufacturer sptm WITH (NOLOCK) ON sptm.SptmStyleID = style.VsID
        INNER JOIN 
            tblVerificationSubStyle sub WITH (NOLOCK) ON sptm.SptmSubStyleID = sub.VssID
        INNER JOIN 
            tblManufacturer ma WITH (NOLOCK) ON ma.MaID = sptm.SptmMaID
        INNER JOIN 
            tblManufacturerBrand brand WITH (NOLOCK) ON brand.MaID = ma.MaID
        INNER JOIN 
            tblPlBrandType bt WITH (NOLOCK) ON bt.ID = brand.BrandTypeID
        WHERE 
            sptm.SptmBclgID = :region_id
        ORDER BY 
            brand.BrandTypeID ASC, ma.MaName ASC
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load manufacturers');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load manufacturers for Region "%s"', $region_id),
          ['region_id' => $region_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }


  /**
   * Loading Brand Catalog for batch
   *
   * @param int $batch_id Batch ID
   *
   * @return int|null
   */
  public function get_batch_brand_catalog(int $batch_id) {
    $this->info(
        sprintf('Loading Brand Catalog for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );

    $sql = 'SELECT BrandCatalogID FROM tblCurationBatch WITH (NOLOCK) WHERE id = :batch_id';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load batch brand catalog');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load batch brand catalog for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    return $statement->fetchColumn();
  }

  /**
   * Load Batch Items
   *
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_batch_items(int $batch_id) : array {
    $this->info(
        sprintf('Loading batch items for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );

    $sql = 'SELECT vi.visku AS sku,
                 vi.ViContextXnID AS context_xn_id, vi.ViQaStatusID AS qa_status
            FROM  tblVerificationItem vi WITH (NOLOCK)
            WHERE ViBatchID = :batch_id
            ORDER BY ViSKU';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load batch items');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot load batch items for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $skus skus
   *
   * @return array
   */
  public function get_parents(array $skus) : array {
    $this->info(
        sprintf('Loading products kit parents for SKUs "%s"', implode(",", $skus)),
        ['skus' => $skus]
    );

    $sql = '
            SELECT DISTINCT parentsku, childsku
            FROM csn_product.dbo.vwExclusivityKitCompositionActive WITH (NOLOCK)
            WHERE childsku IN (' . $this->pdo->paramsForList(count($skus), 'child_sku', SQL::nvarchar(8)) . ')';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValuesList(':child_sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get products kit parents');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get products kit parents for SKUs "%s"', implode(",", $skus)),
          ['skus' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $skus skus
   *
   * @return array
   */
  public function get_statuses(array $skus) : array {
    $this->info(
        'Loading Products status for SKUs',
        ['skus' => $skus]
    );

    $sql = 'SELECT 
              pr.PrSKU AS sku, 
              pr.PrStatus AS status 
            FROM csn_product.dbo.tblProduct pr WITH (NOLOCK) WHERE PrSKU IN (' .
        $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get products status');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get products status for SKUs "%s"', implode(",", $skus)),
          ['skus' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param int   $batch_id Batch ID
   * @param array $skus     skus
   *
   * @return array
   */
  public function get_kitsco(int $batch_id, array $skus) : array {
    $this->info(
        sprintf('Loading kitsco for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id, 'skus' => $skus]
    );

    $sql = 'SELECT 
              ViSKU AS sku
            FROM csn_product.dbo.tblVerificationItem WITH (NOLOCK) 
            WHERE ViBatchID = :batch_id  AND ViIsKitsco = 1 AND ViSKU IN ' .
        $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8));

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get kitsco');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get kitsco for BatchId "%s"', $batch_id),
          ['batch_id' => $batch_id, 'skus' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param int[] $collection_ids Collection IDs
   *
   * @return array
   */
  public function get_collections(array $collection_ids) : array {
    $this->info(
        'Loading collection names for Collections',
        ['collection_ids' => $collection_ids]
    );

    $sql = 'SELECT 
              XnID AS id, 
              XnName AS name
            FROM csn_product.dbo.tblplCollection WITH (NOLOCK) 
            WHERE XnID IN ' . $this->pdo->paramsForList(count($collection_ids), 'id', SQL::int);

    $statement = $this->pdo->prepare($sql);

    $statement->bindValuesList(':id', array_map('intval', $collection_ids), SQL::int);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get collection names');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get collection names for CollectionIds "%s"', implode(",", $collection_ids)),
          ['collection_ids' => implode(",", $collection_ids)]
      );
      throw $exception;
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project|null
   */
  public function get_rebrand_project_for_batch(int $batch_id) {
    $this->info(
        sprintf('Loading rebrand project for batch "%s"', $batch_id),
        ['batch_id' => $batch_id]
    );

    // select the associated rebrand project that has most skus in the current batch
    $sql = '
        SELECT TOP 1 rebrandProject.ID AS id, rebrandProject.Name AS name, COUNT(DISTINCT verificationItem.ViID) AS skus_count
        FROM tblVerificationItem verificationItem WITH (NOLOCK)
        JOIN tblCurationRequestSku requestSku WITH (NOLOCK) ON requestSku.VerificationItemID = verificationItem.ViID
        JOIN tblCurationRequest request WITH (NOLOCK) ON request.ID = requestSku.RequestID
        JOIN tblCurationImportRequest importRequest WITH (NOLOCK) ON importRequest.ID = request.ImportRequestID
        JOIN tblPlRebrandProject rebrandProject WITH (NOLOCK) ON rebrandProject.ID = importRequest.RebrandProjectID

        WHERE verificationItem.ViBatchID = :batch_id
        GROUP BY rebrandProject.ID, rebrandProject.Name
        ORDER BY skus_count DESC';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get rebrand project for batch');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get rebrand project for batch "%s"', $batch_id),
          ['batch_id' => $batch_id]
      );
      throw $exception;
    }

    $result = $statement->fetchAll(PDO::FETCH_CLASS, Rebrand_Project::class);

    return count($result) > 0 ? $result[0] : null;
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason[]
   */
  public function get_rejection_reasons() : array {
    $this->info('Loading rejection reasons');

    $sql = 'SELECT ID AS id, Name AS name
            FROM csn_product.dbo.tblplCurationDecisionRejectionReason WITH (NOLOCK) 
            WHERE Active = :active 
            ORDER BY DisplayIndex';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':active', self::REJECTION_REASON_ACTIVE, PDO::PARAM_BOOL);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get rejection reasons');
      $this->log_throwable_error(
          $exception,
          'Cannot get rejection reasons'
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Rejection_Reason::class);
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
   */
  public function get_suggested_style_rejection_reasons() : array {
    $this->info('Loading suggested style rejection reasons');

    $sql = 'SELECT ID AS id, Name AS name
            FROM csn_product.dbo.tblplCurationDecisionSuggestedStyleRejectionReason WITH (NOLOCK) 
            ORDER BY ID';

    $statement = $this->pdo->prepare($sql);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load suggested style rejection reasons');
      $this->log_throwable_error(
          $exception,
          'Cannot load suggested style rejection reasons'
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Suggested_Style_Rejection_Reason::class);
  }

  /**
   * @param string[] $skus to fetch the prices for
   *
   * @return array
   */
  public function get_skus_prices(array $skus) : array {
    $sql = 'SELECT pr.PrSKU as sku, pr.PrPrice as price
            FROM csn_product.dbo.tblProduct pr WITH (NOLOCK)
            WHERE pr.PrSKU IN (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));

    $res = [];
    $wscCosts = [];
    $skusWithInvalidPrices = [];
    if ($statement->execute()) {
      $rows = $statement->fetchAll(PDO::FETCH_ASSOC);
      foreach ($rows as $row) {
        if (!Curation_Price_Loader::isPriceValid($row['price'])) {
          $skusWithInvalidPrices[$row['sku']] = $row['sku'];
        }
      }
      if (!empty($skusWithInvalidPrices)) {
        $wscCosts = $this->get_skus_wsc_cost_with_margin(array_values($skusWithInvalidPrices));
      }

      foreach ($rows as $row) {
        $price = $row['price'];
        $sku = $row['sku'];
        if (!Curation_Price_Loader::isPriceValid($price) && array_key_exists($sku, $wscCosts)) {
          $price = $wscCosts[$sku];
        }
        $res[$sku] = $price;
      }
    } else {
      $exception = ExecutionException::forStatement($statement, 'Cannot load fallback prices for SKUs');
      $this->log_throwable_error(
          $exception,
          'Cannot load fallback prices for SKUs'
      );
      throw $exception;
    }
    return $res;
  }

    /**
     * @param string[] $skus to fetch the prices for
     *
     * @return array
     * @throws \Exception
     */
    public function get_skus_wsc_cost_with_margin(array $skus) : array {

        $sql = 'select * from 
                (select *, ROW_NUMBER() over (partition by sku order by sku) AS ROWNUMBER from 
                    (SELECT   o.PrSKU as sku, s.ID, marginByClass.margin
                        FROM csn_product.dbo.tblOptionCombination o WITH (NOLOCK)
                        INNER JOIN csn_product.dbo.tblSupplierPart s WITH (NOLOCK)
                        ON s.ManufacturerPartID = o.ManufacturerPartID
                        INNER JOIN csn_product.dbo.tblJoinProductClass AS joinProductClass WITH(NOLOCK)
                        ON joinProductClass.prsku = o.PrSKU
                        LEFT JOIN csn_product.dbo.tblCurationAvgMarginByClass as marginByClass WITH(NOLOCK)
                        ON marginByClass.clid = joinProductClass.clid
                    WHERE o.PrSKU in (' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ')) as sku_supplier_tbl) as get_five_supplierids
            where ROWNUMBER <=' . SELF::ROWS_COUNT;

        $statement = $this->pdo->prepare($sql);
        $statement->bindValuesList(':sku', $skus, SQL::nvarchar(8));
        $res = [];
        if ($statement->execute()) {
            $rows = $statement->fetchAll(PDO::FETCH_ASSOC);
            //Start
            $this->info('List of Skus & SupplierIDs by rows', $rows);
            foreach ($skus as $singleSku) {
                $avgBaseCost = 0;
                foreach ($rows as $row) {
                    if($singleSku === $row['sku']) {
                        $this->info('List of Skus & SupplierIDs by each row: ', $row);
                        $resultAPI = $this->costApiClient->get_part_cost($row['ID']);
                        $this->info('costApiClient get_part_cost returned average baseCost for this sku by supplierPartID ', [$row['ID'] => [$resultAPI]]);
                        if ($row['margin'] === null) {
                          $row['margin'] = SELF::DEFAULT_MARGIN;
                        }
                        $avgBaseCost = $resultAPI;
                        if ($avgBaseCost != 0) {
                          break;
                        }
                    }
                }
                $res[$singleSku] = $avgBaseCost != 0 ? $avgBaseCost : CostApiService::FALLBACK_PRICE;
                $this->info('Final value average baseCost for this Sku ',[$singleSku => $res[$singleSku]]);
            }
            //end
        } else {
            $exception = ExecutionException::forStatement($statement, 'Cannot load WSC fallback prices for SKUs');
            $this->log_throwable_error(
                $exception,
                'Cannot load WSC fallback prices for SKUs'
            );
            throw $exception;
        }
        return $res;
    }

    /**
   * @param float[] $skusPrices The SKUs to look data for. `key` here should be - SKU and the `value` - price
   *
   * @return array
   */
  public function get_curation_price_tiers_for_skus(array $skusPrices) : array {
    $this->info('Loading Price tiers for SKUs');
    $result = [];

    if (empty($skusPrices)) {
      return $result;
    }

    $skusWithInvalidPrices = [];
    $fallbackCosts = [];
    foreach (array_chunk($skusPrices, 1000, true) as $chunk) {
      foreach ($chunk as $sku => $price) {
        if (!Curation_Price_Loader::isPriceValid($price)) {
          $skusWithInvalidPrices[$sku] = $sku;
        }
      }
    }
    if (!empty($skusWithInvalidPrices)) {
      $fallbackCosts = $this->get_skus_prices(array_values($skusWithInvalidPrices));
    }

    foreach (array_chunk($skusPrices, 1000, true) as $chunk) {
      // create temp table to keep same $skus order
      $column_map  = [
        'SKU'   => SQL::nvarchar(8),
        'Price' => SQL::money,
      ];
      $inserted_data = [];
      foreach ($chunk as $sku => $price) {
        if (!Curation_Price_Loader::isPriceValid($price) && array_key_exists($sku, $fallbackCosts)) {
          $price = $fallbackCosts[$sku];
        }
        $inserted_data[] = [
            'SKU'   => $sku,
            'Price' => $price,
        ];
      }

      $sql = SQLBulkHelper::get_temp_table_json_sql($column_map, 'tmpCurationPriceTierSkus');

      $sql .= '
              SELECT
                    p.PrSKU AS sku,
                    pt.CptPriceTier AS price_tier
              FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                    INNER JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = p.PrSKU AND pc.PcMasterClass = 1
                    INNER JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID
                    INNER JOIN #tmpCurationPriceTierSkus tmp ON p.PrSKU = tmp.SKU
                    CROSS APPLY (
                        SELECT TOP (1) ISNULL(CASE MAX(CptPriceTier) WHEN 4 THEN 4 ELSE MAX(CptPriceTier) + 1 END, 1) AS CptPriceTier
                        FROM csn_product.dbo.tblClassPriceTier cpt WITH (NOLOCK)
                        WHERE CptPriceTierCeiling < tmp.Price
                        AND CptClID = cl.ClID
                        AND CptBclgID = p.PrBclgID
                    ) pt';

      $statement = $this->pdo->prepare($sql);
      $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($inserted_data), PDO::PARAM_STR);

      if (!$statement->execute()) {
        throw ExecutionException::forStatement($statement, 'Failed to load context data for skus');
      }

      $result = array_merge($result, $statement->fetchAll(PDO::FETCH_KEY_PAIR));
    }

    foreach ($result as $sku => $price) {
          $this->info('sku:'.$sku. ' pricetier:'.$price);
    }

    return $result;
  }

  /**
   * Relaxing JOINs will allow to handle missing class conditions on further steps
   *
   * @return string
   */
  private function get_strict_class_join() : string {
    return $this->useStrictMode
        ? 'INNER'
        : 'LEFT';
  }
}
