<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\DTO;

final class ValidationErrorResponse extends Response
{
    /**
     * @var bool
     */
    private $isValid;

    /**
     * @var array
     */
    private $validationDetails;

    /**
     * ValidationError constructor.
     *
     * @param bool               $isRequestSuccessful
     * @param bool               $isValid
     * @param ValidationDetail[] $validationDetails
     */
    public function __construct(bool $isRequestSuccessful, bool $isValid, array $validationDetails)
    {
        parent::__construct($isRequestSuccessful);

        $this->isValid = $isValid;
        $this->validationDetails = $validationDetails;
    }

    /**
     * @return bool
     */
    public function isValid(): bool
    {
        return $this->isValid;
    }

    /**
     * @return array
     */
    public function getValidationDetails(): array
    {
        return $this->validationDetails;
    }
}
