## Changelog
<!-- List here all the things you changed //-->

[NOTE]: <> (You must replace ### below with a valid Project Hub ticket number. For example, ME6-123)
PH: MTBW-###
BR: cmenezesdossantos
TESTED: yes/no

## Pre-check
- [ ] My code follows the [CFE development guideline](https://infohub.corp.wayfair.com/x/q5BdGw)
- [ ] I have performed a self-review of my own code

## Documentation
<!-- Did you update the relevant documents related to this code - swagger, user guides, etc? //-->
- [ ] Updated the user guide
- [ ] Updated the technical documentation
- [ ] Updated the README file
- [ ] Other (please add details below)
- [ ] Nothing to update

## Testing
<!-- How did you test your code? //-->
- [ ] Unit tests
- [ ] Integration tests
- [ ] Manually tested in dev
- [ ] Did not do any testing (I don't need to test, I write perfect code)
- [ ] Other (please add details below)

## Logging
<!-- Did you add enough logs to follow the logic of your changes? //-->
- [ ] Logged normal flow
- [ ] Logged exceptional cases
- [ ] Other (please add details below)

**Kibana link (dev):**

## Monitoring / alerts:
<!-- Did you add custom metrics / checked the existing metrics in datadog? //-->
- [ ] Datadog APM
- [ ] Custom metrics
- [ ] Created / updated alerts
- [ ] Created / updated dashboards
- [ ] Other (please add details below)

**Datadog link (dev):**
- [APM](https://app.datadoghq.com/apm/services/brand-worflows-curation-tool/operations/asgi.request/resources?env=dev&hostGroup=%2A&resources=qson%3A%28data%3A%28visible%3A%21t%2Chits%3A%28selected%3Atotal%29%2Cerrors%3A%28selected%3Atotal%29%2Clatency%3A%28selected%3Ap99%29%2CtopN%3A%215%29%2Cversion%3A%210%29&summary=qson%3A%28data%3A%28visible%3A%21t%2Cerrors%3A%28selected%3Acount%29%2Chits%3A%28selected%3Acount%29%2Clatency%3A%28selected%3Alatency%2Cslot%3A%28agg%3A75%29%2Cdistribution%3A%28isLogScale%3A%21f%29%29%2Csublayer%3A%28slot%3A%28layers%3Aservice%29%2Cselected%3Apercentage%29%29%2Cversion%3A%211%29&topGraphs=latency%3Alatency%2Cerrors%3Aversion_count%2Chits%3Aversion_count&start=1660569084744&end=1660572684744&paused=false)

## Description
<!-- Detail of what this change will actually do, or what it's for. Include links to any relevant tickets //-->
