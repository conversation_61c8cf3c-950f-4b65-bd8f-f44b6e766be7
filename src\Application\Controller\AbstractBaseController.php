<?php

declare(strict_types=1);

namespace App\Application\Controller;

use App\Application\Exception\BatchActionNotMatchStatusException;
use App\Application\Logger\LoggerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function in_array;
use function sprintf;

/**
 * @method \WF\PartnerHome\User\Authentication\SymfonyBundle\Entity\PartnerHomeUser|null getUser()
 */
abstract class AbstractBaseController extends AbstractController implements LoggerAwareInterface
{
    use LoggerTrait;
    use LoggerAwareTrait;

    /**
     * @return int|null Employee ID of current user
     */
    protected function getEmployeeId(): ?int
    {
        $user = $this->getUser();
        return $user !== null ? $user->employeeId() : null;
    }

    /**
     * Verify that the requested action matches the batch status.
     * Redirect to the correct page if the action does not match the expected action.
     *
     * @param int $batchId batch id
     * @param string $action Current action, eg index, downstream, qa, etc
     * @param Completion_Batch_Data_Service $batchDataService Completion_Batch_Data_Service
     *
     * @return ?Response
     */
    protected function ensureBatchStatusMatchesAction(
        int $batchId,
        string $action,
        Completion_Batch_Data_Service $batchDataService
    ): ?Response {
        try {
            $batchData = $batchDataService->get($batchId);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->warning(
                'Batch not found',
                ['batch_id' => $batchId, 'exception' => $exception]
            );
            return $this->redirectToRoute('index');
        }
        switch ($batchData->getStatus()) {
            case Batch::STATUS_MANUAL_UNASSIGNED:
            case Batch::STATUS_MANUAL_ASSIGNED:
            case Batch::STATUS_MANUAL_IN_PROGRESS:
            case Batch::STATUS_AUTOMATED_GENERATED:
            default:
                $expectedAction = 'index';
                break;

            case Batch::STATUS_MANUAL_CURATION_COMPLETE:
            case Batch::STATUS_AUTOMATED_DOWNSTREAMED:
            case Batch::STATUS_AUTOMATED_QA_IN_PROGRESS:
            case Batch::STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS:
                $expectedAction = 'qa';
                break;

            case Batch::STATUS_MANUAL_QA_COMPLETE:
            case Batch::STATUS_MANUAL_DOWNSTREAMED:
            case Batch::STATUS_AUTOMATED_QA_COMPLETE:
            case Batch::STATUS_AUTOMATED_RE_DOWNSTREAMED:
                $expectedAction = 'completed';
                break;
        }
        if ($action !== $expectedAction) {
            $this->warning(
                'Action does not match batch status',
                [
                    'batch_id' => $batchId,
                    'action' => $action,
                    'expected_action' => $expectedAction,
                    'current_status' => $batchData->getStatus()
                ]
            );
            throw new BatchActionNotMatchStatusException(null, $expectedAction, $batchId);
        }

        return null;
    }

    /**
     * @param int $batchId batch id
     * @param array $expectedStatus expected batch statuses array
     * @param Completion_Batch_Data_Service $batchDataService Completion_Batch_Data_Service
     *
     * @return bool true if batch status matches $expected_status, false otherwise
     */
    protected function isExpectedBatchStatus(
        int $batchId,
        array $expectedStatus,
        Completion_Batch_Data_Service $batchDataService
    ): bool {
        $this->info(
            'Asserting batch status',
            ['batch_id' => $batchId, 'expected_status' => $expectedStatus]
        );

        try {
            $batchData = $batchDataService->get($batchId);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->error(
                sprintf('Failed to load batch data: %s', $exception->getMessage()),
                ['batch_id' => $batchId, 'exception' => $exception]
            );

            return false;
        }

        $this->info(
            'Batch status validation',
            [
                'actual_status' => $batchData->getStatus(),
                'expected_status' => $expectedStatus,
                'batch_id' => $batchId
            ]
        );

        return in_array($batchData->getStatus(), $expectedStatus, true);
    }
}
