<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

class Suggested_Style_Rejection_Reason implements \JsonSerializable {
  /**
   * @var int
   */
  private $id; /** @phpstan-ignore-line */

  /**
   * @var string
   */
  private $name; /** @phpstan-ignore-line */

  /**
   * Rejection Reason constructor.
   */
  private function __construct() {
    // created only by PDO
  }

  /**
   * @return int
   */
  public function get_id() : int {
    return $this->id;
  }

  /**
   * @return string
   */
  public function get_name() : string {
    return $this->name;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'id'   => $this->id,
        'name' => $this->name
    ];
  }
}
