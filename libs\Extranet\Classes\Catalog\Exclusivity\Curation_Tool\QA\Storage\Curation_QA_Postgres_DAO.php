<?php

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage;

use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;
use PDO;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;

class Curation_QA_Postgres_DAO {
  use Logging_Trait;

  /**
   * @param PostgresConnection $pdo_psql PDO Postgres
   */

  private PostgresConnection $pdo_psql;

  /**
   * @param PostgresConnection             $pdo_psql    the PDO
   * @param \Psr\Log\LoggerInterface|null  $logger Logger
   */
  public function __construct(PostgresConnection $pdo_psql, ?LoggerInterface $logger = null) {
    $this->pdo_psql = $pdo_psql;
    $this->logger = $logger;

  }


  /**
   * @param array $skus skus to check
   * @param int $batch_id batch id
   *
   * @return bool true if all skus have either MAID or Excluded reason are not empty
   */
  public function check_if_ready_to_save(array $skus, int $batch_id): bool {
    $this->info(
      sprintf('Checking readiness to save skus for BatchId "%s"', $batch_id),
      ['batch_id' => $batch_id, 'skus' => $skus]
    );
    $skuInClause = implode(',', array_map(function($sku) {
      return "'" . pg_escape_string($sku) . "'";
    }, $skus));

    $sql = "SELECT 1 AS count
      FROM \"tblVerificationItem\"
      WHERE
          \"ViSKU\" IN ($skuInClause)
          AND \"ViFinalBrandMaID\" IS NULL
          AND \"ViExcludedReasonID\" = 0
          AND \"ViBatchID\" = :batch_id
      LIMIT 1";

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to check for not ready to save skus');
      $this->log_throwable_error(
        $exception,
        sprintf('Failed to check for not ready to save skus for BatchId "%s"', $batch_id),
        ['batch_id' => $batch_id, 'skus' => implode(",", $skus), 'sql' => $sql]
      );
      throw  $exception;
    }

    $row = $statement->fetch();

    return ($row === false);
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision(Curation_QA_Decision $decision): void {
    $this->info(
        sprintf('Saving curation decision for batchId postgres "%s"', $decision->get_batch_id()),
        [
            'batch_id'    => $decision->get_batch_id(),
            'status'      => $decision->get_qa_status()->value(),
            'employee_id' => $decision->get_employee_id(),
            'reason'      => $decision->get_reason()
        ]
    );

    $skus = $decision->get_skus();

    $skuInClause = implode(',', array_map(function($sku) {
      return "'" . pg_escape_string($sku) . "'";
    }, $skus));

    $sql = "
        DO $$ 
        DECLARE 
          statusValue INT := ". $decision->get_qa_status()->value(). ";
          employeeId INT := ". $decision->get_employee_id() .";
        BEGIN
          CREATE TEMP TABLE \"UpdatedSKUs\" AS
          SELECT \"ViID\", \"ViSKU\"
          FROM \"tblVerificationItem\"
          WHERE \"ViBatchID\" = ". $decision->get_batch_id() ."
           AND \"ViSKU\" IN ($skuInClause);
            
          UPDATE \"tblVerificationItem\"
          SET 
            \"ViLockedEmID\" = CASE WHEN \"ViLockedDate\" IS NULL THEN employeeId ELSE \"ViLockedEmID\" END,
            \"ViLockedDate\" = CASE WHEN \"ViLockedDate\" IS NULL THEN CURRENT_TIMESTAMP ELSE \"ViLockedDate\" END,
            \"ViQAStatusID\" = statusValue
          WHERE
              \"tblVerificationItem\".\"ViBatchID\" =  ". $decision->get_batch_id() ." AND
              \"tblVerificationItem\".\"ViSKU\" IN ($skuInClause);
        
          INSERT INTO \"tblVerificationItemQAStatusHistory\"
            (\"VerificationItemID\", \"SKU\", \"StatusID\", \"EmployeeID\", \"Date\", \"Message\")
          SELECT \"ViID\", \"ViSKU\", statusValue, employeeId, CURRENT_TIMESTAMP, '". $decision->get_reason() ."'
          FROM \"UpdatedSKUs\";
          
          DROP TABLE \"UpdatedSKUs\";
        END $$;
    ";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision');
      $this->log_throwable_error(
          $exception,
          sprintf('Failed to save curation decision for batchId "%s"', $decision->get_batch_id()),
          [
              'skus'        => implode(",", $skus),
              'batch_id'    => $decision->get_batch_id(),
              'status'      => $decision->get_qa_status()->value(),
              'employee_id' => $decision->get_employee_id(),
              'reason'      => $decision->get_reason(),
              'sql'         => $sql
          ]
      );
      throw $exception;
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision_and_styles(Curation_QA_Decision $decision): void {
    $this->info(
        sprintf('Saving curation decision and styles for batchId "%s"', $decision->get_batch_id()),
        [
            'batch_id'                 => $decision->get_batch_id(),
            'status'                   => $decision->get_qa_status()->value(),
            'employee_id'              => $decision->get_employee_id(),
            'reason'                   => $decision->get_reason(),
            'price_tier_override'      => $decision->get_price_tier_override(),
            'final_style_id'           => $decision->get_final_style_id(),
            'final_substyle_id'        => $decision->get_final_substyle_id(),
            'final_brand_id'           => $decision->get_final_brand_id()
        ]
    );

    $skus = $decision->get_skus();

    $skuInClause = implode(',', array_map(function($sku) {
      return "'" . pg_escape_string($sku) . "'";
    }, $skus));

    $sql = "
        DO $$
        DECLARE
            statusValue INT := ". $decision->get_qa_status()->value() .";
            priceTierOverride INT := ". $decision->get_price_tier_override() .";
            employeeId INT := ". $decision->get_employee_id() .";
            finalStyleId INT := ". $decision->get_final_style_id() .";
            finalSubStyleId INT := ". $decision->get_final_substyle_id() .";
            finalBrandId INT := ". $decision->get_final_brand_id() .";
        BEGIN
            CREATE TEMP TABLE \"UpdatedSKUs\" AS
            SELECT \"ViID\", \"ViSKU\"
            FROM \"tblVerificationItem\"
            WHERE \"ViBatchID\" = ". $decision->get_batch_id() ."
            AND \"ViSKU\" IN ($skuInClause);
           
            UPDATE \"tblVerificationItem\"
            SET
                \"ViLockedEmID\" = CASE WHEN \"ViLockedDate\" IS NULL THEN employeeId ELSE \"ViLockedEmID\" END,
                \"ViLockedDate\" = CASE WHEN \"ViLockedDate\" IS NULL THEN CURRENT_TIMESTAMP ELSE \"ViLockedDate\" END,
                \"ViQAStatusID\" = statusValue,
                \"ViPriceTierOverride\" = priceTierOverride,
                \"ViFinalStyleID\" = finalStyleId,
                \"ViFinalSubStyleID\" = finalSubStyleId,
                \"ViFinalBrandMaID\" = finalBrandId
            WHERE
                \"ViBatchID\" = ". $decision->get_batch_id() ."
                AND \"ViSKU\" IN ($skuInClause) ;
             
            INSERT INTO \"tblVerificationItemQAStatusHistory\"
            (\"VerificationItemID\", \"SKU\", \"StatusID\", \"EmployeeID\", \"Date\")
            SELECT \"ViID\", \"ViSKU\", statusValue, employeeId, CURRENT_TIMESTAMP
            FROM \"UpdatedSKUs\";
          
            DROP TABLE \"UpdatedSKUs\";   
        END $$;
        ";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision and styles');

      $this->log_throwable_error(
          $exception,
          sprintf('Failed to save curation decision and styles for batchId "%s"', $decision->get_batch_id()),
          [
              'skus'                => implode(",", $skus),
              'batch_id'            => $decision->get_batch_id(),
              'status'              => $decision->get_qa_status()->value(),
              'employee_id'         => $decision->get_employee_id(),
              'reason'              => $decision->get_reason(),
              'price_tier_override' => $decision->get_price_tier_override(),
              'final_style_id'      => $decision->get_final_style_id(),
              'final_substyle_id'   => $decision->get_final_substyle_id(),
              'final_brand_id'      => $decision->get_final_brand_id(),
              'sql'                 => $sql
          ]
      );

      throw $exception;
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision_and_exclude_from_wl(Curation_QA_Decision $decision): void {
    $this->info(
      sprintf('Saving curation decision for batchId postgres(save_decision_and_exclude_from_wl) "%s"', $decision->get_batch_id()),
      [
        'batch_id' => $decision->get_batch_id(),
        'status' => $decision->get_qa_status()->value(),
        'employee_id' => $decision->get_employee_id(),
        'exclusion_reason' => $decision->get_exclusion_reason()
      ]
    );

    $skus = $decision->get_skus();

    $skuInClause = implode(',', array_map(function($sku) {
      return "'" . pg_escape_string($sku) . "'";
    }, $skus));

    $sql = "
      DO $$ 
      DECLARE 
          statusValue INT := ". $decision->get_qa_status()->value() .";
      BEGIN
          CREATE TEMP TABLE \"UpdatedSKUs\" AS
          SELECT \"ViID\", \"ViSKU\"
          FROM \"tblVerificationItem\"
          WHERE \"ViBatchID\" = ". $decision->get_batch_id() ."
           AND \"ViSKU\" IN ($skuInClause);
           
          UPDATE \"tblVerificationItem\"
          SET
              \"ViQAStatusID\" = statusValue,
              \"ViExcludedReasonID\" = ". $decision->get_exclusion_reason() ."
          WHERE
              \"ViBatchID\" = ". $decision->get_batch_id() ." AND
              \"ViSKU\" IN ($skuInClause);
  
          INSERT INTO \"tblVerificationItemQAStatusHistory\"
              (\"VerificationItemID\", \"SKU\",\"StatusID\",\"EmployeeID\", \"Date\")
          SELECT \"ViID\",\"ViSKU\", statusValue, ". $decision->get_employee_id() .", CURRENT_DATE
          FROM \"UpdatedSKUs\";
          
          DROP TABLE \"UpdatedSKUs\";   
      END $$;
      ";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save curation decision');

      $this->error(
        sprintf('Failed to save curation decision for batchId "%s"', $decision->get_batch_id()),
        [
          'skus' => implode(",", $skus),
          'batch_id' => $decision->get_batch_id(),
          'status' => $decision->get_qa_status()->value(),
          'employee_id' => $decision->get_employee_id(),
          'exclusion_reason' => $decision->get_exclusion_reason(),
          'sql' => $sql,
          'exception' => $exception,
        ]
      );
      throw $exception;
    }
  }

}