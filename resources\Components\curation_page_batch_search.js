/**
 * Renders search form to search for a batch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React, {Component} from 'react';
import Translation from '@wayfair/translation';
import {TextInput, InputGroup, Button} from '@wayfair/homebase-extranet';

export class CurationPageBatchSearch extends Component {
  constructor() {
    super();
    this.state = {value: true};
    this.onChange = this.onChange.bind(this);
  }

  onChange(e) {
    this.setState({value: e.target.value === ''});
  }

  render() {
    return (
      <form>
        <InputGroup>
          <TextInput
            isGrouped
            label={Translation({
              msgid:
            'CurationTool.CurationPageBatchSearchBatchIDLabel',
            })}
            name="batch_id"
            onChange={this.onChange}
          />
          <Button type="submit" disabled={this.state.value}>
        <Translation msgid="CurationTool.CurationPageBatchSearchSearchButton" />
          </Button>
        </InputGroup>
      </form>
    );
  }
}
