<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service;

interface Context_SKU_Collection_Retrieve_Interface {

  /**
   * @param string $sku SKU
   *
   * @return int|null
   */
  public function get_context_xnid(string $sku): ?int;

  /**
   * @param string ...$skus SKUs
   *
   * @return array Map of SKU to XN ID (Collection ID)
   */
  public function get_context_xnid_map(string ...$skus): array;
}
