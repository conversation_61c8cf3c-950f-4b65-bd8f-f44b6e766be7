<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

class Context_Data_Collection {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data[]
   */
  private $data = [];

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Empty_Data
   */
  private $empty_data;

  /**
   * Constructor
   */
  public function __construct() {
    $this->empty_data = new Context_Empty_Data();
  }

  /**
   * @param string $sku the SKU to get data for
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data
   */
  public function get_for_sku(string $sku) : Context_Data {
    return $this->data[$sku] ?? $this->empty_data;
  }

  /**
   * @param string                                                                           $sku  SKU
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data $data Context Data
   *
   * @return void
   */
  public function set_for_sku(string $sku, Context_Data $data) {
    $this->data[$sku] = $data;
  }
}