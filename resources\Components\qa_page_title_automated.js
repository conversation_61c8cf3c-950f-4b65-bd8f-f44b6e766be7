/**
 * QA Page title
 *
 * <AUTHOR> <em<PERSON><PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {Column, Grid, IconV2 as Icon} from '@wayfair/homebase-extranet';
import {Box} from '@wayfair/homebase-extranet';
import {faCheck, faPen} from '@fortawesome/free-solid-svg-icons';
import {ALIGNMENT, WIDTHS} from './common_layout_constants';

const QAPageTitleAutomated = ({isReadOnlyMode}) =>
  isReadOnlyMode ? (
    <Grid alignItems={ALIGNMENT.CENTER}>
      <Box>
          <Icon size="25px" icon={faCheck} />
      </Box>
      <Column size={WIDTHS.WIDTH_6}>
        <div className="icon_text_align">
          <Translation msgid="CurationTool.Complete" />
        </div>
      </Column>
    </Grid>
  ) : (
    <Grid alignItems={ALIGNMENT.CENTER}>
      <Box>
          <Icon size="25px"icon={faPen} />
      </Box>
      <Column size={WIDTHS.WIDTH_6}>
        <Translation msgid="CurationTool.QA" />
      </Column>
    </Grid>
  );

QAPageTitleAutomated.propTypes = {
  isReadOnlyMode: PropTypes.bool.isRequired,
};

QAPageTitleAutomated.defaultProps = {
  isReadOnlyMode: false,
};

export default QAPageTitleAutomated;
