<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use App\Application\DTO\CompleteRequest;
use App\Application\Service\Security\ToolAvailabilityCheckerInterface;
use App\Application\View\QAView;
use App\Domain\Service\BatchService;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\Curator_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Config_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason_Loader;

use function set_time_limit;

class CompletedController extends AbstractBaseController
{
    /**
     * @param Config_Loader $config_service the config service
     * @param BatchService $batchService
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param Rebrand_Project_Loader $rebrand_project_loader Rebrand Project Loader
     * @param Rejection_Reason_Loader $rejection_reason_loader Rejection Reason Loader
     * @param Curation_Section_Service $curation_section_service Data Consistency Validator
     * @param Curator_Loader $curator_loader
     * @param CompleteRequest $completeRequest
     * @return QAView
     * @throws Throwable
     * @throws Completion_Batch_Not_Found_Exception
     * @Route(path="/completed", methods={"GET"}, name="completed")
     */
    public function completed(
        Config_Loader $config_service,
        BatchService $batchService,
        Completion_Batch_Data_Service $batch_data_service,
        Rebrand_Project_Loader $rebrand_project_loader,
        Rejection_Reason_Loader $rejection_reason_loader,
        Curation_Section_Service $curation_section_service,
        Curator_Loader $curator_loader,
        CompleteRequest $completeRequest
    ): QAView {
        $batch_id = $completeRequest->getBatchId();
        $batchService->ensureAccessToBatch($batch_id);

        $rebrand_project = null;

        try {
            if ($batch_id !== 0) {
                $this->ensureBatchStatusMatchesAction($batch_id, 'completed', $batch_data_service);

                $this->info(
                    'Loading Rebrand Project',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );
                $rebrand_project = $rebrand_project_loader->getRebrandProject($batch_id);
            }

            // mark all rows as readonly to disable accept/reject buttons
            $this->info(
                'Loading curation sections',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
            );

            set_time_limit(5 * 60); // set page limit in 5 minutes to handle batches with 10k+ SKUs and many collisions

            $sections = [];
            $this->info(
                'Running batch validations',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
            );


            $batch_data = $batch_data_service->get($batch_id);
            $assigned_employee_id = $batch_data->getAssignedEmployeeId();
            $batch_curator = null;
            if ($assigned_employee_id !== null && $assigned_employee_id > 0) {
                $batch_curator = $curator_loader->get_curator($assigned_employee_id);
            }
            $approver_employee_id = $batch_data->getApproverEmployeeId();
            $batch_approver = null;
            if ($approver_employee_id !== null && $approver_employee_id > 0) {
                $batch_approver = $curator_loader->get_curator($approver_employee_id);
            }

            return new QAView(
                $batch_data,
                $batch_curator,
                $batch_approver,
                $config_service->getConfig($batch_id, true),
                $batch_id,
                $batch_data->getProcessTypeID(),
                $sections,
                $rejection_reason_loader->getRejectionReasons(),
                $rejection_reason_loader->getSuggestedStyleRejectionResons(),
                $rebrand_project,
                true,
                [], // no need to show warnings on that page
                $this->isGranted(ToolAvailabilityCheckerInterface::CURATION_ASSORTMENT_WORKFLOW_BY_OFFSHORE)
            );
        } catch (Throwable $exception) {
            $this->error(
                'Failed to Complete batch curation',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'exception' => $exception,
                ]
            );

            throw $exception;
        }
    }
}
