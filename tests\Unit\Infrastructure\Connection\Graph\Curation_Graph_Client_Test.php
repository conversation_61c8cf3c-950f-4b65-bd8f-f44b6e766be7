<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\Infrastructure\Connection\Graph;

use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use App\Infrastructure\Connection\Graphql\GraphClient;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Logging\Logger;
use function json_decode;

class Curation_Graph_Client_Test extends TestCase
{
    use ProphecyTrait;
    private $logger;


    /**
     * @test
     *
     * @return void
     */
    public function get_brand_catalog_by_batch_id_test()
    {
        $batch_ids = [107];
        $expected_brand_catalog_id = 1;

        $this->logger = $this->prophesize(Logger::class);

        // Create a mock for the GraphClient dependency
        $graph_client_mock = $this->getMockBuilder(GraphClient::class)
            ->disableOriginalConstructor()
            ->getMock();

        // Configure the mock behavior for getJsonResponseToQuery
        $graph_client_mock->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->with(
                $this->equalTo(CurationGraphApi::BRAND_CATALOG_BY_BATCH_ID),
                $this->equalTo(['batch_ids' => $batch_ids]),
                $this->anything(),
                $this->anything(),
                $this->anything()
            )
            ->willReturn([
                'data' => [
                    'getCurationBatchesByIds' => [
                        [
                            'brandCatalog' => [
                                'brandCatalogId' => $expected_brand_catalog_id
                            ]
                        ]
                    ]
                ]
            ]);

        $curation_graph_client = new CurationGraphApi($graph_client_mock, $this->logger->reveal());
        $brand_catalog_id = $curation_graph_client->get_brand_catalog_by_batch_id($batch_ids);

        // Assert the expected result
        $this->assertEquals($expected_brand_catalog_id, $brand_catalog_id);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_sku_and_context_collect_id_by_batch_id_test()
    {
        $batch_ids = [107];
        $expected_data = [
            [
                'sku' => 'NOHM1423',
                'context_xn_id' => 601293
            ],
            [
                'sku' => 'NOHM1424',
                'context_xn_id' => 601293
            ]
        ];

        $response = '{
          "data": {
            "getCurationBatchesByIds": [
              {
                "batchProducts": [
                  {
                    "sku": "NOHM1423",
                    "contextCollection": {
                      "contextCollectionId": 601293
                    }
                  },
                  {
                    "sku": "NOHM1424",
                    "contextCollection": {
                      "contextCollectionId": 601293
                    }
                  }
                ]
              }
            ]
          }
        }';
        $response_decoded = json_decode($response, true);
        $graph_client_mock = $this->getMockBuilder(GraphClient::class)
            ->disableOriginalConstructor()
            ->getMock();

        // Configure the mock behavior for getJsonResponseToQuery
        $graph_client_mock->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->with(
                $this->equalTo(CurationGraphApi::SKU_AND_CONTEXT_COLLECTION_BY_BATCH_ID),
                $this->equalTo(['batch_ids' => $batch_ids]),
                $this->anything(),
                $this->anything(),
                $this->anything()
            )
            ->willReturn($response_decoded);
        $this->logger = $this->prophesize(Logger::class);

        $curation_graph_client = new CurationGraphApi($graph_client_mock, $this->logger->reveal());
        $output = $curation_graph_client->get_sku_and_context_collect_id_by_batch_id($batch_ids);

        // Assert the expected result
        $this->assertEquals($expected_data, $output);
    }

    /**
     * @test
     *
     * @return void
     */
    public function testSendGraphQLRequestWithNoData()
    {
        $batch_ids = [107];

        // Configure the mock's getJsonResponseToQuery method to return an empty response
        $graph_client_mock = $this->getMockBuilder(GraphClient::class)
            ->disableOriginalConstructor()
            ->getMock();

        // Configure the mock behavior for getJsonResponseToQuery
        $graph_client_mock->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->with(
                $this->equalTo(CurationGraphApi::BRAND_CATALOG_BY_BATCH_ID),
                $this->equalTo(['batch_ids' => $batch_ids]),
                $this->anything(),
                $this->anything(),
                $this->anything()
            )
            ->willReturn([
                'data' => []
            ]);
        $this->logger = $this->prophesize(Logger::class);

        $curation_graph_client = new CurationGraphApi($graph_client_mock, $this->logger->reveal());

        // Call the sendGraphQLRequest method
        $result = $curation_graph_client->get_brand_catalog_by_batch_id($batch_ids);

        // Assert that the result is an empty array
        $this->assertSame(null, $result);
    }
}
