<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Tests;

use App\Kernel;
use Doctrine\DBAL\Driver\SQLSrv\Driver;
use <PERSON>ymfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class TestingKernel extends Kernel
{
    /**
     * {@inheritDoc}
     */
    protected function build(ContainerBuilder $container): void
    {
        $container->addCompilerPass(
            new class() implements CompilerPassInterface {
                /**
                 * All this is needed to suppress connection attempt to DBCodes database
                 * on each container build
                 *
                 * @param ContainerBuilder $container
                 * @return void
                 */
                public function process(ContainerBuilder $container): void
                {
                    $container->getParameterBag()->add([
                        'dummy_db_user' => '%env(resolve:DB_USER)%',
                        'dummy_db_pass' => '%env(resolve:DB_PASSWORD_SECRET_NAME)%',
                        'dummy_client_id' => '%env(secret:VAULT_SERVICE_CLIENT_ID_NAME)%',
                        'dummy_secret_key' => '%env(secret:VAULT_SERVICE_SECRET_KEY_NAME)%',
                    ]);

                    $container->register(Driver::class, Driver::class);
                }
            }
        );
    }
}
