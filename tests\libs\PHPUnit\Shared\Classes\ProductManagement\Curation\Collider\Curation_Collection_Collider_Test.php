<?php
/**
 * Tests for the \WF\Shared\Classes\ProductManagement\Curation\Curation_Collection_Collider_Test
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Curation\Collider;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider;
use WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO;

class Curation_Collection_Collider_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider
     */
    protected $subject;

    /**
     * @var \WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO
     */
    private $dao;

    /**
     * Set up testing environment
     *
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Collider_DAO::class);

        $this->subject = new Curation_Collection_Collider($this->dao->reveal());
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_unique_skus_with_reset_indexes()
    {
        $skus = ['A', 'A', 'B'];

        $this->dao->getCollectionCollisions(1, [333])->willReturn($skus);
        $this->dao->getCollectionCollisions(99, [333])->willReturn($skus);

        $result = $this->subject->get_collisions_for_collections([1, 99], [333]);

        $this->assertSame(['A', 'B'], $result);
    }
}
