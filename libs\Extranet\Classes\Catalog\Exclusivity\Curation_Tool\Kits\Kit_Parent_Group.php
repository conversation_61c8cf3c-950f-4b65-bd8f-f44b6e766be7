<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits;

use WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface;
use WF\Shared\Exception\Invalid_Argument_Exception;

class Kit_Parent_Group implements Mergeable_Interface {
  /**
   * @var array
   */
  private $skus = [];

  /**
   * @var array
   */
  private $items = [];

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent $item Kit_Parent
   *
   * @return void
   */
  public function addItem(Kit_Parent $item) {
    $this->items[$item->sku] = $item;

    $this->skus = array_unique(array_merge($this->skus, [$item->sku], $item->children));
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent[]
   */
  public function getItems() : array {
    return array_values($this->items);
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return bool
   */
  public function isDependent(Mergeable_Interface $item) : bool {
    if (!$item instanceof Kit_Parent_Group) {
      throw new Invalid_Argument_Exception('Argument type is not supported');
    }

    if (!empty(array_intersect($this->skus, $item->skus))) {
      return true;
    }

    return false;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Utils\Mergeable_Interface $item Groupable_Interface
   *
   * @return void
   */
  public function merge(Mergeable_Interface $item) {
    if (!$item instanceof Kit_Parent_Group) {
      throw new Invalid_Argument_Exception('Argument type is not supported');
    }

    foreach ($item->getItems() as $childItem) {
      $this->addItem($childItem);
    }
  }
}