<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi;

use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use WF\Curation\WhiteLabelApi\Exception\ConfigException;
use WF\Curation\WhiteLabelApi\Http\HttpClientFactory;

final class ClientConfig
{
    private const BASE_URLS = [
        'dev' => 'http://kube-white-label-api.service.intradsm1.sdeconsul.csnzoo.com',
        'prod' => 'http://kube-white-label-api.service.intraiad1.consul.csnzoo.com'
    ];

    private const DEFAULT_ENV = 'dev';

    public const DEFAULT_NUMBER_RETRIES = 3;

    public const DEFAULT_CONNECTION_TIMEOUT = 3;


    private LoggerInterface $logger;
    private ?Client $httpClient;
    private string $baseUri;
    private int $maxRetries;
    private int $connectionTimeout;
    private string $environment;

    /**
     * ClientConfig constructor.
     *
     * @param  LoggerInterface  $logger
     * @param  int  $retriesCount
     * @param  int  $connectionTimeout
     * @param  string  $environment
     * @param  Client|null  $httpClient
     *
     * @throws \Exception
     */
    public function __construct(
        LoggerInterface $logger,
        int $retriesCount = self::DEFAULT_NUMBER_RETRIES,
        int $connectionTimeout = self::DEFAULT_CONNECTION_TIMEOUT,
        string $environment = self::DEFAULT_ENV,
        ?Client $httpClient = null
    ) {
        $this->logger = $logger;
        $this->maxRetries = $retriesCount;
        $this->connectionTimeout = $connectionTimeout;
        $this->environment = $environment;

        $this->setHttpClient($httpClient);
    }

    /**
     * @return LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * @return Client
     * @throws ConfigException
     */
    public function getHttpClient(): Client
    {
        if (empty($this->httpClient)) {
            throw new ConfigException('There\'s no HTTP client configured');
        }

        return $this->httpClient;
    }

    /**
     * @return string
     */
    public function getBaseUri(): string
    {
        return $this->baseUri;
    }

    /**
     * @param  Client|null  $httpClient
     * @throws \Exception
     */
    public function setHttpClient(?Client $httpClient): void
    {
        $this->httpClient = $this->setupHttpClient($this->environment, $httpClient);
    }

    /**
     * @param  string  $environment
     * @param  Client|null  $httpClient
     *
     * @return Client
     * @throws \Exception
     */
    private function setupHttpClient(string $environment, ?Client $httpClient)
    {
        if (empty($environment) && empty($httpClient)) {
            throw new \Exception('Either $environment or $httpClient must be specified');
        }

        if (!empty($environment) && !in_array($environment, array_keys(self::BASE_URLS), true)) {
            throw new \Exception('Invalid $environment set');
        } elseif (!empty($environment)) {
            $this->baseUri = self::BASE_URLS[$environment];
        }

        return HttpClientFactory::create($this->baseUri, $this->maxRetries, $this->connectionTimeout, $httpClient);
    }
}
