<?php
/**
 * Filter Request - user's input on the page to filter results
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool;

use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;

class Filter_Request {

  /**
   * @var array
   */
  private $marketing_categories = [];

  /**
   * @var array
   */
  private $suppliers = [];

  /**
   * @var array
   */
  private $manufacturers = [];

  /**
   * @var int
   */
  private $approval_status;

  /**
   * @var int
   */
  private $product_status;

  /**
   * @var array
   */
  private $skus = [];

  /**
   * @var int
   */
  private $batch_id = 0;

  /**
   * @var int
   */
  private $offset = 0;

  /**
   * @var int
   */
  private $length = 0;

  /**
   * @var int
   */
  private $region = 0;

  /**
   * @var array
   */
  private $brand_catalogs_allowed = [];

  /**
   * @var array
   */
  private $brand_catalogs_excluded = [];

  /**
   * @var int|null
   */
  private $sku_type = Curation_Item_Model::SKU_TYPE_SIMPLE;

  /**
   * Filter_Request constructor
   *
   * @param array $marketing_categories    Marketing categories array
   * @param array $suppliers               Supplier filter array
   * @param array $manufacturers           Manufacturer filter array
   * @param int   $product_status          Product status filter
   * @param int   $approval_status         Approval Status
   * @param array $skus                    Filter Skus value
   * @param int   $batch_id                Batch ID
   * @param int   $region                  Region
   * @param int   $sku_type                SKU type flag
   * @param array $brand_catalogs_allowed  Brand catalogs in which the SKU belongs
   * @param array $brand_catalogs_excluded Brand catalogs in which the SKU do NOT belong
   * @param int   $offset                  Offset
   * @param int   $length                  Length
   */
  public function __construct(
      $marketing_categories,
      $suppliers,
      $manufacturers,
      $product_status,
      $approval_status,
      array $skus,
      int $batch_id,
      int $region,
      $sku_type,
      array $brand_catalogs_allowed,
      array $brand_catalogs_excluded,
      int $offset,
      int $length
  ) {
    $this->marketing_categories    = $marketing_categories;
    $this->suppliers               = $suppliers;
    $this->manufacturers           = $manufacturers;
    $this->product_status          = $product_status;
    $this->approval_status         = $approval_status;
    $this->skus                    = $skus;
    $this->batch_id                = $batch_id;
    $this->region                  = $region;
    $this->sku_type                = $sku_type;
    $this->brand_catalogs_allowed  = $brand_catalogs_allowed;
    $this->brand_catalogs_excluded = $brand_catalogs_excluded;
    $this->offset                  = $offset;
    $this->length                  = $length;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Filter_Request
   */
  public function without_pagination() : Filter_Request {
    $that = clone $this;
    $that->offset = 0;
    $that->length = 0;

    return $that;
  }

  /**
   * @return array|string
   */
  public function get_marketing_categories() {
    return $this->marketing_categories;
  }

  /**
   * @return array|string
   */
  public function get_suppliers() {
    return $this->suppliers;
  }

  /**
   * Retrieves array of manufacturers
   *
   * @return array|string
   */
  public function get_manufacturers() {
    return $this->manufacturers;
  }

  /**
   * @return int
   */
  public function get_product_status() {
    return $this->product_status;
  }

  /**
   * @return int
   */
  public function get_approval_status() : int {
    return $this->approval_status;
  }

  /**
   * @return array
   */
  public function get_skus() : array {
    return $this->skus;
  }

  /**
   * @return int
   */
  public function get_batch_id() : int {
    return $this->batch_id;
  }

  /**
   * @return int
   */
  public function get_offset() : int {
    return $this->offset;
  }

  /**
   * @return int
   */
  public function get_length() : int {
    return $this->length;
  }

  /**
   * @return array
   */
  public function get_brand_catalogs_allowed() : array {
    return $this->brand_catalogs_allowed;
  }

  /**
   * @return array
   */
  public function get_brand_catalogs_excluded() : array {
    return $this->brand_catalogs_excluded;
  }

  /**
   * @return int
   */
  public function get_region() : int {
    return $this->region;
  }

  /**
   * @return int|null
   */
  public function get_sku_type() {
    return $this->sku_type;
  }
}
