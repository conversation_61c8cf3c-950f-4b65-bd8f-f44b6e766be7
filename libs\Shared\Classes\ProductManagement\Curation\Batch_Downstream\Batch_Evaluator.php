<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

use Psr\Log\LoggerInterface;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Batch_Evaluator {
  /**
   * @var Batch_Evaluation_Loader
   */
  private $batch_evaluation_loader;


  /**
   * @var Batch_Evaluator_Factory
   */
  private $ticket_creation_factory;

  /**
   * @var LoggerInterface
   */
  private $logger;

  /**
   * Batch_Evaluator constructor.
   *
   * @param Batch_Evaluation_Loader   $batch_evaluation_loader batch_evaluation_loader
   * @param Batch_Evaluator_Factory   $ticket_creation_factory Ticket Creation Factory
   * @param LoggerInterface $logger                  Logger
   */
  public function __construct(
      Batch_Evaluation_Loader $batch_evaluation_loader,
      Batch_Evaluator_Factory $ticket_creation_factory,
      LoggerInterface $logger
  ) {
    $this->batch_evaluation_loader = $batch_evaluation_loader;
    $this->ticket_creation_factory = $ticket_creation_factory;
    $this->logger                  = $logger;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch to be evaluated
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result
   */
  public function evaluate_batch(Batch $batch) : Batch_Evaluation_Result {
    $verification_item_ids  = $this->get_batch_verification_items_ids($batch);
    $batch_evaluation_items = $this->batch_evaluation_loader->load_evaluation_items_by_ids($verification_item_ids);

    $batched_skus_results = [];
    foreach ($batch_evaluation_items as $item) {
      /**
       * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku
       */
      $verified_batch_sku = $batch->get_sku($item->get_sku());

      if ($verified_batch_sku === null) { /** @phpstan-ignore-line */
        $this->logger->error(
            sprintf(
                'SKU "%s" does not exist in the batch %s',
                $item->get_sku(),
                $batch->get_id()
            )
        );

        continue;
      }

      $sku_evaluation_result = $this->evaluate_sku($item, $verified_batch_sku);

      $this->logger->info(
          sprintf(
              'SKU: %s; Is a kit parent: %s; QA status: %s',
              $verified_batch_sku->get_sku(),
              ($verified_batch_sku->is_kit_parent()) ? 'yes' : 'no',
              $item->get_qa_status_id()
          )
      );

      $batched_skus_results[] = $sku_evaluation_result;
    }

    $evaluation_status = $this->calculate_batch_status($batched_skus_results);
    $this->logger->info(sprintf('Batch %d. Status: %s', $batch->get_id(), (string)$evaluation_status));

    return new Batch_Evaluation_Result($batched_skus_results, $evaluation_status);
  }


  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch to be evaluated
   *
   * @return array
   */
  private function get_batch_verification_items_ids(Batch $batch) : array {
    $vi_ids = [];
    foreach ($batch->models as $item) {
      $vi_ids[] = $item->get_id();
    }

    return $vi_ids;
  }

  /**
   * This function will evaluate a sku of the batch
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Item_Model $evaluation_item    Evaluation item
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU                 $verified_batch_sku Verified batch sku object
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result
   */
  private function evaluate_sku(Batch_Evaluation_Item_Model $evaluation_item, Verified_Batch_SKU $verified_batch_sku) : Batched_SKU_Evaluation_Result {
    if ($evaluation_item->get_qa_status_id() === QA_Status_Object::APPROVED) {
      return $this->ticket_creation_factory->create_batched_sku_evaluation_result($evaluation_item->get_sku(), Batched_SKU_Evaluation_Result::REASON_SKU_APPROVED, QA_Status_Object::APPROVED);
    }

    if ($evaluation_item->get_qa_status_id() === QA_Status_Object::PENDING_APPROVAL) {
      return $this->ticket_creation_factory->create_batched_sku_evaluation_result($evaluation_item->get_sku(), Batched_SKU_Evaluation_Result::REASON_SKU_PENDING, QA_Status_Object::PENDING_APPROVAL);
    }

    if ($evaluation_item->get_qa_status_id() === QA_Status_Object::REJECTED) {
      return $this->ticket_creation_factory->create_batched_sku_evaluation_result($evaluation_item->get_sku(), Batched_SKU_Evaluation_Result::REASON_SKU_REJECTED, QA_Status_Object::REJECTED);
    }

    if ($verified_batch_sku->is_kit_parent()) {
      return $this->ticket_creation_factory->create_batched_sku_evaluation_result(
          $evaluation_item->get_sku(),
          Batched_SKU_Evaluation_Result::REASON_SKU_PARENT,
          QA_Status_Object::NOT_APPLICABLE
      );
    }

    return $this->ticket_creation_factory->create_batched_sku_evaluation_result($evaluation_item->get_sku(), Batched_SKU_Evaluation_Result::REASON_SKU_NOT_CURATED, QA_Status_Object::NOT_APPLICABLE);
  }

  /**
   * This function will calculate the status of the batch
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result[] $batched_skus_evaluation_results List of instances of the class Batched_SKU_Evaluation_Result
   *
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private function calculate_batch_status(array $batched_skus_evaluation_results) : Batch_Evaluation_Status {
    $approved_skus_counter     = 0;
    $not_approved_skus_counter = 0;


    foreach ($batched_skus_evaluation_results as $result) {
      if ($result->get_status() === QA_Status_Object::REJECTED) {
        return Batch_Evaluation_Status::has_rejected();
      }


      if ($result->get_status() === QA_Status_Object::APPROVED
          || ($result->get_status() === QA_Status_Object::NOT_APPLICABLE && $result->get_reason() === Batched_SKU_Evaluation_Result::REASON_SKU_PARENT)
      ) {
        $approved_skus_counter++;

        continue;
      }

      $not_approved_skus_counter++;
    }

    if ($approved_skus_counter > 0 && $not_approved_skus_counter > 0) {
      return Batch_Evaluation_Status::partial();
    }

    if ($approved_skus_counter > 0) {
      return Batch_Evaluation_Status::ready();
    }

    return Batch_Evaluation_Status::not_ready();
  }
}
