
[![View in Campground](https://admin.wayfair.com/d/applife-system-health/v1/badges/health/brand-workflows-curation-tool)](https://admin.wayfair.com/d/application-health-campaign-management-interface/repository/name/brand-workflows-curation-tool)
[![Build status](https://badge.buildkite.com/2cc7397bb0464bf4d0e967fb8be596531a71231c757bb4ecca.svg?branch=main)](https://buildkite.com/wayfair/brand-workflows-curation-tool)
# Brand Workflows Curation Tool

Curation Tool decoupled out of Monolith

This project is designed to be launched at DevBox.
If you want to launch it locally, additional work should be done (e.g. <PERSON>ie copy, etc.)

**Please change README.md as you see fit, it should include the following:**
* The business purpose of your application written so people outside your team can understand
* Links to any technical documentation you may have created outside of the project
* Contact info of the project owners (Email, slack channel, etc.)

## Next steps

Requirements (it's already available at your DevBox):

* make
* docker
* docker-compose
* vault

List of Credentials for this Application

* svc_brand_workflows_curation_tool_app
* curation_tool_client_id_for_white_label_nextgen_api
* curation_tool_client_for_white_label_nextgen_api
* ah-brand-workflows-curation-tool-talent-reader

List of Config Files for this Application

* wf-config.ini
* wf-config-dynamic.ini (optional)

Steps:

1. Clone this repo
2. Pull in the secrets `make pull-secrets`
   * Before it make sure that you have valid Vault access token in your `~/.vault-token` file
   * No line break should be there, only single line with the token
   * To receive such token execute the command `vault wf login`
   * If the token will not be populated to the file, manually copy the **auth.client_token** key valut to the file
3. Make sure that all secrets & config files were pulled in & then stop the container (e.g. launch `make stop-all` in other Terminal window)
4. (Optional) Add the next line to your realsync configuration(.realsync):
   `exclude = **/brand-workflows-curation-tool/vendor/**`
5. Launch your **Realsync** and make sure that all files were synced to the DevBox
6. The next steps might be executed locally or at the DevBox
7. Stop any running containers: `make stop-all`
8. Launch the service: `make`. It will:
   * Build & run everything
9. Verify the application is running http://<your-devbox>.c.wf-gcp-us-sds-prod.internal:8380/d/curation-tool/internal/ping
10. (Optional) Also, it might be useful to run `make composer` at your local device to have all libraries locally to be able to see them into your IDE
11. Then check is it working via Partners Home
   * Go to https://adminwayfaircom.csnzoo.com/ and make sure that you're logged in
   * Switch to your DevBox via dropdown at top left
   * Then go to https://partnerswayfaircom.csnzoo.com/ and make sure that your session was copied there
   * And finally go to https://partnerswayfaircom.csnzoo.com/d/curation-tool/internal/ping
12. Now we need to build the Frontend
13. First option is to do it locally & sync to the DevBox via Realsync
   * you need to have `yarn` installed
   * execute `yarn install` & `yarn build`
   * make sure that no errors are there
   * wait when all changes will be sync to the DevBox
14. Second option is to build the Frontend via docker
   * (not working right now, should be investigated)
   * it can be executed locally & files will be synced to DevBox via Realsync
   * if you'll execute it at DevBox, then you'll need to play with your Realsync config to exclude generated files from sync, because they might be overridden by your local one during next sync
   * execute `make build-frontend`
   * make sure that no errors are there
   * wait when all changes will be sync to the DevBox
15. Got to https://partnerswayfaircom.csnzoo.com/d/curation-tool/index?batch_id=2 and make sure that page is rendered
16. Happy Development !


## Deployment
- to SDE: With any change to any branch (even master/main), the buildkite pipeline will have a button to deploy to SDE on demand.
- to PROD: When any change is merged to master/main the button to unlock a deployment to production will appear.
Commit author will get email notification with each pipeline status, [Customizations to notifications in buildkite are possible](https://buildkite.com/docs/pipelines/notifications#slack-channel-and-direct-messages) in your [pipeline.yaml](./buildkite/pipeline.yaml).


## Development Guide
For more details on how to use this template to write your business code fast, check the [Development Guide](./docs/development-guide.md) that is included with this repo.


## Single-purpose Database Guide
- [What is SPDB?](https://infohub.corp.wayfair.com/display/ENG/SPDB+Overview)
- Follow [SPDB guide](./docs/db-guide.md) to connect to database.


## Links

Application Endpoints

| Environment | Datacenter | Endpoint |
| ----------- | ---------- | -------- |
| Prod        | `gke-prod-c2-iad1` | <https://partners.wayfair.com/d/brand-workflows-curation-tool> |
| SDE         | `gke-sdeprod-c1-dsm1`  | <https://partnerswayfair.csnzoo.com/d/brand-workflows-curation-tool> |

Application Dashboards

| Dashboard                            | Prod                                             | SDE                                             |
| ------------------------------------ | ------------------------------------------------ | ----------------------------------------------- |
| Datadog APM                          | [Link][datadog-prod]                             | [Link][datadog-sde]                             |
| Kubernetes: Deployment Status        | [Link][kubernetes-deployment-status-prod]        | [Link][kubernetes-deployment-status-sde]        |
| Kubernetes: NGINX Ingress Controller | [Link][kubernetes-nginx-ingress-controller-prod] | [Link][kubernetes-nginx-ingress-controller-sde] |

[datadog-prod]: https://app.datadoghq.com/apm/service/brand-workflows-curation-tool/?env=prod&hostGroup=iad1
[datadog-sde]: https://app.datadoghq.com/apm/service/brand-workflows-curation-tool/?env=sde&hostGroup=dsm1

[kubernetes-deployment-status-prod]: https://grafana.csnzoo.com/d/rfCUVOXik/kubernetes-deployment-status?orgId=2&refresh=5m&var-cluster_name=gke-prod-c2-iad1&var-namespace=brand-workflows-curation-tool&var-deployment=brand-workflows-curation-tool-http-dep
[kubernetes-deployment-status-sde]: https://devgrafana.csnzoo.com/d/rfCUVOXik/kubernetes-deployment-status?orgId=2&refresh=5m&var-cluster_name=gke-sdeprod-c1-dsm1&var-namespace=brand-workflows-curation-tool&var-deployment=brand-workflows-curation-tool-http-dep

[kubernetes-nginx-ingress-controller-prod]: https://grafana.csnzoo.com/d/nginx/kubernetes-nginx-ingress-controller?orgId=2&refresh=5m&var-cluster_name=gke-prod-c2-iad1&var-namespace=All&var-controller_class=nginx&var-controller=All&var-ingress=brand-workflows-curation-tool-ing
[kubernetes-nginx-ingress-controller-sde]: https://devgrafana.csnzoo.com/d/nginx/kubernetes-nginx-ingress-controller?orgId=2&refresh=5m&var-cluster_name=gke-sdeprod-c1-dsm1&var-namespace=All&var-controller_class=nginx&var-controller=All&var-ingress=brand-workflows-curation-tool-ing
