<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface;

class Completion_QA_Notify_Curation_Rejected {
  private const FROM_EMAIL_ADDRESS = '<EMAIL>';
  private const FROM_NAME          = 'Batch QA';

  /**
   * @var \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface
   */
  private $mailer;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service
   */
  private $employeeService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service
   */
  private $qaBatchSkuService;

  private string $baseUri;

  /**
   * Email_Notification constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface                                      $mailer               Mailer Interface
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service     $employeeService      Completion_Employee_Service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service $qaRejectedSkuService Completion_QA_Rejected_SKU_Service
   * @param string                                                                                            $baseUri              Product Base URI
   */
  public function __construct(
      Mailer_Interface $mailer,
      Completion_Employee_Service $employeeService,
      Completion_QA_Batch_SKU_Service $qaRejectedSkuService,
      string $baseUri
  ) {
    $this->mailer            = $mailer;
    $this->employeeService   = $employeeService;
    $this->qaBatchSkuService = $qaRejectedSkuService;
    $this->baseUri           = $baseUri;
  }

  /**
   * @param int $batchId            Batch ID
   * @param int $assignedEmployeeId Assigned Employee ID
   * @param int $qaEmployeeId       QA Employee ID
   *
   * @return void
   */
  public function send(int $batchId, int $assignedEmployeeId, int $qaEmployeeId) {
    $email = $this->mailer->create_message();

    $assigned = $this->employeeService->get($assignedEmployeeId);
    $qa       = $this->employeeService->get($qaEmployeeId);

    $email
        ->set_html_body($this->get_body($batchId, $assigned->getName(), $qa->getName()))
        ->set_subject($this->get_subject($batchId))
        ->set_sender(self::FROM_EMAIL_ADDRESS, self::FROM_NAME)
        ->add_recipient($assigned->getEmail(), $assigned->getName());

    $this->mailer->send($email);
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return string
   */
  private function get_subject(int $batchId) : string {
    return sprintf('%d has rejected SKUs in curation QA', $batchId);
  }

  /**
   * @param int    $batchId      Batch ID
   * @param string $assignedName Assigned Name
   * @param string $qaName       QA Name
   *
   * @return string
   */
  private function get_body(int $batchId, string $assignedName, string $qaName) : string {
    $body = '<p>Hi %s,</p>
             <p>Curation decisions on Batch %d were rejected in QA by %s. Please review your curation and resave any changes.</p>
             <p>%s</p>
             <p>Link: %s</p>';

    $link = sprintf('%s/d/curation-tool/index?batch_id=%s', $this->baseUri, $batchId);

    return sprintf(
        $body,
        $assignedName,
        $batchId,
        $qaName,
        $this->get_rejected_skus_message($batchId),
        $link
    );
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return string
   */
  private function get_rejected_skus_message(int $batchId) : string {
    $skus         = $this->qaBatchSkuService->getRejectedSkus($batchId);
    $rowDelimiter = '<br />';

    $message = '';
    foreach ($skus as $sku) {
      $message .= sprintf('%s: %s%s', $sku->get_sku(), $sku->get_message(), $rowDelimiter);
    }

    return $message;
  }
}
