<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api\White_Label_Api_Processor;
use WF\FeatureToggle\FeatureTogglesInterface;

final class Downstream_Process_Factory {

  private const FT_WHITE_LABEL_API_PROCESS = 'enable_curation_white_label_api_downstream';

  /**
   * @var Legacy_White_Label_Processor
   */
  private $legacy_wl_processor;

  /**
   * @var White_Label_Api_Processor
   */
  private $api_wl_processor;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @param Legacy_White_Label_Processor $legacy_wl_processor Legacy WL Processor
   * @param White_Label_Api_Processor    $api_wl_processor    API WL Processor
   * @param FeatureTogglesInterface      $featureToggles      Feature Toggles
   */
  public function __construct(
    Legacy_White_Label_Processor $legacy_wl_processor,
    White_Label_Api_Processor $api_wl_processor,
    FeatureTogglesInterface $featureToggles) {
    $this->legacy_wl_processor = $legacy_wl_processor;
    $this->api_wl_processor    = $api_wl_processor;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @return Downstream_Processor
   */
  public function get_processor() : Downstream_Processor {
    if ($this->featureToggles->isEnabled(self::FT_WHITE_LABEL_API_PROCESS)) {
      return $this->api_wl_processor;
    }

    return $this->legacy_wl_processor;
  }
}
