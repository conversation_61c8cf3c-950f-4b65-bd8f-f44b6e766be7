<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities;

class Completion_QA_Batch_Status {
  /**
   * @var bool
   */
  private $pending;

  /**
   * @var bool
   */
  private $approved;

  /**
   * @return bool
   */
  public function is_pending() : bool {
    return $this->pending;
  }

  /**
   * @return bool
   */
  public function is_approved() : bool {
    return $this->approved;
  }

  /**
   * @param bool $pending  Pending
   * @param bool $approved Approved
   */
  public function __construct(bool $pending, bool $approved) {
    $this->pending  = $pending;
    $this->approved = $approved;
  }
}
