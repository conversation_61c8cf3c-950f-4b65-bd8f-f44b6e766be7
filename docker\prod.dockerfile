# To install custom extensions in the Dockerfile use wf-install-extension
#   example: RUN wf-install-extension imagick

ARG BK_HTTPS_PROXY
ARG HTTPS_PROXY=${BK_HTTPS_PROXY}

FROM wayfair/node:12.22.6-2023.1.10 AS frontend

WORKDIR /app
COPY --chown=80:80 ./ /app/
ENV SASS_BINARY_SITE https://artifactorybase.service.csnzoo.com/artifactory/list/resources-general/node-binaries/node-sass/

#
# node-sass requires python2, which is no longer bundled with base docker image
# param "--setopt=tsflags=nodocs" is used to restrict installation of documentation, thanks to this we save space and time
# param "-y" will not prompt confirmation during yum install
#
USER root
RUN yum --setopt=tsflags=nodocs -y install python2 \
    && rm -rf /var/cache/yum/* \
    && yum clean all

RUN echo -n 'Yarn version and config' \
    && yarn --version \
    && yarn config set registry https://artifactorybase.service.csnzoo.com/artifactory/api/npm/npm \
    && yarn config get registry \
    && echo -n 'Installing dependencies' \
    && yarn install --ignore-optional \
    && yarn build

## Build Base Image
FROM wayfair/php-fpm:7.4.27-2023.01.16 as base

LABEL \
  com.wayfair.app=brand-workflows-curation-tool \
  com.wayfair.description="Curation Tool"

RUN wf-install-extension pdo pdo-sqlsrv pdo-pgsql

## Build Prod (k8s dev and k8s prod)
FROM base as prod

COPY --from=frontend /app/public/ /app/public/

COPY --chown=80:80 ./ /app/
RUN mkdir /app/var/ \
      && chmod 777 /app/var/ \
      && wf-install-composer --2 \
      && cd /app/ \
      && composer install -n --no-dev --classmap-authoritative \
      && rm /usr/local/bin/composer
