<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Curation_Decision_Style_Rejection_Reason extends AbstractEnumeration implements \JsonSerializable {
  /**
   * csn_product.dbo.tblplVerificationDecisionSource
   */

  /**
   * @var array
   */
  private static $values = [
      'Color'               => 1,
      'Design Detail'       => 2,
      'Material'            => 3,
      'Pattern'             => 4,
      'Shape or Silhouette' => 5,
      'Theme'               => 6,
      'Other'               => 7,
  ];

  /**
   * @var string|null
   */
  private $label;

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function color() : self {
    return self::get_value_for_option(self::$values['Color']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function design() : self {
    return self::get_value_for_option(self::$values['Design Detail']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function material() : self {
    return self::get_value_for_option(self::$values['Material']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function pattern() : self {
    return self::get_value_for_option(self::$values['Pattern']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function shape() : self {
    return self::get_value_for_option(self::$values['Shape or Silhouette']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function theme() : self {
    return self::get_value_for_option(self::$values['Theme']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   */
  public static function other() : self {
    return self::get_value_for_option(self::$values['Other']); /** @phpstan-ignore-line */
  }


  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Style_Rejection_Reason
   *
   * @throws \InvalidArgumentException
   */
  public static function create(int $option) : self {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $option);
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * creates a Curation_Decision_Style_Rejection_Reason for the input $label (case insensitive)
   *
   * @param string $label the text value from $values
   *
   * @return self
   *
   * @throws \InvalidArgumentException
   */
  public static function create_from_label(string $label) : self {
    if (!in_array(strtolower($label), array_map('strtolower', array_keys(self::$values)))) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $label);
    }

    $lcValues = array_change_key_case(self::$values, CASE_LOWER);

    return self::get_value_for_option($lcValues[strtolower($label)]); /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function label() : string {
    // if the label is requested for the first time, compute it and save the result
    if ($this->label === null) {
      $this->label = array_search((int)$this->value(), self::$values, true);
    }

    return $this->label;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'label' => $this->label(),
        'value' => $this->value()
    ];
  }
}
