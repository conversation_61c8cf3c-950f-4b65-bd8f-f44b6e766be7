<?php

declare(strict_types = 1);

/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Curation_Batch_Automation_Type extends AbstractEnumeration {
  /**
   * @var array
   */
  private static $values = [
    'DS Model'       => 1,
    'Automapped'     => 2,
  ];

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type
   */
  public static function ds_model() : self {
    return self::get_value_for_option(self::$values['DS Model']); /** @phpstan-ignore-line */
  }

  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Batch_Automation_Type
   */
  public static function automapped() : self {
    return self::get_value_for_option(self::$values['Automapped']); /** @phpstan-ignore-line */
  }

  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return \App\Domain\Enum\AbstractEnumeration
   *
   * @throws \InvalidArgumentException
   */
  public static function create(int $option) : parent {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $option);
    }

    return self::get_value_for_option($option);
  }
}
