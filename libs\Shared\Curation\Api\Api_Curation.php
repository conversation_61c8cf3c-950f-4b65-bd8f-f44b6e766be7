<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;

interface Api_Curation {

  /**
   * @param string $sku A SKU to check
   *
   * @return bool
   *
   * @throws API_Request_Exception
   */
  public function is_eligible(string $sku) : bool;

  /**
   * @param string[] $skus list of SKUS to check
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function is_eligible_batch(array $skus) : array;
}
