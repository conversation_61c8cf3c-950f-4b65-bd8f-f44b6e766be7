<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Rebrand_Project_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
     */
    private $postgresql_dao;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project_Loader
     */
    private $rebrand_project_loader;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $feature_toggle;


    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->postgresql_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);
        $this->feature_toggle = $this->prophesize(FeatureTogglesInterface::class);

        $this->rebrand_project_loader = new Rebrand_Project_Loader(
            $this->dao->reveal(),
            $this->postgresql_dao->reveal(),
            $this->feature_toggle->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_rebrand_project_feature_toggle_on()
    {
        $expected_result = [
            'id' => '1',
            'name' => 'test_rebrand'
        ];
        $this->feature_toggle->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $batch_id = 1;
        $this->postgresql_dao->get_rebrand_project_for_batch(Argument::cetera())->willReturn($expected_result);
        $result = $this->rebrand_project_loader->getRebrandProject($batch_id);
        $this->assertEquals($expected_result, $result);
    }
}
