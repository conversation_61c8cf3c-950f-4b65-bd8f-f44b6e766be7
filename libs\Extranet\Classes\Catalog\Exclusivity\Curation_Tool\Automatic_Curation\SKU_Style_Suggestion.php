<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class SKU_Style_Suggestion {
  /**
   * @var string
   */
  private $sku;

  /**
   * @var float|null
   */
  private $threshold;

  /**
   * @var Style_Suggestion[]
   */
  private $suggestions;

  /**
   * SKU_Style_Suggestion constructor.
   *
   * @param string             $sku         SKU
   * @param float              $threshold   Threshold value
   * @param Style_Suggestion[] $suggestions Style Suggestions
   */
  public function __construct(string $sku, ?float $threshold, array $suggestions) {
    $this->sku         = $sku;
    $this->threshold   = $threshold;
    $this->suggestions = $suggestions;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }


  /**
   * @return float|null
   */
  public function get_threshold() : ?float {
    return $this->threshold !== null ? round($this->threshold, 2) : null;
  }

  /**
   * @return Style_Suggestion[]
   */
  public function get_suggestions() : array {
    return $this->suggestions;
  }

}
