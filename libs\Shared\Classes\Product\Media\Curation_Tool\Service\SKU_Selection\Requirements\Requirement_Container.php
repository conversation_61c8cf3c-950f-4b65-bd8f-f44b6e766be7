<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

class Requirement_Container {

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\SKU_Eligibility_Requirement_Interface
   */
  private $eligibility_requirement;

  /**
   * @var  \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Sku_Right_Product_Status_Requirement_Interface
   */
  private $right_product_status_requirement;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Master_Core_Class_Requirement_Interface
   */
  private $master_core_class_requirement;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Requirement_Interface
   */
  private $holdout_manufacturer_requirement;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Perigold_Only_Requirement_Interface
   */
  private $perigold_only_requirement;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Part_Requirement_Interface
   */
  private $holdout_manufacturer_part_requirement;


  /**
   * Curation_Request_SKU_Source_Model constructor.
   *
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\SKU_Eligibility_Requirement_Interface           $eligibility_requirement               SKU Eligibility Spec
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Sku_Right_Product_Status_Requirement_Interface  $right_product_status_requirement      Product Status Spec
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Master_Core_Class_Requirement_Interface         $master_core_class_requirement         Master Core Class Req
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Requirement_Interface      $holdout_manufacturer_requirement      Holdout Manufacturer Req
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Perigold_Only_Requirement_Interface             $perigold_only_requirement             Perigold Only Requirement
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Part_Requirement_Interface $holdout_manufacturer_part_requirement Holdout Manufacturer Part Req
   *
   * @throws \Exception
   */
  public function __construct(
      SKU_Eligibility_Requirement_Interface $eligibility_requirement,
      Sku_Right_Product_Status_Requirement_Interface $right_product_status_requirement,
      Master_Core_Class_Requirement_Interface $master_core_class_requirement,
      Holdout_Manufacturer_Requirement_Interface $holdout_manufacturer_requirement,
      Perigold_Only_Requirement_Interface $perigold_only_requirement,
      Holdout_Manufacturer_Part_Requirement_Interface $holdout_manufacturer_part_requirement
  ) {
    $this->eligibility_requirement                = $eligibility_requirement;
    $this->right_product_status_requirement       = $right_product_status_requirement;
    $this->master_core_class_requirement          = $master_core_class_requirement;
    $this->holdout_manufacturer_requirement       = $holdout_manufacturer_requirement;
    $this->perigold_only_requirement              = $perigold_only_requirement;
    $this->holdout_manufacturer_part_requirement  = $holdout_manufacturer_part_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\SKU_Eligibility_Requirement_Interface
   */
  public function get_eligibility_requirement() : SKU_Eligibility_Requirement_Interface {
    return $this->eligibility_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Sku_Right_Product_Status_Requirement_Interface
   */
  public function get_right_product_status_requirement() : Sku_Right_Product_Status_Requirement_Interface {
    return $this->right_product_status_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Master_Core_Class_Requirement_Interface
   */
  public function get_master_core_class_requirement() : Master_Core_Class_Requirement_Interface {
    return $this->master_core_class_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Requirement_Interface
   */
  public function get_holdout_manufacturer_requirement() : Holdout_Manufacturer_Requirement_Interface {
    return $this->holdout_manufacturer_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Perigold_Only_Requirement_Interface
   */
  public function get_perigold_only_requirement() : Perigold_Only_Requirement_Interface {
    return $this->perigold_only_requirement;
  }

  /**
   * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Holdout_Manufacturer_Part_Requirement_Interface
   */
  public function get_holdout_manufacturer_part_requirement() : Holdout_Manufacturer_Part_Requirement_Interface {
    return $this->holdout_manufacturer_part_requirement;
  }
}
