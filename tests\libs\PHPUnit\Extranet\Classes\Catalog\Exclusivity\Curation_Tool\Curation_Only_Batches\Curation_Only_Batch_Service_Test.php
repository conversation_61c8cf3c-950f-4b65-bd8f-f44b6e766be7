<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;

class Curation_Only_Batch_Service_Test extends TestCase
{
    use ProphecyTrait;


    /**
     * @var Curation_Only_Batch_Service_Storage|\Prophecy\Prophecy\ObjectProphecy
     */
    private $storage;


    /**
     * @var Curation_Only_Batch_Service_Postgres_Storage|\Prophecy\Prophecy\ObjectProphecy
     */
    private $postgres_storage;

    /**
     * @var FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->storage = $this->prophesize(Curation_Only_Batch_Service_Storage::class);
        $this->postgresql_dao = $this->prophesize(Curation_Only_Batch_Service_Postgres_Storage::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);


        $this->subject = new Curation_Only_Batch_Service(
            $this->storage->reveal(),
            $this->postgresql_dao->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function mark_as_set_downstream_feature_toggle_on()
    {
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->set_verification_id_for_skus(1, 1);
        $this->storage->mark_batch_as_sent(1, 1, false);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->create_verification_id()->willReturn(1);
        $this->postgresql_dao->set_verification_id_for_skus(1, 1);
        $this->postgresql_dao->mark_batch_as_sent(1, 1, false);
        $this->subject->mark_as_set_downstream(1);
    }

    /**
     * @test
     *
     * @return void
     */
    public function mark_as_set_downstream_feature_toggle_off()
    {
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->set_verification_id_for_skus(1, 1);
        $this->storage->mark_batch_as_sent(1, 1, false);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->create_verification_id()->willReturn(1);
        $this->postgresql_dao->set_verification_id_for_skus(1, 1);
        $this->postgresql_dao->mark_batch_as_sent(1, 1, false);
        $this->subject->mark_as_set_downstream(1);
    }
}
