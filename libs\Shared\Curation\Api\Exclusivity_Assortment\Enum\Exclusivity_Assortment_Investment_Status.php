<?php
declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment\Enum;

use App\Domain\Enum\AbstractEnumeration;

final class Exclusivity_Assortment_Investment_Status extends AbstractEnumeration {
  /**
   * csn_merch_tool..tblplAssortmentInvestmentStatus
   */

  /**
   * @var array
   */
  private static $values = [
      'Approved' => 1,
      'Imported' => 2
  ];

  /**
   * @var string|null
   */
  private $label;


  /**
   * @return self
   */
  public static function approved() : self {
    return self::get_value_for_option(self::$values['Approved']); /** @phpstan-ignore-line */
  }

  /**
   * @return self
   */
  public static function imported() : self {
    return self::get_value_for_option(self::$values['Imported']); /** @phpstan-ignore-line */
  }

  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return self
   *
   * @throws \InvalidArgumentException
   */
  public static function create(int $option) : self {
    if (!in_array($option, self::$values)) {
      throw new \InvalidArgumentException('Value for selected option is not supported: ' . $option);
    }

    return self::get_value_for_option($option); /** @phpstan-ignore-line */
  }

  /**
   * @return string
   */
  public function label() : string {
    // if the label is requested for the first time, compute it and save the result
    if ($this->label === null) {
      $this->label = array_search((int)$this->value(), self::$values, true);
    }

    return $this->label;
  }
}
