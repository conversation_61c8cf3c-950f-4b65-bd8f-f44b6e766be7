#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page.js

msgid "Curation"
msgstr "Curation"

msgid "Reviewed"
msgstr "reviewed"

msgid "QA"
msgstr "QA"

msgid "CompleteCuration"
msgstr "Complete Curation"

# @skipTranslationCheck
msgid "failedToCompleteCurationReason"
msgstr "Failed to complete curation! {reason}"

msgid "CurationTool"
msgstr "Curation Tool"

msgid "QACurationStatusCurationStatus"
msgstr "Curation Status"

msgid "QACurationStatusQAStatus"
msgstr "QA Status"

msgid "QACurationStatusFilterPlacholder"
msgstr "Select a status..."

msgid "QACurationStatusFilterOptionSavedDecision"
msgstr "Saved decision"

msgid "QACurationStatusFilterOptionUpdatedDecision"
msgstr "Updated decision"

msgid "batchCompleted"
msgstr "Batch Completed"

msgid "qaApprovedBy"
msgstr "QA Approved by"

msgid "XOutYSkusStatus"
msgstr "{skusSavedCount} out {skusTotalCount} SKUs {status}"

# @skipTranslationCheck
msgid "batchDetailsTitle"
msgstr "Batch Details"

# @skipTranslationCheck
msgid "batchDetailsStatusTitle"
msgstr "Batch Status:"

# @skipTranslationCheck
msgid "batchDetailsId"
msgstr "Batch ID:"

# @skipTranslationCheck
msgid "batchDetailsType"
msgstr "Type:"

# @skipTranslationCheck
msgid "batchDetailsCreated"
msgstr "Batch Created:"

# @skipTranslationCheck
msgid "batchDetailsCurator"
msgstr "Curator:"

msgid "batchProgressTitle"
msgstr "Batch Progress"

msgid "CurationIsCompletedAndRefresh"
msgstr "Curation is completed. The Page will refresh."

msgid "ThereAreMoreSKUsToCuratedDotThePageWillRefreshDot"
msgstr "There are more SKUs to curated. The page will refresh."

msgid "FailedToCompleteCuration"
msgstr "Failed to complete curation!"

msgid "CurationBatchManagementPageTitle"
msgstr "Curation Batch Management"

msgid "toolTitleText"
msgstr "Curation Tool"

msgid "primaryStyleWithDSSuggestions"
msgstr "Primary Style (# are suggestions)"

msgid "primaryStyleDropdownLabel"
msgstr "Primary Style"

msgid "secondarySubstyleWithDSSuggestions"
msgstr "Secondary Style (# are suggestions)"

msgid "secondaryStyleDropdownLabel"
msgstr "Secondary Style"

msgid "decisionPrimaryStyleText"
msgstr "Primary Style: {styleText}"

msgid "decisionSecondaryStyleText"
msgstr "Secondary Style: {styleText}"

msgid "removeFromKitscoConfirmationModalTitle"
msgstr "Remove from Kitsco confirmation"

msgid "removeFromKitscoConfirmationModalQuestion"
msgstr "Are you sure you want to remove the SKU from Kitsco?"

msgid "removeFromKitscoConfirmationModalSku"
msgstr "SKU: {sku}"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_page_header.js

msgid "qAPageHeaderApproveAll"
msgstr "Approve all"

msgid "qAPageHeaderSku"
msgstr "SKU"

msgid "qAPageHeaderName"
msgstr "Name"

msgid "qAPageHeaderManufacturer"
msgstr "Manufacturer"

msgid "qAPageHeaderClass"
msgstr "Class"

msgid "qAPageHeaderSupplier"
msgstr "Supplier"

msgid "qAPageHeaderPrice"
msgstr "Sale Price"

msgid "qAPageHeaderImage"
msgstr "Image"

msgid "qAPageHeaderCurationStatus"
msgstr "Curation Status"

msgid "qAPageHeaderActions"
msgstr "Actions"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_manufacturer.js

# @skipTranslationCheck
msgid "EBAutoWhitelabel"
msgstr "EB Assortment Decision: Auto whitelabel"

#merch-eng-curation-owners-fyi-/resources/st4/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_bulk_action_section.js

# @skipTranslationCheck
msgid "QABulkActionTitle"
msgstr "Bulk Actions"

# @skipTranslationCheck
msgid "QABulkActionSelectAllCheckbox"
msgstr "Select all SKUs"

# @skipTranslationCheck
msgid "QABulkActionDropdownReasonLabel"
msgstr "Reason"

# @skipTranslationCheck
msgid "QABulkActionDropdownReasonPlaceholderText"
msgstr "Select reason"

# @skipTranslationCheck
msgid "QABulkActionDropdownRejectionNotesPlaceholderText"
msgstr "Enter notes"

# @skipTranslationCheck
msgid "QABulkActionDropdownRejectionNotesLabel"
msgstr "Notes"

# @skipTranslationCheck
msgid "QABulkActionRejectButton"
msgstr "Reject selected"

# @skipTranslationCheck
msgid "QABulkActionApproveButton"
msgstr "Approve selected"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_save_column.js
msgid "QASaveColumnUpdated"
msgstr "Updated"
msgid "QASaveColumnUpdate"
msgstr "Update"
msgid "QASaveColumnChange"
msgstr "Change"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page_batch_search.js

msgid "CurationPageBatchSearchBatchIDLabel"
msgstr "Batch ID"
msgid "CurationPageBatchSearchBatchID"
msgstr "Batch ID"
msgid "CurationPageBatchSearchSearchButton"
msgstr "Search"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page_header.js
msgid "Sku"
msgstr "SKU"
msgid "SaveAll"
msgstr "Save all"
msgid "Name"
msgstr "Name"
msgid "Manufacturer"
msgstr "Manufacturer"
msgid "Class"
msgstr "Class"
msgid "Supplier"
msgstr "Supplier"
msgid "Price"
msgstr "Sale Price"
msgid "Image"
msgstr "Image"
msgid "ExclusionReason"
msgstr "Exclusion Reason"
msgid "FinalStyle"
msgstr "Final Style"
msgid "Actions"
msgstr "Actions"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page_save_all_confirmation_modal.js

msgid "CurationToolSaveAllConfirmationModalTitle"
msgstr "Save all confirmation"
msgid "CurationToolSaveAllSelectedConfirmationModalText"
msgstr "The following SKUs have suggested primary and/or secondary styles, it is recommended to curate them individually."
msgid "CurationToolSaveAllConfirmationModalSkus"
msgstr "SKU(s): {skus}"
msgid "CurationToolSaveAllConfirmationModalQuestion"
msgstr "Are you sure you want to save all selected SKUs?"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page_section.js

msgid "SharedComponents"
msgstr "Shared Components"
msgid "Kits"
msgstr "Kits"
msgid "ContextSkus"
msgstr "Context SKUs"
msgid "CandidateSkus"
msgstr "Candidate SKUs"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_page_suggested_style_feedback_modal.js

msgid "suggestedStyleFeedbackModalFieldRequired"
msgstr "Field cannot be blank"
msgid "suggestedStyleFeedbackModalTitle"
msgstr "Suggested Style Feedback"
# The following two records are the same and the first one is only kept since php and resources don't deply at the same time
msgid "suggestedStyleOrSubstyleFeedbackModalExplanation"
msgstr "You have selected primary and/or secondary styles for the following skus that are not part of the suggested list. To improve future style suggestions, please explain your decision."
msgid "suggestedPrimaryStyleOrSecondaryStyleFeedbackModalExplanation"
msgstr "You have selected primary and/or secondary styles for the following skus that are not part of the suggested list. To improve future style suggestions, please explain your decision."
msgid "suggestedStyleFeedbackModalSkus"
msgstr "SKU(s): {skus}"
msgid "suggestedStyleFeedbackModalReason"
msgstr "Reason"
msgid "suggestedStyleFeedbackModalNotes"
msgstr "Notes"
msgid "suggestedStyleFeedbackModalSubmitLabel"
msgstr "Submit"
msgid "suggestedStyleFeedbackModalCancelLabel"
msgstr "Cancel"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_shared_sku.js

msgid "KitParents"
msgstr "Kit Parents"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_shared_sku_save.js

msgid "Kitsco"
msgstr "Kitsco"
msgid "RemoveFromKitsco"
msgstr "Remove from Kitsco"
msgid "SaveAsKitsco"
msgstr "Save As Kitsco"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku.js

msgid "BrandCatalogName"
msgstr "Brand Catalog:"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_code.js

msgid "curationSkuPredictedWinner"
msgstr "Predicted Winner"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_decision.js

msgid "CurationSkuDecisionWarningWrongContinent"
msgstr "This SKU is in the wrong continent"
msgid "SkuRecentlyClonedMessage"
msgstr "This SKU was cloned in the last  days"
msgid "excludeFromWLDropdownLabel"
msgstr "Exclude from WL"
msgid "ClonedOnDate"
msgstr "Cloned on {lastCloneDate}"
msgid "alertMoveToHeaderBrand"
msgstr "EB Assortment Decision: Move to Header Brand"
msgid "alertMoveToTailBrand"
msgstr "EB Assortment Decision: Move to Tail Brand"
msgid "priceTierDropdownLabel"
msgstr "Price Tier"
msgid "brandDropdownLabel"
msgstr "Brand"
msgid "granularStyleDropdownLabel"
msgstr "Granular Style"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_manufacturer.js

msgid "EBFlagshipBrandLabel"
msgstr "EB - Flagship Brand"
msgid "EBTailBrandLabel"
msgstr "EB - Tail Brand"
msgid "siteWhiteLabelBrandLabel"
msgstr "Site White Label Brand"
msgid "categoryBrandLabel"
msgstr "Category Brand"
msgid "holdOutManufacturerLabel"
msgstr "Hold-Out Manufacturer"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_name.js

msgid "ViewPDP"
msgstr "View PDP"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_price.js

msgid "CurationSkuPriceValue"
msgstr "${thispropsprice}"
msgid "PriceOptions"
msgstr "Price Options"
msgid "NumberOfPriceOptionsExceededMessage"
msgstr "The number of price options have exceeded the allowed number of rows to display."

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_rejected_info_tooltip.js

msgid "CurationSkuRejectedInfoTooltipRejectedAtXByY"
msgstr "Rejected at {rejectedAt} by {rejectedBy}"
msgid "CurationSkuRejectedInfoTooltipRejectedNote"
msgstr "Note:"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_save.js

msgid "Save"
msgstr "Save"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_saved_info_tooltip.js

msgid "SaveAtXByY"
msgstr "Saved at {savedAt} by {savedBy}"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_supplier_filter.js

msgid "CurationSupplierFilterLabel"
msgstr "Filter by Supplier"
msgid "CurationSupplierFilterPlaceholder"
msgstr "Begin typing to search..."

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_tool_constants.js

msgid "FailedToSaveDataExclamationPoint"
msgstr "Failed to save data!"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_tool_page_header.js

msgid "CurationPageBatchBatchXX"
msgstr "Batch {batchId}"
msgid "CurationBatchQaPageRebrandProjectX"
msgstr "Rebrand Project: {rebrandProjectname}"
msgid "CurationPageHeaderExpandAll"
msgstr "Expand all"
msgid "CurationPageHeaderCollapseAll"
msgstr "Collapse all"
msgid "Complete"
msgstr "Complete"
msgid "CompleteCuration"
msgstr "Complete Curation"
msgid "QAPageBatchCompleteQA"
msgstr "QA Complete"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_tool_progress_bar.js

msgid "XOfYSkus"
msgstr "{savedCount} of {totalCount} SKUs"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_curation_style_decision_note.js

msgid "qaDecisionNotesLabelText"
msgstr "Notes:"
msgid "qaDecisionNoteIconTitleText"
msgstr "Decision note"


#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_curation_sku_decision.js

msgid "decisionPrimaryStyleText"
msgstr "Primary Style: {styleText}"
msgid "decisionSecondaryStyleText"
msgstr "Secondary Style: {subStyleText}"
msgid "decisionExclusionReasonText"
msgstr "Excluded: {excludedReason}"
msgid "decisionPriceTierText"
msgstr "Price Tier: {priceTier}"
msgid "decisionBrandText"
msgstr "Brand: {brandText}"
msgid "decisionGranularstyleText"
msgstr "Granular Style: {granularStyle}"
msgid "decisionMatchedStyleSuggestionText"
msgstr "Suggested style matched: Yes"
msgid "decisionNotMatchedStyleSuggestionText"
msgstr "Suggested style matched: No"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_curation_status_filter.js

msgid "QACurationStatusFilterOptionPendingDecision"
msgstr "Pending decision"
msgid "QACurationStatusFilterOptionPendingDecisionWithSuggestedStyles"
msgstr "Pending decision (only skus with suggested styles)"
msgid "QACurationStatusFilterOptionPendingDecisionWithoutSuggestedStyle"
msgstr "Pending decision (only skus without suggested styles)"
msgid "QACurationStatusFilterOptionApproved"
msgstr "QA Approved"
msgid "QACurationStatusFilterOptionRejected"
msgstr "QA Rejected"
msgid "QACurationStatusCurationQAStatus"
msgstr "Curation/QA Status"
msgid "QACurationStatusFilterPlacholder"
msgstr "Select QA status..."

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_curation_style_decision_note.js

msgid "qaDecisionNotesLabelText"
msgstr "Notes:"
msgid "extranetebCurationqaDecisionNoteIconTitleText"
msgstr "Decision note"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_page.js

msgid "QAIsCompletedAndRefreshed"
msgstr "QA is completed. The Page will refresh."
msgid "ThereAreMoreSKUsToQAedDotThePageWillRefreshDot"
msgstr "There are more SKUs to QA or downstream failed. The page will refresh."
msgid "FailedToCompleteQAExclamationSign"
msgstr "Failed to complete QA!"
msgid "QAPageCompletedTitle"
msgstr "Curation Completed"
msgid "QAPageTitle"
msgstr "Curation QA"

# @skipTranslationCheck
msgid "failedToCompleteQAReason"
msgstr "Failed to complete QA! {reason}"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_page_header.js

msgid "QAPageHeaderApproveAll"
msgstr "Approve all"
msgid "QAPageHeaderSku"
msgstr "SKU"
msgid "QAPageHeaderName"
msgstr "Name"
msgid "QAPageHeaderManufacturer"
msgstr "Manufacturer"
msgid "QAPageHeaderClass"
msgstr "Class"
msgid "QAPageHeaderSupplier"
msgstr "Supplier"
msgid "QAPageHeaderPrice"
msgstr "Sale Price"
msgid "QAPageHeaderImage"
msgstr "Image"
msgid "QAPageHeaderCurationStatus"
msgstr "Curation Status"
msgid "QAPageHeaderActions"
msgstr "Actions"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_page_section.js

msgid "QAPageSubsectionsSharedComponents"
msgstr "Shared Components"
msgid "QAPageSubsectionsKits"
msgstr "Kits"
msgid "QAPageSubsectionsContextSkus"
msgstr "Context SKUs"
msgid "QAPageSubsectionsCandidateSkus"
msgstr "Candidate SKUs"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_save_column.js

msgid "QASaveColumnApproved"
msgstr "Approved"
msgid "QASaveColumnRejected"
msgstr "Rejected"
msgid "QASaveColumnApprove"
msgstr "Approve"
msgid "QASaveColumnReject"
msgstr "Reject"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_shared_sku_row.js

msgid "QAKitComponentKitscoStatusRemovedFromKitsco"
msgstr "Removed From Kitsco"
msgid "QAKitComponentKitscoStatusSavedAsKitsco"
msgstr "Saved As Kitsco"
msgid "QASharedSkuRowKitParents"
msgstr "Kit Parents"

#merch-eng-curation-owners-fyi-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/qa_with_rejection_popup_hoc.js

msgid "WithRejectionPopupRejectionReasonForX"
msgstr "Rejection reason for SKU: {thisstatesku}"
msgid "WithRejectionPopupRejectionDropdownReasonLabel"
msgstr "Reason"
msgid "WithRejectionPopupRejectionDropdownReasonPlaceholderText"
msgstr "Select Reason"
msgid "WithRejectionPopupNotesPlaceholder"
msgstr "Enter optional notes"
msgid "WithRejectionPopupNotesLabel"
msgstr "Notes (optional)"
msgid "WithRejectionPopupReject"
msgstr "Reject"
msgid "WithRejectionPopupRejectCancelLabel"
msgstr "Cancel"

#-/resources/st/includes/js/amd_modules/extranet/react/catalog/exclusivity/curation_tool/curation_sku_manufacturer.js

msgid "EBAutoWhitelabel"
msgstr "EB Assortment Decision: Auto whitelabel"
