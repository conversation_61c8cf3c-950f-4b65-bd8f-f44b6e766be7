<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <ymini<PERSON><EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Completion_Verification_Item_Service
{
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage
     */
    private $dao;
  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;
    /**
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage $dao Completion_Verification_Item_Storage
     */
    public function __construct(Completion_Verification_Item_Storage $dao, Batch_Management_Postgres_DAO $dao_psql, FeatureTogglesInterface $featureToggles)
    {
      $this->dao = $dao;
      $this->dao_psql = $dao_psql;
      $this->featureToggles = $featureToggles;
    }

    /**
     * @param int $batch_id Batch ID
     * @param int $employee_id Employee ID
     *
     * @return bool
     */
    public function update_locked_data_if_null(int $batch_id, int $employee_id): bool
    {
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        return $this->dao_psql->update_locked_data_if_null($batch_id, $employee_id);
      } else {
        return $this->dao->update_locked_data_if_null($batch_id, $employee_id);
      }

    }
}
