<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace App\Infrastructure\Connection\Driver;

use Doctrine\DBAL\Statement;
use JsonException;
use PDO;
use WF\Curation\ExclusivityAssortment\Infrastructure\StatementProxy as StatementProxyInterface;
use function call_user_func;
use function call_user_func_array;
use function count;
use function json_encode;
use function print_r;

/**
 * @method bool closeCursor ()
 * @method int columnCount ()
 * @method void debugDumpParams ()
 * @method string errorCode ()
 * @method array errorInfo ()
 * @method mixed fetch (int $fetch_style = \PDO::ATTR_DEFAULT_FETCH_MODE, int $cursor_orientation = \PDO::FETCH_ORI_NEXT, int $cursor_offset = 0)
 * @method array fetchAll (int $fetch_style = \PDO::ATTR_DEFAULT_FETCH_MODE, mixed $fetch_argument = null, array $ctor_args = [])
 * @method mixed fetchColumn (int $column_number = 0)
 * @method mixed getAttribute (int $attribute)
 * @method array getColumnMeta (int $column)
 * @method int rowCount ()
 * @method bool setAttribute (int $attribute, mixed $value)
 * @method bool setFetchMode (int $fetchMode, mixed $arg2 = null, ?array $arg3 = null)
 * @method bool execute()
 */
class StatementProxy implements StatementProxyInterface
{
    private const T_SQL_ARRAY_STR_POST_FIX = '_array';

    private Statement $statement;

    /**
     * StatementProxy constructor.
     *
     * @param  Statement  $statement
     */
    public function __construct(Statement $statement)
    {
        $this->statement = $statement;
    }

    /**
     * @param  string  $placeholder
     * @param  mixed  $value
     * @param  int  $data_type
     */
    public function bindValue(string $placeholder, $value, int $data_type = PDO::PARAM_STR): void
    {
        $this->statement->bindValue($placeholder, $value, $data_type);
    }

    /**
     * @param  string  $placeholder
     * @param  array  $values
     * @param  mixed  $type
     */
    public function bindValuesList(string $placeholder, array $values, /* @deprecated */ $type): void
    {
        $prefix = $placeholder === '?' ? 'json' : $placeholder;
        $arrayStr = $prefix . self::T_SQL_ARRAY_STR_POST_FIX;

        $jsonEncoded = json_encode($values);
        if ($jsonEncoded === false) {
            throw new JsonException('Could not encode $values=' . print_r($values, true));
        }

        $this->bindValue($arrayStr, $jsonEncoded, PDO::PARAM_STR);
    }

    /**
     * @param  string  $name
     * @param  array  $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments)
    {
        if (count($arguments) === 0) {
            return call_user_func([$this->statement, $name]);
        }

        return call_user_func_array([$this->statement, $name], $arguments);
    }
}
