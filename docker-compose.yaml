# for more info about how to use docker-compose
# https://docs.docker.com/compose/gettingstarted/
version: '3.4'

x-app-service: &x-app-service
  working_dir: /app
  volumes:
    - ./:/app #mount app code inside
    - ./docker/local/wayfair/etc:/wayfair/etc:delegated # local wayfair specific configs
    - ~/.composer:/.composer:delegated
    - var_vol:/app/var/:delegated #this is local caching and other purpose 777 directory
  dns_search:
    - csnzoo.com
  environment: &dev_environment
    COMPOSER_MEMORY_LIMIT: -1
    APP_ENV: dev
    APP_DEBUG: 'true'
    DD_SERVICE_NAME: brand-workflows-curation-tool
    DD_AGENT_HOST: apmproxy.query.sdeconsul.csnzoo.com
    DD_TRACE_AGENT_PORT: 31601
    #DB_HOST: db
    #DB_PORT: 1433
    #DB_NAME: MyDb
    DB_USER: "svc_brand_workflows_curation_tool_app"
    DB_PASSWORD_SECRET_NAME: "svc_brand_workflows_curation_tool_app"
    WL_API_CLIENT_ID: "curation_tool_client_id_for_white_label_nextgen_api"
    WL_API_CLIENT_SECRET: "curation_tool_client_for_white_label_nextgen_api"
    TALENT_API_CLIENT_ID: "ah-brand-workflows-curation-tool-talent-reader"
    TALENT_API_CLIENT_SECRET: "ah-brand-workflows-curation-tool-talent-reader"
    COST_API_CLIENT_ID: "curation_tool_client_id_for_cost_api"
    COST_API_CLIENT_SECRET: "curation_tool_client_secret_for_cost_api"
    CURATION_DB_USER_PASSWORD_POSTGRES: "curation-db-user-password"

services:
  devbox:
    <<: *x-app-service
    build:
      context: .
      dockerfile: ./docker/dev.dockerfile
      target: php74

  devbox81:
    <<: *x-app-service
    build:
      context: .
      dockerfile: ./docker/dev.dockerfile
      target: php81
    depends_on:
      - brand-workflows-curation-tool-cloudsql

  frontend:
    <<: *x-app-service
    build:
      context: .
      dockerfile: ./docker/dev.dockerfile
      target: frontend

  # prod fpm, accessed via local-ingress-nginx
  prod:
    image: wayfair/brand-workflows-curation-tool-service
    build:
      context: .
      dockerfile: ./docker/prod.dockerfile
      target: prod
    user: 80:80
    volumes:
      - ./docker/local/wayfair/etc:/wayfair/etc:ro
    dns_search:
      - csnzoo.com
    environment:
      <<: *dev_environment
      APP_ENV: prod
      APP_DEBUG: 'false'

  # do we need it ?..
  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=Iam!StrongPassword

  #entry point
  ingress:
    build:
      context: ./docker/local/ingress-nginx/
      dockerfile: Dockerfile
    ports:
    # HOST:CONTAINER
      - 8380:8380 # devbox
      - 8480:8480 # prod
    depends_on:
      - devbox


  brand-workflows-curation-tool-cloudsql:
    image: "gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.6.0"
    command: "wf-gcp-us-btbb-bc-dev:us-central1:btbb-bc-curation-51e9aaac -a 0.0.0.0 -p 5432 -i --credentials-file=/wayfair/etc/priv/credentials/credentials.json"
    volumes:
      - "./docker/local/wayfair/etc/priv/credentials:/wayfair/etc/priv/credentials:ro"
    ports:
      - "127.0.0.1:5432:5432"
  local-secret-distributor:
    # Please include tag latest to get automatic updates
    image: "wayfair/local-secret-distributor:latest"
    environment:
      DISTRIBUTIONS: "k8s_brand-workflows-curation-tool-dev"
      WF_CONFIG_ENABLED: 1
    volumes:
      - "./docker/local/wayfair/etc:/wayfair/etc/priv/k8s_brand-workflows-curation-tool-dev"

      # Mount of users home directory so vault can get current ~/.vault-token
      - "~/.vault-token:/root/hosthomedir/.vault-token"

      # Allow running docker inside docker
      - "/var/run/docker.sock:/run/docker.sock"

volumes:
# we are creating a volume with type tmpfs to be used later as writable directory for cache
  var_vol:
    driver_opts:
      type: tmpfs
      device: tmpfs
