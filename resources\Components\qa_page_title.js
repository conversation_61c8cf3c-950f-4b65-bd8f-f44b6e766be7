/**
 * QA Page title
 *
 * <AUTHOR> <em<PERSON><PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';

const QAPageTitle = ({isReadOnlyMode}) =>
  isReadOnlyMode ? (
    <Translation msgid="CurationTool.QAPageCompletedTitle" />
  ) : (
    <Translation msgid="CurationTool.QAPageTitle" />
  );

QAPageTitle.propTypes = {
  isReadOnlyMode: PropTypes.bool.isRequired,
};

QAPageTitle.defaultProps = {
  isReadOnlyMode: false,
};

export default QAPageTitle;
