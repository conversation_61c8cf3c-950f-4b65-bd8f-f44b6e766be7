<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\WhiteLabel;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Curation\World_Regions;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer;

class Kitsco_Manufacturer_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->subject = new Kitsco_Manufacturer();
    }

    /**
     * @test
     *
     * @dataProvider getItCalculatesIsKitscoManufacturerData
     *
     * @param int  $manufacturerId Manufacturer Id
     * @param bool $expectedResult Expected Result
     *
     * @return void
     */
    public function itCalculatesIsKitscoManufacturer(int $manufacturerId, bool $expectedResult)
    {
        $this->assertEquals($expectedResult, $this->subject->is_kitsco_manufacturer($manufacturerId));
    }

    /**
     * @return array
     */
    public function getItCalculatesIsKitscoManufacturerData(): array
    {
        return [
            'It\'s kitsco manufacturer' => [
                Kitsco_Manufacturer::KITSCO_US, // manufacturer id,
                true, // expected result
            ],
            'It\'s kitsco manufacturer' => [
                Kitsco_Manufacturer::KITSCO_EU, // manufacturer id,
                true, // expected result
            ],
            'It\'s not kitsco manufacturer' => [
                0, // manufacturer id,
                false, // expected resul
            ]
        ];
    }

    /**
     * @test
     *
     * @return void
     */
    public function itReturnsManufacturerId()
    {
        $this->assertNotNull($this->subject->get_manufacturer_id(World_Regions::REGION_US));
    }
}
