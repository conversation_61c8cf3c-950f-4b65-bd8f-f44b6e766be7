<?php declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client;

use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\Downstream_Fail_Exception;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Base_Response;

class White_Label_Nextgen_Api_Service {
  public const MAX_RETRIES = 5;
  private const RESPONSE_CODE_OK = 200;
  private const RESPONSE_CODE_BAD_REQUEST = 400;

  /**
   * @var White_Label_Nextgen_API_Client
   */
  private White_Label_Nextgen_API_Client $client;

  /**
   * @var LoggerInterface
   */
  private LoggerInterface $logger;

  /**
   * White_Label_Nextgen_Api_Service constructor.
   *
   * @param White_Label_Nextgen_API_Client $client White Label Nextgen API Client
   * @param LoggerInterface                $logger Logger
   */
  public function __construct(White_Label_Nextgen_API_Client $client, LoggerInterface $logger) {
    $this->client = $client;
    $this->logger = $logger;
  }

  /**
   * @param array $batch The batch data to send to nextgen api
   *
   * @return White_Label_Batch_Base_Response
   */
  public function post_request_to_white_label_nextgen_api(array $batch) : int {
    $retries_left = self::MAX_RETRIES;
    $response_code = self::RESPONSE_CODE_OK;
    $last_error = null;
    while ($retries_left-- > 0) {
      try {
        $this->client->createBatch($batch);
        if ($response_code == 200){
            return $response_code ;
        }else{
            $error_message = 'Publish to WL event topic failed, Response code :' . self::RESPONSE_CODE_BAD_REQUEST;
            $this->logger->info($error_message);
            throw new Downstream_Fail_Exception;
        }

      } catch (\Exception $e) {
        $last_error = $e;
        $this->logger->error(
            sprintf("Retryable exception: %s", $e->getMessage()),
            [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]
        );
        sleep(1);
      }
    }

    $error_message = 'Publish to WL event topic failed, Response code : ' . self::RESPONSE_CODE_BAD_REQUEST
                     . '. Total Retries: ' . self::MAX_RETRIES;
    $this->logger->info($error_message);
    throw new \RuntimeException($last_error->getMessage());
  }
}
