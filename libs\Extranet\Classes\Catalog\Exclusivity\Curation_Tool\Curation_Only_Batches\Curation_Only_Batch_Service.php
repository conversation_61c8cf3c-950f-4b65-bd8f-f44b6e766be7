<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches;
use \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
class Curation_Only_Batch_Service {
  /**
   * @var Curation_Only_Batch_Service_Storage
   */
  private $storage;


  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage $postgres_storage Curation_Only_Batch_Service_Postgres_Storage
   */
  private $postgres_storage;

  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;


  /**
   * Curation_Only_Batch_Service constructor.
   *
   * @param Curation_Only_Batch_Service_Storage $storage Storage
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage $postgres_storage Curation_Only_Batch_Service_Postgres_Storage
   * @param FeatureTogglesInterface  $featureToggles FeatureTogglesInterface
   */
  public function __construct(Curation_Only_Batch_Service_Storage $storage,
                              Curation_Only_Batch_Service_Postgres_Storage $postgres_storage,
                              FeatureTogglesInterface $featureToggles) {
    $this->storage = $storage;
    $this->postgres_storage    = $postgres_storage;
    $this->featureToggles    = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return void
   */
  public function mark_as_set_downstream(int $batch_id) : void {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      $verification_id = $this->postgres_storage->create_verification_id();
      $this->postgres_storage->mark_batch_as_sent($batch_id, $verification_id, false);
      $this->postgres_storage->set_verification_id_for_skus($batch_id, $verification_id);
    } else {
      $verification_id = $this->storage->create_verification_id();
      $this->storage->mark_batch_as_sent($batch_id, $verification_id, false);
      $this->storage->set_verification_id_for_skus($batch_id, $verification_id);
    }
  }
}