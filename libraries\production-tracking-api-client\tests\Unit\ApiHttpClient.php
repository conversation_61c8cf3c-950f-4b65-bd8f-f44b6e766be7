<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Tests\Unit;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Guz<PERSON>Http\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Request;

final class ApiHttpClient
{
    /**
     * @return Client
     */
    public static function createConnectionTimeoutClientMock(): Client
    {
        $responses = [
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
        ];

        return self::createClient($responses);
    }


    /**
     * @return Client
     */
    public static function createSuccessfulClientMock(): Client {
        $responseBody = <<<BODY

{
    "data":{
        "productTracking": {
            "update":[1]
        }
    }
}
BODY;

        $responses = [
            new Response(200, [], $responseBody),
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createSuccessfulClientWithErrorParamsMock(): Client {
        $responseBody = <<<BODY

{
    "errors":[{
        "message":"Variable \$projectId of required type Int32 was not provided.",
        "extensions":{
            "category":"graphql"
        },
        "locations":[{
            "line":1,
            "column":17
        }],
        "category":"graphql"
    }],
    "data":[]
}
BODY;

        $responses = [
            new Response(200, [], $responseBody),
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createErrenousClientMock(): Client {
        $responses = [
            new Response(401, [], null),
            new RequestException('Error Communicating with Server', new Request('POST', '/v1/graphql')),
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createValidationErrorClientMock(): Client
    {
        $responses = [];

        return self::createClient($responses);
    }

    /**
     * @param array $responses
     * @return Client
     */
    private static function createClient(array $responses): Client
    {
        $mock = new MockHandler($responses);

        $handlerStack = HandlerStack::create($mock);

        return new Client(['handler' => $handlerStack, 'http_errors' => false]);
    }
}
