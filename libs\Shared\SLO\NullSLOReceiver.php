<?php
/**
 * Mock SLOReceiver for tests
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Shared\SLO;

use Exception;

class NullSLOReceiver implements SLOReceiver
{
    use \Prophecy\PhpUnit\ProphecyTrait;

    /**
     * @var string|null
     */
    public $name = null;

    /**
     * @var array
     */
    public $tags = [];

    /**
     * @var bool
     */
    public $isStopped = true;

    /**
     * @var bool
     */
    public $isMarkedAsFailed = false;

    /**
     * @param array $tags Arbitrary tags for the service.  e.g. ["collection" => "denorm"] for tagging solr
     *
     * @return SLOReceiver
     * @throws \Exception if called after stop()
     */
    public function withTags(array $tags): SLOReceiver
    {
        $this->tags = $tags;
        return $this;
    }

    /**
     * Finalize the measurement
     *
     * @return void
     * @throws \Exception if called after already stop()-ped
     */
    public function stop(): void
    {
        $this->isStopped = true;
    }

    /**
     * Marks the service call as not successful.
     *
     * @phpstan-ignore-next-line
     * @param string|Exception $message Optional error message.  Specified as a string or an \Exception
     *
     * @return self
     * @throws \Exception
     */
    public function markFailed($message = null): SLOReceiver
    {
        $this->isMarkedAsFailed = true;
        return $this;
    }

    /**
     * Indicates of service call has been marked as not successful
     *
     * @return bool
     */
    public function hasFailed(): bool
    {
        return boolval($this->isMarkedAsFailed);
    }
}
