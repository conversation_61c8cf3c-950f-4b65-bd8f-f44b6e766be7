<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Batch;

use App\Infrastructure\Connection\InternalToolsConnection;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use StdClass;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO;
use WF\Shared\Curation\Api\Curation\TalentApi;

class Curator_Loader_DAO_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var TalentApi the talent api object
     */
    private $talentApiObj;
    /**
     * @var InternalToolsConnection
     */
    private $pdoInternalTools;

    /**
     * @var object
     */
    private $curatorObj;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->talentApiObj = $this->prophesize(TalentApi::class);
        $this->talepdoInternalToolsntApiObj = $this->prophesize(InternalToolsConnection::class);

        $this->curatorObj = new Curator_Loader_DAO(
            $this->talepdoInternalToolsntApiObj->reveal(),
            $this->talentApiObj->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_curator_data()
    {
        $employeeId = 1;
        $employeeData = new StdClass();
        $employeeData->id = 1;
        $employeeData->firstName = 'First Name';
        $employeeData->lastName = 'Last Name';
        $employeeData->email = '<EMAIL>';
        $this->talentApiObj->getEmployeeDetails($employeeId)->willReturn($employeeData);
        $actualResult = $this->curatorObj->get_curator($employeeId);
        $this->assertEquals($employeeData->id, $actualResult['employee_id']);
        $this->assertEquals($employeeData->firstName, $actualResult['first_name']);
        $this->assertEquals($employeeData->lastName, $actualResult['last_name']);
        $this->assertEquals($employeeData->email, $actualResult['email_address']);
    }
    /**
     * @test
     *
     * @return void
     */
    public function it_returns_curator_legacy_data()
    {
        $employeeId = 1;
        $employeeData = new StdClass();
        $employeeData->id = 1;
        $employeeData->firstName = 'First Name';
        $employeeData->lastName = 'Last Name';
        $employeeData->email = '<EMAIL>';
        $this->talentApiObj->getEmployeeDetails($employeeId)->willReturn($employeeData);
        $actualResult = $this->curatorObj->get_curator_legacy($employeeId);
        $this->assertEquals($employeeData->id, $actualResult['employee_id']);
        $this->assertEquals($employeeData->firstName, $actualResult['first_name']);
        $this->assertEquals($employeeData->lastName, $actualResult['last_name']);
        $this->assertEquals($employeeData->email, $actualResult['email_address']);
    }
}
