<?php
/**
 * The DAO for the Curation Request
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use PDO;

use function count;

class Curation_Request_Postgres_DAO {

  private ProductConnection $pdo;

  private PostgresConnection $pdo_psql;

  /**
   * Curation_Request_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   * @param PostgresConnection $pdo_psql PDO_PSQL
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
  }

  /**
   * @param int $request_id Curation Request ID
   *
   * @return array
   */
  public function get_skus(int $request_id) : array {
    $internal_array_data = $this->get_skus_internal($request_id);
    $skus = array_column($internal_array_data, 'sku');
    $external_array_data = $this->get_skus_external($skus);
    $data = [];
    foreach ($internal_array_data as $internal_data) {
      $index = array_search($internal_data['sku'], array_column($external_array_data, 'sku'));
      if ($index !== false) {
        $row = array_merge($internal_data, $external_array_data[$index]);
      } else {
        $row = array_merge($internal_data, ['product_status'=>null]);
      }
      array_push($data, $row);
    }
    return $data;
  }

  /**
   * @param int $request_id Curation Request ID
   *
   * @return array
   */
  private function get_skus_internal(int $request_id) : array {
    $sql = '
            SELECT
              "ID" AS id,
              "RequestID" AS request_id,
              "SKU" AS sku,
              "EligibilityStatus" AS eligibility_status,
              "IsMasterCoreClass" AS is_master_core_class,
              "IsHoldoutManufacturer" AS is_holdout_manufacturer,
              "IsWayfairChannel" AS is_wayfair_channel,
              "IsStandardBrand" AS is_standard_brand,
              "IsActiveJoinSupplier" AS is_active_join_supplier,
              "ReadyForCurationID" AS ready_for_curation_id,
              "IsRightPrStatus" AS is_right_product_status,
              "HasImages" AS has_images,
              "UpdatedDate" AS updated_date,
              "ImportStatusID" AS import_status_id,
              "ImportDate" AS import_date,
              "IsKitPrSku" AS is_kit_pr_sku,
              "IsRightAssignedSupplierMethod" AS is_right_assigned_supplier_method,
              "IsPerigoldOnly" AS is_perigold_only,
              "VerificationItemID" AS verification_item_id,
              "HasHoldoutManufacturerPart" AS has_holdout_manufacturer_part,
              "IsEu" AS is_eu,
              "IsExceedingPriceCeiling" as is_exceeding_price_ceiling,
              "PriceMissingCount" as price_missing_count
            FROM "tblCurationRequestSKU"
            WHERE "RequestID" = :request_id
            ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':request_id', $request_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot load SKUs for the request - ' . $request_id);
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $skus SKUs
   *
   * @return array
   */
  private function get_skus_external(array $skus) : array {
    $sql = '
            SELECT
              PrSKU AS sku,
              PrStatus AS product_status
            FROM csn_product.dbo.tblProduct
            WHERE PrSKU IN (' . $this->pdo->paramsForList(count($skus), 'sku_list', SQL::nvarchar(8)) . ')
            ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList(':sku_list', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot load SKUs for the request - ' . $skus);
    }

    return $statement->fetchAll();
  }
}
