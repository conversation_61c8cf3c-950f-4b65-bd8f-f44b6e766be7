import React from 'react';
import ErrorBoundary from 'react-error-boundary';
import Translation from '@wayfair/translation';
import {Box, PageContainer} from '@wayfair/homebase-extranet';
import ErrorScreen from './ErrorScreen';

const AccessDeniedEntry = () => {
  // eslint-disable-next-line new-cap
  document.title = Translation({
    msgid: 'accessDeniedTitle',
  });
  return (
    <Box px={45}>
      <PageContainer
        pageTitle={<Translation msgid="accessDeniedTitle" />}
        actions={<PageContainer.HelpLink href="#" />}
      >
        <ErrorBoundary FallbackComponent={ErrorScreen}>
          <p>
            <Translation msgid="accessDeniedMessage" />
          </p>
        </ErrorBoundary>
      </PageContainer>
    </Box>
  );
};

export default AccessDeniedEntry;
