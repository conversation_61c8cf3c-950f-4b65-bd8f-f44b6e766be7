<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Tests\Unit;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Request;

final class HttpClient
{
    /**
     * @return Client
     */
    public static function createConnectionTimeoutClientMock(): Client {
        $responses = [
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create')),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create')),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create')),
        ];

        return self::createClient($responses);
    }


    /**
     * @return Client
     */
    public static function createSuccessfulClientMock(): Client {
        $responses = [
            new Response(200, [], '{"isRequestSuccessful": true}'),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create'))
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createValidationErrorClientMock(): Client {
        $responseBody = <<<BODY
{
  "isRequestSuccessful": true,
  "isValid": false,
  "validationErrors": [
    {
      "batchId": 173199,
      "errors": [
        "The white label batch id \"173199\" already exists in tblWhiteLabelBatch",
        "The brand catalog id \"15\" is not allowed to be used"
      ],
      "skus": []
    },
    {
      "batchId": 173207,
      "errors": [
        "The brand catalog id \"16\" is not allowed to be used"
      ],
      "skus": []
    }
  ]
}
BODY;

        $responses = [
            new Response(422, [], $responseBody),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create'))
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createResponseErrorClientMock(): Client {
        $responseBody = <<<BODY
{
   "errorCode": 1,
   "errorMessage":"The request does not follow the Payload structure"
}
BODY;

        $responses = [
            new Response(400, [], $responseBody),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create'))
        ];

        return self::createClient($responses);
    }

    /**
     * @return Client
     */
    public static function createUnhandledErrorClientMock(): Client {
        $responses = [
            new Response(500, [], ''),
            new RequestException('Error Communicating with Server', new Request('POST', '/batch/create'))
        ];

        return self::createClient($responses);
    }

    /**
     * @param  array  $responses
     * @return Client
     */
    private static function createClient(array $responses): Client {
        $mock = new MockHandler($responses);

        $handlerStack = HandlerStack::create($mock);
        return new Client(['handler' => $handlerStack, 'http_errors' => true]);
    }
}