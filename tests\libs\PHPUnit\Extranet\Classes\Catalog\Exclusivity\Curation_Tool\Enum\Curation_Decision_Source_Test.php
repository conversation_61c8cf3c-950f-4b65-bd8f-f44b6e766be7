<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum;

use App\Domain\Enum\AbstractEnumeration;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;

class Curation_Decision_Source_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_final()
    {
        $rc = new ReflectionClass(Curation_Decision_Source::class);

        $this->assertTrue($rc->isFinal());
    }

    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_enumeration()
    {
        $rc = new ReflectionClass(Curation_Decision_Source::class);

        $this->assertTrue($rc->isSubclassOf(AbstractEnumeration::class));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_wraps_values_from_db_picklist()
    {
        $map = [
            1 => Curation_Decision_Source::manual(),
            2 => Curation_Decision_Source::automatic_by_context_sku(),
            3 => Curation_Decision_Source::automatic_by_class(),
            4 => Curation_Decision_Source::automatic_by_style(),
        ];

        foreach ($map as $id => $option) {
            /** @var Curation_Decision_Source $option */
            $this->assertEquals($id, $option->value(), $option->label());
        }
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_throws_exception_when_invalid_value()
    {
        $this->expectException(InvalidArgumentException::class);

        Curation_Decision_Source::create(99);
    }
}
