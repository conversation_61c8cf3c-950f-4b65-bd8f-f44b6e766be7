<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQL;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use PDO;

class Curation_Batch_DAO {

  const CURATION_REQUEST_SKU_IMPORT_STATUS_COMPLETED = 2;

  const CURATION_DOWNSTREAMED_STATUS_ID = 6;

  private const MANUAL_CURATION_ONLY_TYPE = 7;

  private ProductConnection $pdo;

  /**
   * Batch_SKU_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * @param int $batch_id the batcn id
   *
   * @return array
   */
  public function get_batch_info(int $batch_id) : array {
    $sql = '
      SELECT
        cb.id,
        cb.SUID AS supplier_id,
        cb.BrandCatalogID AS brand_catalog_id,
        cb.CurationRequestTypeID AS curation_request_type
      FROM csn_product.dbo.tblCurationBatch cb WITH(NOLOCK)
      WHERE cb.id = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batch info ');
    }

    return $statement->fetch();
  }

  /**
   * Returns not sent to downstream batches
   *
   * @return array
   */
  public function get_not_sent_batches() : array {
    $sql = '
      SELECT cb.id,
        cb.SUID AS supplier_id,
        cb.BrandCatalogID AS brand_catalog_id,
        bc.BclgShortName AS brand_catalog_short_name,
        cb.CurationRequestTypeID AS curation_request_type,
        e.EmFirstName + \' \' + e.EmLastName AS assigned_employee,
       cr.ManualTypeID AS curation_manual_request_type
      FROM csn_product.dbo.tblCurationBatch cb WITH(NOLOCK)
      LEFT JOIN csn_product.dbo.tblBrandCatalog bc WITH(NOLOCK) ON bc.BclgID = cb.BrandCatalogID
      LEFT JOIN csn_hr.dbo.tblEmployee e WITH(NOLOCK) ON e.EmID = cb.AssignedEmID
      OUTER APPLY (
        SELECT TOP 1 tcr.ManualTypeID
        FROM csn_product.dbo.tblVerificationItem vi WITH (NOLOCK)
        JOIN csn_product.dbo.tblCurationRequestSKU crs WITH (NOLOCK) ON vi.ViID = crs.VerificationItemID
        JOIN csn_product.dbo.tblCurationRequest tcr WITH (NOLOCK) ON tcr.ID = crs.RequestID
        WHERE ViBatchID = cb.ID
      ) cr
      WHERE VerificationID IS NULL
      ';

    $statement = $this->pdo->prepare($sql);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch not sent batches');
    }

    return $statement->fetchAll();
  }

  /**
   * Returns not sent to downstream batches
   *
   * @param int $sent_downstream_max_days_ago Sent Downstream Max Days Ago
   *
   * @return array
   */
  public function get_sent_downstream_ready_for_whitelabel_batches(int $sent_downstream_max_days_ago) : array {
    $sql = '
      SELECT cb.id,
        cb.SUID AS supplier_id,
        cb.BrandCatalogID AS brand_catalog_id,
        bc.BclgShortName AS brand_catalog_short_name,
        cb.CurationRequestTypeID AS curation_request_type,
        e.EmFirstName + \' \' + e.EmLastName AS assigned_employee
      FROM csn_product.dbo.tblCurationBatch cb WITH(NOLOCK)
      LEFT JOIN csn_product.dbo.tblBrandCatalog bc WITH(NOLOCK) ON bc.BclgID = cb.BrandCatalogID
      LEFT JOIN csn_hr.dbo.tblEmployee e WITH(NOLOCK) ON e.EmID = cb.AssignedEmID
      WHERE cb.VerificationID IS NOT NULL
        AND NOT EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.tblWhiteLabelBatch wb WITH (NOLOCK) WHERE wb.ID = cb.ID)
        AND NOT EXISTS (
          SELECT TOP 1 1 FROM csn_product.dbo.tblVerificationItem vi WITH (NOLOCK)
          JOIN csn_product.dbo.tblCurationRequestSKU crs WITH (NOLOCK ) ON vi.ViID = crs.VerificationItemID
          JOIN csn_product.dbo.tblCurationRequest cr WITH (NOLOCK)
          ON crs.RequestID = cr.ID
          WHERE vi.ViBatchID = cb.ID
          AND cr.ManualTypeID = :curation_only
        )
      AND cb.SentWLAt > DATEADD(DAY, :sent_downstream_days_ago, GETDATE())
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sent_downstream_days_ago', (-1) * $sent_downstream_max_days_ago, PDO::PARAM_INT);
    $statement->bindValue(':curation_only', self::MANUAL_CURATION_ONLY_TYPE, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batches ready for white label');
    }

    return $statement->fetchAll();
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch Batch object
   *
   * @return array
   */
  public function get_batch_curation_requests(Batch $batch) : array {
    $sql = '
      SELECT DISTINCT cr.id, cr.QuickformID AS quickform_id
      FROM csn_product.dbo.tblCurationRequest cr WITH(NOLOCK)
      JOIN csn_product.dbo.tblCurationRequestSKU crs WITH(NOLOCK) ON crs.RequestID = cr.ID
      JOIN csn_product.dbo.tblVerificationItem vi WITH(NOLOCK) ON vi.ViSKU = crs.SKU
      WHERE vi.ViBatchID = :batch_id AND crs.ImportStatusID = :import_status
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch->get_id(), PDO::PARAM_INT);
    $statement->bindValue(':import_status', static::CURATION_REQUEST_SKU_IMPORT_STATUS_COMPLETED, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot fetch batch curation requests');
    }

    return $statement->fetchAll();
  }

  /**
   * @param array $ids A list of IDs to retrieve
   *
   * @return array
   */
  public function load_evaluation_items_by_ids($ids) {
    $sql = '
      SELECT  vi.ViID         AS id,
              pr.PrSKU        AS sku,
              vi.ViQAStatusID AS qa_status_id
      FROM    csn_product.dbo.tblProduct pr WITH (NOLOCK)
      JOIN    csn_product.dbo.tblVerificationItem vi WITH (NOLOCK) ON vi.ViSKU = pr.PrSKU
      WHERE   vi.ViID IN ' . $this->pdo->paramsForList(count($ids), 'ids', SQL::int);

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('ids', $ids, SQL::int);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to load evaluation items for IDs');
    }

    return $statement->fetchAll();
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   *
   * @return void
   */
  public function send_curation_only_batch_downstream(int $batch_id, int $verification_id) : void {
    $sql = '
      UPDATE csn_product.dbo.tblCurationBatch
      SET VerificationID = :verification_id, 
          StatusID = :status_id
      WHERE ID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':status_id', self::CURATION_DOWNSTREAMED_STATUS_ID, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot set verficiation ID on Batch ID: ' . $batch_id);
    }
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return int|null
   */
  public function get_manual_curation_type_for_batch(int $batch_id) : ?int {

    $sql = '
        SELECT  cr.ManualTypeID 
        FROM csn_product.dbo.tblVerificationItem vi WITH (NOLOCK)
        JOIN csn_product.dbo.tblCurationRequestSKU crs WITH (NOLOCK )
             ON vi.ViID = crs.VerificationItemID
        JOIN csn_product.dbo.tblCurationRequest cr WITH (NOLOCK)
             ON crs.RequestID = cr.ID
        WHERE vi.ViBatchID = :batch_id
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Could not load curation manual type id for batch ID: ' . $batch_id);
    }

    return $statement->fetchColumn(0);
  }
}
