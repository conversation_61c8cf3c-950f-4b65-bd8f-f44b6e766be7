<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\QA;

use App\Application\DTO\QARequest;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\RouterInterface;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_QA_Service;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function sprintf;

class CompleteQAController extends AbstractQAControllerAbstract
{
    /**
     * @param Completion_QA_Service $qa_service Completion service
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param Downstream_QA_Service $downstream_service Downstream QA Service
     * @param RouterInterface $router
     * @param FeatureTogglesInterface $featureToggles
     * @param QARequest $request
     *
     * @return Response
     * @throws Throwable
     * @Route(path="/complete_qa", methods={"GET"})
     * @Security("is_granted('ARA:Curation QA') or is_granted('ARA:Curation Batch Management EU Nearshore Curators') or is_granted('Curation - US Offshore')")
     * @todo: verify email sending flow for rejected SKUs email. All emails in dev should have prefix `[DEV]`
     * @todo: all emails in Dev should be send to the current active user
     */
    public function __invoke(
        Completion_QA_Service $qa_service,
        Completion_Batch_Data_Service $batch_data_service,
        Downstream_QA_Service $downstream_service,
        RouterInterface $router,
        FeatureTogglesInterface $featureToggles,
        QARequest $request
    ): Response {
        $batch_id = $request->getBatchId();
        if ($batch_id === 0) {
            $this->error(
                'Failed to complete QA for batch, missing batch_id',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId()
                ]
            );

            return $this->json(['reason' => 'Bad request. Try to update your page!'], JsonResponse::HTTP_BAD_REQUEST);
        }

        try {
            // safety check
            if (!$this->is_expected_process_type(
                $batch_id,
                Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                $batch_data_service
            )
            ) {
                $this->warning(
                    sprintf(
                        'Failed to complete QA for batch_id=%d. Unexpected batch process type',
                        $batch_id
                    ),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'Bad request! Cannot complete automated batch!'], JsonResponse::HTTP_BAD_REQUEST);
            }
            if (!$this->isExpectedBatchStatus($batch_id, [Batch::STATUS_MANUAL_CURATION_COMPLETE], $batch_data_service)) {
                $this->warning(
                    sprintf(
                        'Failed to complete QA for batch_id=%d. Unexpected batch status',
                        $batch_id
                    ),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'There are some more SKU to QA ! The page will refresh or Try update page to see latest batch status'], JsonResponse::HTTP_BAD_REQUEST);
            }

            $result = $qa_service->complete($batch_id, $this->getEmployeeId());

            if ($qa_service->isAllSkuApproved($batch_id)) {
                $this->info(
                    'All SKUs confirmed on QA step',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );
                $result = $downstream_service->downstream_manual_batch($batch_id, $this->getEmployeeId());
                return $this->json($result);
            }

            return $this->json(['result' => $result]);
        } catch (Throwable $exception) {
            $this->error(
                sprintf('An error occurred during QA complete step: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );
            throw $exception;
        }
    }
}
