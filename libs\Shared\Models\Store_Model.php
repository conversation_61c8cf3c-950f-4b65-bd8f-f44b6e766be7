<?php
/**
 * A model for accessing various store constants
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Models;

/**
 * Class Store_Model
 */
class Store_Model {

  /**
   * Store ID constants
   *
   * @const int
   */
  const AMAZON_DE_ID          = 401;
  const AMAZON_DWELL_ID       = 424;
  const AMAZON_ID             = 377;
  const AMAZON_UK_ID          = 402;
  const BARNSANDNOBLE_ID      = 409;
  const BESTBUY_ID            = 405;
  const BLUESTEM_ID           = 448;
  const DWELL_RETAIL_ID       = 419;
  const DWELL_STUDIO_ID       = 417;
  const DWELL_WHOLESALE_ID    = 418;
  const EBAY_DE_ID            = 381;
  const EBAY_UK_ID            = 400;
  const EBAY_US_ID            = 375;
  const HSN_ID                = 363;
  const JOSSANDMAIN_ID        = 450;
  const JOSSANDMAIN_LEGACY_ID = 379;
  const KMART_ID              = 339;
  const KOHLS_ID              = 442;
  const MERCHANTRY_ID         = 413;
  const OCM_ID                = 416;
  const QUILL_ID              = 415;
  const STAPLES_ADVANTAGE_ID  = 449;
  const RAKUTEN_ID            = 380;
  const SEARS_ID              = 376;
  const SEARS_LEGACY_ID       = 338;
  const STAPLES_ID            = 414;
  const TESCO_ID              = 410;
  const WAYFAIR_DE_ID         = 368;
  const WAYFAIR_ID            = 49;
  const WAYFAIR_UK_ID         = 321;
  const WAYFAIR_CA_ID         = 446;
  const WAYFAIR_IE_ID         = 471;
  const GOOGLE_EXPRESS_US_ID  = 451;
  const ALL_MODERN_ID         = 81;
  const BIRCH_LANE_ID         = 422;
  const PERIGOLD_ID           = 457;
  const ARLOW_ID              = 460;
  const THE_KNOT_ID           = 470;

  // constants from global_constants.php
  // these should be probably retired later
  const ALL_MODERN_UK_ID      = 374;
  const JOSSMAIN_UK_ID        = 421;
  const JOSSMAIN_DE_ID        = 444;
  const JOSSMAIN_FR_ID        = 445;
  const WAYFAIR_AU_ID         = 408;
  const WAYFAIR_SUPPLY_ID     = 411;
  const ROAD_TO_OZ_ID         = 423;

  // CastleGate store ID
  const CASTLEGATE_ID         = 343;
}
