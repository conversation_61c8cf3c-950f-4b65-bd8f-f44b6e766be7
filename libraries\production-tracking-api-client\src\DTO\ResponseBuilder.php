<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\DTO;

use Psr\Http\Message\ResponseInterface;

final class ResponseBuilder
{
    private function __construct()
    {
    }

    /**
     * @param ResponseInterface $response
     *
     * @return Response
     */
    public static function buildResponseDTO(ResponseInterface $response): Response
    {
        $responseCode = $response->getStatusCode();

        $responseData = \json_decode($response->getBody()->getContents(), true);

        if ($responseCode === 200) {
            $isRequestSuccessful = !empty($responseData['errors']) ? false : true;

            return new Response($isRequestSuccessful);
        }

        return new Response(false);
    }
}
