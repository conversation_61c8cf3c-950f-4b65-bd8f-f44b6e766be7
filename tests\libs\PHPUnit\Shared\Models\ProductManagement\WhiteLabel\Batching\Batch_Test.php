<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Mytsyk <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Models\ProductManagement\WhiteLabel\Batching;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Models\Model;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

class Batch_Test extends TestCase
{
    public const WF_US_BRAND_CATALOG = 1;
    public const WF_UK_BRAND_CATALOG = 2;

    public const PRODUCT_ADDITION_BATCH = 0;
    public const MANUAL_ADDITION_BATCH = 1;

    use ProphecyTrait;

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->subject = new Batch();
    }

    /**
     * @param string      $sku_model_index SKU Model Index
     * @param string      $sku_input       SKU Input
     * @param string|null $expected_result Expected Result
     *
     * @test
     * @dataProvider get_sku_case_insensitive_provider
     *
     * @return void
     */
    public function get_sku_case_insensitive(string $sku_model_index, string $sku_input, $expected_result)
    {
        $this->subject->models = [
            $sku_model_index => $sku_model_index
        ];

        $result = $this->subject->get_sku($sku_input);

        $this->assertSame($expected_result, $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function batch_fields_should_not_be_null_when_creating_batch()
    {
        $this->assertSame(Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW, $this->subject->get_process_type());
        $this->assertSame(Batch::UNKNOWN_MANUAL_REQUEST_TYPE, $this->subject->get_curation_request_type());
        $this->assertSame('', $this->subject->get_brand_catalog_short_name());
        $this->assertSame(0, $this->subject->get_supplier_id());
        $this->assertSame(0, $this->subject->get_brand_catalog_id());
    }

    /**
     * @return array
     */
    public function get_sku_case_insensitive_provider()
    {
        return [
            'Insensitive from left to right' => [
                'a', // Model Index
                'A', // SKU Input
                'a'  // Expected Result
            ],
            'Insensitive from right to left' => ['A', 'a', 'A'],
            'Same upper case' => ['A', 'A', 'A'],
            'Same lower case' => ['a', 'a', 'a'],
            'Different SKUs' => ['A', 'B', null],
        ];
    }

    /**
     * @param int $brand_catalog         Batch brand catalog
     * @param int $curation_request_type Batch curation request type
     *
     * @test
     * @dataProvider get_display_sku_eligible_data
     *
     * @return void
     */
    public function it_is_not_display_sku_eligible_when_not_us_or_not_pa_batch(int $brand_catalog, int $curation_request_type)
    {
        $this->subject->set_brand_catalog_id($brand_catalog);
        $this->subject->set_curation_request_type($curation_request_type);

        $this->assertFalse($this->subject->is_display_sku_eligible());
    }

    /**
     * @return array
     */
    public function get_display_sku_eligible_data(): array
    {
        return [
            'Not WF US Batch' => [
                'brand catalog' => static::WF_UK_BRAND_CATALOG,
                'curation request type' => static::PRODUCT_ADDITION_BATCH,
            ],
            'Not PA Batch' => [
                'brand catalog' => static::WF_US_BRAND_CATALOG,
                'curation request type' => static::MANUAL_ADDITION_BATCH,
            ],
            'Not WF US Batch and not PA Batch' => [
                'brand catalog' => static::WF_UK_BRAND_CATALOG,
                'curation request type' => static::MANUAL_ADDITION_BATCH,
            ]
        ];
    }
}
