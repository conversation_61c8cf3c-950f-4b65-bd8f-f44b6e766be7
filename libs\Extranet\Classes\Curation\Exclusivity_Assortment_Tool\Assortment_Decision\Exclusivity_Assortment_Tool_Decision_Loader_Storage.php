<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision;

use WF\Curation\ExclusivityAssortment\Domain\Enum\ExclusivityAssortmentCurationStatus;

interface Exclusivity_Assortment_Tool_Decision_Loader_Storage {
  /**
   * @param string[]                            $skus             skus
   * @param ExclusivityAssortmentCurationStatus $requested_status status
   *
   * @return array
   */
  public function get_decisions_for_skus(array $skus, ExclusivityAssortmentCurationStatus $requested_status) : array;
}
