# Services and Tools Links

This page lists all the frequently used services and tools to develop, maintain and monitor Curation workflows.
The relevance score doesn't mean the importance level but is more about how frequently you will have to touch it for your daily job as an engineer.

## Services on _PROD_
### [Curation Jobs Migrated](http://kube-curation-job-consumer.service.intraiad1.consul.csnzoo.com/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config#/)

- This is the Java [curation-job-consumer](https://github.com/wayfair-shared/curation-job-consumer). The most frequently used API is /management/execution/summaries-import-job/{start}/{end} to track the import job runs.
- Relevance: **10 / 10**

### [Curation Tool](https://partners.wayfair.com/d/curation-tool/index)

- The core tool in Curation to decide the style, price tier and brand for a given SKU from PA or Curation Loader/Assortment Tool.
- Relevance: **10 / 10**

### [Curation Jobs on Jenkins](https://partners.jenkins.service.csnzoo.com/view/Brand%20Workflows%20-%20Curation%20/)

- There are 3 most complicated jobs (flag, import PA and import manual) have been migrated to Java [curation-job-consumer](https://github.com/wayfair-shared/curation-job-consumer)
but the other jobs are still running in Jenkins. In very rare cases when certain job (mostly likely import PA) in Java stops working, you need to manually kick off the equivalent in Jenkins to backfill the data/keep the lights on.
- Relevance: **8 / 10**

### [Curation Batch Management](https://partners.wayfair.com/v/catalog/curation_batch/index)

- The first stop for Curation and NEST batches. It's still in monolith and the very next target to migrate to decoupled service.
- Relevance: **7 / 10**

### [Curation Service](http://kube-brandworkflows-curation-service.service.intraiad1.consul.csnzoo.com/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config#)

- It has the curation eligibility API used by PA team and the curation batch status update APIs used by curation jobs and tools.
- Relevance: **7 / 10**

### [Uber Loader](https://kube-brand-workflow-uber-loader.service.intraiad1.consul.csnzoo.com/curation/verification-style/)

- A backdoor data manipulation service to help with fixing inconsistent data points. This tool talks directly to the DB tables so proceed with caution.
- Relevance: **9 / 10**

### [Manage Manufacturer Tool](https://admin.wayfair.com/v/manage_manufacturer/index)

- Curation owns this tool to manage manufacturer. This is a very old tool but used by a lot of team across WF so the team gets inquiries about changing some data every once in a while.
- Relevance: **7 / 10**

### [NEST Tool](https://partners.wayfair.com/v/curation/style_tagging_tool/qa?batch_id=1258738)

- Non-EB style tagging tool is used to tag the primary style of a SKU whose price is higher than the predefined range in class.
- Relevance: **6 / 10**

### [Curation Loader](https://admin.wayfair.com/prodmng/quick_loader.php)

- Offshore team uploads CSV files with SKUs that they want to curation. This tool is in monolith.
- Relevance: **5 / 10**

### [Style Suggestion API](https://kube-style-suggestion-api.service.intraiad1.consul.csnzoo.com)

- Used by curation tool and curation jobs to find possible styles and target manufacturer suggestion.
Powered by machine learning. The repo is [py-style-suggestion-api](https://github.com/wayfair-shared/py-style-suggestion-api)
- Relevance: **5 / 10**

## Tools

### [Buildkite Dashboard](https://buildkite.com/organizations/wayfair/teams/brand-workflows/pipelines)

- This is the entrypoint to all the Buildkite pipelines in B&C. You need to rely on it for any changes to deploy and ship.
- Relevance: **10 / 10**

### [Datadog Curation Alerts](https://app.datadoghq.com/dashboard/26w-ukw-3dj/bw-curation?from_ts=1652973081634&to_ts=1652976681634&live=true)

- The DD alert across services in Curation domain. All of them are relevant. But more time is needed to prioritize them better.
- Relevance: **9 / 10**

### [Curation Logs on PROD](https://kibanaapplog.csnzoo.com/app/discover#/view/8e74d9c0-7b0c-11ed-9317-939469049863?_g=h@bf2c9f3&_a=h@4773d39)

- The Kibana dashboard to monitor the logs of multiple mission critical services. To see the logs for a certain service, disable/enable it in the filter.
- Relevance: **10 / 10**

### [Curation Logs on DEV](https://devkibanaapplog.csnzoo.com/app/discover#/view/d03e4980-a344-11ed-8058-b34fe681f441)

- Same as above but incomplete
- Relevance: **7 / 10**

### [Curation On-Call Schedule](https://wayfair.pagerduty.com/schedules#PX63RJ8)

- A PagerDuty schedule to indicate who is on-call for which week. The on-call engineer needs to monitor [BtBB Brand Workflows](https://wayfair.slack.com/archives/C02LATFEW07) for incoming customer inquiries and tickets.
- Relevance: **8 / 10**