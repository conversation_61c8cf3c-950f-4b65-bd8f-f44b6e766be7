<?php
/**
 * Error for Invalid Response from GraphQL Client
 * PHP version 7
 *
 * <AUTHOR> Dhamne <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\Graphql;

use Exception;
use function implode;

class GraphException extends Exception
{
    /**
     * @var string[] Database error information about the failed query
     */
    public $errors = [];

    /**
     * Query_Failed_Exception constructor
     *
     * @param string[] $errors Array with details about the error (Optional)
     */
    public function __construct(array $errors = [])
    {
        $this->errors = $errors;
        parent::__construct(implode(', ', $this->errors));
    }
}
