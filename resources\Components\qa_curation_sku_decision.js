/**
 * This displays a curation decision in the curation QA page
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {ALIGNMENT} from './common_layout_constants';
import {IconV2 as Icon, Text, Column, Grid, FLEX_KEYWORDS, TEXT_STYLE, SPACING} from '@wayfair/homebase-extranet';
import CurationToolShapes from './curation_tool_shapes';
import {faExclamationTriangle, faTags} from '@fortawesome/free-solid-svg-icons';

class QACurationSkuDecision extends React.Component {
  static propTypes = {
    curationConfig: CurationToolShapes.curationConfigShape.isRequired,
    decision: CurationToolShapes.decisionShape,
    hasStyleSuggestions: PropTypes.bool.isRequired,
    isAutomaticCurationPostQaEnabled: PropTypes.bool,
  };

  static defaultProps = {
    decision: {
      priceTier: null,
      styleId: null,
      substyleId: null,
      manufacturerId: null,
      granularStyleId: null,
      exclusionReasonId: null,
      automaticExcludedReason: null,
      isSuggestedStyleMatched: false,
      suggestedStyleRejectionReasonId: null,
    },
    hasStyleSuggestions: false,
    isAutomaticCurationPostQaEnabled: false,
  };

  shouldComponentUpdate() {
    // This is not a mistake, it should never re-render
    return false;
  }

  getTextForValue = (items, searchedValue) => {
    const found = items.find(item => item.value === searchedValue);

    return found ? String(found.text) : null;
  };

  getExcludedReasonTextForValue = (items, searchedValue) => {
    const found = items.find(item => item.value === searchedValue);
    //Get Automatic Excluded Reason
    if (searchedValue === 9) {
      let txt = found ? String(found.text) : "";
      let reason = txt + ". Actual Reason: ";
      if (this.props.decision.automaticExcludedReason) {
        reason = reason + this.props.decision.automaticExcludedReason;
      }
      else {
        reason = reason + "NA";
      }
      return reason;
    }
    else {
      return found ? String(found.text) : null;
    }

  };

  getStyleText = () =>
    this.getTextForValue(
      this.props.curationConfig.styles,
      this.props.decision.styleId
    );

  getSubstyleText = () =>
    this.getTextForValue(
      this.props.curationConfig.substyles,
      this.props.decision.substyleId
    );

  getBrandText = () =>
    this.getTextForValue(
      this.props.curationConfig.manufacturers,
      this.props.decision.manufacturerId
    );

  getExclusionReasonText = () =>
    this.getExcludedReasonTextForValue(
      this.props.curationConfig.exclusionReasons,
      this.props.decision.exclusionReasonId
    );

  getGranularstyleText = () =>
    this.getTextForValue(
      this.props.curationConfig.granularStyles,
      this.props.decision.granularStyleId
    );

  isExcluded = () => !!this.props.decision.exclusionReasonId;

  render() {
    return (
      <Grid alignItems={ALIGNMENT.CENTER}>
        <Column size={9} flexDirection={FLEX_KEYWORDS.COLUMN}>
          {this.isExcluded() ? (
            <Text fontStyle={TEXT_STYLE.BOLD}>
              <Translation
                msgid="CurationTool.decisionExclusionReasonText"
                params={{
                  excludedReason: this.getExclusionReasonText(),
                }}
              />
            </Text>
          ) : (
            <>
              <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                <Translation
                  msgid="CurationTool.decisionPriceTierText"
                  params={{
                    priceTier: this.props.decision.priceTier,
                  }}
                />
              </Text>
              <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                <Translation
                  msgid="CurationTool.decisionPrimaryStyleText"
                  params={{styleText: this.getStyleText()}}
                />
              </Text>
              <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                <Translation
                  msgid="CurationTool.decisionSecondaryStyleText"
                  params={{subStyleText: this.getSubstyleText()}}
                />
              </Text>
              <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                <Translation
                  msgid="CurationTool.decisionBrandText"
                  params={{brandText: this.getBrandText()}}
                />
              </Text>
              {this.props.decision.granularStyleId && (
                <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                  <Translation
                    msgid="CurationTool.decisionGranularstyleText"
                    props={{granularStyle: this.getGranularstyleText()}}
                  />
                </Text>
              )}
              {this.props.hasStyleSuggestions && (
                <Text fontStyle={TEXT_STYLE.BOLD} mb={SPACING.SPACE_MEDIUM}>
                  {this.props.decision.isSuggestedStyleMatched ? (
                    <Translation msgid="CurationTool.decisionMatchedStyleSuggestionText" />
                  ) : (
                    <Translation msgid="CurationTool.decisionNotMatchedStyleSuggestionText" />
                  )}
                </Text>
              )}
            </>
          )}
        </Column>
      </Grid>
    );
  }
}

export default QACurationSkuDecision;
