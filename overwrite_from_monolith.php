<?php

declare(strict_types=1);

/**
 * This class copies files from Monolith over the files in folder libs for review
 * Before run, please make sure all changes are under version, otherwise you can lose them
 */
class FileInfo
{
    public string $fullPath;
    public string $className;
    public string $namespace;

    public function __construct(string $fullPath, string $className, string $namespace)
    {
        $this->fullPath = $fullPath;
        $this->className = $className;
        $this->namespace = $namespace;
    }
}


final class FolderIndexer
{
    private RecursiveDirectoryIterator $targetDirectory;

    public function __construct(string $targetDirectory)
    {
        $this->targetDirectory = new \RecursiveDirectoryIterator($targetDirectory);
    }

    /**
     * @return FileInfo[]
     */
    public function all(): array
    {
        printf("Indexing '%s'\n", $this->targetDirectory->getRealPath());
        $result = [];

        /** @var string[]|\SplFileInfo[]|\FilesystemIterator|\Iterator $fileIterator */
        $fileIterator = new \RecursiveIteratorIterator($this->targetDirectory);

        foreach ($fileIterator as $info) {
            if ($info->isFile() && preg_match('/\.php$/', $info->getPathname())) {
                $fullPath = $info->getRealPath();
                $fileContent = file_get_contents($fullPath);
                // remove commented lines
                $fileContent = preg_replace('!/\*.*?\*/!s', '', $fileContent);
                // remove commented lines
                $fileContent = preg_replace('/\n\s*\n/', "\n", $fileContent);

                preg_match('/(?:class|interface|trait)\s+([a-zA-Z0-9_]+)/', $fileContent, $classNameMatches);
                preg_match('/namespace\s+(WF\\\\[a-zA-Z0-9_\\\\]+);/', $fileContent, $namespaceMatches);

                if (count($classNameMatches) !== 2) {
                    //printf("In file '%s' class/interface/trait name not found\n", $fullPath);
                    continue;
                }

                if (count($namespaceMatches) !== 2) {
                    //printf("In file '%s' namespace not found\n", $fullPath);
                    continue;
                }

                $result[strtolower($namespaceMatches[1] . '\\' . $classNameMatches[1])] = new FileInfo(
                    $fullPath,
                    $classNameMatches[1],
                    $namespaceMatches[1]
                );
                /*if (strpos($fileContent, $info->getBasename('.php')) === false) {
                    if (preg_match('/' . $info->getBasename('.php') . '/i', $fileContent, $matches)) {
                        $newName = preg_replace('/' . $info->getBasename('.php') . '/i', $matches[0], $fullPath);

                        rename(
                            $fullPath,
                            $newName
                        );

                        echo(sprintf('"%s" renamed to "%s"', $fullPath, $newName));
                        echo "\n";
                    }
                }*/
            }
        }
        printf("Found %d valid files\n", count($result));
        return $result;
    }
}

// target is current directory
$decoupledFolder = new FolderIndexer('./libs');

// where to look for classes in Monolith
// path to folder with `WF\Extranet` namespace prefix
$monolithExtranetDirectory = new FolderIndexer('../php/extranet/app_includes');
$filesInExtranetNamespace = $monolithExtranetDirectory->all();

// path to folder with `WF\Shared` namespace prefix
$monolithSharedDirectory = new FolderIndexer('../php/includes');
$filesInSharedNamespace = $monolithSharedDirectory->all();

foreach ($decoupledFolder->all() as $fqcn => $file) {
    // echo $fqcn . ' => ' . $file->fullPath . "\n";

    if (stripos($fqcn, 'WF\\Shared\\') === 0) {
        if (!array_key_exists(strtolower($fqcn), $filesInSharedNamespace)) {
            printf("Missing namespace %s\n", $fqcn);
            continue;
        }

        printf("Coping %s => %s \n", $filesInSharedNamespace[strtolower($fqcn)]->fullPath, $file->fullPath);
        copy($filesInSharedNamespace[strtolower($fqcn)]->fullPath, $file->fullPath);
    } elseif (stripos($fqcn, 'WF\\Extranet\\') === 0) {
        if (!array_key_exists(strtolower($fqcn), $filesInExtranetNamespace)) {
            printf("Missing namespace %s\n", $fqcn);
            continue;
        }

        printf("Coping %s => %s \n", $filesInExtranetNamespace[strtolower($fqcn)]->fullPath, $file->fullPath);
        copy($filesInExtranetNamespace[strtolower($fqcn)]->fullPath, $file->fullPath);
    } else {
        printf("Unknown namespace %s\n", $fqcn);
    }
}
