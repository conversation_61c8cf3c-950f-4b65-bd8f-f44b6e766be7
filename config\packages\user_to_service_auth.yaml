user_to_service_auth:
  # optional, by default the bundle redirects an unauthenticated user
  # back to his last visited URL after login.
  # But if your application doesn't support that behaviour
  # you can define a static entrypoint here
  #app_entrypoint: /d/curation-tool/

  # optional, ARA roles your application relies on
  # you have to define all required ARA roles here in order to access them later
  # if your app has no specific ARA roles remove this
  ara_roles:
    - 'Curation QA'
    - 'Curation - US Offshore'
    - 'Curation Batch Management EU Nearshore Curators'
    - 'Curation Assortment Workflow by Offshore'

  # optional, Partner Home User roles your application relies on
  # you have to define all required roles here  in order to access them later
  # if your app has no specific roles remove this
#  user_roles:
#    - 'Order Management'
