<?php declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model;


class White_Label_SKU_Status {
  /**
   * @var string
   */
  private string $sku;

  /**
   * @var string
   */
  private string $skuStatus;

  /**
   * @var bool
   */
  private bool $isSkuExcluded;

  /**
   * @var string|null
   */
  private ?string $batchState = null;

  /**
   * @var bool
   */
  private bool $isBatchExcluded;

  /**
   * @param string      $sku             SKU
   * @param string      $skuStatus       SKU Status
   * @param bool        $isSkuExcluded   SKU Excluded
   * @param bool        $isBatchExcluded Batch Excluded/Cancelled
   * @param string|null $batchState      Batch State
   */
  public function __construct(
      string $sku,
      string $skuStatus,
      bool   $isSkuExcluded,
      bool   $isBatchExcluded,
      ?string $batchState
  ) {
    $this->sku             = $sku;
    $this->skuStatus       = $skuStatus;
    $this->isSkuExcluded   = $isSkuExcluded;
    $this->isBatchExcluded = $isBatchExcluded;
    $this->batchState      = $batchState;
  }

  /**
   * @return string
   */
  public function getSku() : string {
    return $this->sku;
  }

  /**
   * @return string
   */
  public function getSkuStatus() : string {
    return $this->skuStatus;
  }

  /**
   * @return bool
   */
  public function isSkuExcluded() : bool {
    return $this->isSkuExcluded;
  }

  /**
   * @return string|null
   */
  public function getBatchState() : ?string {
    return $this->batchState;
  }

  /**
   * @return bool
   */
  public function isBatchExcluded() : bool {
    return $this->isBatchExcluded;
  }
}
