<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch;

use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_With_Id;

class Batch implements Batch_With_Id {
  public const STATUS_MANUAL_UNASSIGNED         = 1;
  public const STATUS_MANUAL_ASSIGNED           = 2;
  public const STATUS_MANUAL_IN_PROGRESS        = 3;
  public const STATUS_MANUAL_CURATION_COMPLETE  = 4;
  public const STATUS_MANUAL_QA_COMPLETE        = 5;
  public const STATUS_MANUAL_DOWNSTREAMED       = 6;
  public const STATUS_AUTOMATED_GENERATED       = 7;
  public const STATUS_AUTOMATED_DOWNSTREAMED    = 8;
  public const STATUS_AUTOMATED_QA_IN_PROGRESS  = 9;
  public const STATUS_AUTOMATED_QA_COMPLETE     = 10;
  public const STATUS_AUTOMATED_RE_DOWNSTREAMED = 11;
  public const STATUS_AUTOMATED_POST_LAUNCH_QA_ASSIGNED = 12;
  public const STATUS_AUTOMATED_RE_DOWNSTREAM_INPROGRESS = 13;
  public const STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS = 14;

  public const PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW = 1;
  public const PROCESS_TYPE_CURATION_BATCH_AUTOMATED     = 2;

  /**
   * @var int
   */
  private $id = 0;

  /**
   * @var string
   */
  private $source_name = '';

  /**
   * @var int
   */
  private $source_id = 0;

  /**
   * @var int
   */
  private $skus_count = 0;

  /**
   * @var string
   */
  private $supplier_name = '';

  /**
   * @var int
   */
  private $supplier_id = 0;

  /**
   * @var string
   */
  private $supplier_marketing_category_name = '';

  /**
   * @var int
   */
  private $supplier_marketing_category_id = 0;

  /**
   * @var int
   */
  private $assigned_em_id = null;

  /**
   * @var int
   */
  private $status_id = self::STATUS_MANUAL_UNASSIGNED;

  /**
   * @var string
   */
  private $status_name = '';

  /**
   * @var int
   */
  private $brand_catalog_id = 0;

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result
   */
  private $evaluation_result;

  /**
   * @var int|null
   */
  private $rebrand_project_id; /** @phpstan-ignore-line */

  /**
   * @var string|null
   */
  private $rebrand_project_name; /** @phpstan-ignore-line */

  /**
   * @var int
   */
  private $batch_process_type_id; /** @phpstan-ignore-line */

  /**
   * @var string
   */
  private $batch_process_type_name = 'Not specified';

  /**
   * @return int|null
   */
  public function get_id() : ?int {
    return $this->id;
  }

  /**
   * @return string
   */
  public function get_source_name() : string {
    return $this->source_name;
  }

  /**
   * @return int
   */
  public function get_source_id() : int {
    return $this->source_id;
  }

  /**
   * @return int
   */
  public function get_skus_count() : int {
    return $this->skus_count;
  }

  /**
   * @return string
   */
  public function get_supplier_name() : string {
    return $this->supplier_name;
  }

  /**
   * @return int
   */
  public function get_supplier_id() : int {
    return $this->supplier_id;
  }

  /**
   * @return string
   */
  public function get_supplier_marketing_category_name() : string {
    return $this->supplier_marketing_category_name;
  }

  /**
   * @return int
   */
  public function get_supplier_marketing_category_id() : int {
    return $this->supplier_marketing_category_id;
  }

  /**
   * @return int|null
   */
  public function get_assigned_em_id() {
    return $this->assigned_em_id;
  }

  /**
   * @return int
   */
  public function get_status_id() : int {
    return $this->status_id;
  }

  /**
   * @return string
   */
  public function get_status_name() : string {
    return $this->status_name;
  }

  /**
   * @return int
   */
  public function get_brand_catalog_id() : int {
    return $this->brand_catalog_id;
  }

  /**
   * @return int|null
   */
  public function get_rebrand_project_id() {
    return $this->rebrand_project_id;
  }

  /**
   * @return null|string
   */
  public function get_rebrand_project_name() {
    return $this->rebrand_project_name;
  }

  /**
   * @return int|null
   */
  public function get_batch_process_type_id() {
    return $this->batch_process_type_id;
  }

  /**
   * @return null|string
   */
  public function get_batch_process_type_name() {
    return $this->batch_process_type_name;
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result $evaluation_result Evaluation result
   *
   * @return void
   */
  public function set_evaluation_result(Batch_Evaluation_Result $evaluation_result) {
    $this->evaluation_result = $evaluation_result;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result
   */
  public function get_evaluation_result() : Batch_Evaluation_Result {
    return $this->evaluation_result;
  }
}
