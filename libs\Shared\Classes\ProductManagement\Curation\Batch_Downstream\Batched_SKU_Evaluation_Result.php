<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

class Batched_SKU_Evaluation_Result {

  const REASON_SKU_NOT_CURATED = 'SKU is not curated yet';
  const REASON_SKU_PENDING     = 'SKU is curated but pending approval';
  const REASON_SKU_REJECTED    = 'SKU has been rejected';
  const REASON_SKU_APPROVED    = 'SKU approved';
  const REASON_SKU_PARENT      = 'Parent SKU';

  /**
   * @var string
   */
  private $sku;

  /**
   * @var string
   */
  private $reason;

  /**
   * @var int
   */
  private $qa_status;

  /**
   * @var \DateTime
   */
  private $evaluation_time;

  /**
   * Batched_SKU_Evaluation_Result constructor.
   *
   * @param string    $sku             Sku
   * @param string    $reason          Reason
   * @param int       $qa_status       QA Status
   * @param \DateTime $evaluation_time Evaluation Time
   */
  public function __construct(string $sku, string $reason, int $qa_status, \DateTime $evaluation_time) {
    $this->sku             = $sku;
    $this->reason          = $reason;
    $this->qa_status       = $qa_status;
    $this->evaluation_time = $evaluation_time;
  }

  /**
   * @return string
   */
  public function get_sku() : string {
    return $this->sku;
  }

  /**
   * @return string
   */
  public function get_reason() : string {
    return $this->reason;
  }

  /**
   * @return int
   */
  public function get_status() : int {
    return $this->qa_status;
  }

  /**
   * @return \DateTime
   */
  public function get_evaluation_time() : \DateTime {
    return $this->evaluation_time;
  }

  /**
   * @return bool
   */
  public function is_curated() : bool {
    return $this->get_reason() !== self::REASON_SKU_NOT_CURATED;
  }
}
