<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Models\Catalog\Exclusivity\C<PERSON>_<PERSON>ch\Batch;

class Completion_Curation_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service
     */
    private $batchDataService;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater
     */
    private $batchStatusUpdater;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Batch_Checker
     */
    private $curationBatchChecker;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Service
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->batchDataService = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->batchStatusUpdater = $this->prophesize(Completion_Batch_Status_Updater::class);
        $this->curationBatchChecker = $this->prophesize(Completion_Curation_Batch_Checker::class);

        $this->subject = new Completion_Curation_Service(
            $this->batchDataService->reveal(),
            $this->batchStatusUpdater->reveal(),
            $this->curationBatchChecker->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_false_when_is_not_completed()
    {
        $batchId = 1;
        $employeeId = 1;

        $this->curationBatchChecker->isCurationCompleted($batchId)->willReturn(false);

        $actualResult = $this->subject->complete($batchId, $employeeId);

        $this->assertEquals(false, $actualResult['status']);
    }

    /**
     * @param array $batchData Batch data
     *
     * @test
     *
     * @dataProvider get_batch_data
     *
     * @return void
     */
    public function it_returns_false_when_is_not_in_progress(array $batchData)
    {
        $batchId = 1;
        $employeeId = 1;

        $batch = new Completion_Batch_Data(
            $batchId,
            $batchData['StatusID'],
            $batchData['BrandCatalogID'],
            $batchData['AssignedEmID'],
            $batchData['CurationBatchProcessTypeID'],
            $batchData['CurationBatchProcessTypeName'],
            $batchData['CreatedAt'],
            $batchData['ApproverEmpId'],
            $batchData['ApprovedAt'],
        );

        $this->curationBatchChecker->isCurationCompleted($batchId)->willReturn(true);
        $this->batchDataService->get($batchId)->willReturn($batch)->shouldBeCalled();

        $actualResult = $this->subject->complete($batchId, $employeeId);

        $this->assertEquals(false, $actualResult['status']);
        $this->batchStatusUpdater->changeStatus(
            $batchId,
            Batch::STATUS_MANUAL_CURATION_COMPLETE,
            $employeeId
        )->shouldNotHaveBeenCalled();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_calls_update_batch_status_when_is_not_updated()
    {
        $batchId = 1;
        $employeeId = 1;
        $brandCatalogId = 1;
        $processTypeName = 'Manual';
        $createdAt = new DateTime();
        $batchData = new Completion_Batch_Data(
            $batchId,
            Batch::STATUS_MANUAL_IN_PROGRESS,
            $brandCatalogId,
            $employeeId,
            Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
            $processTypeName,
            $createdAt,
            null,
            null
        );

        $this->curationBatchChecker->isCurationCompleted($batchId)->willReturn(true);

        $this->batchDataService->get($batchId)->willReturn($batchData)->shouldBeCalled();

        $this->batchStatusUpdater->changeStatus(
            $batchId,
            Batch::STATUS_MANUAL_CURATION_COMPLETE,
            $employeeId
        )->willReturn()->shouldBeCalled();

        $this->subject->complete($batchId, $employeeId);
    }

    /**
     * @return array
     */
    public function get_batch_data(): array
    {
        return [
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_QA_COMPLETE,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => 1,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => 1,
                    'ApprovedAt' => new DateTime(),
                ]
            ],
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_IN_PROGRESS,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_ASSIGNED,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => 1,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
        ];
    }
}
