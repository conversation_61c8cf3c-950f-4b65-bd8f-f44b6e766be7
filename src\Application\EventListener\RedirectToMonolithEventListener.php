<?php

declare(strict_types=1);

namespace App\Application\EventListener;

use App\Application\Logger\LoggerTrait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use RuntimeException;
use Symfony\Component\HttpFoundation\RedirectResponse;

use Symfony\Component\HttpFoundation\Request;

use Symfony\Component\HttpKernel\Event\RequestEvent;
use function in_array;
use function mb_strpos;
use function sprintf;
use function str_replace;

/**
 * @deprecated It's a temporary listener designed to redirect Curation Tool users back to the Monolith
 *              it was done as backup plan if something will go bad during migration
 */
class RedirectToMonolithEventListener implements LoggerAwareInterface
{
    use LoggerTrait;
    use LoggerAwareTrait;

    private const ALLOWED_TO_REDIRECT_METHODS = [
        Request::METHOD_GET,
        Request::METHOD_OPTIONS,
        Request::METHOD_HEAD,
        Request::METHOD_CONNECT,
    ];
    private const MONOLITH_CURATION_TOOL_ROOT_ROUTE = '/v/catalog/curation_tool';

    private FeatureTogglesInterface $featureToggles;

    private string $appRootRoute;

    public function __construct(FeatureTogglesInterface $featureToggles, string $appRootRoute)
    {
        $this->featureToggles = $featureToggles;
        $this->appRootRoute = $appRootRoute;
    }

    public function onRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $routeName = $event->getRequest()->attributes->get('_route');

        // skip tool disabled checks for health and liveliness probes
        if (mb_strpos($routeName, 'ping') === 0) {
            return;
        }

        $isToolEnabled = $this->featureToggles->isEnabled(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL);
        $isParallelTestingEnabled = $this->featureToggles->isEnabled(
            FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST
        );

        if ($isToolEnabled || $isParallelTestingEnabled) {
            return; // we're proving an ability to use this tool
        }

        $this->warning('Trying to redirect Curation Tool from decoupled app to Monolith.');

        $requestMethod = $event->getRequest()->getMethod();
        if (!in_array($requestMethod, self::ALLOWED_TO_REDIRECT_METHODS, true)) {
            // we do not have a time to implement the nice solution for such kind of redirects
            // so was decided to fail the request and expect that the user will reload the page
            // or will reach us (Team) via the Slack channel
            throw new RuntimeException(
                sprintf(
                    'We can not redirect "%s" HTTP request to the Monolith.',
                    $requestMethod
                )
            );
        }

        $requestUri = $event->getRequest()->getRequestUri();
        if (mb_strpos($requestUri, $this->appRootRoute) === false) {
            $this->error(
                'The request URI does not contain an app root route. Redirect aborted.',
                ['request_uri' => $requestUri, 'app_root_route' => $this->appRootRoute]
            );

            return;
        }

        $redirectUri = str_replace($this->appRootRoute, self::MONOLITH_CURATION_TOOL_ROOT_ROUTE, $requestUri);

        $this->warning(
            sprintf(
                'Performing redirect Curation Tool from Monolith to decoupled app: "%s".',
                $redirectUri
            ),
            ['request_uri' => $requestUri, 'app_root_route' => $this->appRootRoute]
        );

        $event->setResponse(
            new RedirectResponse($redirectUri)
        );
    }
}
