<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage\Batch_Evaluation_Tracker_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage\Batch_Evaluation_Tracker_Postgres_DAO;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result;
use WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result;

class Batch_Evaluation_Tracker {
  private const DATE_FORMAT = 'Y-m-d H:i:s';

  /**
   * @var Batch_Evaluation_Tracker_DAO
   */
  private $dao;

  /**
   * @var Batch_Evaluation_Tracker_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;

  /**
   * Batch_Evaluation_Tracker constructor.
   *
   * @param Batch_Evaluation_Tracker_DAO $dao DAO
   * @param Batch_Evaluation_Tracker_Postgres_DAO $dao_psql DAO PSQL
   */
  public function __construct(Batch_Evaluation_Tracker_DAO $dao, Batch_Evaluation_Tracker_Postgres_DAO $dao_psql, FeatureTogglesInterface $featureToggles) {
    $this->dao            = $dao;
    $this->dao_psql       = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param Batch_Evaluation_Result $batch_evaluation_result The result of the valuation of the batch
   *
   * @return void
   */
  public function save_batch_evaluation_result(Batch_Evaluation_Result $batch_evaluation_result) {
    $sku_evaluation_records = $this->get_evaluation_records($batch_evaluation_result);

    foreach ($batch_evaluation_result->get_batched_skus_evaluation_result() as $result) {
      $last_check_at = $result->get_evaluation_time()->format(self::DATE_FORMAT);
      $ready_date    = $this->get_ready_date($result, $sku_evaluation_records);

      if (!isset($sku_evaluation_records[$result->get_sku()])) {
        $first_check_at = $result->get_evaluation_time()->format(self::DATE_FORMAT);
        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
          $this->dao_psql->insert($result->get_sku(), $first_check_at, $last_check_at, $ready_date);
        } else {
          $this->dao->insert($result->get_sku(), $first_check_at, $last_check_at, $ready_date);
        }
        continue;
      }
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $this->dao_psql->update($sku_evaluation_records[$result->get_sku()]['ID'], $last_check_at, $ready_date);
      } else {
        $this->dao->update($sku_evaluation_records[$result->get_sku()]['ID'], $last_check_at, $ready_date);
      }
    }
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result $result                 Result
   * @param array                                                                                        $sku_evaluation_records Evaluation Records
   *
   * @return null|string
   */
  private function get_ready_date(Batched_SKU_Evaluation_Result $result, array $sku_evaluation_records) {
    if ($result->get_status() !== QA_Status_Object::APPROVED) {
      return null;
    }

    return $sku_evaluation_records[$result->get_sku()]['ReadyDateAt'] ?? $result->get_evaluation_time()->format(self::DATE_FORMAT);
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Result $batch_evaluation_result Batch Evaluation Result
   *
   * @return array
   */
  private function get_evaluation_records(Batch_Evaluation_Result $batch_evaluation_result) : array {
    $sku_list = array_map(
        function ($sku) {
          /**
           * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batched_SKU_Evaluation_Result $sku
           */
          return $sku->get_sku();
        }, $batch_evaluation_result->get_batched_skus_evaluation_result()
    );

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $sku_data = $this->dao_psql->get_evaluation_records($sku_list);
    } else {
      $sku_data = $this->dao->get_evaluation_records($sku_list);
    }

    $results = [];
    foreach ($sku_data as $sku) {
      $results[$sku['SKU']] = $sku;
    }

    return $results;
  }

}
