<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Config_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Style_Suggestion_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Manufacturer_Data;

class Automatic_Style_Curation_Save<PERSON> {
  /**
   * @var Curation_Decision_Service
   */
  private $curationDecisionService;

  /**
   * @var Config_Loader
   */
  private $configLoader;

  /**
   * @var Style_Suggestion_Storage
   */
  private $styleSuggestionStorage;

  /**
   * Automatic_Style_Curation_Saver constructor.
   *
   * @param Curation_Decision_Service $curationDecisionService Curation decision service
   * @param Config_Loader             $configLoader            Config Loader
   * @param Style_Suggestion_Storage  $styleSuggestionStorage  Style Suggestion Storage
   */
  public function __construct(
      Curation_Decision_Service $curationDecisionService,
      Config_Loader $configLoader,
      Style_Suggestion_Storage $styleSuggestionStorage
  ) {
    $this->curationDecisionService = $curationDecisionService;
    $this->configLoader            = $configLoader;
    $this->styleSuggestionStorage  = $styleSuggestionStorage;
  }

  /**
   * @param int       $batchID    Batch ID
   * @param Section[] $sections   Curation section
   * @param int       $employeeID Employee ID
   *
   * @return int
   * @throws \Exception
   */
  public function execute(int $batchID, array $sections, int $employeeID) : int {
    $savedAt           = date('Y-m-d H:i:s');
    $savedItemsCounter = 0;

    $items              = $this->getNotCuratedItems($sections);
    $styleManufacturers = $this->configLoader->getStyleManufacturersForBatch($batchID);

    $skusList = array_map(
        function (Curation_Item $item) {
          return $item->get_sku();
        }, $items
    );

    try {
      $suggestedStylesCollection    = $this->styleSuggestionStorage->get_style_suggestion($skusList);
      $suggestedSubstylesCollection = $this->styleSuggestionStorage->get_substyle_suggestion($skusList);
    } catch (Automatic_Style_Curation_Exception $apiException) {
      // Style suggestion API thrown an exception? Failback to default curation
      return $savedItemsCounter;
    }

    foreach ($items as $item) {
      $sku = $item->get_sku();

      $skuSuggestedStyles  = $suggestedStylesCollection->get_styles_for_sku($sku);
      $styleThreshold      = $suggestedStylesCollection->get_threshold_for_sku($sku);
      $autoSelectedStyleID = $this->findBestFitStyleSuggestion($skuSuggestedStyles, $styleThreshold);

      $skuSuggestedSubstyles  = $suggestedSubstylesCollection->get_substyles_for_sku($sku);
      $substyleThreshold      = $suggestedSubstylesCollection->get_threshold_for_sku($sku);
      $autoSelectedSubstyleID = $this->findBestFitSubStyleSuggestion($skuSuggestedSubstyles, $substyleThreshold);

      // Pre-select the final style or final substyle if they are set
      $item->set_final_style_id($autoSelectedStyleID);
      $item->set_final_sub_style_id($autoSelectedSubstyleID);

      if (empty($autoSelectedStyleID) || empty($autoSelectedSubstyleID)) {
        continue;
      }

      $manufacturer_id = $this->findManufacturer($autoSelectedSubstyleID, $item->get_price_tier(), $styleManufacturers);
      if (empty($manufacturer_id)) {
        continue;
      }

      $this->curationDecisionService->save_curated(
          $batchID,
          [$sku],
          $item->get_price_tier(),
          $autoSelectedStyleID,
          $autoSelectedSubstyleID,
          $manufacturer_id,
          $item->get_final_granular_style_id(),
          $savedAt,
          $employeeID,
          Curation_Decision_Source::automatic_by_style()
      );

      $savedItemsCounter++;
    }

    return $savedItemsCounter;
  }

  /**
   * @param Style_Suggestion[] $styleSuggestions Style Suggestion array
   * @param float              $threshold        Master style threshold
   *
   * @return int|null
   */
  private function findBestFitStyleSuggestion(array $styleSuggestions, ?float $threshold) : ?int {
    // We should check threshold for null, because 0 is a valid value
    if (empty($styleSuggestions) || $threshold === null) {
      return null;
    }

    \usort(
        $styleSuggestions,
        function (Style_Suggestion $suggestion1, Style_Suggestion $suggestion2) {
          return $suggestion1->get_rank() - $suggestion2->get_rank();
        }
    );

    return ($styleSuggestions[0]->get_probability() >= $threshold) ? $styleSuggestions[0]->get_style_id() : null;
  }

  /**
   * @param Substyle_Suggestion[] $styleSuggestions Style Suggestion array
   * @param float                 $threshold        Master style threshold
   *
   * @return int|null
   */
  private function findBestFitSubStyleSuggestion(array $styleSuggestions, ?float $threshold) : ?int {
    // We should check threshold for null, because 0 is a valid value
    if (empty($styleSuggestions) || $threshold === null) {
      return null;
    }

    \usort(
        $styleSuggestions,
        function (Substyle_Suggestion $suggestion1, Substyle_Suggestion $suggestion2) {
          return $suggestion1->get_rank() - $suggestion2->get_rank();
        }
    );

    return ($styleSuggestions[0]->get_probability() >= $threshold) ? $styleSuggestions[0]->get_substyle_id() : null;
  }

  /**
   * @param int                       $substyleId         Substyle id
   * @param int                       $priceTier          price tier
   * @param Style_Manufacturer_Data[] $styleManufacturers Style Manufacturers config arrat
   *
   * @return int|null
   */
  private function findManufacturer(int $substyleId, int $priceTier, array $styleManufacturers) : ?int {
    foreach ($styleManufacturers as $styleManufacturer) {
      if ($styleManufacturer->substyleId() === $substyleId && $styleManufacturer->priceTier() === $priceTier) {
        return $styleManufacturer->manufacturerId();
      }
    }

    return null;
  }

  /**
   * @param Section[] $sections Section data
   *
   * @return Curation_Item[]
   */
  private function getNotCuratedItems(array $sections) : array {
    $items = [];

    foreach ($sections as $section) {
      $notCurated = array_filter(
          $section->get_curation_items(),
          function (Curation_Item $curationItem) {
            return empty($curationItem->get_saved_at());
          }
      );

      $items = array_merge($items, $notCurated);
    }

    return $items;
  }
}
