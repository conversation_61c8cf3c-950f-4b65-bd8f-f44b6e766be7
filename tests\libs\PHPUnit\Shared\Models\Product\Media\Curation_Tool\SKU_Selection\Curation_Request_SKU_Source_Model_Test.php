<?php
/**
 * Tests for the \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Source_Model
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Models\Product\Media\Curation_Tool\SKU_Selection;

use Exception;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Eligibility_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Holdout_Manufacturer_Part_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Holdout_Manufacturer_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Master_Core_Class_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Perigold_Only_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Right_Product_Status_Requirement;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Source_Model;

class Curation_Request_SKU_Source_Model_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Eligibility_Requirement
     */
    private $eligibility_requirement;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Right_Product_Status_Requirement
     */
    private $right_product_status_requirement;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Master_Core_Class_Requirement
     */
    private $master_core_class_requirement;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Holdout_Manufacturer_Requirement
     */
    private $holdout_manufacturer_requirement;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Perigold_Only_Requirement
     */
    private $perigold_only_requirement;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Automatic_Job_Holdout_Manufacturer_Part_Requirement
     */
    private $holdout_manufacturer_part_requirement;

    /**
     * SetUp
     *
     * @return void
     */
    public function setUp(): void
    {
        $this->eligibility_requirement = $this->prophesize(Automatic_Job_Eligibility_Requirement::class);
        $this->right_product_status_requirement = $this->prophesize(Automatic_Job_Right_Product_Status_Requirement::class);
        $this->master_core_class_requirement = $this->prophesize(Automatic_Job_Master_Core_Class_Requirement::class);
        $this->holdout_manufacturer_requirement = $this->prophesize(Automatic_Job_Holdout_Manufacturer_Requirement::class);
        $this->perigold_only_requirement = $this->prophesize(Automatic_Job_Perigold_Only_Requirement::class);
        $this->holdout_manufacturer_part_requirement = $this->prophesize(Automatic_Job_Holdout_Manufacturer_Part_Requirement::class);

        $this->eligibility_requirement->is_eligible(Argument::any())->willReturn(true);
        $this->right_product_status_requirement->is_right_product_status(Argument::any())->willReturn(true);
        $this->master_core_class_requirement->is_master_core_class(Argument::any())->willReturn(true);
        $this->holdout_manufacturer_requirement->is_holdout_manufacturer(Argument::any())->willReturn(true);
        $this->perigold_only_requirement->is_perigold_only(Argument::any())->willReturn(true);
        $this->holdout_manufacturer_part_requirement->has_holdout_manufacturer_part(Argument::any())->willReturn(true);
    }

    /**
     * @return \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container
     */
    private function get_requirement_container()
    {
        return new Requirement_Container(
            $this->eligibility_requirement->reveal(),
            $this->right_product_status_requirement->reveal(),
            $this->master_core_class_requirement->reveal(),
            $this->holdout_manufacturer_requirement->reveal(),
            $this->perigold_only_requirement->reveal(),
            $this->holdout_manufacturer_part_requirement->reveal()
        );
    }

    /**
     *  Test that sync_stock_product throws exception
     *
     * @return void
     */
    public function test_instantiate_sku_exception()
    {
        $this->expectException(Exception::class);

        new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            '',
            []
        );
    }

    /**
     * @return void
     */
    public function test_get_sku()
    {
        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            []
        );

        $this->assertEquals(
            'ABC123',
            $subject->get_sku()
        );
    }

    /**
     * @dataProvider get_id_data_provider
     * @param string $sku      SKU
     * @param mixed  $input    Input
     * @param int    $expected Expected
     *
     * @return void
     */
    public function test_get_id($sku, $input, $expected)
    {
        $map[Curation_Request_SKU_Source_Model::COLUMN_ID] = $input;

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            $sku,
            $map
        );
        $this->assertEquals($expected, $subject->get_id());
    }

    /**
     * @return array
     */
    public function get_id_data_provider()
    {
        return [
            ['ABC123', null, 0],
            ['ABC123', false, 0],
            ['BBB023', 0, 0],
            ['CCC231', 1, 1],
            ['ACC231', 99, 99],
        ];
    }

    /**
     * @dataProvider is_standard_brand_data_provider
     * @param string $sku      SKU
     * @param mixed  $input    Input
     * @param int    $expected Expected
     *
     * @return void
     */
    public function test_is_standard_brand($sku, $input, $expected)
    {
        $map[Curation_Request_SKU_Source_Model::COLUMN_BRAND_TYPE] = $input;

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            $sku,
            $map
        );
        $this->assertEquals($expected, $subject->is_standard_brand());
    }

    /**
     * @return array
     */
    public function is_standard_brand_data_provider()
    {
        return [
            ['BBB023', Curation_Request_SKU_Base_Model::MANUFACTURER_STANDARD_BRAND, true],
            ['CCC231', Curation_Request_SKU_Base_Model::MANUFACTURER_LEGACY_BRAND, true],
            ['ACC231', 99999, false]
        ];
    }

    /**
     * @return array
     */
    public function import_status_id_data_provider()
    {
        return [
            [
                'ABC123',
                [
                    Curation_Request_SKU_Source_Model::COLUMN_ID => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_MASTER_CORE_CLASS => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_WAYFAIR_CHANNEL => null,
                    Curation_Request_SKU_Source_Model::COLUMN_BRAND_TYPE => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_ACTIVE_JOIN_SUPPLIER => null,
                    Curation_Request_SKU_Source_Model::COLUMN_PRODUCT_STATUS => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IRP_ACTIVE => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IRO_ACTIVE => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_RIGHT_ASSIGNED_SUPPLIER_METHOD => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_PERIGOLD_ONLY => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER_PART => null,
                ], Curation_Request_SKU_Base_Model::IMPORT_STATUS_NOT_APPLICABLE
            ],
            [
                'BBB222',
                [
                    Curation_Request_SKU_Source_Model::COLUMN_ID => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_MASTER_CORE_CLASS => true,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER => false,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_WAYFAIR_CHANNEL => true,
                    Curation_Request_SKU_Source_Model::COLUMN_BRAND_TYPE => 0,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_ACTIVE_JOIN_SUPPLIER => true,
                    Curation_Request_SKU_Source_Model::COLUMN_PRODUCT_STATUS => 999,
                    Curation_Request_SKU_Source_Model::COLUMN_IRP_ACTIVE => 1,
                    Curation_Request_SKU_Source_Model::COLUMN_IRO_ACTIVE => 1,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_RIGHT_ASSIGNED_SUPPLIER_METHOD => false,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_PERIGOLD_ONLY => false,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER_PART => false
                ], Curation_Request_SKU_Base_Model::IMPORT_STATUS_NOT_APPLICABLE
            ],
            [
                'CCC333',
                [
                    Curation_Request_SKU_Source_Model::COLUMN_ID => null,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_MASTER_CORE_CLASS => true,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER => false,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_WAYFAIR_CHANNEL => true,
                    Curation_Request_SKU_Source_Model::COLUMN_BRAND_TYPE => 0,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_ACTIVE_JOIN_SUPPLIER => true,
                    Curation_Request_SKU_Source_Model::COLUMN_PRODUCT_STATUS => 1,
                    Curation_Request_SKU_Source_Model::COLUMN_IRP_ACTIVE => 1,
                    Curation_Request_SKU_Source_Model::COLUMN_IRO_ACTIVE => 1,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_RIGHT_ASSIGNED_SUPPLIER_METHOD => true,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_PERIGOLD_ONLY => false,
                    Curation_Request_SKU_Source_Model::COLUMN_IS_HOLDOUT_MANUFACTURER_PART => false
                ], Curation_Request_SKU_Base_Model::IMPORT_STATUS_AWAITING
            ]
        ];
    }

    /**
     * @param bool   $is_right_product_status product_status
     * @param string $message                 error message
     *
     * @dataProvider calculate_right_product_status_data_provider
     *
     * @return void
     */
    public function test_calculate_right_product_status($is_right_product_status, $message)
    {
        $this->right_product_status_requirement->is_right_product_status(Argument::any())->willReturn($is_right_product_status);

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            []
        );

        $this->assertEquals($is_right_product_status, $subject->is_right_product_status(), $message);
    }

    /**
     * @return array
     */
    public function calculate_right_product_status_data_provider()
    {
        return [
            [true, 'is_right_product_status is true'],
            [false, 'is_right_product_status is false'],
        ];
    }

    /**
     * @param bool   $irp_active     irp_active
     * @param bool   $iro_active     iro_active
     * @param int    $product_status product_status
     * @param bool   $expected       expected
     * @param string $message        message
     *
     * @dataProvider calculate_has_images_data_provider
     *
     * @return void
     */
    public function test_calculate_has_images($irp_active, $iro_active, $product_status, $expected, $message)
    {
        $map[Curation_Request_SKU_Source_Model::COLUMN_IRP_ACTIVE] = $irp_active;
        $map[Curation_Request_SKU_Source_Model::COLUMN_IRO_ACTIVE] = $iro_active;
        $map[Curation_Request_SKU_Source_Model::COLUMN_PRODUCT_STATUS] = $product_status;

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            $map
        );
        $this->assertEquals($expected, $subject->has_images(), $message);
    }

    /**
     * @return array
     */
    public function calculate_has_images_data_provider()
    {
        return [
            [false, false, 2, null, 'Product Status is null'],
            [true, true, 2, true, 'irp_active and iro_active are true'],
            [true, false, 2, true, 'irp_active is true'],
            [false, true, 2, true, 'iro_active is true'],
            [false, false, 1, false, 'irp_active and iro_active are true and product status is not 2'],
        ];
    }

    /**
     * @param bool   $is_eligible is_eligible
     * @param string $message     message
     *
     * @dataProvider is_eligible_data_provider
     *
     * @return void
     */
    public function test_calculate_eligible($is_eligible, $message)
    {
        $this->eligibility_requirement->is_eligible(Argument::any())->willReturn($is_eligible);

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            []
        );

        $this->assertEquals($is_eligible, $subject->calculate_eligible(), $message);
    }

    /**
     * @return array
     */
    public function is_eligible_data_provider()
    {
        return [
            [true, 'is_eligible is true'],
            [false, 'is_eligible is false'],
        ];
    }

    /**
     * @param bool   $is_eligible             is_eligible
     * @param bool   $is_right_product_status is_right_product_status
     * @param array  $image_map               image_map
     * @param bool   $expected                expected
     * @param string $message                 message
     *
     * @dataProvider calculate_ready_for_curation_id_data_provider
     *
     * @return void
     */
    public function test_calculate_ready_for_curation_id($is_eligible, $is_right_product_status, $image_map, $expected, $message)
    {
        $this->eligibility_requirement->is_eligible(Argument::any())->willReturn($is_eligible);
        $this->right_product_status_requirement->is_right_product_status(Argument::any())->willReturn($is_right_product_status);

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            $image_map
        );
        $this->assertEquals($expected, $subject->calculate_ready_for_curation_id(), $message);
    }

    /**
     * @param bool   $is_eligible             is_eligible
     * @param bool   $is_right_product_status is_right_product_status
     * @param array  $image_map               image_map
     * @param bool   $expected                expected
     * @param string $message                 message
     *
     * @dataProvider calculate_ready_for_curation_id_data_provider
     *
     * @return void
     */
    public function test_calculate_import_status_id($is_eligible, $is_right_product_status, $image_map, $expected, $message)
    {
        $this->eligibility_requirement->is_eligible(Argument::any())->willReturn($is_eligible);
        $this->right_product_status_requirement->is_right_product_status(Argument::any())->willReturn($is_right_product_status);

        $subject = new Curation_Request_SKU_Source_Model(
            $this->get_requirement_container(),
            'ABC123',
            $image_map
        );
        $expected = ($expected !== 1 ? 0 : 1);
        $this->assertEquals($expected, $subject->calculate_import_status_id(), $message);
    }

    /**
     * @return array
     */
    public function calculate_ready_for_curation_id_data_provider()
    {
        return [
            [true, true, ['irp_active' => 1, 'sale_price' => 100], 1, 'is_eligible is true, is_right_product_status is true, has_images is true'],
            [true, true, ['irp_active' => 0, 'iro_active' => 0, 'product_status' => 2, 'sale_price' => 100], 1, 'is_eligible is true, is_right_product_status is true, has_images is null'],
            [true, true, [], 2, 'is_eligible is true, is_right_product_status is true, has_images is false'],
            [true, false, ['irp_active' => 1, 'sale_price' => 100], 2, 'is_eligible is true, is_right_product_status is false, has_images is true'],
            [true, false, [], 2, 'is_eligible is true, is_right_product_status is false, has_images is false'],
            [false, true, ['irp_active' => 1, 'sale_price' => 100], 0, 'is_eligible is false, is_right_product_status is true, has_images is true'],
        ];
    }
}
