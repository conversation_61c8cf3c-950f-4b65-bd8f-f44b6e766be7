<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Infrastructure;

use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use WF\BrandWorkflows\PuREST\Contract\ClientInterface as PuRESTClientInterface;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTException;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTRequestException;

class Client implements PuRESTClientInterface
{
    private ClientInterface $httpClient;

    /**
     * @param ClientInterface $httpClient
     */
    public function __construct(
        ClientInterface $httpClient
    ) {
        $this->httpClient = $httpClient;
    }

    /**
     * @param RequestInterface $request PuREST request. See WF\BrandWorkflows\PuREST\Infrastructure\RequestFactory
     *
     * @return ResponseInterface
     *
     * @throws PuRESTException
     */
    public function request(RequestInterface $request): ResponseInterface
    {
        try {
            return $this->httpClient->sendRequest($request);
        } catch (\Throwable $exception) {
            throw new PuRESTRequestException($exception->getMessage(), 0, $exception);
        }
    }
}
