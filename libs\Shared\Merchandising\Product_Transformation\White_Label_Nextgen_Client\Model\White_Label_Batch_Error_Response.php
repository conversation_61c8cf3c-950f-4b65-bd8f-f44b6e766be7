<?php declare(strict_types = 1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model;

final class White_Label_Batch_Error_Response implements White_Label_Batch_Base_Response {
  /**
   * @var string
   */
  private $errorCode;

  /**
   * @var array
   */
  private $errorMessages;

  /**
   * @param string $errorCode     Error Code
   * @param array  $errorMessages Error Messages
   */
  public function __construct(string $errorCode, array $errorMessages) {
    $this->errorCode     = $errorCode;
    $this->errorMessages = $errorMessages;
  }


  /**
   * @return array
   */
  public function getErrorMessages() : array {
    return $this->errorMessages;
  }

  /**
   * @return string
   */
  public function getErrorCode() : string {
    return $this->errorCode;
  }

  /**
   * @return bool
   */
  public function isRequestSuccessful() : int {
    return 400;
  }
}
