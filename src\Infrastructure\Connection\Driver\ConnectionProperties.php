<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace App\Infrastructure\Connection\Driver;

final class ConnectionProperties
{
    private string $dbCode;
    private string $username;
    private string $credentialName;
    private string $appName;
    private string $auditString;
    private array $connectionStringOptions;
    private array $driverOptions;

    /**
     * @param string $dbCode                                 db code for legacy Wayfair databases
     * @param string $username                               user to login as
     * @param string $credentialName                         credential name - password will be decrypted internally
     * @param string $appName                                application identifier metadata
     * @param string $auditString                            optional audit string to set in connection context
     * @param array<string, string> $connectionStringOptions optional settings to pass to the connection string
     * @param array<int, int> $driverOptions                 optional settings to pass to the driver
     */
    public function __construct(
        string $dbCode,
        string $username,
        string $credentialName,
        string $appName,
        string $auditString = '',
        array $connectionStringOptions = [],
        array $driverOptions = []
    ) {
        $this->dbCode = $dbCode;
        $this->username = $username;
        $this->credentialName = $credentialName;
        $this->appName = $appName;
        $this->auditString = $auditString;
        $this->connectionStringOptions = $connectionStringOptions;
        $this->driverOptions = $driverOptions;
    }

    public function toArray(): array
    {
        return [
            $this->dbCode,
            $this->username,
            $this->credentialName,
            $this->appName,
            $this->auditString,
            $this->connectionStringOptions,
            $this->driverOptions,
        ];
    }
}
