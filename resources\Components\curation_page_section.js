/**
 * CurationPageSection
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import CurationSku from './curation_sku';
import CurationSharedSku from './curation_shared_sku';
import CurationPageHeader from './curation_page_header';
import CurationToolShapes, {PAGE_LIMIT} from './curation_tool_shapes';
import {SKU_TYPE} from './curation_sku_constants';
import Translation from '@wayfair/translation';
import {Text, TEXT_ALIGNMENTS, TEXT_STYLE} from '@wayfair/homebase-extranet';
import CurationPagination from "./CurationPagination";
import {Loading} from '@homebase/core';

const isRowSelected = (selectedRows, sku) => {
  return selectedRows.includes(sku);
};

const isRowChanged = (changedRows, sku) => {
  return changedRows.includes(sku);
};

// const getSkusSavedCount = curationItems => {
//   return curationItems.filter(
//     item => item.savedAt && item.type !== SKU_TYPE.SHARED
//   ).length;
// };
//
// const getSkusTotalCount = curationItems => {
//   return curationItems.filter(item => item.type !== SKU_TYPE.SHARED).length;
// };

const isSubsectionEnabled = (curationItems, skuType) => {
  return curationItems.some(item => item.type === skuType);
};



const CurationPageSection = ({
  title,
  curationConfig,
  curationItems,
    savingState,
  onHeaderDecisionChange,
  onCurationItemDecisionChange,
  onSelectAll,
  onSelectionChange,
  onSaveAllClick,
  checkedIndeterminate,
  checkedAll,
  headerDecision,
  onSaveClick,
  onSharedSkuSave,
  selectedRows,
  changedRows,
  isExpanded,
  onExpandClick,
  isAssortmentWorkflowOffshoreUser,
  suggestedStyleRejectionReasons,
  skusTotalCount,
  skusSavedCount,
  fetchSectionDataFromApi,
  isLoading,
  error,
  currentPage
}) => {
  const getClassifiedSkuList = (curationItems) => {

    const classifiedSkuList = {
      [SKU_TYPE.SIMPLE]: [],
      [SKU_TYPE.SHARED]: [],
      [SKU_TYPE.KIT_PARENT]: [],
      [SKU_TYPE.CONTEXT]: [],
      [SKU_TYPE.CANDIDATE]: [],
    }
    for (let k = 0; k < curationItems.length; k++) {
      // eslint-disable-next-line react/prop-types
      curationItems[k].isSaving = savingState.isSaving && savingState.skusSaving.includes(curationItems[k].sku);
      switch (curationItems[k].type) {
        case SKU_TYPE.SIMPLE :
          classifiedSkuList[SKU_TYPE.SIMPLE].push(curationItems[k]);
          break;
        case SKU_TYPE.SHARED :
          classifiedSkuList[SKU_TYPE.SHARED].push(curationItems[k]);
          break;
        case SKU_TYPE.KIT_PARENT :
          classifiedSkuList[SKU_TYPE.KIT_PARENT].push(curationItems[k]);
          break;
        case SKU_TYPE.CONTEXT :
          classifiedSkuList[SKU_TYPE.CONTEXT].push(curationItems[k]);
          break;
        case SKU_TYPE.CANDIDATE :
          classifiedSkuList[SKU_TYPE.CANDIDATE].push(curationItems[k]);
          break;
      }
    }
    return classifiedSkuList;
  }

  const frestCurationItems = [...curationItems];

  const classifiedSkuList = getClassifiedSkuList(frestCurationItems);
  function getCurationItemsSimpleSkus() {
    const curationItemsUpdated = [];
    for (let i = 0; i < classifiedSkuList[SKU_TYPE.SIMPLE].length; i++) {
        curationItemsUpdated.push((
            <CurationSku
                curationConfig={curationConfig}
                isSaving={classifiedSkuList[SKU_TYPE.SIMPLE][i].isSaving}
                isSaved={classifiedSkuList[SKU_TYPE.SIMPLE][i].savedBy != null}
                key={classifiedSkuList[SKU_TYPE.SIMPLE][i].sku}
                curationItem={classifiedSkuList[SKU_TYPE.SIMPLE][i]}
                isChanged={isRowChanged(changedRows, classifiedSkuList[SKU_TYPE.SIMPLE][i].sku)}
                isSelected={isRowSelected(selectedRows, classifiedSkuList[SKU_TYPE.SIMPLE][i].sku)}
                onSelectionChange={onSelectionChange}
                onCurationItemDecisionChange={onCurationItemDecisionChange}
                onSaveClick={onSaveClick}
                isAssortmentWorkflowOffshoreUser={
                  isAssortmentWorkflowOffshoreUser
                }
                suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
            />
        ))

    }
    return curationItemsUpdated
  }

  function getCurationItemsShared() {
    const curationItemsUpdated = [];
    for (let i = 0; i < classifiedSkuList[SKU_TYPE.SHARED].length; i++) {

        curationItemsUpdated.push((
            <CurationSharedSku
                key={classifiedSkuList[SKU_TYPE.SHARED][i].sku}
                curationItem={classifiedSkuList[SKU_TYPE.SHARED][i]}
                onSave={onSharedSkuSave}
            />
        ))

    }
    return curationItemsUpdated;
  }

  function getCurationItemsKitParents() {
    const curationItemsUpdated = [];
    for (let i = 0; i < classifiedSkuList[SKU_TYPE.KIT_PARENT].length; i++) {
        curationItemsUpdated.push((<CurationSku
            curationConfig={curationConfig}
            isSaving={classifiedSkuList[SKU_TYPE.KIT_PARENT][i].isSaving}
            isSaved={classifiedSkuList[SKU_TYPE.KIT_PARENT][i].savedBy != null}
            key={classifiedSkuList[SKU_TYPE.KIT_PARENT][i].sku}
            curationItem={classifiedSkuList[SKU_TYPE.KIT_PARENT][i]}
            isChanged={isRowChanged(changedRows, classifiedSkuList[SKU_TYPE.KIT_PARENT][i].sku)}
            isSelected={isRowSelected(selectedRows, classifiedSkuList[SKU_TYPE.KIT_PARENT][i].sku)}
            onSelectionChange={onSelectionChange}
            onCurationItemDecisionChange={onCurationItemDecisionChange}
            onSaveClick={onSaveClick}
            isAssortmentWorkflowOffshoreUser={
              isAssortmentWorkflowOffshoreUser
            }
            suggestedStyleRejectionReasons={
              suggestedStyleRejectionReasons
            }
        />))
    }
    return curationItemsUpdated;
  }

  function getCurationItemsContext() {
    const curationItemsUpdated = [];
    for (let i = 0; i < classifiedSkuList[SKU_TYPE.CONTEXT].length; i++) {
        curationItemsUpdated.push((<CurationSku
            curationConfig={curationConfig}
            isSaving={classifiedSkuList[SKU_TYPE.CONTEXT][i].isSaving}
            isSaved={classifiedSkuList[SKU_TYPE.CONTEXT][i].savedBy != null}
            key={classifiedSkuList[SKU_TYPE.CONTEXT][i].sku}
            curationItem={classifiedSkuList[SKU_TYPE.CONTEXT][i]}
            isChanged={isRowChanged(changedRows, classifiedSkuList[SKU_TYPE.CONTEXT][i].sku)}
            isSelected={isRowSelected(selectedRows, classifiedSkuList[SKU_TYPE.CONTEXT][i].sku)}
            onSelectionChange={onSelectionChange}
            onCurationItemDecisionChange={onCurationItemDecisionChange}
            onSaveClick={onSaveClick}
            isAssortmentWorkflowOffshoreUser={
              isAssortmentWorkflowOffshoreUser
            }
            suggestedStyleRejectionReasons={
              suggestedStyleRejectionReasons
            }
        />));
      }
    return curationItemsUpdated;
  }

  function getCurationItemsCandidate() {
    const curationItemsUpdated = [];
    for (let i = 0; i < classifiedSkuList[SKU_TYPE.CANDIDATE].length; i++) {
        curationItemsUpdated.push((
            <CurationSku
                curationConfig={curationConfig}
                isSaving={classifiedSkuList[SKU_TYPE.CANDIDATE][i].isSaving}
                isSaved={classifiedSkuList[SKU_TYPE.CANDIDATE][i].savedBy != null}
                key={classifiedSkuList[SKU_TYPE.CANDIDATE][i].sku}
                curationItem={classifiedSkuList[SKU_TYPE.CANDIDATE][i]}
                isChanged={isRowChanged(changedRows, classifiedSkuList[SKU_TYPE.CANDIDATE][i].sku)}
                isSelected={isRowSelected(selectedRows, classifiedSkuList[SKU_TYPE.CANDIDATE][i].sku)}
                onSelectionChange={onSelectionChange}
                onCurationItemDecisionChange={onCurationItemDecisionChange}
                onSaveClick={onSaveClick}
                isAssortmentWorkflowOffshoreUser={
                  isAssortmentWorkflowOffshoreUser
                }
                suggestedStyleRejectionReasons={
                  suggestedStyleRejectionReasons
                }
            />
        ));

    }
    return curationItemsUpdated
  }

  return (
  <div>
    <CurationPageHeader
      title={title}
      curationConfig={curationConfig}
      onDecisionChange={onHeaderDecisionChange}
      onSelectAll={onSelectAll}
      onSaveAllClick={onSaveAllClick}
      checkedAll={checkedAll}
      checkedIndeterminate={checkedIndeterminate}
      decision={headerDecision}
      isExpanded={isExpanded}
      curationItems={curationItems}
      selectedRows={selectedRows}
      onExpandClick={onExpandClick}
      // skusTotalCount={getSkusTotalCount(curationItems)}
      // skusSavedCount={getSkusSavedCount(curationItems)}
      skusTotalCount={skusTotalCount}
      skusSavedCount={skusSavedCount}
      suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
    />
    {isExpanded && (
      <div>
          {skusTotalCount > PAGE_LIMIT && <CurationPagination
              fetchSectionDataFromApi={(pageNum, limit) =>
                  fetchSectionDataFromApi(pageNum, limit)
              }
              totalSkus={skusTotalCount}
              currentPage={currentPage}
          />}
        {isLoading ? <Loading text="Loading" /> :
            <>
              {error &&
                  <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                    {error}
                  </Text> }
        {classifiedSkuList[SKU_TYPE.SIMPLE].length>0 && getCurationItemsSimpleSkus()}
        {classifiedSkuList[SKU_TYPE.SHARED].length>0 && (
          <div>
            {/*<p className="text_xxl text_bold text_center padding_top_small">*/}
            <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.SharedComponents" />
            </Text>
            {getCurationItemsShared()}
          </div>
        )}
        {classifiedSkuList[SKU_TYPE.KIT_PARENT].length>0 && (
          <div>
            {/*<p className="text_xxl text_bold text_center padding_top_small">*/}
            <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.Kits" />
            </Text>
            {getCurationItemsKitParents()}
          </div>
        )}
        {classifiedSkuList[SKU_TYPE.CONTEXT].length>0 && (
          <div>
            {/*<p className="text_xxl text_bold text_center padding_top_small">*/}
            <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.ContextSkus" />
            </Text>
            {getCurationItemsContext()}
          </div>
        )}
        {classifiedSkuList[SKU_TYPE.CANDIDATE].length>0 && (
          <div>
            {/*<p className="text_xxl text_bold text_center padding_top_small">*/}
            <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
              <Translation msgid="CurationTool.CandidateSkus" />
            </Text>
            {getCurationItemsCandidate()}
          </div>
        )}
      </>
    }
          {skusTotalCount > PAGE_LIMIT && <CurationPagination
              fetchSectionDataFromApi={(pageNum, limit) =>
                  fetchSectionDataFromApi(pageNum, limit)
              }
              totalSkus={skusTotalCount}
              currentPage={currentPage}
          />}
      </div>
    )}
  </div>
)};

CurationPageSection.propTypes = {
  title: PropTypes.string.isRequired,
  curationConfig: CurationToolShapes.curationConfigShape.isRequired,
  curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  changedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  checkedIndeterminate: PropTypes.bool.isRequired,
  checkedAll: PropTypes.bool.isRequired,
  headerDecision: CurationToolShapes.decisionShape.isRequired,
  isExpanded: PropTypes.bool,
  onCurationItemDecisionChange: PropTypes.func.isRequired,
  onHeaderDecisionChange: PropTypes.func.isRequired,
  onSelectAll: PropTypes.func.isRequired,
  onSelectionChange: PropTypes.func.isRequired,
  onSaveAllClick: PropTypes.func.isRequired,
  onSaveClick: PropTypes.func.isRequired,
  onSharedSkuSave: PropTypes.func.isRequired,
  onExpandClick: PropTypes.func.isRequired,
  isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
  suggestedStyleRejectionReasons: PropTypes.arrayOf(
    CurationToolShapes.reasonShape
  ).isRequired,
  skusTotalCount:PropTypes.number.isRequired,
  skusSavedCount: PropTypes.number.isRequired,
  fetchSectionDataFromApi: PropTypes.func.isRequired,
  isLoading:PropTypes.bool.isRequired,
  error: PropTypes.string.isRequired,
  currentPage: PropTypes.number.isRequired
};

CurationPageSection.defaultProps = {
  isExpanded: true,
};

export default CurationPageSection;
