.PHONY: default
default: var composer build_and_up assets

# Build and spin up your dev container services
.PHONY: build_and_up
build_and_up:
	docker-compose up -d --build devbox ingress

# Builds the Frontend files
.PHONY: build-frontend
build-frontend:
	docker-compose run --no-deps --user $$(echo "`id -u`:`id -g`") --entrypoint yarn frontend install
	docker-compose run --no-deps --user $$(echo "`id -u`:`id -g`") --entrypoint yarn frontend build

# Pull secrets from the Vault for this service
.PHONY: pull-secrets
pull-secrets:
	docker-compose run local-secret-distributor

# Stops all currently running containers
.PHONY: stop-all
stop-all:
	docker container stop $$(docker container ps -q)

# Install composer from within the dev container (running composer install outside of this is not recommended)
.PHONY: composer
composer:
	docker-compose run --no-deps --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv install -n

.PHONY: composer-update
composer-update:
	docker-compose run --no-deps --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv update

# Run the verify scripts defined in composer.json from within the dev container
.PHONY: tests
tests:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv verify

# Executes PHPStan checks
.PHONY: phpstan
phpstan:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv phpstan

# Executes checks of PHP versions compatible code
.PHONY: php-versions-check
php-versions-check:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv php-versions-check

# Executes easy-code-style checks
.PHONY: ecs
ecs:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv ecs

# Executes easy-code-style checks & fix errors where it's possible
.PHONY: ecs-fix
ecs-fix:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv ecs-fix

# Executes Symfony container lint checks
.PHONY: ecs-fix
lint-container:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv lint-container

# Executes PHPUnit tests
.PHONY: phpunit
phpunit:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv phpunit

# Executes PHPUnit tests on PHP 8.1
.PHONY: phpunit81
phpunit81:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox81 -vvv phpunit

# Executes Mutation tests
.PHONY: tests-mutation
tests-mutation:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv tests-mutation

# Install composer for prod from within the dev container and build the prod service
.PHONY: prod-build
prod-build:
	docker-compose run --no-deps --user $$(echo "`id -u`:`id -g`") --entrypoint composer devbox -vvv install --no-dev -n
	docker-compose build prod

.PHONY: prod
prod: prod-build
	docker-compose up prod

.PHONY: fix-ownership
fix-ownership:
	sudo chown -R $$(echo "`id -u`:`id -g`") .

var:
	mkdir var

.PHONY: cache-warmup
cache-warmup:
	docker-compose run --entrypoint "./bin/console cache:warmup" devbox

cache-clear:
	docker-compose run --entrypoint "./bin/console cache:clear" devbox

# Run the symfony command that initializes assets
.PHONY: assets
assets:
	docker-compose run --user $$(echo "`id -u`:`id -g`") --entrypoint php devbox bin/console assets:install public

# Spin up and open a bash shell inside dev container (using dc run)
.PHONY: nbash
nbash:
	docker-compose run --user 80:80 devbox bash

# jump into running container bash with your current user
.PHONY: ibash
ibash:
	docker-compose exec --user $$(echo "`id -u`:`id -g`") devbox bash || echo "\nNOTE: This command opens a bash shell inside an already running container with your current user.  You may be looking for 'make nbash'."

# jump into running container bash with container default user
.PHONY: bash
bash:
	docker-compose exec --user 80:80 devbox bash || echo "\nNOTE: This command opens a bash shell inside an already running container with the container's default user.  You may be looking for 'make nbash'."

# Show the last 200 lines of logs from the running services
.PHONY: logs
logs:
	docker-compose logs --tail 200

.PHONY: devvm-xdebug
xdebug: devvm-xdebug-setup devvm-xdebug-recover

# When running the app in devvm, Enable xdebug for remote debugging (be sure to run xdebug-recover when finished)
.PHONY: devvm-xdebug-setup
devvm-xdebug-setup:
	@sudo touch /etc/puppet.crontab.off \
	&& sudo sed -i 's/GatewayPorts=clientspecified//' /etc/ssh/sshd_config \
	&& sudo sed -i '1s/^/GatewayPorts=clientspecified/' /etc/ssh/sshd_config \
	&& sudo service sshd restart \
	&& echo -e "\nPUPPET DEACTIVATED!\nXdebug tunnel port available for connections.\nPress any key to recover sshd configuration and reactivate Puppet." \
	&& read key

# When running the app in devvm, Restore your dev VM after you're finished remote debugging
.PHONY: devvm-xdebug-recover
devvm-xdebug-recover:
	@sudo sed -i 's/GatewayPorts=clientspecified//' /etc/ssh/sshd_config \
	&& sudo service sshd restart \
	&& sudo rm /etc/puppet.crontab.off \
	&& echo -e "\nPUPPET IS ACTIVE!"
