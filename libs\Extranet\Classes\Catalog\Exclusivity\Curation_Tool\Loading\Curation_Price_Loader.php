<?php

declare(strict_types=1);
/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Psr\Log\LoggerInterface;
use WF\Curation\ExclusivityAssortment\Domain\Price\Client\AletheiaApiClient;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Traits\Logging_Trait;

use function array_key_exists;
use function array_keys;
use function array_diff;
use function count;

class Curation_Price_Loader {
  use Logging_Trait;

  public const FALLBACK_PRICE = 9999.00;
  public const FALLBACK_PRICE_TIER = 4;

  private AletheiaApiClient $aletheia_api_client;

  private Curation_Tool_DAO $curation_tool_dao;

  private Curation_Tool_Postgres_DAO $curation_tool_dao_psql;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @param AletheiaApiClient    $api               Curation API Factory
   * @param Curation_Tool_DAO    $curation_tool_dao curation_tool_dao
   * @param Curation_Tool_Postgres_DAO    $curation_tool_dao_psql $curation_tool_dao_psql
   * @param FeatureTogglesInterface $featureToggles $featureToggles
   * @param LoggerInterface|null $logger            Logger
   */
  public function __construct(
      AletheiaApiClient $api,
      Curation_Tool_DAO $curation_tool_dao,
      Curation_Tool_Postgres_DAO $curation_tool_dao_psql,
      FeatureTogglesInterface $featureToggles,
      LoggerInterface $logger = null
  ) {
      $this->aletheia_api_client = $api;
      $this->curation_tool_dao   = $curation_tool_dao;
      $this->curation_tool_dao_psql   = $curation_tool_dao_psql;
      $this->featureToggles   = $featureToggles;
      $this->logger              = $logger;
  }

  /**
   * @param mixed $price to check
   *
   * @deprecated please, use is_price_valid instead this one
   *
   * @return bool
   */
  public static function isPriceValid($price): bool {
    if ($price === null) {
      return false;
    }
    if (!is_numeric($price)) {
      return false;
    }
    $floatPrice = (float) $price;
    if ($floatPrice === 0.00) {
      return false;
    }

    $intFallback = (int) self::FALLBACK_PRICE;
    $intPrice = (int) $price;

    return $intPrice !== $intFallback;
  }

  /**
   * @param string[] $skus             SKU
   * @param int      $brand_catalog_id Brand Catalog ID
   *
   * @return array<string, float> Array of prices where key is SKU and value is price
   * @throws \JsonException
   * @throws \Psr\Http\Client\ClientExceptionInterface
   */
  public function get_prices_for_skus(array $skus, int $brand_catalog_id): array {
    $this->info('Loading prices for SKUs', ['skus' => $skus, 'brand_catalog_id' => $brand_catalog_id]);
    $result = [];

    try {
      $prices = $this->aletheia_api_client->get_unit_sale_price($skus, $brand_catalog_id);
      foreach ($prices as $price_line) {
        if ($price_line->get_price() !== null && $price_line->get_price() > 0) {
          $result[$price_line->get_sku()] = $price_line->get_price();
        }
      }
      if (count($skus) !== count($result)) {
        $result = $this->populate_with_fallback_prices($result, $skus);
      }
    } catch (\Throwable $exception) {
      $this->error(
          'Unable to load prices for SKUs',
          [
            'exception' => $exception,
            'skus' => $skus,
            'brand_catalog_id' => $brand_catalog_id
          ]
      );
    }

    return $result;
  }

  /**
   * @param string[] $skus             SKUs
   * @param int      $brand_catalog_id Brand Catalog ID
   *
   * @return array<string, int> Array of price tiers where key is SKU and value is price tier
   *
   * @throws \JsonException
   * @throws \Psr\Http\Client\ClientExceptionInterface
   */
  public function get_curation_price_tiers_for_skus(array $skus, int $brand_catalog_id) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $price_tiers = $this->curation_tool_dao_psql->get_curation_price_tiers_for_skus(
            $this->get_prices_for_skus($skus, $brand_catalog_id));
    } else {
        $price_tiers = $this->curation_tool_dao->get_curation_price_tiers_for_skus(
            $this->get_prices_for_skus($skus, $brand_catalog_id));
    }

    if (count($skus) !== count($price_tiers)) {
      $diff_skus = array_diff($skus, array_keys($price_tiers));
      foreach ($diff_skus as $sku) {
        $price_tiers[$sku] = self::FALLBACK_PRICE_TIER;
      }
    }

    return $price_tiers;
  }

  /**
   * @param mixed $price to check
   *
   * @return bool
   */
  public function is_price_valid($price): bool {
    if ($price === null) {
      return false;
    }
    if (!is_numeric($price)) {
      return false;
    }
    $floatPrice = (float) $price;
    if ($floatPrice === 0.00) {
      return false;
    }

    $intFallback = (int) self::FALLBACK_PRICE;
    $intPrice = (int) $price;

    return $intPrice !== $intFallback;
  }

  /**
   * @param array<string, float> $result Original results from an API
   * @param array                $skus   List of SKUs for which prices were requested
   *
   * @return array<string, float> Array of prices where key is SKU and value is price
   */
  private function populate_with_fallback_prices(array $result, array $skus): array {
    $skus_without_prices = array_diff($skus, array_keys($result));

    $prices = $this->curation_tool_dao->get_skus_wsc_cost_with_margin($skus_without_prices);

    foreach ($skus_without_prices as $sku) {
      if (!array_key_exists($sku, $prices) || !$this->is_price_valid($prices[$sku])) {
        $result[$sku] = self::FALLBACK_PRICE;

        continue;
      }

      $result[$sku] = $prices[$sku];
    }

    return $result;
  }
}
