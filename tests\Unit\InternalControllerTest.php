<?php

declare(strict_types=1);

namespace App\Tests\Unit;

use App\Application\Controller\InternalController;
use PHPUnit\Framework\TestCase;

final class InternalControllerTest extends TestCase
{
    public function testPingMethodReturnsResponse(): void
    {
        $controller = new InternalController();

        $responseContents = $controller->ping()->getContent();
        self::assertNotFalse($responseContents);
        self::assertSame('pong', $responseContents);
    }
}
