<?php

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */
declare(strict_types=1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Api_Logger_Storage;

class Production_Tracking_Api_Logger implements LoggerInterface {
  /**
   * @var Production_Tracking_Api_Logger_Storage
   */
  private Production_Tracking_Api_Logger_Storage $storage;

  /**
   * @var string
   */
  private string $application;

  private TokenStorageInterface $tokenStorage;

  /**
   * @param Production_Tracking_Api_Logger_Storage $storage Storage
   * @param TokenStorageInterface $tokenStorage
   * @param string $application Application which making changes
   */
  public function __construct(
    Production_Tracking_Api_Logger_Storage $storage,
    TokenStorageInterface $tokenStorage,
    string $application
  ) {
    $this->storage = $storage;
    $this->application = $application;
    $this->tokenStorage = $tokenStorage;
  }

  private function getEmployeeId() : ?int {
    $token = $this->tokenStorage->getToken();
    /** @var \WF\PartnerHome\User\Authentication\SymfonyBundle\Entity\PartnerHomeUser|null $user */
    $user = $token !== null ? $token->getUser(): null;
    return $user!== null ? $user->employeeId(): null;
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function error($message, array $context = []) : void {
    $this->addLog(LogLevel::ERROR, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function info($message, array $context = []) : void {
    $this->addLog(LogLevel::INFO, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function emergency($message, array $context = []) : void {
    $this->addLog(LogLevel::EMERGENCY, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function alert($message, array $context = []) : void {
    $this->addLog(LogLevel::ALERT, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function critical($message, array $context = []) : void {
    $this->addLog(LogLevel::CRITICAL, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function warning($message, array $context = []) : void {
    $this->addLog(LogLevel::WARNING, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function notice($message, array $context = []) : void {
    $this->addLog(LogLevel::NOTICE, $message, $context);
  }

  /**
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function debug($message, array $context = []) : void {
    $this->addLog(LogLevel::DEBUG, $message, $context);
  }

  /**
   * @param mixed  $level   level
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  public function log($level, $message, array $context = []) : void {
    $this->addLog($level, $message, $context);
  }

  /**
   * @param mixed  $level   level
   * @param string $message message
   * @param array  $context context
   *
   * @return void
   */
  private function addLog($level, $message, array $context) : void {
    $this->storage->addLog($level, $message, json_encode($context), $this->application, (int)$this->getEmployeeId());
  }
}
