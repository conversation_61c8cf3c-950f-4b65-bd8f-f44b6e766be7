<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision;

use WF\Curation\ExclusivityAssortment\Domain\Enum\ExclusivityAssortmentCurationStatus;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader;

class Exclusivity_Assortment_Tool_Decision_Loader implements Assortment_Decision_DTO_Loader {
  /**
   * @var Exclusivity_Assortment_Tool_Decision_Loader_Storage
   */
  private $dao;

  /**
   * @param Exclusivity_Assortment_Tool_Decision_Loader_Storage $dao dao
   */
  public function __construct(Exclusivity_Assortment_Tool_Decision_Loader_Storage $dao) {
    $this->dao = $dao;
  }

  /**
   * @param string[] $skus skus to get decisions for
   *
   * @return \WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Collection
   */
  public function get_decisions_for_skus(array $skus) : Assortment_Decision_DTO_Collection {
    $decisions = $this->dao->get_decisions_for_skus($skus, ExclusivityAssortmentCurationStatus::imported());

    return new Assortment_Decision_DTO_Collection($decisions);
  }
}
