<?php

declare(strict_types=1);

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use App\Application\DTO\CurationRequest;
use App\Application\Service\Security\ToolAvailabilityCheckerInterface;
use App\Application\View\IndexView;
use App\Domain\Service\BatchService;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\Curator_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Config_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rejection_Reason_Loader;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU_Nearshore;

use function set_time_limit;
use function sprintf;

class IndexController extends AbstractBaseController
{
    /**
     * @param Config_Loader $config_service the config service
     * @param BatchService $batchService
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param Rebrand_Project_Loader $rebrand_project_loader Rebrand Project Loader
     * @param Rejection_Reason_Loader $rejection_reason_loader Rejection Reason Loader
     * @param Curation_Data_Consistency_Validator $data_consistency_validator Data Consistency Validator
     * @param Curator_Loader $curator_loader
     * @param Curation_Section_Service $curation_section_service_with_collisions
     * @param World_Region_EU_Nearshore $nearshoreRegion
     * @param FeatureTogglesInterface $featureToggles
     * @param CurationRequest $curationRequest
     *
     * @return Response|IndexView
     *
     * @throws \Throwable
     * @throws \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception
     * @Route(path="/index", methods={"GET"}, name="index")
     */
    public function __invoke(
        Config_Loader $config_service,
        BatchService $batchService,
        Completion_Batch_Data_Service $batch_data_service,
        Rebrand_Project_Loader $rebrand_project_loader,
        Rejection_Reason_Loader $rejection_reason_loader,
        Curation_Data_Consistency_Validator $data_consistency_validator,
        Curator_Loader $curator_loader,
        Curation_Section_Service $curation_section_service_with_collisions,
        World_Region_EU_Nearshore $nearshoreRegion,
        FeatureTogglesInterface $featureToggles,
        CurationRequest $curationRequest
    ) {
        try {
            $batch_id = $curationRequest->getBatchId();

            $batchService->ensureAccessToBatch($batch_id);

            $rebrand_project = null;

            if ($batch_id > 0) {
                $this->info(
                    'Ensuring batch status matches requested action',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                $batchStatusCheckResult = $this->ensureBatchStatusMatchesAction(
                    $batch_id,
                    'index',
                    $batch_data_service
                );

                if ($batchStatusCheckResult !== null) {
                    return $batchStatusCheckResult;
                }

                $this->info(
                    'Loading rebrand project',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );
                $rebrand_project = $rebrand_project_loader->getRebrandProject($batch_id);
                $this->info(
                    'Loaded rebrand project',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'rebrand_project' => $rebrand_project]
                );
            }

            $this->info(
                'Loading with automatic curation saving',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
            );

            // set page limit in 5 minutes to handle batches with 10k+ SKUs and many collisions
            set_time_limit(5 * 60);

            $sections = [];

            $this->info(
                'Running batch validations',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
            );
            $warnings = $data_consistency_validator->run_validations($batch_id, $sections);

            $this->info(
                'Building Index_View',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
            );

            $batch_data = null;
            $batch_curator = null;
            if ($batch_id !== 0) {
                $batch_data = $batch_data_service->get($batch_id);
                $assigned_employee_id = $batch_data->getAssignedEmployeeId();
                if ($assigned_employee_id !== null && $assigned_employee_id > 0) {
                    $batch_curator = $curator_loader->get_curator($assigned_employee_id);
                }
            }

            return new IndexView(
                $batch_data,
                $batch_curator,
                $config_service->getConfig($batch_id, false),
                $batch_id,
                $sections,
                $rejection_reason_loader->getSuggestedStyleRejectionResons(),//2
                $rebrand_project,
                $warnings,
                $this->isGranted(ToolAvailabilityCheckerInterface::CURATION_ASSORTMENT_WORKFLOW_BY_OFFSHORE)
            );
        } catch (Throwable $exception) {
            $this->error(
                sprintf('Exception occurred during batch validation: %s', $exception->getMessage()),
                ['batch_id' => $batch_id ?? null, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );

            throw $exception;
        }
    }
}
