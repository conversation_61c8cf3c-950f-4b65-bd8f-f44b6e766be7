<?php
declare(strict_types=1);

/**
 * PHP version 8
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\ProductManagement\WorldRegion;

use WF\Shared\Models\Globalization\Brand_Catalog_Model;
use WF\Shared\Classes\ProductManagement\Curation\World_Regions;

class World_Region_EU implements World_Region_Interface {

  const REGION_ID                                   = World_Regions::REGION_EU;
  const CURATION_BATCH_MANAGEMENT_CURATORS_RESOURCE = 'Curation Batch Management EU Curators';

  /**
   * @return int
   */
  public function getId() : int {
    return self::REGION_ID;
  }

  /**
   * @return string
   */
  public function getName() : string {
    return 'Europe';
  }

  /**
   * @return string
   */
  public function getShortName() : string {
    return 'EU';
  }

  /**
   * @return array
   */
  public function getBrandCatalogIds() : array {
    return [
        Brand_Catalog_Model::BRAND_CATALOG_WAYFAIR_UK,
        Brand_Catalog_Model::BRAND_CATALOG_WAYFAIR_GERMANY
    ];
  }

  /**
   * @return string
   */
  public function getCuratorListAra() : string {
    return static::CURATION_BATCH_MANAGEMENT_CURATORS_RESOURCE;
  }

  /**
   * @return bool
   */
  public function isAllowedToSwitchRegion() : bool {
    return true;
  }
}
