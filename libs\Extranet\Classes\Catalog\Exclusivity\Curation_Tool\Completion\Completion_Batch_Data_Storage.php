<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

interface Completion_Batch_Data_Storage {
  /**
   * @param int $batch_id Batch ID
   *
   * @return array
   */
  public function get_batch_data(int $batch_id) : array;

  /**
   * @return array
   */
  public function get_process_types() : array;

  /**
   * @param int $batch_id    Batch ID
   * @param int $status_id   Status ID
   * @param int $employee_id Employee ID
   *
   * @return void
   * @throws \WF\Shared\DAOs\Exception\Execution_Exception
   */
  public function change_status(int $batch_id, int $status_id, int $employee_id);

    /**
     * @param int $batch_id Batch ID
     *
     * @return array
     */
    public function get_qa_batch_skus_with_exclude_reason(int $batch_id) : array;
  /**
   * @param int $emp_id Employee ID
   *
   * @return array
   */
  public function get_batch_list(int $emp_id) : array;
}