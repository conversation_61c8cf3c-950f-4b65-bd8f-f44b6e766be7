<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

class Automatic_Class_Curation_Configuration {
  /**
   * Map of configuration items grouped by class and price tier
   *
   * @var Automatic_Class_Curation_Configuration_Item[]
   */
  private $items;

  /**
   * @param Automatic_Class_Curation_Configuration_Item[] $items Items
   */
  public function __construct(array $items) {
    // store them grouped by class and price tier for faster access
    foreach ($items as $item) {
      $this->items[$item->get_class_id()][$item->get_price_tier()] = $item;
    }
  }

  /**
   * @param int $class_id   Class ID
   * @param int $price_tier Price Tier
   *
   * @return Automatic_Class_Curation_Configuration_Item|null
   */
  public function get_for_class_with_price_tier(int $class_id, int $price_tier) : ?Automatic_Class_Curation_Configuration_Item {
    return $this->items[$class_id][$price_tier] ?? null;
  }
}