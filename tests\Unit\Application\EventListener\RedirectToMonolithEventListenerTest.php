<?php

declare(strict_types=1);

namespace App\Tests\Unit\Application\EventListener;

use App\Application\EventListener\RedirectToMonolithEventListener;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Mockery;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;
use RuntimeException;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;

final class RedirectToMonolithEventListenerTest extends TestCase
{
    private $featureTogglesMock;

    private string $appRootRoute = '/d/curation-tool';

    private RedirectToMonolithEventListener $subject;

    /**
     * @param string $requestMethod
     * @param string $requestUri
     * @param string $expectedRedirect
     *
     * @test
     * @dataProvider redirectDataProvider
     *
     * @return void
     */
    public function shouldRedirectRequest(string $requestMethod, string $requestUri, string $expectedRedirect): void
    {
        // Arrange
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL)
            ->andReturn(false);
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST)
            ->andReturn(false);

        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('completed');

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;
        $requestMock->shouldReceive('getMethod')
            ->withNoArgs()
            ->andReturn($requestMethod);
        $requestMock->shouldReceive('getRequestUri')
            ->withNoArgs()
            ->andReturn($requestUri);

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        $requestEventMock->shouldReceive('setResponse')
            ->withArgs(static function (Response $response) use ($expectedRedirect) {
                self::assertInstanceOf(RedirectResponse::class, $response);
                self::assertSame($expectedRedirect, $response->getTargetUrl());

                return true;
            });

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
    }

    public function redirectDataProvider(): array
    {
        return [
            'GET' => [
                Request::METHOD_GET,
                '/d/curation-tool/completed?batch_id=7',
                '/v/catalog/curation_tool/completed?batch_id=7'
            ],
            'OPTIONS' => [
                Request::METHOD_OPTIONS,
                '/d/curation-tool/completed?batch_id=7',
                '/v/catalog/curation_tool/completed?batch_id=7'
            ],
            'HEAD' => [
                Request::METHOD_HEAD,
                '/d/curation-tool/completed?batch_id=7',
                '/v/catalog/curation_tool/completed?batch_id=7'
            ],
            'CONNECT' => [
                Request::METHOD_CONNECT,
                '/d/curation-tool/completed?batch_id=7',
                '/v/catalog/curation_tool/completed?batch_id=7'
            ],
            'without params' => [
                Request::METHOD_GET,
                '/d/curation-tool/index',
                '/v/catalog/curation_tool/index'
            ],
            'with multiple params' => [
                Request::METHOD_GET,
                '/d/curation-tool/index?batch_id&force=true&test=1',
                '/v/catalog/curation_tool/index?batch_id&force=true&test=1'
            ],
            'for qa' => [
                Request::METHOD_GET,
                '/d/curation-tool/qa?batch_id=5',
                '/v/catalog/curation_tool/qa?batch_id=5'
            ],
            'for completed' => [
                Request::METHOD_GET,
                '/d/curation-tool/completed?batch_id=5',
                '/v/catalog/curation_tool/completed?batch_id=5'
            ],
        ];
    }

    /**
     * @test
     *
     * @return void
     */
    public function shouldSkipNonMainRequest(): void
    {
        // Arrange
        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(false);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
        $requestEventMock->shouldNotHaveReceived('setResponse');
        self::assertTrue(true, 'Needed to prevent warnings. PHPUnit ignores Mockery assertions.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function shouldSkipPingHealthRequest(): void
    {
        // Arrange
        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('ping');

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
        $requestEventMock->shouldNotHaveReceived('setResponse');
        self::assertTrue(true, 'Needed to prevent warnings. PHPUnit ignores Mockery assertions.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function shouldIgnoreWhenFeatureEnabled(): void
    {
        // Arrange
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL)
            ->andReturn(true);
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST)
            ->andReturn(false);

        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('completed');

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
        $requestEventMock->shouldNotHaveReceived('setResponse');
        self::assertTrue(true, 'Needed to prevent warnings. PHPUnit ignores Mockery assertions.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function shouldIgnoreWhenParallelTestingIsEnabled(): void
    {
        // Arrange
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL)
            ->andReturn(false);
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST)
            ->andReturn(true);

        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('completed');

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
        $requestEventMock->shouldNotHaveReceived('setResponse');
        self::assertTrue(true, 'Needed to prevent warnings. PHPUnit ignores Mockery assertions.');
    }

    /**
     * @test
     *
     * @return void
     */
    public function shouldIgnoreWhenEndpointIsNotRootOne(): void
    {
        // Arrange
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL)
            ->andReturn(false);
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST)
            ->andReturn(false);

        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('completed');

        $requestUri = '/d/archetype-tool/completed?batch_id=7';

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;
        $requestMock->shouldReceive('getMethod')
            ->withNoArgs()
            ->andReturn(Request::METHOD_GET);
        $requestMock->shouldReceive('getRequestUri')
            ->withNoArgs()
            ->andReturn($requestUri);

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
        $requestEventMock->shouldNotHaveReceived('setResponse');
        self::assertTrue(true, 'Needed to prevent warnings. PHPUnit ignores Mockery assertions.');
    }

    /**
     * @param string $requestMethod
     *
     * @test
     * @dataProvider unsupportedMethodsProvider
     *
     * @return void
     */
    public function shouldThrowAnExceptionForUnsupportedMethod(string $requestMethod): void
    {
        // Arrange
        self::expectException(RuntimeException::class);

        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL)
            ->andReturn(false);
        $this->featureTogglesMock->shouldReceive('isEnabled')
            ->with(FeatureToggle::MTBW_ENABLE_DECOUPLED_CURATION_TOOL_PARALLEL_TEST)
            ->andReturn(false);

        $attributesMock = Mockery::mock(ParameterBag::class);
        $attributesMock->shouldReceive('get')
            ->with('_route')
            ->andReturn('completed');

        $requestUri = '/d/curation-tool/completed?batch_id=7';

        $requestMock = Mockery::mock(Request::class);
        $requestMock->attributes = $attributesMock;
        $requestMock->shouldReceive('getMethod')
            ->withNoArgs()
            ->andReturn($requestMethod);
        $requestMock->shouldReceive('getRequestUri')
            ->withNoArgs()
            ->andReturn($requestUri);

        $requestEventMock = Mockery::mock(RequestEvent::class);
        $requestEventMock->shouldReceive('isMainRequest')
            ->withNoArgs()
            ->andReturn(true);
        $requestEventMock->shouldReceive('getRequest')
            ->withNoArgs()
            ->andReturn($requestMock);

        // Act
        $this->subject->onRequest($requestEventMock);

        // Assert
    }

    public function unsupportedMethodsProvider(): array
    {
        return [
            'POST' => [Request::METHOD_POST],
            'PATCH' => [Request::METHOD_PATCH],
            'PUT' => [Request::METHOD_PUT],
            'DELETE' => [Request::METHOD_DELETE],
            'PURGE' => [Request::METHOD_PURGE],
            'TRACE' => [Request::METHOD_TRACE],
        ];
    }

    protected function setUp(): void
    {
        $this->featureTogglesMock = Mockery::mock(FeatureTogglesInterface::class);
        $this->loggerMock = new NullLogger();

        $this->subject = new RedirectToMonolithEventListener(
            $this->featureTogglesMock,
            $this->appRootRoute
        );
        $this->subject->setLogger($this->loggerMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
    }
}
