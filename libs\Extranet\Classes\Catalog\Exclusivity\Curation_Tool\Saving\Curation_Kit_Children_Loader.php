<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Curation_Kit_Children_Loader {

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;
  /**
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO $dao DAO object
   */
  public function __construct(Curation_Decision_DAO $dao,
                              Curation_Decision_Postgres_DAO $dao_psql,
                              FeatureTogglesInterface $featureToggles
  ) {
    $this->dao = $dao;
    $this->dao_psql = $dao_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param array $skus        Array of skus
   * @param int   $batchId     Batch Id
   * @param bool  $skip_kitsco If should skip kitsco
   *
   * @return array
   */
  public function replaceKitParents(array $skus, int $batchId, bool $skip_kitsco = true) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $kitWithChildren = $this->dao_psql->get_kits_with_active_children($skus, $batchId);
    } else {
      $kitWithChildren = $this->dao->get_kits_with_active_children($skus, $batchId);
    }
    $skusWithKeys = array_combine($skus, $skus);
    foreach ($kitWithChildren as $kit => $children) {
      foreach ($children as $childRow) {
        if ($skip_kitsco && $childRow['IsKitsco'] === 1) {
          continue;
        }

        $skusWithKeys[$childRow['ChildSKU']] = $childRow['ChildSKU'];
      }
    }
    return array_values($skusWithKeys);
  }
}
