<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Retrieve_Interface;
use WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer;

class Curation_Decision_Factory {

  private Context_SKU_Collection_Retrieve_Interface $context_sku_service;

  /**
   * @var \WF\Shared\Classes\ProductManagement\WhiteLabel\Kitsco_Manufacturer
   */
  private $kitsco_manufacturer_service;

  /**
   * @param Context_SKU_Collection_Retrieve_Interface $context_sku_service         Context SKU Service
   * @param Kitsco_Manufacturer                       $kitsco_manufacturer_service Kitsco Manufacturer
   */
  public function __construct(
      Context_SKU_Collection_Retrieve_Interface $context_sku_service,
      Kitsco_Manufacturer $kitsco_manufacturer_service
  ) {
    $this->context_sku_service         = $context_sku_service;
    $this->kitsco_manufacturer_service = $kitsco_manufacturer_service;
  }

  /**
   * @param int                      $batch_id          Batch ID
   * @param string                   $sku               SKU
   * @param int                      $price_tier        Price Tier
   * @param int                      $style_id          Style ID
   * @param int                      $substyle_id       Substyle ID
   * @param int                      $brand_id          Brand ID
   * @param int|null                 $granular_style_id Granularstyle ID
   * @param string                   $saved_at          Saved at
   * @param int                      $employee_id       Employee ID
   * @param Curation_Decision_Source $decision_source   Decision Source
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision
   * @throws \Exception
   */
  public function create_curated(
      int $batch_id,
      string $sku,
      int $price_tier,
      int $style_id,
      int $substyle_id,
      int $brand_id,
      ?int $granular_style_id,
      string $saved_at,
      int $employee_id,
      Curation_Decision_Source $decision_source
  ) : Curation_Decision {
    $excluded_reason_id = 0;
    $context_xn_id      = $this->context_sku_service->get_context_xnid($sku);
    $kitsco             = false;
    $qa_status          = Curation_QA_Status::pending();

    return new Curation_Decision(
        $batch_id,
        $sku,
        $employee_id,
        $saved_at,
        $excluded_reason_id,
        $price_tier,
        $style_id,
        $substyle_id,
        $brand_id,
        $granular_style_id,
        $context_xn_id,
        $kitsco,
        $qa_status,
        $decision_source
    );
  }

  /**
   * @param int                      $batch_id           Batch ID
   * @param string                   $sku                SKU
   * @param int                      $excluded_reason_id Excluded Reason ID
   * @param string                   $saved_at           Saved At
   * @param int                      $employee_id        Employee ID
   * @param Curation_Decision_Source $decision_source    Decision Source
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision
   */
  public function create_excluded(
      int $batch_id,
      string $sku,
      int $excluded_reason_id,
      string $saved_at,
      int $employee_id,
      Curation_Decision_Source $decision_source
  ) : Curation_Decision {
    $price_tier        = null;
    $style_id          = null;
    $substyle_id       = null;
    $brand_id          = null;
    $granular_style_id = null;
    $kitsco            = false;
    $context_xn_id     = $this->context_sku_service->get_context_xnid($sku);
    $qa_status         = Curation_QA_Status::pending();

    return new Curation_Decision(
        $batch_id,
        $sku,
        $employee_id,
        $saved_at,
        $excluded_reason_id,
        $price_tier,
        $style_id,
        $substyle_id,
        $brand_id,
        $granular_style_id,
        $context_xn_id,
        $kitsco,
        $qa_status,
        $decision_source
    );
  }

  /**
   * @param int                      $batch_id        Batch ID
   * @param string                   $sku             SKU
   * @param string                   $saved_at        Saved At
   * @param int                      $employee_id     Employee ID
   * @param int                      $region_id       Region ID
   * @param Curation_Decision_Source $decision_source Decision Source
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision
   */
  public function create_kitsco(
      int $batch_id,
      string $sku,
      string $saved_at,
      int $employee_id,
      int $region_id,
      Curation_Decision_Source $decision_source
  ) : Curation_Decision {
    $excluded_reason_id = 0;
    $price_tier         = null;
    $style_id           = null;
    $substyle_id        = null;
    $brand_id           = $this->kitsco_manufacturer_service->get_manufacturer_id($region_id);
    $granular_style_id  = null;
    $kitsco             = true;
    $context_xn_id      = $this->context_sku_service->get_context_xnid($sku);
    $qa_status          = Curation_QA_Status::pending();

    return new Curation_Decision(
        $batch_id,
        $sku,
        $employee_id,
        $saved_at,
        $excluded_reason_id,
        $price_tier,
        $style_id,
        $substyle_id,
        $brand_id,
        $granular_style_id,
        $context_xn_id,
        $kitsco,
        $qa_status,
        $decision_source
    );
  }

  /**
   * @param int                      $batch_id        Batch ID
   * @param string                   $sku             SKU
   * @param string                   $saved_at        Saved At
   * @param int                      $employee_id     Employee ID
   * @param Curation_Decision_Source $decision_source Decision Source
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision
   */
  public function create_empty(
      int $batch_id,
      string $sku,
      string $saved_at,
      int $employee_id,
      Curation_Decision_Source $decision_source
  ) : Curation_Decision {
    $excluded_reason_id = 0;
    $price_tier         = null;
    $style_id           = null;
    $substyle_id        = null;
    $brand_id           = null;
    $granular_style_id  = null;
    $kitsco             = false;
    $context_xn_id      = $this->context_sku_service->get_context_xnid($sku);
    $qa_status          = Curation_QA_Status::pending();

    return new Curation_Decision(
        $batch_id,
        $sku,
        $employee_id,
        $saved_at,
        $excluded_reason_id,
        $price_tier,
        $style_id,
        $substyle_id,
        $brand_id,
        $granular_style_id,
        $context_xn_id,
        $kitsco,
        $qa_status,
        $decision_source
    );
  }
}