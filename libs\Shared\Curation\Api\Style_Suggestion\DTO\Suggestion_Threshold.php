<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Style_Suggestion\DTO;

class Suggestion_Threshold {

  /**
   * @var int
   */
  private $class_id;

  /**
   * @var string
   */
  private $class_name;

  /**
   * @var float
   */
  private $value;

  /**
   * Suggestion_Threshold constructor.
   *
   * @param int $class_id Class ID
   * @param string $class_name Class Name
   * @param float $value The threshold value
   */
  public function __construct(int $class_id, string $class_name, float $value) {
    $this->class_id   = $class_id;
    $this->class_name = $class_name;
    $this->value      = $value;
  }

  /**
   * @return int
   */
  public function get_class_id() : int {
    return $this->class_id;
  }

  /**
   * @return string
   */
  public function get_class_name() : string {
    return $this->class_name;
  }

  /**
   * @return float
   */
  public function get_value() : float {
    return $this->value;
  }
}
