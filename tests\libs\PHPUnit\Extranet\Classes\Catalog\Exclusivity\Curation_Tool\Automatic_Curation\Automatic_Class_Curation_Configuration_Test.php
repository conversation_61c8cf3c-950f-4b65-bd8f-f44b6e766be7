<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item;

class Automatic_Class_Curation_Configuration_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_returns_config_for_specified_class_and_price_tier()
    {
        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item $item1 */
        $item1 = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $item1->get_class_id()->willReturn(1);
        $item1->get_price_tier()->willReturn(1);

        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item $item2 */
        $item2 = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $item2->get_class_id()->willReturn(2);
        $item2->get_price_tier()->willReturn(2);

        $items = [$item1->reveal(), $item2->reveal()];

        $subject = new Automatic_Class_Curation_Configuration($items);

        $this->assertEquals($item1->reveal(), $subject->get_for_class_with_price_tier(1, 1));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_null_when_class_not_found()
    {
        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item $item */
        $item = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $item->get_class_id()->willReturn(1);
        $item->get_price_tier()->willReturn(1);

        $items = [$item->reveal()];

        $subject = new Automatic_Class_Curation_Configuration($items);

        $this->assertNull($subject->get_for_class_with_price_tier(2, 1));
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_null_when_class_found_but_different_price_tier()
    {
        /** @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item $item */
        $item = $this->prophesize(Automatic_Class_Curation_Configuration_Item::class);
        $item->get_class_id()->willReturn(1);
        $item->get_price_tier()->willReturn(1);

        $items = [$item->reveal()];

        $subject = new Automatic_Class_Curation_Configuration($items);

        $this->assertNull($subject->get_for_class_with_price_tier(1, 2));
    }
}
