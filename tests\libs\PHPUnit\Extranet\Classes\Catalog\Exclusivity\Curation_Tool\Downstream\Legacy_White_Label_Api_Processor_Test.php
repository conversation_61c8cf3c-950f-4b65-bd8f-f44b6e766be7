<?php

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Tests\libs\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Legacy_White_Label_Processor;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_DAO;
use WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch as Batch_Model;

class Legacy_White_Label_Api_Processor_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var White_Label_Batch_DAO
     */
    private $dao;

    /**
     * @var White_Label_Batch_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch
     */
    private $mock_batch;

    /**
     * @var ObjectProphecy|LoggerInterface
     */
    private $logger;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(White_Label_Batch_DAO::class);
        $this->dao_psql = $this->prophesize(White_Label_Batch_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->mock_batch = $this->prophesize(Batch_Model::class);

        $this->subject = new Legacy_White_Label_Processor(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * Test send_downstream method when the batch already exists in DAO when feature toggle off.
     */
    public function testSendDownstreamBatchExistsFeatureToggleOff()
    {
        $existingBatchId = 123;
        $this->mock_batch->get_id()->willReturn($existingBatchId);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch($existingBatchId)->willReturn(['some_data']);
        $responseCode = $this->subject->send_downstream($this->mock_batch->reveal());
        $this->assertEquals(200, $responseCode);
    }

    /**
     * Test send_downstream method when the batch already exists in DAO when feature toggle on.
     */
    public function testSendDownstreamBatchExistsFeatureToggleOn()
    {
        $existingBatchId = 123;
        $this->mock_batch->get_id()->willReturn($existingBatchId);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_batch($existingBatchId)->willReturn(['some_data']);
        $responseCode = $this->subject->send_downstream($this->mock_batch->reveal());
        $this->assertEquals(200, $responseCode);
    }

    /**
     * Test send_downstream method when the batch does not exist in DAO when feature toggle off.
     */
    public function testSendDownstreamBatchNotExistsFeatureToggleOff()
    {
        $nonExistingBatchId = 456;
        $this->mock_batch->get_id()->willReturn($nonExistingBatchId);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->dao->get_batch($nonExistingBatchId)->willReturn([]);
        $this->dao->insert_batch($this->mock_batch->reveal())->shouldBeCalled();
        $responseCode = $this->subject->send_downstream($this->mock_batch->reveal());
        $this->assertEquals(200, $responseCode);
    }

    /**
     * Test send_downstream method when the batch does not exist in DAO when feature toggle on.
     */
    public function testSendDownstreamBatchNotExistsFeatureToggleOn()
    {
        $nonExistingBatchId = 456;
        $this->mock_batch->get_id()->willReturn($nonExistingBatchId);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->dao_psql->get_batch($nonExistingBatchId)->willReturn([]);
        $this->dao->insert_batch($this->mock_batch->reveal())->shouldBeCalled();
        $responseCode = $this->subject->send_downstream($this->mock_batch->reveal());
        $this->assertEquals(200, $responseCode);
    }
}
