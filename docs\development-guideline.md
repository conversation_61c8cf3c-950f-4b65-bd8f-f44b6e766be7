# Development, Test & Deployment Guideline

## Work priority
During our day-to-day work, an engineer should focus on the following routine:

* Investigating technical solutions (spikes)
* Implementing new logic in scope of current task
* Manual testing your and teammates' code change
* Performing code review (CR) for teammate
* Communicate to product manager or stakeholders to sign off bug fixes or features added

## Task development pipeline (steps)

For most of our tasks we’ll use such development pipeline:

1. Local development (including automated tests):
     * Use feature branch approach: <username>_<issue_description>_ph_<issue-ID>
         * Maximum length of the branch is 54 characters. In other cases you may face such [an error in Buildkite](https://buildkite.com/wayfair/brand-workflows-assortment-management-tool-ui/builds/507#6eeb6f50-c32f-4e2c-b83f-90c99bd5e232).
     * [Feature branch should include the username of engineer and <PERSON><PERSON>(ProjectHub) issue ticket](https://infohub.corp.wayfair.com/display/DP/Git+Workflow#GitWorkflow-Creatingyourfeaturebranch)
         * E.g.: sh55q_setup_exclusivity_tblSupplierCompetitorExclusivityPart_ph_MTBW-1234
         * E.g.: shlushko_setup_exclusivity_tblSupplierCompetitorExclusivityPart_ph_MTBW-1234
     * Each commit message should starts from Jira(ProjectHub) issue number
         * E.g.: MTBW-1234 SupplierCompetitorExclusivityPart model creation
     * Reach the unit test coverage in Sonarqube
     * Make sure to create necessary logging and Datadog monitoring (optional) to help you debug on PROD

2. Local or DEV manual testing. Your test should cover all the aspects to build confidence that your code is going to work on PROD. As an example, for curation jobs, you need to follow [Testing and Deploy of PA Automation](../curation-knowledge-links/#testing-and-deploy-of-pa-automation)
3. Creating pull request (PR) & performing self review
4. Verify that all checks and build (via Buildkite) finished successfully
5. Send PR to team channel **#btbb-brand-workflows-prs** for the Code review (CR). Fill `Changelog` section with text description or screenshots
6. Code adjustments based on CR feedbacks
7. Deploy the branch to DEV environment and test again
8. Communicate to product manager and stakeholder (optional for bug fixes) to review the change on DEV environment 
9. After you know that it's going to work on PROD, merge PR to main / master branch;
    * Use “Squash and merge” approach where possible
    * Delete the branch if it's not deleted automatically after merging
10. Deploy the code to PROD environment
11. Communicate to stakeholder to sign off the change. Use your best judgement or ask tech lead/engineering manager/product manager to determine if a demo should be given to stakeholder
    * E.g. a backend logging enhancement should not need to notify stakeholder
    * E.g. a bug fix should be communicated to stakeholder
    * E.g. a UI feature should involve stakeholder and a demo should be presented


## Coding Standards
 * PHP - In Wayfair we have a [Easy-Coding-Standards configuration](https://github.com/wayfair-shared/php-platform-coding-standard), which under the hood is using [PSR-12](https://www.php-fig.org/psr/psr-12/).
Here is doc how to configure it in PhpStorm.
 * Java - [Wayfair Java Coding Standard](https://docs.csnzoo.com/java/docs/coding-standards/)
 * Python - [Wayfair Python Best Practices](https://docs.csnzoo.com/python/docs/best_practices/style_guide/)
 * JS/React - [Wayfair JS/React/Redux/Test Standards](https://docs.csnzoo.com/frontend/docs/standards/javascript/js-debugging)
 * Working with shared repo / library flow [Shared repo/library usage](https://docs.google.com/document/u/0/d/1lb9t9XKz7AG7UJ16_4QEZDbveFEPZwO_n_7eDsoTV-I/edit)

## Tech Designs
Engineers might from time to time design a service, a piece of it or simply a tech investigation (spike). The outcomes are usually not code but in text format with diagrams and spreadsheets.

There are generally 3 scopes to tech designs:

* A technology deep dive or investigation that is from a spike ticket (1~3 points)
* A tech design of one or more components in a service that is from a spike ticket (3~5 points)
* An overall design of a full initiative which could lead to an **RFC** being created (5+ points)

While we don't demand the tools you use to provide the design, which could be through Google Docs, InfoHub Pages or a markdown page, we maintain a directory for them here:

* [Curation](../curation-tech-designs)
* [Assortment](../assortment-tech-designs)
* [Differentiated 2.0 (Exclusivity)](../exclusivity-tech-designs)