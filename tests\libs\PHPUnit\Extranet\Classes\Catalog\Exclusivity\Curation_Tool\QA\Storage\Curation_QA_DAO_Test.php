<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Storage\Curation_QA_DAO;

class Curation_QA_DAO_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_qa_decision_storage()
    {
        $rc = new ReflectionClass(Curation_QA_DAO::class);

        $this->assertTrue($rc->implementsInterface(Curation_QA_Decision_Storage::class));
    }
}
