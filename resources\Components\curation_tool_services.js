/**
 * Curation Tool Services
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

import {createWretch} from '@wayfair/framework-request-utils';
import {APP_ROOT_PATH} from '../Application/Constants'

const transactionId = btoa(Math.random());
const wretch = createWretch({
    txid: transactionId,
    canClearCache: true,
});

const CURATION_DECISION_SAVE_URL = APP_ROOT_PATH + '/save';
const CURATION_DECISION_TOGGLE_IS_KITSCO_URL = APP_ROOT_PATH + '/save_kitsco';
const COMPLETE_CURATION_URL = APP_ROOT_PATH + '/complete_curation';
const QA_DECISION_SAVE_URL = APP_ROOT_PATH + '/save_qa';
const QA_DECISION_UPDATE_URL = APP_ROOT_PATH + '/update_qa';
const COMPLETE_QA_URL = APP_ROOT_<PERSON> + '/complete_qa';
const COMPLETE_AUTOMATED_QA_URL = APP_ROOT_PATH + '/complete_automated_qa';
const PRICE_LOAD_URL = APP_ROOT_PATH + '/get_price_options';
export const Next_Batch_ID_Path = APP_ROOT_PATH + '/batch_list';
export const CURRENT_PAGE_URL = APP_ROOT_PATH + '/completed';
export const Batch_Data_Path = APP_ROOT_PATH + '/batch_data';
export const Batch_Details_Path = APP_ROOT_PATH + '/batch_details';


export const saveDecisionService = ({
  batchId,
  skus,
  decision,
  suggestedNotes,
  suggestedStyleRejectionReason,
}) =>
  wretch(CURATION_DECISION_SAVE_URL)
    .json({
      batch_id: batchId,
      skus,
      exclusion_reason_id: decision.exclusionReasonId,
      price_tier: decision.priceTier,
      substyle_id: decision.substyleId,
      granular_style_id: decision.granularStyleId,
      style_id: decision.styleId,
      brand_id: decision.manufacturerId,
      suggested_notes: suggestedNotes,
      suggested_reason: suggestedStyleRejectionReason,
    })
    .post()
    .json();

export const toggleKitscoService = ({
  batchId,
  sku,
  isKitsco,
  sharedComponents,
}) =>
  wretch(CURATION_DECISION_TOGGLE_IS_KITSCO_URL)
    .json({
      batch_id: batchId,
      sku,
      is_kitsco: isKitsco,
      shared_components: sharedComponents,
    })
    .post()
    .json();

export const completeCurationService = batchId =>
  wretch(COMPLETE_CURATION_URL)
    .query({batch_id: batchId})
    .get()
    .json();

export const saveQaDecisionService = ({
  skus,
  qaStatus,
  batchId,
  reason,
  priceTierOverride,
  finalStyleID,
  finalSubStyleID,
  finalBrandMalID,
  exclusionReason,
  suggestedReason,
}) =>
  wretch(QA_DECISION_SAVE_URL)
    .json({
      skus,
      qaStatus,
      batchId,
      reason,
      priceTierOverride,
      finalStyleID,
      finalSubStyleID,
      finalBrandMalID,
      exclusionReason,
      suggestedReason,
    })
    .post()
    .json();

export const updateQaDecisionService = ({skus, batchId}) =>
  wretch(QA_DECISION_UPDATE_URL)
    .json({
      skus,
      batchId,
    })
    .post()
    .json();

export const completeQaService = batchId =>
  wretch(COMPLETE_QA_URL)
    .query({batch_id: batchId})
    .get()
    .json();

export const completeAutomatedQaService = batchId =>
  wretch(COMPLETE_AUTOMATED_QA_URL)
    .query({batch_id: batchId})
    .get()
    .json();

export const getNextBatchId = batchId =>
  wretch(Next_Batch_ID_Path)
    .query({batch_id: batchId})
    .get()
    .json();

export const getPriceOptionsService = sku =>
  wretch(PRICE_LOAD_URL)
    .query({sku})
    .get()
    .json();

export const getBatchData = (batchId, countBase) =>
    wretch(Batch_Data_Path)
        .query({batch_id: batchId,
        count_base: countBase})
        .get()
        .json();

export const getBatchDetails = (batchId, sectionName, pageNumber, limit, qaStatus, supplier, pageType, xnId) => {
    let request = {batch_id: batchId,
        section_name: sectionName,
        page_number: pageNumber,
        limit: limit,
        count_base: qaStatus,
        page_type: pageType,
        xn_id: xnId}
    if(supplier) {
        request = {
            ...request,
            supplier: supplier
        }
    }
    return wretch(Batch_Details_Path)
        .query(request)
        .get()
        .json();
}

export const getBatchDetailsFirstPage = (batchId, limit, qaStatus, supplier, pageType="qa") => {
    let request = {batch_id: batchId,
        page_number: 1,
        limit,
        page_type: pageType,
        count_base: qaStatus}
    if(supplier) {
        request = {
            ...request,
            supplier: supplier
        }
    }
    return wretch(Batch_Details_Path)
        .query(request)
        .get()
        .json();
}



