/**
 * Filter toolbar for the Curation and Curation QA tools
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Grid, Column} from '@wayfair/homebase-extranet';
import {CurationStatusFilter} from './qa_curation_status_filter';
import CurationSupplierFilter from './curation_supplier_filter';

class CurationFilterToolbar extends React.Component {
  static propTypes = {
    onChangeFilter: PropTypes.func.isRequired,
    selectedFilters: PropTypes.object.isRequired,
    filterOptions: PropTypes.object.isRequired,
    isAutomaticCurationPostQaEnabled: PropTypes.bool.isRequired,
    isQAPage: PropTypes.bool.isRequired,
  };

  handleChangeSupplierFilter = suppliers =>
    this.props.onChangeFilter({suppliers});

  handleChangeStatusFilter = qaStatus => this.props.onChangeFilter({qaStatus});

  render() {
    return (
      <Grid>
        <Column size={6}>
          <CurationSupplierFilter
            onChange={this.handleChangeSupplierFilter}
            values={this.props.selectedFilters.suppliers}
            options={this.props.filterOptions.suppliers}
          />
        </Column>
        <Column size={6}>
          <CurationStatusFilter
            onChange={this.handleChangeStatusFilter}
            canFilterByHavingSuggestedStyles={
              this.props.filterOptions.canFilterByHavingSuggestedStyles
            }
            isAutomaticCurationPostQaEnabled={
              this.props.isAutomaticCurationPostQaEnabled
            }
            value={this.props.selectedFilters.qaStatus}
            isQAPage={this.props.isQAPage}
          />
        </Column>
      </Grid>
    );
  }
}

export default CurationFilterToolbar;
