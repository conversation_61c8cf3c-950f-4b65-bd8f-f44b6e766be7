# White Label API client

API Client for the White Label API Service owned by ME5.
Detail regarding the API can be found in this [Infohub article](https://infohub.corp.wayfair.com/display/MENG/Batch+Creation)

---

## Usage

```php
<?php
    use WF\Curation\WhiteLabelApi\Client;
    use WF\Curation\WhiteLabelApi\ClientConfig;
    use WF\Curation\WhiteLabelApi\Entity\Batch;
    use WF\Curation\WhiteLabelApi\Entity\SKU;

    $batchId = 132;
    $logger = new Logger();

    $config = new ClientConfig($logger);
    $wlClient = new Client($config);

    $skus = [
        new SKU('NGO6508', 40128),
        new SKU('DART1416', 36988),
        new SKU('DART1420', 40128, true),
    ];

    $batch = new Batch($batchId, 1, $skus);
    $response = $wlClient->createBatch([$batch]);

    echo "Response was successful? {$response->isRequestSuccessful()}";
```

## ClientConfig constructor

 - **LoggerInterface $logger**: PSR Log interface used to log the requests and responses for each API call
 - **[int $retriesCount]**: The number of maximum retries the request will make if the call fails because of 
 some connection error (default: 3);
 - **[int $connectionTimeout]**: How many seconds the client will wait trying to connect to the API (Default: 3 secs);
 - **[string $environment]**: The environment the client will connect. Possible values are ['dev', 'prod']. (Default: 'dev');
 - **[GuzzleHttp\Client $httpClient]**: A possible override for the default GuzzleHttp\Client That  
 
 ## Reach us
 
 ME6 - Curation team
 
 * Slack: #merch-eng-brand-workflows 