<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\DatabaseConstantsInterface;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use Psr\Log\LoggerInterface;
use WF\Shared\Helpers\SQL;
use PDO;
use WF\Shared\Traits\Logging_Trait;

class Curation_Image_Loader_DAO {
  use Logging_Trait;

  /**
   * @var ProductConnection
   */
  private $pdo;

  /**
   * Curation_Image_Loader_DAO constructor.
   *
   * @param ProductConnection    $pdo    PDO
   * @param LoggerInterface|null $logger logger
   */
  public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null) {
    $this->pdo    = $pdo;
    $this->logger = $logger;
  }

  /**
   * @param string $sku sku to look up images for
   *
   * @return int[]
   */
  public function get_images_for_sku(string $sku) : array {
    $this->log_info('Loading images for SKU', ['sku' => $sku]);

    $sql = '
      WITH CTE AS (
         SELECT
            imageResource.IreID AS image_resource_id,
            joinImageResourceProduct.IrpRank AS rank
         FROM csn_product.dbo.tblImageResource imageResource WITH (NOLOCK)
         INNER JOIN csn_product.dbo.tbljoinImageResourceProduct joinImageResourceProduct WITH (NOLOCK)
            ON joinImageResourceProduct.IrpIreID = imageResource.IreID
         WHERE
            joinImageResourceProduct.IrpPrSKU = :sku1 AND joinImageResourceProduct.IrpActive = 1 

         UNION

         SELECT
            imageResource.IreID AS image_resource_id,
            imageResourceProductOptionGrid.IroRank AS rank
         FROM  csn_product.dbo.tblImageResource imageResource WITH (NOLOCK)
         INNER JOIN csn_product.dbo.tblJoinImageResourceProductOptionGridFileGroup imageResourceProductOptionGrid WITH (NOLOCK)
            ON imageResourceProductOptionGrid.IroIreID = imageResource.IreID
         WHERE 
            imageResourceProductOptionGrid.IroPrSKU = :sku2 AND imageResourceProductOptionGrid.IroActive = 1
      )

      SELECT image_resource_id FROM CTE ORDER BY rank
    ';

    $hints = [
        'sku1' => SQL::nvarchar(8),
        'sku2' => SQL::nvarchar(8),
    ];

    $statement = $this->pdo->prepare($sql, [DatabaseConstantsInterface::WF_ATTR_EXECUTESQL_PARAMS => $hints]);
    $statement->bindValue('sku1', $sku, PDO::PARAM_STR);
    $statement->bindValue('sku2', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get SKU images');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get images for SKU "%s"', $sku),
          ['sku' => $sku]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array
   */
  public function get_main_images_for_skus(array $skus) : array {
    $this->log_info('Loading main images for SKUs', ['SKUs' => $skus]);

    $columns = [
        'SKU' => SQL::nvarchar(8)
    ];

    $items = array_map(
        static function ($sku) {
          return ['SKU' => $sku];
        }, $skus
    );

    $xml = SQLBulkHelper::getXmlAsString($items, $columns);
    $sql = SQLBulkHelper::getTempTableXmlSql($columns, 'tmpSkus') . PHP_EOL;

    $sql .= '
      ; WITH CTE AS (
         SELECT
            imageResource.IreID AS image_resource_id,
            joinImageResourceProduct.IrpRank AS rank,
            joinImageResourceProduct.IrpPrSKU AS sku
         FROM csn_product.dbo.tblImageResource imageResource WITH (NOLOCK)
         INNER JOIN csn_product.dbo.tbljoinImageResourceProduct joinImageResourceProduct WITH (NOLOCK)
            ON joinImageResourceProduct.IrpIreID = imageResource.IreID
         INNER JOIN #tmpSkus skus ON skus.SKU = joinImageResourceProduct.IrpPrSKU 
         WHERE joinImageResourceProduct.IrpActive = 1

         UNION

         SELECT
            imageResource.IreID AS image_resource_id,
            imageResourceProductOptionGrid.IroRank AS rank,
            imageResourceProductOptionGrid.IroPrSKU AS sku
         FROM  csn_product.dbo.tblImageResource imageResource WITH (NOLOCK)
         INNER JOIN csn_product.dbo.tblJoinImageResourceProductOptionGridFileGroup imageResourceProductOptionGrid WITH (NOLOCK)
            ON imageResourceProductOptionGrid.IroIreID = imageResource.IreID
         INNER JOIN #tmpSkus skus ON skus.SKU = imageResourceProductOptionGrid.IroPrSKU 
         WHERE imageResourceProductOptionGrid.IroActive = 1
      )

      SELECT skus.sku, image.image_resource_id 
      FROM #tmpSkus skus
      OUTER APPLY (
       SELECT TOP 1 image_resource_id FROM CTE images WHERE images.sku = skus.sku ORDER BY rank
      ) image
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':xml', $xml, PDO::PARAM_STR);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot get main SKUs images');
      $this->log_throwable_error(
          $exception,
          sprintf('Cannot get main images for SKUs "%s"', implode(",", $skus)),
          ['sku' => $skus]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_KEY_PAIR);
  }
}
