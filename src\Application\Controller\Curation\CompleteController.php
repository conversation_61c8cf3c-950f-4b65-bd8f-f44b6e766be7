<?php

declare(strict_types=1);

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\Curation;

use App\Application\Controller\AbstractBaseController;
use App\Application\DTO\CurationRequest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Curation_Service;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;

use function sprintf;

class CompleteController extends AbstractBaseController
{
    /**
     * @param Completion_Curation_Service $curation_service the curation batch service
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/complete_curation", methods={"GET"})
     */
    public function __invoke(
        Completion_Curation_Service $curation_service,
        Completion_Batch_Data_Service $batch_data_service,
        CurationRequest $curationRequest
    ): JsonResponse {
        $batch_id = $curationRequest->getBatchId();

        try {
            if ($batch_id === 0) {
                $this->error('Failed complete curation');
                $this->warning('Failed to complete curation. batch_id is empty!', ['employee_id' => $this->getEmployeeId()]);

                return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
            }

            // safety check
            if (!$this->isExpectedBatchStatus(
                $batch_id,
                [Batch::STATUS_MANUAL_UNASSIGNED, Batch::STATUS_MANUAL_ASSIGNED, Batch::STATUS_MANUAL_IN_PROGRESS],
                $batch_data_service,
            )) {
                $this->warning(
                    sprintf('Failed to complete curation for batch_id=%d. Unexpected batch status!', $batch_id),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'Unexpected batch status! Try update page to see latest batch status!'], JsonResponse::HTTP_BAD_REQUEST);
            }
            if (!$this->is_curator_assigned($batch_id, $batch_data_service)) {
                $this->warning(
                    sprintf('Failed to complete curation for batch_id=%d. Curator is not assigned to batch!', $batch_id),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json(['reason' => 'Curator is not assigned to batch!'], JsonResponse::HTTP_BAD_REQUEST);
            }

            return $this->json($curation_service->complete($batch_id, $this->getEmployeeId()));
        } catch (Throwable $exception) {
            $this->error(
                sprintf('Exception occurred during completion of curation: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );

            throw $exception;
        }
    }

    /**
     * @param int                           $batch_id           batch id
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     *
     * @return bool true if batch has curator assigned, false otherwise
     */
    private function is_curator_assigned(
        int $batch_id,
        Completion_Batch_Data_Service $batch_data_service
    ): bool {
        $this->info(
            'Asserting batch curator',
            ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
        );

        try {
            $batch_data = $batch_data_service->get($batch_id);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->error(
                sprintf('Failed to load batch data: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'exception' => $exception]
            );

            return false;
        }

        $this->info(
            'Batch curator validation',
            ['batch_curator' => $batch_data->getAssignedEmployeeId(), 'employee_id' => $this->getEmployeeId(), 'batch_id' => $batch_id]
        );

        return $batch_data->getAssignedEmployeeId() !== null && $batch_data->getAssignedEmployeeId() > 0;
    }
}
