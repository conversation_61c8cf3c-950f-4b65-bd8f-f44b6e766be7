<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;

class Curation_Kit_Children_Loader_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO
     */
    private $dao;
    /**
     * @var \WF\Extranet\<PERSON>\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface|\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader
     */
    private $subject;

    /**
     * @return void
     */
    public function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Decision_DAO::class);
        $this->dao_psql = $this->prophesize(Curation_Decision_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->subject = new Curation_Kit_Children_Loader(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @param array $skus             Input Skus
     * @param array $kitsWithChildren Kits with active children
     * @param array $expectedResult   Expected Result
     *
     * @dataProvider  getKitsWithActiveChildrenData
     *
     * @return void
     */
    public function itLoadsKitChildren(array $skus, array $kitsWithChildren, array $expectedResult)
    {
        $batchId = 1;
        $this->dao->get_kits_with_active_children($skus, $batchId)->willReturn($kitsWithChildren);

        $actualResult = $this->subject->replaceKitParents($skus, $batchId);

        $this->assertEquals($expectedResult, $actualResult);
    }

    /**
     * @test
     *
     * @return void
     */
    public function itSkipsKitscoDependingOnParameter()
    {
        $kit = [
            ['ChildSKU' => 'dummyChildSku11', 'IsKitsco' => 0],
            ['ChildSKU' => 'dummyChildSku12', 'IsKitsco' => 0],
            ['ChildSKU' => 'kitscoChildSKU1', 'IsKitsco' => 1],
        ];
        $this->dao->get_kits_with_active_children(Argument::any(), Argument::any())->willReturn([$kit]);
        $this->dao_psql->get_kits_with_active_children(Argument::any(), Argument::any())->willReturn([$kit]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $actualResult = $this->subject->replaceKitParents(['SKU1'], 443);
        $this->assertEquals(['SKU1', 'dummyChildSku11', 'dummyChildSku12'], $actualResult);

        $actualResult = $this->subject->replaceKitParents(['SKU1'], 443, false);
        $this->assertEquals(['SKU1', 'dummyChildSku11', 'dummyChildSku12', 'kitscoChildSKU1'], $actualResult);
    }

    /**
     * @return array
     */
    public function getKitsWithActiveChildrenData(): array
    {
        return [
            [
                'skus' => [
                    'parentDummySKU1',
                    'dummySKU2',
                    'parentDummySKU2',
                    'parentDummySKU3',
                ],
                'kitsWithChildren' => [
                    'parentDummySKU1' => [
                        ['ChildSKU' => 'dummyChildSku11', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku12', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku13', 'IsKitsco' => 1],
                    ],
                    'parentDummySKU2' => [
                        ['ChildSKU' => 'dummyChildSku11', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku22', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku23', 'IsKitsco' => 0],
                    ],
                    'parentDummySKU3' => [
                        ['ChildSKU' => 'dummyChildSku31', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku22', 'IsKitsco' => 0],
                        ['ChildSKU' => 'dummyChildSku33', 'IsKitsco' => 1],
                    ],
                ],
                'expectedResult' => [
                    'parentDummySKU1',
                    'dummySKU2',
                    'parentDummySKU2',
                    'parentDummySKU3',
                    'dummyChildSku11',
                    'dummyChildSku12',
                    'dummyChildSku22',
                    'dummyChildSku23',
                    'dummyChildSku31'
                ]
            ]
        ];
    }
}
