<?php

declare(strict_types=1);

namespace WO\BrandWorkflows\PuREST\Tests\Unit\Infrastructure;

use PHPUnit\Framework\TestCase;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTUrlBuilderException;
use WF\BrandWorkflows\PuREST\Infrastructure\UrlBuilder;

final class UrlBuilderTest extends TestCase
{
    public function testBuildUrlSuccess(): void
    {
        $purestUrlBuilder = new UrlBuilder('server');
        $url = $purestUrlBuilder->buildUrl('service', 'resource');

        $this->assertEquals('server/purest/service/resource', $url, 'PuREST url should be the same as expected');
    }

    public function testBuildUrlException(): void
    {
        $purestUrlBuilder = new UrlBuilder('');
        $this->expectException(PuRESTUrlBuilderException::class);

        $purestUrlBuilder->buildUrl('service', 'resource');
    }
}
