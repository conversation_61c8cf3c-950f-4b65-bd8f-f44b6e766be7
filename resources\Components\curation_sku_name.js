/**
 * Show product name of a sku
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Button} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';

const openPopup = url => {
  if (url) {
    window.open(
      url,
      '',
      'width=1200, height=800, scrollbars=yes, resizable=yes, location=yes'
    );
  }
};

class CurationSkuName extends React.PureComponent {
  static propTypes = {
    name: PropTypes.string.isRequired,
    url: PropTypes.string,
  };

  static defaultProps = {
    url: null,
  };

  render() {
    return (
      <div className="text_center">
        <p>{this.props.name}</p>
        <Button onClick={() => openPopup(this.props.url)} text>
          <Translation msgid="CurationTool.ViewPDP" />
        </Button>
      </div>
    );
  }
}

export default CurationSkuName;
