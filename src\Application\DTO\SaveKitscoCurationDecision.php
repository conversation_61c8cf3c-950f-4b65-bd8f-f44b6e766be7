<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\DTO;

class SaveKitscoCurationDecision extends AbstractCurationRequest
{
    protected const FIELD_SKU = 'sku';
    protected const FIELD_SHARED_COMPONENTS = 'shared_components';
    protected const FIELD_IS_KITSCO = 'is_kitsco';

    protected string $sku;
    protected array $sharedComponents;
    protected bool $isKitsco;

    /**
     * @param int $batchId
     * @param string $sku
     * @param bool $isKitsco
     * @param array $sharedComponents
     */
    public function __construct(
        int $batchId = 0,
        string $sku,
        bool $isKitsco,
        array $sharedComponents
    ) {
        parent::__construct($batchId);

        $this->sku = $sku;
        $this->isKitsco = $isKitsco;
        $this->sharedComponents = $sharedComponents;
    }

    /**
     * @return string
     */
    public function getSku(): string
    {
        return $this->sku;
    }

    /**
     * @return array
     */
    public function getSharedComponents(): array
    {
        return $this->sharedComponents;
    }

    /**
     * @return bool
     */
    public function isKitsco(): bool
    {
        return $this->isKitsco;
    }

    /**
     * @param array $params
     * @return self
     */
    public static function fromArray(array $params): self
    {
        return new self(
            (int)($params[self::FIELD_BATCH_ID] ?? 0),
            (string)$params[self::FIELD_SKU],
            (bool)($params[self::FIELD_IS_KITSCO] ?? false),
            (array)($params[self::FIELD_SHARED_COMPONENTS] ?? [])
        );
    }
}
