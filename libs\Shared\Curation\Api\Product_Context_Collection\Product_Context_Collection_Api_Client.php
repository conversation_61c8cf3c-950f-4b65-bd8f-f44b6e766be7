<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Product_Context_Collection;

use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\Api_Product_Context_Collection;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Environment;

class Product_Context_Collection_Api_Client implements Api_Product_Context_Collection {

  private const BASE_URL_DEV  = 'kube-product-context-collection-api-nextgen.service.intradsm1.sdeconsul.csnzoo.com';

  private const BASE_URL_PROD = 'kube-product-context-collection-api-nextgen.service.intraiad1.consul.csnzoo.com';

  /**
   * @var string
   */
  private $base_url;

  /**
   * @var LoggerInterface
   */
  private $logger;

  /**
   * Product_Context_Collection_Api_Client constructor.
   *
   * @param LoggerInterface $logger      Logger
   * @param string $environment Environment
   */
  public function __construct(LoggerInterface $logger, string $environment) {
    $this->base_url = $environment === Environment::DEVELOPMENT ? self::BASE_URL_DEV : self::BASE_URL_PROD;
    $this->logger   = $logger;
  }

  /**
   * @param string ...$skus SKUs
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function find_product_context_collection(string ...$skus) : array {
    return $this->make_request('/find', ['skus' => $skus]);
  }

  /**
   * @param string[] $skus list of SKUS to get collisions
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function find_sku_collisions(array $skus) : array {
    return $this->make_request('/find-collisions', ['skus' => $skus]);
  }

  // TODO refactor methods below to be shared across all (or most) of api clients

  /**
   * @param string     $path    path
   * @param array|null $payload payload
   *
   * @return array
   * @throws API_Request_Exception
   */
  private function make_request(string $path, ?array $payload = null) : array {
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
    ];

    $curl_resource = curl_init();

    if ($curl_resource === false) { /** @phpstan-ignore-line */
      $this->logger->error('Failed curl initialization');
      throw new API_Request_Exception('Failed curl initialization');
    }

    $endpoint = $this->base_url . $path;
    curl_setopt($curl_resource, CURLOPT_URL, $endpoint);
    curl_setopt($curl_resource, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl_resource, CURLOPT_HEADER, 0);
    curl_setopt($curl_resource, CURLOPT_RETURNTRANSFER, 1);

    $json_payload = null;
    if ($payload !== null) {
      $json_payload = \json_encode($payload);
      curl_setopt($curl_resource, CURLOPT_POSTFIELDS, $json_payload);
    }

    $this->logger->info(sprintf('GET "%s"', $endpoint));
    if (!empty($json_payload)) {
      $this->logger->info(sprintf('BODY: %s', $json_payload));
    }
    $response = curl_exec($curl_resource);

    if (curl_errno($curl_resource) > 0) {
      throw new \Exception('CURL encountered an error:  ' . curl_error($curl_resource));
    }

    $this->logger->info(sprintf('RESPONSE: %s', $response));
    curl_close($curl_resource);

    return $this->decode_response($response);
  }

  /**
   * @param string $response The HTTP response body
   *
   * @return array
   * @throws API_Request_Exception
   */
  private function decode_response(string $response) : array {
    $decoded_response = \json_decode($response, true);
    if (is_null($decoded_response)) {
      $this->logger->error('Response data is an invalid JSON: ' . $response);
      throw new \Exception('Response data is an invalid JSON: ' . $response);
    }

    if (isset($decoded_response['code'])) {
      $error_message = sprintf("Response error: %d - %s", $decoded_response['code'], $decoded_response['message']);
      $this->logger->error($error_message);
      throw new \Exception($error_message);
    }

    return $decoded_response;
  }
}
