<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers;

use WF\Shared\Models\Globalization\Catalog_Model;

class Supplier_Data_Factory {
  /**
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data_Collection
   */
  public function create_supplier_collection() : Supplier_Data_Collection {
    return new Supplier_Data_Collection();
  }

  /**
   * @param array $rows Data
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suppliers\Supplier_Data
   */
  public function create_supplier_data(array $rows) : Supplier_Data {
    $names = [];

    // is canadian only if there is at least one supplier
    $isCanadianOnly = !empty($rows);

    foreach ($rows as $row) {
      $names[] = $row['name'];

      // if there is a supplier not in canada catalog
      if ($row['clg_id'] !== Catalog_Model::CATALOG_ID_CANADA) {
        $isCanadianOnly = false;
      }
    }

    return new Supplier_Data($names, $isCanadianOnly);
  }
}