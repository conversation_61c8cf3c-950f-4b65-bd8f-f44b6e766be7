<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batch;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage\Curation_Only_Batches_Postgres_DAO;

class Curation_Only_Batch_Service_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Curation_Only_Batch_Service_Storage
     */
    private $storage;

    /**
     * @var Curation_Only_Batches_Postgres_DAO
     */
    private $storage_psql;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->storage = $this->prophesize(Curation_Only_Batch_Service_Storage::class);
        $this->storage_psql = $this->prophesize(Curation_Only_Batches_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Curation_Only_Batch_Service(
            $this->storage->reveal(),
            $this->storage_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_mark_as_set_downstream_feature_toggle_off(): void
    {
        $batch_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);
        $this->storage->create_verification_id()->willReturn(1);
        $this->storage->mark_batch_as_sent(1, 1, false)->shouldBeCalled();
        $this->storage->set_verification_id_for_skus(1, 1)->shouldBeCalled();
        $this->subject->mark_as_set_downstream($batch_id);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_mark_as_set_downstream_feature_toggle_on(): void
    {
        $batch_id = 1;
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);
        $this->storage_psql->create_verification_id()->willReturn(1);
        $this->storage_psql->mark_batch_as_sent(1, 1, false)->shouldBeCalled();
        $this->storage_psql->set_verification_id_for_skus(1, 1)->shouldBeCalled();
        $this->subject->mark_as_set_downstream($batch_id);
    }
}
