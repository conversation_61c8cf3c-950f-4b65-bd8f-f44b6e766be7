<?php
/**
 * Model representing a single product in the batch verification pipeline.
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\Product\Media;

use WF\Shared\Models\Product\Media\Curation_Tool\Curation_Exclusion_Reasons;
use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;

class Batch_Verification_Item_Model {
  const SOURCE_LEGACY           = Curation_Item_Model::SOURCE_LEGACY;
  const SOURCE_WPP              = Curation_Item_Model::SOURCE_WPP;
  const SOURCE_COLLISION        = Curation_Item_Model::SOURCE_COLLISION;
  const SOURCE_LEGACY_ADDED     = Curation_Item_Model::SOURCE_LEGACY_ADDED;
  const SOURCE_PRODUCT_ADDITION = Curation_Item_Model::SOURCE_PRODUCT_ADDITION;
  const SOURCE_MANUALLY_ADDED   = Curation_Item_Model::SOURCE_MANUALLY_ADDED;

  const MAX_PRICE_TIER                = 4;
  const VERIFICATION_SOURCE_CANDIDATE = 1;
  const VERIFICATION_SOURCE_PULLED_IN = 2;

  const SIMPLE_TYPE_SKU     = 'Simple';
  const KIT_TYPE_SKU        = 'Kit';
  const COLLECTION_TYPE_SKU = 'Collection';

  const EXCLUDED_REASON_AUTOMATED_EXCLUSION_NOT_ELIGIBLE_ANYMORE = Curation_Exclusion_Reasons::EXCLUDED_REASON_AUTOMATED_EXCLUSION_NOT_ELIGIBLE_ANYMORE;


    /**
     * @var int
     */
    public $id;

    /**
     * SKU
     *
     * @var string
     */
    public $sku;

    /**
     * The white label sku corresponding to the original sku
     *
     * @var string
     */
    public $white_label_sku;

    /**
     * The date the quickform project associated with this SKU was added
     *
     * @var string
     */
    public $date_execution_complete;

    /**
     * URL to the PDP
     *
     * @var string
     */
    public $sku_url;

    /**
     * Product name
     *
     * @var string
     */
    public $name;

    /**
     * Category name
     *
     * @var string
     */
    public $category_name;

    /**
     * Image URL
     *
     * @var string
     */
    public $image_url;

    /**
     * Large Image URL
     *
     * @var string
     */
    public $zoomed_image_url;

    /**
     * Price tier
     *
     * @var int
     */
    public $price_tier;

    /**
     * Price tier we want to use to override the calculated price tier
     *
     * @var int
     */
    public $price_tier_override;

    /**
     * Sale Price (corresponds to PrSalePrice)
     *
     * @var float
     */
    public $sale_price;

    /**
     * Final style
     *
     * @var int
     */
    public $final_style_id;

    /**
     * Final sub-style
     *
     * @var int
     */
    public $final_sub_style_id;

    /**
     * Final brand MaID
     *
     * @var int
     */
    public $final_brand_id;

    /**
     * Current brand name
     *
     * @var string
     */
    public $current_brand_name;

    /**
     * Current brand MaBrwId
     *
     * @var int|null
     */
    public $current_brand_brw_id;

    /**
     * Whether this is an investment SKU
     *
     * @var int
     */
    public $is_investment_sku;

    /**
     * Employee ID for the user who updated this item
     *
     * @var int
     */
    public $locked_by_employee_id;

    /**
     * Employee name for the user who updated this item
     *
     * @var string
     */
    public $locked_by_employee_name;

    /**
     * Value of the exclusion reason
     *
     * @var int
     */
    public $excluded_reason_id;

    /**
     * Whether this item is a verification candidate (true) or a context SKU (false)
     *
     * @var bool
     */
    public $is_verification_candidate = true;

    /**
     * Whether this item is a kit collision or not
     *
     * @var bool
     */
    public $is_kit_collision;

    /**
     * Photo request type
     *
     * @var int
     */
    public $photo_request_type;

    /**
     * List of supplier names who carry this product
     *
     * @var string
     */
    public $supplier_names;

    /**
     * List of suppliers who carry this product
     *
     * @var string
     */
    public $supplier_ids;

    /**
     * START: Kit information
     */

    /**
     * Kit SKU
     *
     * @var string
     */
    public $kit_sku;

    /**
     * Simple, Kit or Collection
     *
     * @var string
     */
    public $sku_type;

    /**
     * Whether the sku is parent sku
     *
     * @var bool
     */
    public $is_kit_parent_sku;

    /**
     * Kit name
     *
     * @var string
     */
    public $kit_name;

    /**
     * Class name
     *
     * @var string
     */
    public $class_name;

    /**
     * Class name
     *
     * @var string
     */
    public $kit_class_name;

    /**
     * Head class
     *
     * @var string
     */
    public $head_class_name;

    /**
     * Child sale price
     *
     * @var float
     */
    public $child_sale_price;

    /**
     * END: Kit information
     */

    /**
     * START: Collection information
     */

    /**
     * Collection name
     *
     * @var string
     */
    public $collection_name;

    /**
     * Collection ID
     *
     * @var int
     */
    public $collection_id;

    /**
     * END: Collection information
     */

    /**
     * White label style ID
     *
     * @var int
     */
    public $white_label_style_id;

    /**
     * White label style name
     *
     * @var string
     */
    public $white_label_style_name;

    /**
     * The reason this SKU exists in tblVerificationItem
     *
     * @var int
     */
    public $source = self::VERIFICATION_SOURCE_PULLED_IN;

    /**
     * Locked date
     *
     * @var string
     */
    public $locked_date;

    /**
     * Project id
     *
     * @var int
     */
    public $project_id;

    /**
     * SKU verified(cloned) date
     *
     * @var string
     */
    public $verified_date;

    /**
     * Project completed date
     *
     * @var string
     */
    public $project_completed_date;

    /**
     * Is directly launch or not
     *
     * @var bool
     */
    public $is_direct_launch;

    /**
     * Product Status(PrStatus)
     *
     * @var int
     */
    public $status;

    /**
     * Product Status Name
     *
     * @var string
     */
    public $status_name;

    /**
     * Is Kitsco product
     *
     * @var bool
     */
    public $is_kitsco = false;

    /**
     * Is Kit Children
     *
     * @var bool
     */
    public $is_kit_children = false;

    /**
     * QA Status ID - possible values in the pick list tblplVerificationItemQAStatus
     *
     * @var int
     */
    public $qa_status_id = 0;

    /**
     * Number of non Canadian joins for an SKU
     *
     * @var bool
     */
    public $is_canadian_sku = false;

    /**
     * QA Status Object
     *
     * @var \WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object
     */
    public $qa_status_object = null;

    /**
     * Id of verification batch current item belongs to. Filled after item was sent downstream.
     *
     * @var int|null
     */
    public $verification_id = null;

    /**
     * @var int
     */
    public $price_options_count = 0;

    /**
     * @var int
     */
    public $brand_catalog_id;

    /**
     * @var int|null
     */
    public $context_xn_id;

    /**
     * @var int
     */
    public $batch_id;

    /**
     * @var string|null
     */
    public $last_clone_date;

    /**
     * @var bool
     */
    public $is_recently_cloned = false;
}
