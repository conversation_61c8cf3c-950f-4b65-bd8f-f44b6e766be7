<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;

class Automatic_Class_Curation_Saver implements Automatic_Curation_Saver {
  /**
   * @var Curation_Decision_Service
   */
  private $curationDecisionService;

  /**
   * @var Automatic_Class_Curation_Configuration_Loader
   */
  private $configurationLoader;

  /**
   * @var Region_Service
   */
  private $regionService;

  /**
   * @param Curation_Decision_Service                     $curationDecisionService Curation decision service
   * @param Automatic_Class_Curation_Configuration_Loader $configurationLoader     Configuration Loader
   * @param Region_Service                                $regionService           Region Service
   */
  public function __construct(
      Curation_Decision_Service $curationDecisionService,
      Automatic_Class_Curation_Configuration_Loader $configurationLoader,
      Region_Service $regionService
  ) {
    $this->curationDecisionService = $curationDecisionService;
    $this->configurationLoader     = $configurationLoader;
    $this->regionService           = $regionService;
  }

  /**
   * @param int       $batchId    Batch ID
   * @param Section[] $sections   Section data
   * @param int       $employeeId Employee ID
   *
   * @return int
   * @throws \Exception
   */
  public function execute(int $batchId, array $sections, int $employeeId) : int {
    $region        = $this->regionService->get_region($batchId);
    $configuration = $this->configurationLoader->get_configuration($region);

    $savedAt           = date('Y-m-d H:i:s');
    $savedItemsCounter = 0;

    $items = $this->getNotCuratedItems($sections);

    foreach ($items as $item) {
      if (empty($item->get_class_id()) || empty($item->get_price_tier())) {
        continue;
      }

      $classConfig = $configuration->get_for_class_with_price_tier($item->get_class_id(), $item->get_price_tier());

      // if there is no automatic config for the current class and price tier
      if (empty($classConfig)) {
        continue;
      }

      $this->curationDecisionService->save_curated(
          $batchId,
          [$item->get_sku()],
          $item->get_price_tier(),
          $classConfig->get_style_id(),
          $classConfig->get_substyle_id(),
          $classConfig->get_manufacturer_id(),
          $item->get_final_granular_style_id(),
          $savedAt,
          $employeeId,
          Curation_Decision_Source::automatic_by_class()
      );

      $savedItemsCounter++;
    }

    return $savedItemsCounter;
  }

  /**
   * @param Section[] $sections Section data
   *
   * @return Curation_Item[]
   */
  private function getNotCuratedItems(array $sections) : array {
    $items = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $item) {
        // if it was previously saved
        if (!empty($item->get_saved_at())) {
          continue;
        }

        $items[] = $item;
      }
    }

    return $items;
  }
}
