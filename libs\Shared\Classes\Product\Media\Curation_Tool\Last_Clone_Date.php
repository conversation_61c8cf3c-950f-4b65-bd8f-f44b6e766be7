<?php
/**
 * Last Clone Date
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\Product\Media\Curation_Tool;

class Last_Clone_Date {
  const RECENTLY_CLONED_MIN_DAYS = 90;

  /**
   * @var string|null
   */
  private $last_clone_date;

  /**
   * @var bool
   */
  private $is_recently_cloned = false;

  /**
   * @var \DateTimeImmutable
   */
  private static $start_clone_recent_datetime = null;

  /**
   * Last_Clone_Date constructor.
   *
   * @param string|null $last_cloned Last Cloned
   */
  public function __construct(string $last_cloned = null) {
    if (empty($last_cloned)) {
      return;
    }

    $last_clone_datetime = new \DateTimeImmutable($last_cloned);
    $this->is_recently_cloned = $last_clone_datetime > $this->get_date_start_recently_cloned();

    // set the date formatted
    $this->last_clone_date    = $last_clone_datetime->format('Y-m-d');
  }

  /**
   * Gets the start date from which a cloning process is considered recent (e.g. 90 days ago)
   *
   * @return \DateTimeImmutable
   */
  private function get_date_start_recently_cloned() {
    // save the date as static to avoid recalculation for every instance
    if (self::$start_clone_recent_datetime === null) {
      // it is recent if it happened N days ago
      self::$start_clone_recent_datetime = (new \DateTimeImmutable())->modify(sprintf('%d days', self::RECENTLY_CLONED_MIN_DAYS * -1));
    }

    return self::$start_clone_recent_datetime;
  }

  /**
   * @return bool
   */
  public function is_recently_cloned() : bool {
    return $this->is_recently_cloned;
  }

  /**
   * @return string|null
   */
  public function get_date_formatted() {
    return $this->last_clone_date;
  }
}
