<?php

/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Tests\libs\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Api\Production_Tracking_Api_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Legacy_Production_Tracking_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Process_Factory;
use WF\Secrets\SecretProviderInterface;

class Production_Tracking_Process_Factory_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Legacy_Production_Tracking_Processor
     */
    private $legacy_pt_processor;
    /**
     * @var Production_Tracking_Api_Processor
     */
    private $pt_api_processor;
    /**
     * @var SecretProviderInterface
     */
    private SecretProviderInterface $secretProvider;
    private bool $curationProductionTrackingApi;
    /**
     * @var ObjectProphecy|LoggerInterface
     */
    private $logger;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->legacy_pt_processor = $this->prophesize(Legacy_Production_Tracking_Processor::class);
        $this->pt_api_processor = $this->prophesize(Production_Tracking_Api_Processor::class);
        $this->secretProvider = $this->createMock(SecretProviderInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
    }

    /**
     * Test
     */
    public function testGetProcessorWithCurationProductionTrackingApiTrue()
    {
        $this->curationProductionTrackingApi = true;

        $secretProvider = $this->createMock(SecretProviderInterface::class);
        $secretProvider->method('get')->willReturnMap([
            [Production_Tracking_Process_Factory::CURATION_PRODUCTION_TRACKING_API_CLIENT_ID, 'some_client_id'],
            [Production_Tracking_Process_Factory::CURATION_PRODUCTION_TRACKING_API_CLIENT_SECRET, 'some_client_secret'],
        ]);

        $factory = new Production_Tracking_Process_Factory(
            $this->legacy_pt_processor->reveal(),
            $this->pt_api_processor->reveal(),
            $secretProvider
        );
        $processor = $factory->get_processor();
        $this->assertInstanceOf(Production_Tracking_Api_Processor::class, $processor);
    }

    /**
     * Test
     */
    public function testGetProcessorWithCurationProductionTrackingApiFalse()
    {
        $this->curationProductionTrackingApi = false;

        $factory = new Production_Tracking_Process_Factory(
            $this->legacy_pt_processor->reveal(),
            $this->pt_api_processor->reveal(),
            $this->secretProvider // We don't need SecretProviderInterface in this test case
        );

        $factory->setLogger($this->logger);
        $processor = $factory->get_processor();
        $this->assertInstanceOf(Legacy_Production_Tracking_Processor::class, $processor);
    }
}
