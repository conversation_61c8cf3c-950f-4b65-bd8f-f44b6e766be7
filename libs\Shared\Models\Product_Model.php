<?php
/**
 * This file contains the ProductModel class
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models;

/**
 * Class Product_Model
 *
 * @package WF\Shared\Models
 */
class Product_Model {
  const STATUS_CAN_BE_DELETED              = 0;
  const STATUS_BEING_ADDED                 = 1;
  const STATUS_KIT_COMPONENT               = 2;
  const STATUS_ADMINS_CAN_SELL             = 3;                     //Ticket 1679594 PrStatus logic examined
  const STATUS_LIVE_PRODUCT                = 4;
  const STATUS_MANUFACTURER_DISCONTINUED   = 5;
  const STATUS_SUPPLIER_DISCONTINUED       = 6;
  const STATUS_INTERNAL_DISCONTINUED       = 7;
  const STATUS_PHASING_OUT                 = 13;
  const STATUS_TEMPORARY_HOLD              = 14;
  const STATUS_INDETERMINATE_BACKORDER     = 15;
  const STATUS_REPLACEMENT_PART_ONLY       = 16;
  const STATUS_KIT_PHASING_OUT             = 17;
  const STATUS_SUPER_RELATED_ITEM          = 18;
  const STATUS_RETURN_ITEM_ONLY            = 19;                    // Integer: A constant saying that a PrStatus equal to 19 means the item is 'return item only'
  const STATUS_KIT_COMPONENT_DISCONTINUED  = 20;
  const STATUS_GIFT_CERTIFICATE            = 21;
  const STATUS_PRIVATE_SALE_REPORTING_ONLY = 22;
}
