<?php
/**
 * Mail Result Test
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Mailer;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Result;

class Mail_Result_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     * @dataProvider argument_cases
     * @return void
     *
     * @param mixed  $success             success
     * @param mixed  $reason              reason
     * @param bool   $success_expectation success expectation
     * @param string $reason_expectation  reason expectation
     */
    public function it_casts_arguments_to_valid_types($success, $reason, $success_expectation, $reason_expectation)
    {
        $subject = new Mail_Result($success, $reason);

        $this->assertEquals($success_expectation, $subject->is_successful());
        $this->assertEquals($reason_expectation, $subject->get_reason());
    }

    /**
     * @return array
     */
    public function argument_cases()
    {
        return [
            'integer instead of bool' => [1, 'test', true, 'test'],
            'empty string instead of bool' => ['', 'test', false, 'test'],
            'boolean instead of string' => [true, true, true, '1'],
            'null instead of boolean' => [null, 'test', false, 'test'],
            'regular arguments' => [true, 'abc', true, 'abc']
        ];
    }
}
