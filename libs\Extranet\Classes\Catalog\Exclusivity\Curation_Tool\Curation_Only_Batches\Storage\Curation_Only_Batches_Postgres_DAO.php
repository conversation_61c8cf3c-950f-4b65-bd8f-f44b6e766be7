<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Wayfair LLC - All rights reserved
 */
namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Storage;
use WF\Shared\Traits\Logging_Trait;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;
use \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service_Postgres_Storage;
use PDO;
class Curation_Only_Batches_Postgres_DAO implements Curation_Only_Batch_Service_Postgres_Storage {

  use Logging_Trait;

  private PostgresConnection $pdo_postgres;

  /**
   * @param PostgresConnection         $pdo_postgres postgres
   * @param LoggerInterface|null $logger   Logger
   */
  public function __construct(PostgresConnection $pdo_postgres, ?LoggerInterface $logger = null) {
    $this->pdo_postgres = $pdo_postgres;
    $this->logger = $logger;
  }

  /**
   * @return int
   */
  public function create_verification_id() : int {
    $sql = '
      INSERT INTO "tblVerification" ("VerifiedDate", "CloningPrtID")
      VALUES (NOW(), :ticket_id)
      RETURNING "VerificationID";
    ';

    $statement = $this->pdo_postgres->prepare($sql);
    $statement->bindValue(':ticket_id', 0, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot create Verification ID');
    }

    return $statement->fetch(PDO::FETCH_COLUMN);
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   * @param bool $fromPWL from partial white label
   *
   * @return void
   */
  public function mark_batch_as_sent(int $batch_id, int $verification_id, bool $fromPWL = false) : void {
    $statusUpd = '"StatusID" = :status_id,';
    if($fromPWL)
    {
      $statusUpd = '';
    }
    $sql = '
    UPDATE "tblCurationBatch"
    SET
        "SentWLAt" = NOW(),
         '. $statusUpd .'
        "VerificationID" = :verification_id
    WHERE "ID" = :batch_id;
    ';
    $statement = $this->pdo_postgres->prepare($sql);

    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);
    if(!$fromPWL) {
      $statement->bindValue(':status_id', Batch::DOWNSTREAMED_STATUS, PDO::PARAM_INT);
    }

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot mark batch as sent');
    }
  }

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verficiation ID
   *
   * @return void
   */
  public function set_verification_id_for_skus(int $batch_id, int $verification_id) : void {
    $sql = '
        UPDATE "tblVerificationItem"
        SET "ViVerificationID" = :verification_id
        WHERE "ViBatchID" = :batch_id;
    ';

    $statement = $this->pdo_postgres->prepare($sql);
    $statement->bindValue(':verification_id', $verification_id, PDO::PARAM_INT);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot set verification id for skus');
    }
  }
}
