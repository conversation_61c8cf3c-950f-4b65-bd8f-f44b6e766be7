<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Log\LoggerInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Config_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Manufacturer_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Price_Tier_Data;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Style_Manufacturer_Data;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_US;

class Config_Loader_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service
     */
    private $regionService;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
     */
    private $postgresql_dao;

    /**
     * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
     */


    private $featureToggles;


    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Curation_Tool_DAO::class);
        $this->regionService = $this->prophesize(Region_Service::class);
        $this->logger = $this->prophesize(LoggerInterface::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);
        $this->postgresql_dao = $this->prophesize(Curation_Tool_Postgres_DAO::class);


        $this->subject = new Config_Loader(
            $this->dao->reveal(),
            $this->regionService->reveal(),
            $this->featureToggles->reveal(),
            $this->postgresql_dao->reveal(),
            $this->logger->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_granular_styles_feature_toggle_off()
    {
        $regionId = 1;
        $this->dao->get_granular_styles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_granular_styles(Argument::cetera())->willReturn([]);
        $this->subject->getGranularStyles($regionId);
    }


    /**
     * @test
     *
     * @return void
     */
    public function get_granular_styles_call_feature_toggle_on()
    {
        $regionId = 1;
        $this->dao->get_granular_styles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_granular_styles(Argument::cetera())->willReturn([]);
        $this->subject->getGranularStyles($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_substyles_feature_toggle_off()
    {
        $regionId = 1;
        $this->dao->get_substyles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_substyles(Argument::cetera())->willReturn([]);
        $this->subject->getSubstyles($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_substyles_call_feature_toggle_on()
    {
        $regionId = 1;
        $this->dao->get_substyles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_substyles(Argument::cetera())->willReturn([]);
        $this->subject->getSubstyles($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_price_tier_call()
    {
        $data = [];
        for ($i = 1; $i <= 4; $i++) {
            $data[] = new Price_Tier_Data($i, (string)$i);
        }
        $result = $this->subject->getPriceTiers();
        $this->assertEquals($data, $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_styles_feature_toggle_off()
    {
        $regionId = 1;
        $this->dao->get_styles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_styles(Argument::cetera())->willReturn([]);
        $this->subject->getStyles($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_styles_call_feature_toggle_on()
    {
        $regionId = 1;
        $this->dao->get_styles(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_styles(Argument::cetera())->willReturn([]);
        $this->subject->getStyles($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_exclusion_reasons_feature_toggle_off()
    {
        $regionId = 1;
        $this->dao->get_exclusion_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(false)->shouldBeCalledOnce();
        $this->postgresql_dao->get_exclusion_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getExclusionReasons($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_exclusion_reasons_call_feature_toggle_on()
    {
        $regionId = 1;
        $this->dao->get_exclusion_reasons(Argument::cetera())->willReturn([]);
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_exclusion_reasons(Argument::cetera())->willReturn([]);
        $this->subject->getExclusionReasons($regionId);
    }

    /**
     * @test
     *
     * @return void
     */
    public function get_style_manufacturer_for_batch_feature_toggle_on()
    {
        $region_id = 1;
        $data = [[
            'style_id' => 8,
            'substyle_id' => 111,
            'price_tier' => 3,
            'manufacturer_id' => 44315,
            'name' => '17 Stories',
            'brand_class' => 'tail'
        ]];
        $expected_manufac_details = [];
        $expected_manufac_details[] = new Style_Manufacturer_Data(
            $data[0]['manufacturer_id'],
            $data[0]['substyle_id'],
            $data[0]['price_tier']
        );
        $this->regionService->get_region(Argument::cetera())->willReturn(new World_Region_US());
        $this->featureToggles->isEnabled(Argument::any())->willReturn(true)->shouldBeCalledOnce();
        $this->postgresql_dao->get_manufacturers($region_id)->willReturn($data);
        $result = $this->subject->getStyleManufacturersForBatch(1);
        $this->assertEquals($expected_manufac_details, $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function getStyleManufacturers_test()
    {
        $data = [
            [
                'style_id' => 8,
                'substyle_id' => 111,
                'price_tier' => 3,
                'manufacturer_id' => 44315,
                'name' => '17 Stories',
                'brand_class' => 'tail'
            ]
        ];
        $expected_manufac_details = [];
        $expected_manufac_details[] = new Style_Manufacturer_Data(
            $data[0]['manufacturer_id'],
            $data[0]['substyle_id'],
            $data[0]['price_tier']
        );
        $result = $this->subject->getStyleManufacturers($data);
        $this->assertEquals($expected_manufac_details, $result);
    }

    /**
     * @test
     *
     * @return void
     */
    public function getManufacturers_test()
    {
        $data = [
            [
                'style_id' => 8,
                'substyle_id' => 111,
                'price_tier' => 3,
                'manufacturer_id' => 44315,
                'name' => '17 Stories',
                'brand_class' => 'tail'
            ]
        ];
        $expected_manufac_details = [];
        $expected_manufac_details[] = new Manufacturer_Data(
            $data[0]['manufacturer_id'],
            $data[0]['name'],
            $data[0]['brand_class']
        );
        $result = $this->subject->getManufacturers($data);
        $this->assertEquals($expected_manufac_details, $result);
    }
}
