<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream;

class Batch_Evaluation_Status {
  const STATUS_READY         = 'Ready';
  const STATUS_NOT_READY     = 'Not Ready';
  const STATUS_PARTIAL_READY = 'Partial Ready';
  const STATUS_HAS_REJECTED  = 'Has rejected skus';

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private static $status_ready;

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private static $status_not_ready;

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private static $status_partial;

  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  private static $status_has_rejected;

  /**
   * @var string
   */
  private $status;

  /**
   * @param string $status The batch status string value
   */
  private function __construct(string $status) {
    $this->status = $status;
  }

  /**
   * @return string
   */
  public function __toString() : string {
    return $this->status;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  public static function ready() : Batch_Evaluation_Status {
    if (self::$status_ready === null) {
      self::$status_ready = new self(static::STATUS_READY);
    }

    return self::$status_ready;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  public static function not_ready() : Batch_Evaluation_Status {
    if (self::$status_not_ready === null) {
      self::$status_not_ready = new self(static::STATUS_NOT_READY);
    }

    return self::$status_not_ready;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  public static function partial() : Batch_Evaluation_Status {
    if (self::$status_partial === null) {
      self::$status_partial = new self(static::STATUS_PARTIAL_READY);
    }

    return self::$status_partial;
  }

  /**
   * @return \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Batch_Evaluation_Status
   */
  public static function has_rejected() : Batch_Evaluation_Status {
    if (self::$status_has_rejected === null) {
      self::$status_has_rejected = new self(static::STATUS_HAS_REJECTED);
    }

    return self::$status_has_rejected;
  }
}
