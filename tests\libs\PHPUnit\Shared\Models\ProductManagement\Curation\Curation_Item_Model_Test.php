<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Models\ProductManagement\Curation;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;

class Curation_Item_Model_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     * @dataProvider type_expectations
     *
     * @param int  $xn_id                   collection id
     * @param int  $in_kit                  in kit flag
     * @param bool $is_simple_sku           is simple sku
     * @param bool $is_collection_component is collection component
     * @param bool $is_kit_component        is kit component
     *
     * @return void
     */
    public function it_has_type_depending_on_collection_id_and_kit_flag($xn_id, $in_kit, bool $is_simple_sku, bool $is_collection_component, bool $is_kit_component)
    {
        $model = new Curation_Item_Model();
        $model->context_xn_id = $xn_id;
        $model->is_kit_component = $in_kit;

        $this->assertEquals($is_simple_sku, $model->of_type(Curation_Item_Model::SKU_TYPE_SIMPLE));
        $this->assertEquals($is_collection_component, $model->of_type(Curation_Item_Model::SKU_TYPE_COLLECTION_COMPONENT));
        $this->assertEquals($is_kit_component, $model->of_type(Curation_Item_Model::SKU_TYPE_KIT_COMPONENT));
    }

    /**
     * @return array
     */
    public function type_expectations(): array
    {
        return [
            'It is a simple sku by default' => [null, null, true, false, false],
            'It is collection component when has collection but not in kit' => [1, null, false, true, false],
            'It is kit component when has not collection but is in kit' => [null, 1, false, false, true],
            'It is both collection and kit component when has both flags' => [1, 1, false, true, true]
        ];
    }
}
