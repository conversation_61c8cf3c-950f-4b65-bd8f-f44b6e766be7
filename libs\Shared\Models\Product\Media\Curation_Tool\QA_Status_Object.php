<?php
/**
 * QA Status Object
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Models\Product\Media\Curation_Tool;

class QA_Status_Object {

  public const NOT_APPLICABLE   = 0;
  public const PENDING_APPROVAL = 1;
  public const APPROVED         = 2;
  public const REJECTED         = 3;
  public const MIXED            = 4;
  public const UPDATED          = 5;

  public const NAMES = [
      self::NOT_APPLICABLE   => '',
      self::PENDING_APPROVAL => 'Pending approval',
      self::APPROVED         => 'Approved',
      self::REJECTED         => 'Rejected',
      self::MIXED            => 'MIXED',
      self::UPDATED          => 'Updated',
  ];

  /**
   * @var int
   */
  public $id;

  /**
   * @var bool
   */
  public $is_authorized_to_qa = false;

  /**
   * @var string
   */
  public $name;

  /**
   * Check if the detail link should be shown
   *
   * @var bool
   */
  public $is_available_details;

  /**
   * @var bool
   */
  public $is_available_approve;

  /**
   * @var bool
   */
  public $is_available_reject;

  /**
   * QA_Status_Object constructor.
   *
   * @param int  $status_id           Status ID
   * @param bool $is_authorized_to_qa Is Authorized
   */
  public function __construct(int $status_id, bool $is_authorized_to_qa) {
    if (!isset(self::NAMES[$status_id])) {
      throw new \InvalidArgumentException(
          sprintf('Incorrect Status ID: %d', $status_id)
      );
    }

    $this->id                  = $status_id;
    $this->is_authorized_to_qa = $is_authorized_to_qa;
    $this->name                = self::NAMES[$this->id];

    $this->is_available_details = $this->is_available_details();
    $this->is_available_approve = $this->is_available_approve();
    $this->is_available_reject  = $this->is_available_reject();
  }

  /**
   * @return bool
   */
  public function is_available_details() : bool {
    return in_array($this->id, [self::APPROVED, self::REJECTED]);
  }

  /**
   * @return bool
   */
  public function is_available_approve() : bool {
    return $this->is_authorized_to_qa && in_array($this->id, [self::PENDING_APPROVAL, self::REJECTED, self::MIXED]);
  }

  /**
   * @return bool
   */
  public function is_available_reject() : bool {
    return $this->is_authorized_to_qa && in_array($this->id, [self::PENDING_APPROVAL, self::APPROVED, self::MIXED]);
  }

}
