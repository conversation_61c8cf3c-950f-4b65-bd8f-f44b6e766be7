<?php
/**
 * Mail message formatter test
 *
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Tests\PHPUnit\Shared\Classes\ProductManagement\Mailer;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message;
use WF\Shared\Classes\ProductManagement\Mailer\Mail_Message_Log_Formatter;

class Mail_Message_Log_Formatter_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     * @return void
     */
    public function it_skips_content_of_attachments()
    {
        $attachments = [
            ['some content', 'test1', 'boo1'],
            ['some content', 'test2', 'boo2']
        ];

        $message = $this->prophesize(Mail_Message::class);
        $message->to_array()->willReturn(['info' => 'some info', 'attachments' => $attachments]);

        $subject = new Mail_Message_Log_Formatter();
        $result = $subject->format($message->reveal());
        foreach ($result as $key => $value) {
            $this->assertArrayHasKey('info', $result);
            $this->assertArrayHasKey('attachments', $result);
            $this->assertSame('some info', $result['info']);
            $this->assertSame(['test1', 'boo1'], $result['attachments'][0]);
            $this->assertSame(['test2', 'boo2'], $result['attachments'][1]);
        }
    }
}
