/**
 * Displays the SKU and the Predicted Winner flag, if needed
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {Checkbox, Alert, Text, TEXT_ALIGNMENTS, TEXT_STYLE, SPACING} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';

class CurationSkuCode extends React.PureComponent {
  static propTypes = {
    sku: PropTypes.string.isRequired,
    relatedKits: PropTypes.arrayOf(PropTypes.string).isRequired,
    isEditable: PropTypes.bool.isRequired,
    isSelected: PropTypes.bool,
    isPredictedWinner: PropTypes.bool.isRequired,
    onSelectionChange: PropTypes.func,
  };

  static defaultProps = {
    isSelected: false,
    onSelectionChange() {},
  };

  handleChange = event => {
    this.props.onSelectionChange(
      this.props.sku,
      this.props.relatedKits,
      event.target.checked
    );
  };

  render() {
    return (
      <React.Fragment>
        {this.props.isEditable ? (
          <Checkbox
            label={this.props.sku}
            checked={this.props.isSelected}
            onChange={this.handleChange}
          />
        ) : (
          <Text align={TEXT_ALIGNMENTS.CENTER} mb={SPACING.SPACE_MEDIUM} fontStyle={TEXT_STYLE.BOLD}>
            {this.props.sku}
          </Text>
        )}
        {this.props.isPredictedWinner && (
          <Alert variation="success">
            <Translation msgid="CurationTool.curationSkuPredictedWinner" />
          </Alert>
        )}
      </React.Fragment>
    );
  }
}

export default CurationSkuCode;
