.QABulkActionHeader {
  background: $bg-color--white;
  margin-bottom: 24px;
  padding: 10px;
  border: 1px solid $border-color--medium;
  z-index: 1001 !important;
}

.QABulkActionFlex {
  display: flex;
  justify-content: right;
  align-items: right;
}

.QABulkActionRejectContainer {
  display: inherit;
  background: $bg-color--white;
  margin: 4px;
  padding: 10px;

  & > div {
    margin-right: 8px;
    width: 200px;
  }

  &.QABulkActionRejectBorder {
    border: 1px solid $border-color--medium;
  }
}

.bulkActionsContainer {
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  flex-direction: column;

  .bulkActions {
    flex: 1;

    span {
      margin-left: 5px;
    }

    & > .fa {
      cursor: pointer;
    }
  }
}
