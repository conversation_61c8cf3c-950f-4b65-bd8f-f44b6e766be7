<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Rebrand_Project_Loader {
  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $postgres_dao;

  private $feature_toggle;

  /**
   * Data_Store constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                       $dao            Curation_Tool_DAO
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO $postgres_dao   Curation_Tool_Postgres_DAO
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                          $feature_toggle Feature Toggle
   */
  public function __construct(Curation_Tool_DAO $dao, Curation_Tool_Postgres_DAO $postgres_dao, FeatureTogglesInterface $feature_toggle) {
    $this->dao = $dao;
    $this->postgres_dao = $postgres_dao;
    $this->feature_toggle = $feature_toggle;
  }

  /**
   * @param int $batchId Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project|null
   */
  public function getRebrandProject(int $batchId) {
    if ($this->feature_toggle->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->postgres_dao->get_rebrand_project_for_batch($batchId); // need to fix function
    } else {
      return $this->dao->get_rebrand_project_for_batch($batchId);
    }
  }
}