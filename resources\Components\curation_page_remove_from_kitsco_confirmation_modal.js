/**
 * Curation - Remove from Kitsco Confirmation Modal
 *
 * <AUTHOR> <epaculs<PERSON><EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import {ConfirmationModal, SPACING, Text} from '@wayfair/homebase-extranet';

const CurationPageRemoveFromKitscoConfirmationModal = ({
  isOpen,
  onConfirm,
  onCancel,
  sku,
}) => (
  <ConfirmationModal
    title={
      <Translation msgid="CurationTool.removeFromKitscoConfirmationModalTitle" />
    }
    isOpen={isOpen}
    onConfirm={() => {
      onConfirm(sku);
    }}
    onCancel={onCancel}
  >
    <Text>
      <Translation msgid="CurationTool.removeFromKitscoConfirmationModalQuestion" />
    </Text>
    <Text mb={SPACING.SPACE_SMALL}>
      <Translation
        msgid="CurationTool.removeFromKitscoConfirmationModalSku"
        params={{sku}}
      />
    </Text>
  </ConfirmationModal>
);

export default CurationPageRemoveFromKitscoConfirmationModal;

CurationPageRemoveFromKitscoConfirmationModal.propTypes = {
  isOpen: PropTypes.bool,
  sku: PropTypes.string,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
};

CurationPageRemoveFromKitscoConfirmationModal.defaultProps = {
  isOpen: false,
  sku: '',
};
