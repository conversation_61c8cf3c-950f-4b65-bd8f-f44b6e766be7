<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use Psr\Log\LoggerInterface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Image_Loader_DAO;
use WF\Shared\Traits\Logging_Trait;

class Curation_Image_Loader {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Image_Loader_DAO
   */
  private $dao;

  /**
   * Curation_Image_Loader constructor.
   *
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Image_Loader_DAO $dao    Image Loader DAO
   * @param LoggerInterface|null                                                $logger Logger
   */
  public function __construct(Curation_Image_Loader_DAO $dao, ?LoggerInterface $logger = null) {
    $this->dao    = $dao;
    $this->logger = $logger;
  }

  /**
   * @param string $sku SKU to load image for
   *
   * @return array
   */
  public function get_images_for_sku(string $sku) : array {
    $this->log_info(
        sprintf('Loaded images for SKU "%s"', $sku),
        ['sku' => $sku]
    );

    return $this->dao->get_images_for_sku($sku);
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array
   */
  public function get_main_images_for_skus(array $skus) : array {
    if (empty($skus)) {
      return [];
    }

    $this->log_info(
        'Loaded main images for SKUS',
        ['skus' => $skus]
    );

    return $this->dao->get_main_images_for_skus($skus);
  }
}
