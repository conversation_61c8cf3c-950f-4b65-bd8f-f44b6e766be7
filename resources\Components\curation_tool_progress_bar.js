/**
 * CurationToolProgressBar
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {ProgressBar} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';

const getPercentage = (savedCount, totalCount) => {
  if (totalCount === 0) {
    return 100;
  }

  return Math.round((savedCount / totalCount) * 100);
};

const getStatus = (savedCount, totalCount) => {
  return savedCount < totalCount ? 'success' : null;
};

const CurationToolProgressBar = ({savedCount, totalCount, label}) => (
  <div className="text_center">
    <ProgressBar
      current={getPercentage(savedCount, totalCount)}
      showInnerLabel={false}
      showOuterLabel={false}
      status={getStatus(savedCount, totalCount)}
    />
    <span>
      {label || (
        <Translation
          msgid="CurationTool.XOfYSkus"
          params={{
            savedCount,
            totalCount,
          }}
        />
      )}
    </span>
  </div>
);

CurationToolProgressBar.propTypes = {
  savedCount: PropTypes.number,
  totalCount: PropTypes.number,
  label: PropTypes.node,
};

CurationToolProgressBar.defaultProps = {
  savedCount: 0,
  totalCount: 0,
  label: null,
};

export default CurationToolProgressBar;
