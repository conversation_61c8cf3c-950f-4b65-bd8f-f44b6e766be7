<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Tests\Unit\Infrastructure;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Prophecy\Prophecy\ObjectProphecy;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use WF\BrandWorkflows\PuREST\Infrastructure\Client;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTRequestException;

final class ClientTest extends TestCase
{
    use ProphecyTrait;

    /**
     * @var ClientInterface|ObjectProphecy
     */
    private $httpClient;

    private Client $purestClient;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->httpClient = $this->prophesize(ClientInterface::class);
        $this->purestClient = new Client($this->httpClient->reveal());
    }

    public function testPostSuccess(): void
    {
        /** @var ResponseInterface|ObjectProphecy $response */
        $response = $this->prophesize(ResponseInterface::class);
        /** @var RequestInterface|ObjectProphecy $request */
        $request = $this->prophesize(RequestInterface::class);

        $this->httpClient->sendRequest($request)
            ->shouldBeCalled()
            ->willReturn($response);

        $this->purestClient->request(
            $request->reveal()
        );
    }

    public function testPostPuRESTRequestException(): void
    {
        $this->expectException(PuRESTRequestException::class);

        /** @var RequestInterface|ObjectProphecy $request */
        $request = $this->prophesize(RequestInterface::class);

        $this->httpClient->sendRequest($request)
            ->willThrow(\Throwable::class);

        $this->purestClient->request($request->reveal());
    }
}
