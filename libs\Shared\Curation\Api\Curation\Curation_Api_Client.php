<?php
declare(strict_types=1);
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Curation;

use Psr\Log\LoggerInterface;
use WF\Shared\Curation\Api\Api_Curation;
use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;
use WF\Shared\Environment;

use function array_key_exists;
use function curl_close;
use function curl_errno;
use function curl_error;
use function curl_exec;
use function curl_getinfo;
use function curl_init;
use function curl_setopt;
use function json_decode;
use function json_encode;
use function sprintf;

/**
 * @todo it is possible to significantly simplified by using PuREST_Client class. See example
 * @link https://github.com/wayfair-secure/php/blob/985fca2c9f8cd8a8cbf0f3edf01a02a63cb78558/extranet/app_includes/controllers/catalog_media/tag_assets/api_controller.php#L61
 */
class Curation_Api_Client implements Api_Curation {

  private const HTTP_STATUS_SUCCESS        = 200;
  private const HTTP_CONNECTION_TIMEOUT_MS = 4000;
  private const REQUEST_BATCH_SIZE = 1000;

  /**
   * TODO extract URL constants to special place, please take a look
   * php/physical_stores/app_includes/environment_constants.php:58
   * `return Environment_Constants::$constants[$constant_id][$environment];`
   */
  private const BASE_URL_DEV  = 'kube-brandworkflows-curation-service.service.intradsm1.sdeconsul.csnzoo.com';
  private const BASE_URL_PROD = 'kube-brandworkflows-curation-service.service.intraiad1.consul.csnzoo.com';

  private string $base_url;

  private LoggerInterface $logger;

  /**
   * @param LoggerInterface $logger      Logger
   * @param string $environment Env
   */
  public function __construct(LoggerInterface $logger, string $environment) {
    $this->base_url = $environment === Environment::DEVELOPMENT ? self::BASE_URL_DEV : self::BASE_URL_PROD;
    $this->logger   = $logger;
  }

  /**
   * @param string $sku SKU to check
   *
   * @return bool
   * @throws API_Request_Exception
   */
  public function is_eligible(string $sku) : bool {
    $response = $this->make_request('/eligibility/sku/' . $sku);

    $key = 'eligibleForCuration';
    if (!array_key_exists($key, $response)) {
      $message = sprintf('Required "%s" key not found in response: %s', $key, json_encode($response));
      $this->logger->error($message, ['sku' => $sku]);

      throw new API_Request_Exception($message);
    }

    return (bool) $response[$key];
  }

  /**
   * @param string[] $skus list of SKUS to check
   *
   * @return array
   */
  public function is_eligible_batch(array $skus) : array {
    $chunks = array_chunk($skus, self::REQUEST_BATCH_SIZE);
    $result = [];
    foreach ($chunks as $chunk) {
      $eligible = $this->make_request('/eligibility/sku', ['skus' => $chunk]);
      $result = array_merge($result, $eligible);
    }
    return $result;
  }

  // TODO refactor methods below to be shared across all (or most) of api clients

  /**
   * @param string     $path    The request endpoint
   * @param null|array $payload Optional payload to send
   *
   * @return array
   *
   * @throws API_Request_Exception
   */
  private function make_request(string $path, ?array $payload = null) : array {
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
    ];

    $curl_resource = curl_init();

    if ($curl_resource === false) { /** @phpstan-ignore-line */
      $message = 'Failed curl initialization';
      $this->logger->error($message, ['path' => $path]);

      throw new API_Request_Exception($message);
    }

    $endpoint = $this->base_url . $path;

    curl_setopt($curl_resource, CURLOPT_URL, $endpoint);
    curl_setopt($curl_resource, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl_resource, CURLOPT_HEADER, 0);
    curl_setopt($curl_resource, CURLINFO_HEADER_OUT, 1);
    curl_setopt($curl_resource, CURLOPT_CONNECTTIMEOUT_MS, self::HTTP_CONNECTION_TIMEOUT_MS);
    curl_setopt($curl_resource, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl_resource, CURLOPT_FOLLOWLOCATION, 1);

    $json_payload = null;
    if ($payload !== null) {
      $json_payload = json_encode($payload);
      curl_setopt($curl_resource, CURLOPT_POSTFIELDS, $json_payload);
    }

    $log_request_context = [
        'url'          => $endpoint,
        'json_request' => $json_payload
    ];

    $this->logger->info(sprintf('GET "%s"', $endpoint), $log_request_context);
    if (!empty($json_payload)) {
      $this->logger->info(sprintf('BODY: %s', $json_payload), $log_request_context);
    }

    $response = curl_exec($curl_resource);

    $request_error = curl_errno($curl_resource);
    if ($request_error > 0) {
      $error_message = 'CURL encountered an error:  ' . curl_error($curl_resource);
      $this->logger->error($error_message, $log_request_context);

      throw new API_Request_Exception($error_message, $request_error);
    }

    curl_setopt($curl_resource, CURLOPT_HEADER, true);
    $response_http_code = curl_getinfo($curl_resource, CURLINFO_RESPONSE_CODE);

    $this->logger->info(sprintf('RESPONSE: [%s] %s', $response_http_code, $response), $log_request_context);
    curl_close($curl_resource);

    return $this->decode_response($response, $response_http_code);
  }

  /**
   * @param string $response      The HTTP response body
   * @param int    $response_code The HTTP response code
   *
   * @return array
   *
   * @throws API_Request_Exception
   */
  private function decode_response(string $response, int $response_code) : array {
    $decoded_response = json_decode($response, true);
    if ($decoded_response === null) {
      $error_message = 'Response data is an invalid JSON: ' . $response;
      $this->logger->error($error_message);

      throw new API_Request_Exception($error_message);
    }

    if ($response_code !== self::HTTP_STATUS_SUCCESS) {
      $error_message = sprintf('Response error [%d]: %s', $response_code, $response);
      $this->logger->error($error_message);

      throw new API_Request_Exception($error_message);
    }

    return $decoded_response;
  }
}
