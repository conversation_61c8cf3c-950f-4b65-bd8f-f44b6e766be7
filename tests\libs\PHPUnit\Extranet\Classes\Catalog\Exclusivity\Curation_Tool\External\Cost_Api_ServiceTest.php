<?php

namespace App\Tests\Integration\WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External;

use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Mockery;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use WF\Shared\Curation\Api\Cost\CostApiService;
use WF\Shared\Logging\Logger;
use function json_encode;
use function time;

class CostApiServiceTest extends TestCase
{
    use ProphecyTrait;
    public const FALLBACK_PRICE = 9999.00;
    private CostApiService $costApiService;
    private CurationGraphApi $curationGraphApi;
    /**
     * @var Client Client.
     */
    private $httpClient;
    /**
     * @var WF\Shared\Logging\Logger|\Prophecy\Prophecy\ObjectProphecy
     */
    private $mockLogger;
    public const CACHE_KEY = 'cost_api_access_token';
    public const CACHE_EXPIRY_THRESHOLD_IN_SECONDS = 86400; // 24 hr

    protected function setUp(): void
    {
        $this->httpClient = Mockery::mock(Client::class);
        $this->mockLogger = $this->prophesize(Logger::class);
        $this->cache = Mockery::mock(CacheItemPoolInterface::class);
        $this->httpclint = $this->prophesize(Client::class);
        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() + 86400]);
        $this->cache->shouldReceive('getItem')->with(CostApiService::CACHE_KEY)->andReturn($cacheItem);
        $this->curationGraphApi = Mockery::mock(CurationGraphApi::class);
        $this->costApiService = new CostApiService($this->mockLogger->reveal(), 'dev', '131', 'abctestsecret', $this->httpClient, $this->cache, $this->curationGraphApi);
    }

    /**
     * @test
     * @return void
     * @throws GuzzleException
     * @throws \Exception
     */
    public function getPartCostTest()
    {
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "def"}'));
        //$this->costApiService->getBaseCost()->willReturn('9999');
        $this->cache->shouldReceive('deleteItem')->with(CostApiService::CACHE_KEY);
        $finalResp = $this->costApiService->get_part_cost('123');
        $this->assertEquals('9999.00', $finalResp);
    }

    /**
     * @test
     * @return void
     */
    public function getAuthToken_ExpiredCache_Test()
    {
        $cacheItem = Mockery::mock(CacheItemInterface::class);
        $cacheItem->shouldReceive('isHit')->andReturn(true);
        $cacheItem->shouldReceive('get')->andReturn(['token' => 'abc', 'expires_at' => time() - 3600]);
        $this->cache->shouldReceive('getItem')->with(CostApiService::CACHE_KEY)->andReturn($cacheItem);
        $this->httpClient->shouldReceive('post')->andReturn(new Response(200, [], '{"access_token": "def"}'));
        $this->cache->shouldReceive('save')->with($cacheItem)->andReturnTrue();
        $actual = $this->costApiService->getTokenFromCache();
        self::assertEquals('abc', $actual);
    }

    /**
     * @test
     * @return void
     */
    public function getAccessToken_SuccessfulRequest_Test()
    {
        $request = [
            'form_params' => [
                'client_id' => $this->costApiService->clientID,
                'client_secret' => $this->costApiService->clientSecret,
                'grant_type' => 'client_credentials'
            ]
        ];
        $url = $this->costApiService->authEndpoint;

        $responseBody = json_encode(['access_token' => 'abc']);
        $this->httpClient->shouldReceive('post')
            ->with($url, $request)
            ->andReturn(new Response(200, [], $responseBody));

        $this->costApiService->cache->shouldReceive('save')->andReturnTrue();

        $actual = $this->costApiService->getAuthorizationToken();

        self::assertEquals('abc', $actual);
    }

    /**
     * @test
     * @return void
     */
    public function clearAccessToken_Test()
    {
        $this->cache->shouldReceive('deleteItem')->with(CostApiService::CACHE_KEY)->andReturnTrue();

        $this->costApiService->clearAccessToken();

        $this->assertTrue(true);
    }
}
