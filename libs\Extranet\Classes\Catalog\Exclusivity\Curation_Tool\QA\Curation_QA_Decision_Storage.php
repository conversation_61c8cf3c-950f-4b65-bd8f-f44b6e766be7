<?php
/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision;

interface Curation_QA_Decision_Storage {
  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision(Curation_QA_Decision $decision) : void;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision_and_styles(Curation_QA_Decision $decision) : void;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Entities\Curation_QA_Decision $decision The decision
   *
   * @return void
   */
  public function save_decision_and_exclude_from_wl(Curation_QA_Decision $decision) : void;

    /**
     * @param array $skus     skus to check
     * @param int   $batch_id batch id
     *
     * @return bool true if all skus have either MAID or Excluded reason are not empty
     */
    public function check_if_ready_to_save(array $skus, int $batch_id);
}
