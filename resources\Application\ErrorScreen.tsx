import React from 'react';
import Translation from '@wayfair/translation';
import {Box, Button, Heading, Text} from '@wayfair/homebase-extranet';

// eslint-disable-next-line no-undef
const reload = () => window.location.reload();

const ErrorScreen = () => {
  // eslint-disable-next-line new-cap
  document.title = Translation({msgid: 'titleError'});

  return (
    <>
      <Heading>
        <Translation msgid="titleError" />
      </Heading>
      <Text>
        <Translation msgid="errorDetails" />
      </Text>
      <Box mt={4}>
        <Button onClick={reload}>
          <Translation msgid="refresh" />
        </Button>
      </Box>
    </>
  );
};

export default ErrorScreen;
