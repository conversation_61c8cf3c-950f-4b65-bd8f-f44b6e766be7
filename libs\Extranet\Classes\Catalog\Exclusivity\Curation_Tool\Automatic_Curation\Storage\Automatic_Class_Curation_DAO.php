<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use PDO;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Class_Curation_Configuration_Storage;
use WF\Shared\Helpers\SQL;

class Automatic_Class_Curation_DAO implements Automatic_Class_Curation_Configuration_Storage, LoggerAwareInterface {
  use Lo<PERSON><PERSON>wareTrait;
  use LoggerTrait;

  private ProductConnection $pdo;

  /**
   * @param ProductConnection $pdo PDO
   */
  public function __construct(ProductConnection $pdo) {
    $this->pdo = $pdo;
  }

  /**
   * @param int $region_id Region ID
   *
   * @return Automatic_Class_Curation_Configuration_Item[]
   */
  public function get_configuration_items(int $region_id) : array {
    $this->info('Loading Automatic class curation data for region', ['region' => $region_id]);

    $sql = '
      SELECT ClassID AS class_id,
            PriceTier AS price_tier,
            ManufacturerID AS manufacturer_id,
            StyleID AS style_id,
            SubStyleID AS substyle_id
      FROM csn_product.dbo.tblVerificationAutomaticClassCuration WITH (NOLOCK)
      WHERE RegionID = :region_id
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':region_id', $region_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load automated class configuration');
      $this->error(
          $exception->getMessage(),
          ['region_id' => $region_id, 'sql' => $sql, 'exception' => $exception]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Automatic_Class_Curation_Configuration_Item::class);
  }

  /**
   * @param string[] $skus             skus
   * @param int      $brand_catalog_id brand_catalog_id
   *
   * @return Automatic_Class_Curation_Item[]
   */
  public function get_class_by_skus(array $skus, int $brand_catalog_id) : array {
    $this->info(
        'Loading Automatic class curation data for skus',
        [
          'skus' => $skus,
        ]
    );

    $sql = '
      SELECT DISTINCT
        pr.PrSKU AS sku,
        vacc.ClassID AS class_id,
        vacc.ManufacturerID AS manufacturer_id,
        vacc.StyleID AS style_id,
        vacc.SubStyleID AS substyle_id
      FROM csn_product.dbo.tblProduct pr WITH (NOLOCK)
      JOIN csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK) ON pc.PrSKU = pr.PrSKU AND pc.PcMasterClass = 1
      JOIN csn_product.dbo.tblClass cl WITH (NOLOCK) ON cl.ClID = pc.ClID AND cl.ClBclgID = pr.PrBclgID
      JOIN csn_product.dbo.tblVerificationAutomaticClassCuration vacc WITH (NOLOCK) ON vacc.ClassID = cl.ClID
      WHERE pr.PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8)) . '
      AND pr.PrBclgID = :brand_catalog_id
      ORDER BY pr.PrSKU;
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValuesList('skus', $skus, SQL::nvarchar(8));
    $statement->bindValue(':brand_catalog_id', $brand_catalog_id, PDO::PARAM_INT);

    if ($statement->execute() === false) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load automated class configuration');
      $this->error(
          $exception->getMessage(),
          [
            'skus' => $skus,
            'sql' => $sql,
            'exception' => $exception,
          ]
      );
      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Automatic_Class_Curation_Item::class);
  }
}
