<?php

declare(strict_types=1);

namespace App\Infrastructure\External\FeatureToggle;

use Psr\Log\LoggerInterface;
use WF\FeatureToggle\Exception\FeatureLoadingException;
use WF\FeatureToggle\FeatureTogglesInterface as FeatureTogglesClientInterface;
use function array_key_exists;

class FeatureToggleService implements FeatureTogglesInterface
{
    private FeatureTogglesClientInterface $featureToggleClient;
    private ?LoggerInterface $logger;

    public function __construct(FeatureTogglesClientInterface $featureToggleClient, ?LoggerInterface $logger = null)
    {
        $this->featureToggleClient = $featureToggleClient;
        $this->logger = $logger;
    }

    public function isEnabled(string $featureName): bool
    {
        try {
            return $this->featureToggleClient->isEnabled($featureName);
        } catch (FeatureLoadingException $exception) {
            if ($this->logger !== null) {
                $this->logger->warning($exception->getMessage(), ['exception' => $exception]);
            }

            return array_key_exists($featureName, self::DEFAULT_FEATURE_TOGGLE_STATE)
                ? self::DEFAULT_FEATURE_TOGGLE_STATE[$featureName]
                : false;
        }
    }
}
