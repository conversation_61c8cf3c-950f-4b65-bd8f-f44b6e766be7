<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

use WF\Shared\Classes\ProductManagement\WhiteLabel\White_Label_Brand_Picklist;
use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;

class Context_Data_Manufacturer_Service {
  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Configuration
   */
  private $context_data_configuration;

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Configuration $context_data_configuration Context data configuration
   */
  public function __construct(Context_Data_Configuration $context_data_configuration) {
    $this->context_data_configuration = $context_data_configuration;
  }

  /**
   * @param int      $manufacturer_id                  Manufacturer ID
   * @param int      $manufacturer_brwd_id             Manufacture Brw ID
   * @param int|null $source_id                        Source ID
   * @param int|null $recently_curated_manufacturer_id Recently Curated Manufacturer
   *
   * @return int|null
   */
  public function get_final_manufacturer_id(int $manufacturer_id, int $manufacturer_brwd_id, int $source_id = null, int $recently_curated_manufacturer_id = null) {
    if (!$this->is_collision($source_id)) {
      return null;
    }

    // if it is a collision and it was recently curated
    if (!empty($recently_curated_manufacturer_id)) {
      return $recently_curated_manufacturer_id;
    }

    // exists as in tblProduct assigned to exclusive brand
    if ($manufacturer_brwd_id === White_Label_Brand_Picklist::EXCLUSIVE_BRAND
        || in_array($manufacturer_id, $this->context_data_configuration->get_standard_brands())
    ) {
      return $manufacturer_id;
    }

    return null;
  }

  /**
   * @param int|null $source_id Source id value
   *
   * @return bool
   */
  private function is_collision(int $source_id = null) {
    return !isset($source_id) || $source_id === Curation_Item_Model::SOURCE_COLLISION;
  }
}