parameters:
    level: 6
    paths:
        - .
    excludePaths:
        - vendor/*
        - bin/*
        - config/*
        - public/*
        - var/*
        - node_modules/*
        - resources/*
        - templates/*
        - translations/*
        - docs/*
        - docker/*
        - tests/* # TDOO remove it after tests fix
        - libraries/**/tests/* # TDOO remove it after tests fix
    # TODO remove this ignore block after major troubles refactoring
    ignoreErrors:
        - '#Construct empty\(\) is not allowed. Use more strict comparison#'
        - '#Call to function in_array\(\) requires parameter \#3 to be set#'
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    # Example of ignoring errors:
    #    ignoreErrors:
    #        - '#Construct empty\(\) is not allowed. Use more strict comparison.#'
    #        - '#Method [a-zA-Z0-9\\_]+::[a-zA-Z0-9\\_]+\(\) should return [a-zA-Z0-9\\_]+ but returns ProductSelectionUi\\Domain\\Enum\\AbstractEnumeration#'
    #        -
    #            message: '#Call to an undefined method [a-zA-Z0-9\\_]+::getResponse\(\)#'
    #            paths:
    #                - src/Application/Http/Exception/*
