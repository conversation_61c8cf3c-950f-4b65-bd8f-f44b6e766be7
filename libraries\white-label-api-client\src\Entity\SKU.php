<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Entity;

final class SKU
{
    /**
     * @var string
     */
    private $sku;

    /**
     * @var int|null
     */
    private $targetManufacturerId;

    /**
     * @var bool
     */
    private $isExcluded;

    /**
     * Sku constructor.
     *
     * @param  string  $sku
     * @param  int|null  $targetManufacturerId
     * @param  bool  $isExcluded
     */
    public function __construct(string $sku, ?int $targetManufacturerId, bool $isExcluded = false)
    {
        $this->sku = $sku;
        $this->targetManufacturerId = $targetManufacturerId;
        $this->isExcluded = $isExcluded;
    }

    /**
     * @return string
     */
    public function getSku(): string
    {
        return $this->sku;
    }

    /**
     * @return int|null
     */
    public function getTargetManufacturerId(): ?int
    {
        return $this->targetManufacture<PERSON>I<PERSON>;
    }

    /**
     * @return bool
     */
    public function isExcluded(): bool
    {
        return $this->isExcluded;
    }
}
