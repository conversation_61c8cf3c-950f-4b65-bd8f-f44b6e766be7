<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller\QA;

use App\Application\DTO\SaveQADecision;
use App\Application\Exception\CurationNotCompletedException;
use App\Application\Exception\UnexpectedQAStatusException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Suggestion_Info_Saver;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Not_Found_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Decision_Service;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\FeatureToggle\FeatureTogglesInterface;
use WF\Shared\Models\Product\Media\Curation_Tool\QA_Status_Object;

use function count;
use function date;
use function in_array;
use function sprintf;

class SaveQAController extends AbstractQAControllerAbstract
{
    /**
     * @param Curation_QA_Decision_Service $qa_decision_service QA Decision service
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @param Automatic_Suggestion_Info_Saver $suggested_style_info_saver
     * @param FeatureTogglesInterface $featureToggles
     * @param SaveQADecision $decision DTO with decision. Mapped via ParamConvertor
     *
     * @return JsonResponse
     * @throws Throwable
     * @Route(path="/save_qa", methods={"POST","OPTIONS"}, requirements={"_format"="json"})
     * @Security("is_granted('ARA:Curation QA') or is_granted('ARA:Curation Batch Management EU Nearshore Curators') or is_granted('Curation - US Offshore')")
     */
    public function __invoke(
        Curation_QA_Decision_Service $qa_decision_service,
        Completion_Batch_Data_Service $batch_data_service,
        Automatic_Suggestion_Info_Saver $suggested_style_info_saver,
        FeatureTogglesInterface $featureToggles,
        SaveQADecision $decision
    ): JsonResponse {
        $qa_status_id = $decision->getQAStatus();
        $skus = $decision->getSkus();
        $batch_id = $decision->getBatchId();

        // Optional:
        $price_tier_override = $decision->getPriceTierOverride();
        $final_style_id = $decision->getFinalStyleId();
        $final_substyle_id = $decision->getFinalSubStyleId();
        $final_brand_id = $decision->getFinalBrandId();
        $reason = $decision->getReason();
        $exclusion_reason = $decision->getExclusionReason();
        $suggested_notes = $decision->getSuggestedNotes();
        $suggested_reason = $decision->getSuggestedReason();

        try {
            if ($batch_id === 0 || count($skus) === 0) {
                $this->warning(
                    'Failed to save QA step for batch. `batchId` or `skus` parameters are missing!',
                    [
                        'qa_status_id' => $qa_status_id,
                        'skus' => $skus,
                        'batch_id' => $batch_id,
                        'reason' => $reason,
                        'employee_id' => $this->getEmployeeId()
                    ]
                );

                return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
            }

            // safety check
            if (!$this->isExpectedBatchStatus($batch_id, [Batch::STATUS_MANUAL_CURATION_COMPLETE, Batch::STATUS_AUTOMATED_QA_IN_PROGRESS, Batch::STATUS_AUTOMATED_POST_LAUNCH_QA_INPROGRESS], $batch_data_service)) {
                $this->warning(
                    sprintf(
                        'Failed to save QA step for batch_id=%d. Unexpected batch status!',
                        $batch_id
                    ),
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );

                return $this->json([], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
            }

            $this->assertExpectedQAStatus($batch_id, $qa_status_id, $batch_data_service);

            if (!$qa_decision_service->is_ready_to_save($skus, $batch_id)) {
                $this->warning(
                    'Failed to save QA step for batch. Batch is not ready to save!',
                    ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                );
                throw new CurationNotCompletedException();
            }

            switch ($qa_status_id) {
                case QA_Status_Object::APPROVED:
                    $this->info(
                        'QA decision is `approved`',
                        ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'skus' => $skus]
                    );

                    $qa_decision_service->approve($batch_id, $skus, $this->getEmployeeId());
                    break;
                case QA_Status_Object::REJECTED:
                    $this->info(
                        'QA decision is `rejected`',
                        [
                            'batch_id' => $batch_id,
                            'employee_id' => $this->getEmployeeId(),
                            'skus' => $skus,
                            'reason' => $reason
                        ]
                    );

                    $qa_decision_service->reject($batch_id, $skus, $this->getEmployeeId(), $reason);

                    break;
                case QA_Status_Object::UPDATED:
                    $this->info(
                        'QA decision is `updated`',
                        [
                            'batch_id' => $batch_id,
                            'employee_id' => $this->getEmployeeId(),
                            'skus' => $skus,
                            'price_tier_override' => $price_tier_override,
                            'final_style_id' => $final_style_id,
                            'final_substyle_id' => $final_substyle_id,
                            'final_brand_id' => $final_brand_id,
                            'exclusion_reason' => $exclusion_reason,
                            'suggested_reason' => $suggested_reason,
                        ]
                    );

                    // if `exclusion_reason` is not specified
                    if ($exclusion_reason === -1) {
                        $qa_decision_service->update(
                            $batch_id,
                            $skus,
                            $this->getEmployeeId(),
                            $price_tier_override,
                            $final_style_id,
                            $final_substyle_id,
                            $final_brand_id,
                        );


                        $this->info(
                            'Saving suggested info for curated SKUs',
                            [
                                'batch_id' => $batch_id,
                                'employee_id' => $this->getEmployeeId(),
                                'skus' => $skus
                            ]
                        );
                        $saved_at = date('Y-m-d H:i:s');

                        $suggested_style_info_saver->save_suggested_info_for_curated_skus(
                            $batch_id,
                            $skus,
                            $final_style_id,
                            $final_substyle_id,
                            $saved_at,
                            $suggested_notes,
                            $suggested_reason
                        );
                        break;
                    }

                    // If optional request parameters, overwrite on verificationItem
                    $qa_decision_service->excludeFromWL(
                        $batch_id,
                        $skus,
                        $this->getEmployeeId(),
                        $exclusion_reason
                    );

                    break;
                default:
                    $this->warning(
                        'Failed to save QA step for batch. Unexpected QA status!',
                        ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId()]
                    );
                    throw new UnexpectedQAStatusException(
                        'Failed to save QA step for batch. Unexpected QA status!'
                    );
            }

            return $this->json([
                'qaStatus' => $qa_status_id,
                'skus' => $skus
            ]);
        } catch (Throwable $exception) {
            $this->error(
                'An error occurred during saving QA decision',
                ['batch_id' => $batch_id, 'employee_id' => $this->getEmployeeId(), 'exception' => $exception]
            );
            throw $exception;
        }
    }

    /**
     * Check the QA status is valid for that process type.
     *
     * @param int $batch_id batch id
     * @param int $qa_status_id qa status id
     * @param Completion_Batch_Data_Service $batch_data_service Completion_Batch_Data_Service
     * @return bool
     */
    private function is_expected_qa_status(
        int $batch_id,
        int $qa_status_id,
        Completion_Batch_Data_Service $batch_data_service
    ): bool {
        $this->info(
            'Asserting batch QA status for QA process type',
            [
                'batch_id' => $batch_id,
                'qa_status_id' => $qa_status_id,
            ]
        );

        try {
            $batch_data = $batch_data_service->get($batch_id);
        } catch (Completion_Batch_Not_Found_Exception $exception) {
            $this->error(
                sprintf('Failed to load batch data: %s', $exception->getMessage()),
                ['batch_id' => $batch_id, 'exception' => $exception]
            );

            return false;
        }

        $process_type_id = $batch_data->getProcessTypeID();

        $this->info(
            'Batch QA status for QA process type',
            [
                'batch_id' => $batch_id,
                'status' => $batch_data->getStatus(),
                'process_type' => $process_type_id,
                'qa_status_id' => $qa_status_id,
            ]
        );

        $validation = [
            QA_Status_Object::APPROVED => [
                Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
            ],
            QA_Status_Object::REJECTED => [
                Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
            ],
            QA_Status_Object::UPDATED => [
                Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                Batch::PROCESS_TYPE_CURATION_BATCH_AUTOMATED,
            ],
        ];
        if ($process_type_id === null) {
            return true;
        }
        return in_array($process_type_id, $validation[$qa_status_id], true);
    }

    /**
     * @param int $batch_id
     * @param int $qa_status_id
     * @param Completion_Batch_Data_Service $batch_data_service
     * @return void
     */
    private function assertExpectedQAStatus(
        int $batch_id,
        int $qa_status_id,
        Completion_Batch_Data_Service $batch_data_service
    ): void {
        if (!$this->is_expected_qa_status(
            $batch_id,
            $qa_status_id,
            $batch_data_service
        )
        ) {
            $this->warning(
                sprintf(
                    'Failed to save QA step for batch_id=%d. Unexpected batch QA status for QA process type!',
                    $batch_id
                ),
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $this->getEmployeeId(),
                    'qa_status_id' => $qa_status_id
                ]
            );

            throw new UnexpectedQAStatusException(
                sprintf(
                    'Failed to save QA step for batch_id=%d. Unexpected batch QA status for QA process type!',
                    $batch_id
                )
            );
        }
    }
}
