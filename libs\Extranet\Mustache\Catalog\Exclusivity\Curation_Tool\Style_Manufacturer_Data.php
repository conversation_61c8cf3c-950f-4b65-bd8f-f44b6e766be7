<?php

declare (strict_types=1);

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool;

class Style_Manufacturer_Data implements \JsonSerializable {

  /**
   * @var int
   */
  private $manufacturerId;

  /**
   * @var int
   */
  private $substyleId;

  /**
   * @var int
   */
  private $priceTier;

  /**
   * Style_Manufacturer_Data constructor.
   *
   * @param int $manufacturerId Manufacturer ID
   * @param int $substyleId     Substyle ID
   * @param int $priceTier      Price Tier
   */
  public function __construct(int $manufacturerId = null, int $substyleId = null, int $priceTier = null) {
    $this->manufacturerId = $manufacturerId;
    $this->substyleId     = $substyleId;
    $this->priceTier      = $priceTier;
  }

  /**
   * @return int
   */
  public function manufacturerId() {
    return $this->manufacturerId;
  }

  /**
   * @return int
   */
  public function substyleId() {
    return $this->substyleId;
  }

  /**
   * @return int
   */
  public function priceTier() {
    return $this->priceTier;
  }

    public function jsonSerialize()
    {
        return [
            'manufacturerId' => $this->manufacturerId(),
            'substyleId' => $this->substyleId(),
            'priceTier' => $this->priceTier(),
        ];
    }
}
