<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream;

use DateTime;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Symfony\Component\HttpFoundation\JsonResponse;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Curation_Only_Batch_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Curation_Production_Tracking_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downs<PERSON>am_Batch_Service_Interface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_QA_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\Downstream_Fail_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\White_Label_Batch_Already_Exists_Exception;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Batch;
use WF\Shared\Traits\Logging_Trait;

class Downstream_QA_Service_Test extends TestCase
{
    use ProphecyTrait;
    use Logging_Trait;
    /**
     * @var Downstream_Batch_Service_Interface
     */
    private $downstream_batch_service;

    /**
     * @var Curation_Only_Batch_Service
     */
    private $curation_only_batch_service;

    /**
     * @var Curation_Production_Tracking_Service
     */
    private $production_tracking_service;

    /**
     * @var WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Error_Response
     */
    private $error_response;

    /**
     * @var Completion_Batch_Data_Service
     */
    private $batch_data_service;
    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_QA_Service
     */
    private $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->downstream_batch_service = $this->prophesize(Downstream_Batch_Service_Interface::class);
        $this->curation_only_batch_service = $this->prophesize(Curation_Only_Batch_Service::class);
        $this->production_tracking_service = $this->prophesize(Curation_Production_Tracking_Service::class);
        $this->batch_data_service = $this->prophesize(Completion_Batch_Data_Service::class);
        $this->subject = new Downstream_QA_Service(
            $this->downstream_batch_service->reveal(),
            $this->curation_only_batch_service->reveal(),
            $this->production_tracking_service->reveal(),
            $this->batch_data_service->reveal()
        );
    }
    /**
     * @test
     * @dataProvider get_it_returns_array_data
     * @return void
     */
    public function it_returns_false_when_is_not_completed(array $expected_data)
    {
        $batchId = 0;
        $employeeId = 1;
        $status = 5;
        $actualResult = $this->subject->downstream_manual_batch($batchId, $employeeId);
        $this->subject->downstreanbatchID($status, $batchId, $employeeId);
        $errorResponse = new White_Label_Batch_Already_Exists_Exception();
        $this->subject->callaftercatchwhilelabelException($errorResponse, $batchId, $employeeId);
        $this->assertEquals($expected_data, $actualResult);
    }


    /**
    * @test
    * @dataProvider get_it_returns_array_data
    * @return void
    */
    public function testValidatesMultipleException(array $expected_data)
    {
        $batchId = 0;
        $employeeId = 1;
        $status = 5;
        $actualResult = $this->subject->downstream_manual_batch($batchId, $employeeId);
        $exception = new Downstream_Fail_Exception();
        $this->subject->downstreanbatchID($status, $batchId, $employeeId);
        $this->subject->callaftercatchtrowException($exception, $batchId, $employeeId);
        $this->assertEquals($expected_data, $actualResult);
    }
    /**
    * @test
    * @return void
    */
    public function it_returns_true_when_is_not_downstream()
    {
        $batchId = 1;
        $employeeId = 1;
        $status = 5;
        $this->subject->downstream_manual_batch($batchId, $employeeId);
        $actualResult = $this->subject->downstreanbatchID($status, $batchId, $employeeId);
        $this->assertEquals(['result' => true], $actualResult);
    }


    /**
     * @test
    * @return void
    */
    public function it_returns_true_when_is_batch_already_exists_in_wite_labeling()
    {
        $batchId = 1;
        $employeeId = 1;
        $status = 7;
        $this->subject->downstream_manual_batch($batchId, $employeeId);
        $errorResponse = new White_Label_Batch_Already_Exists_Exception();
        $actualResult = $this->subject->callaftercatchwhilelabelException($errorResponse, $batchId, $employeeId);
        $this->assertEquals(['result' => true], $actualResult);
    }



    /**
     * @test
     * @return void
     */
    public function it_returns_false_when_is_not_expected_batch_status()
    {
        $batchId = 1;
        $employeeId = 1;
        $status = 1;
        $this->subject->downstream_manual_batch($batchId, $employeeId);
        $actualResult = $this->subject->isExpectedBatchStatusTrue($status, $batchId, $employeeId);
        $this->assertEquals(false, $actualResult);
    }

    /**
     * @test
     * @return void
     */
    public function it_returns_true_when_is_expected_batch_status()
    {
        $batchId = 1;
        $employeeId = 1;
        $status = 6;
        $actualResult = $this->subject->isExpectedBatchStatusTrue($status, $batchId, $employeeId);
        $this->assertEquals(true, $actualResult);
    }

    /**
    * @return array
    */
    public function get_it_returns_array_data(): array
    {
        return [
            [
                [
                    JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                ],
                [
                    'result' => true,
                    'errorCode' => 200,
                    'errorMessage' => 'Dummy Text',
                ],
                [
                    'result' => false,
                ],
            ]
        ];
    }
    /**
     * @return array
     */
    public function get_batch_data(): array
    {
        return [
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_QA_COMPLETE,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => 1,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => 1,
                    'ApprovedAt' => new DateTime(),
                ]
            ],
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_IN_PROGRESS,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
            [
                [
                    'StatusID' => Batch::STATUS_MANUAL_DOWNSTREAMED,
                    'BrandCatalogID' => 1,
                    'AssignedEmID' => null,
                    'CurationBatchProcessTypeID' => Batch::PROCESS_TYPE_CURATION_BATCH_MANUAL_REVIEW,
                    'CurationBatchProcessTypeName' => 'Manual',
                    'CreatedAt' => new DateTime(),
                    'ApproverEmpId' => null,
                    'ApprovedAt' => null,
                ]
            ],
        ];
    }
}
