<?php

declare(strict_types=1);

namespace WF\Shared\Helpers;

final class SQL
{
    public const bit = 'bit';
    public const int = 'int';
    public const datetime = 'datetime';
    public const smallint = 'smallint';
    public const money = 'money';

    /**
     * Return the type descriptor for the decimal data type.
     *
     * @param int $precision total number of digits default 18.
     * @param int $scale digits to right of decimal point.
     *
     * @return string
     */
    public static function decimal(int $precision = 18, int $scale = 0)
    {
        return 'decimal(' . $precision . ',' . $scale . ')';
    }

    /**
     * @param int $value Value
     *
     * @return string
     */
    public static function nvarchar(int $value): string
    {
        return 'nvarchar(' . $value . ')';
    }

    /**
     * @param int $value Value
     *
     * @return string
     */
    public static function varchar(int $value): string
    {
        return 'varchar(' . $value . ')';
    }
}
