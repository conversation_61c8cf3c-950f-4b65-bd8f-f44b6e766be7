<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Models\ProductManagement\WhiteLabel\Batching;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use ReflectionClass;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch_SKU;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Downstreamable_SKU;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;

class Verified_Batch_SKU_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_batch_sku()
    {
        $rc = new ReflectionClass(Verified_Batch_SKU::class);

        $this->assertTrue($rc->implementsInterface(Batch_SKU::class));
    }

    /**
     * @test
     * @return void
     * @throws \ReflectionException
     */
    public function it_is_downstreamable_sku()
    {
        $rc = new ReflectionClass(Verified_Batch_SKU::class);

        $this->assertTrue($rc->implementsInterface(Downstreamable_SKU::class));
    }

    /**
     * @param bool  $is_excluded       is excluded
     * @param mixed $target_ma_id      final brand
     * @param int   $status            status
     * @param bool  $block_expectation expectation
     *
     * @test
     * @dataProvider display_sku_blocking_expectations
     *
     * @return void
     */
    public function it_blocks_display_sku_eligibility_when_applicable(bool $is_excluded, $target_ma_id, int $status, bool $block_expectation)
    {
        $sku = new Verified_Batch_SKU();
        $sku->set_is_excluded_from_wl($is_excluded);
        $sku->set_target_ma_id($target_ma_id);
        $sku->set_status($status);

        $this->assertEquals($block_expectation, $sku->blocks_display_sku_eligibility());
    }

    /**
     * @return array
     */
    public function display_sku_blocking_expectations(): array
    {
        return [
            'Does not block when excluded' => [true, null, 3, false],
            'Does not block when has a final brand and is being added' => [false, 123, 1, false],
            'Does not block when has a final brand and is kit component' => [false, 123, 2, false],
            'Blocks when has no final brand but not excluded' => [false, null, 1, true],
            'Blocks when has final brand, but not in a proper status' => [false, 123, 3, true],
        ];
    }
}
