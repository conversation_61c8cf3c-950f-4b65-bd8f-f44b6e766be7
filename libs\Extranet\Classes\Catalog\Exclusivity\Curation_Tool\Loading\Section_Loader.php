<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading;

use App\Domain\Service\Loading\CurationItemFactory;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA\Curation_QA_Status;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use WF\Shared\Classes\Product\Media\Curation_Tool\Last_Clone_Date;
use Psr\Log\LoggerInterface;
use WF\Shared\Helpers\Feature;
use WF\Shared\Models\Globalization\Product_Status_Model;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU;
use WF\Shared\SLO\SLOFactory;
use WF\Shared\SLO\SLOReceiver;
use WF\Shared\Traits\Logging_Trait;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;

class Section_Loader implements Section_Loader_Interface {
  use Logging_Trait;
  private const SLO_SERVICE_NAME = 'brand-workflows-curation-tool.section_loader.';

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $dao_psql;


  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader_Interface
   */
  private $itemGroupLoader;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service
   */
  private $contextDataService;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader
   */
  private $assortmentCurationDecisionLoader;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper
   */
  private $kitParentGrouper;

  /**
   * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader
   */
  private $imageLoader;

  /**
   * @var World_Region_EU
   */
  private $regionEU;

  private SLOFactory $slo;

  private Curation_Price_Loader $priceLoader;

  private CurationItemFactory $curationItemFactory;

  private FeatureTogglesInterface $featureToggles;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader;
     */
    private $sectionAdditionalDataLoader;

  /**
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                                                   $dao                              Curation_Tool_DAO
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service                                $contextDataService               Context data service
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader Curation Decision Loader
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader                                    $imageLoader                      Image Loader
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader_Interface                     $itemGroupLoader                  Curation Loader
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper                                          $kitParentGrouper                 Kit_Parent_Grouper
   * @param \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU                                                         $regionEU                         World Region EU
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader                                    $priceLoader                      Price Loader
   * @param \App\Domain\Service\Loading\CurationItemFactory                                                                         $curationItemFactory              Curation Item Factory
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                                                      $featureToggles                   Feature toggle
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO                             $dao_psql                         Curation_Tool_Postgres_DAO
   * @param \Psr\Log\LoggerInterface|null                                                                                           $logger                           Logger

   **/
  public function __construct(
      Curation_Tool_DAO $dao,
      Context_Data_Service $contextDataService,
      Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader,
      Curation_Image_Loader $imageLoader,
      Curation_Item_Group_Loader_Interface $itemGroupLoader,
      Kit_Parent_Grouper $kitParentGrouper,
      World_Region_EU $regionEU,
      Curation_Price_Loader $priceLoader,
      CurationItemFactory $curationItemFactory,
      FeatureTogglesInterface $featureToggles,
      Curation_Tool_Postgres_DAO $dao_psql,
      ?LoggerInterface $logger = null,
      Section_Additional_Data_Loader $sectionAdditionalDataLoader
  ) {
    $this->dao                              = $dao;
    $this->contextDataService               = $contextDataService;
    $this->assortmentCurationDecisionLoader = $assortmentCurationDecisionLoader;
    $this->imageLoader                      = $imageLoader;
    $this->itemGroupLoader                  = $itemGroupLoader;
    $this->kitParentGrouper                 = $kitParentGrouper;
    $this->regionEU                         = $regionEU;
    $this->slo                              = new SLOFactory();
    $this->priceLoader                      = $priceLoader;
    $this->curationItemFactory              = $curationItemFactory;
    $this->featureToggles                   = $featureToggles;
    $this->dao_psql                         = $dao_psql;
    $this->logger                           = $logger;
    $this->sectionAdditionalDataLoader      = $sectionAdditionalDataLoader;
  }


    /**
     * @param int $batchId Batch ID
     *
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    public function getSectionsCountAndData(int $batchId, bool $count, string $sectionName, int $pageNum, int $limit, string $supplier, string $countBase, string $pageType, int $xnId) : array
    {

        $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
        try {
            $slo->withTags(['batchId' => $batchId]);
        } catch (\Exception $e) {
            // Don't break the execution if the monitor fails
        }

        $this->log_info('Loading item groups', ['batch_id' => $batchId]);
        $groups = $this->itemGroupLoader->load($batchId, $xnId, $limit, $pageNum, $supplier, (int) $countBase);

        $collectionIds = [];
        $kitsDecisionSkus = [];
        foreach ($groups as $idx => $group) {
            $collectionIds[] = $group->getItems()[0]->get_context_xn_id();
            $kitsDecisionSkus = array_merge($kitsDecisionSkus, $this->getKitsWithDecisionSkus($group));
        }

        $collections = $this->getCollectionNames($collectionIds);
        $this->log_info('Loaded collections', ['collections' => $collections]);

        $sectionsKits = [];
        $sectionsCollections = [];
        $sectionsSimple = [];

        $lastGroupIndex = 1;
        foreach ($groups as $idx => $group) {
            if ($group->isKitGroup()) {
                $this->log_info('Creating kit group section', ['skus' => $group->getSkus()]);
                $sectionsKits[] = $this->createKitGroupingSection('Kits & Composites ' . ($lastGroupIndex++), $group);
            } elseif ($group->isCollectionGroup()) {
                $collectionId = $group->getItems()[0]->get_context_xn_id();
                $this->log_info('Creating collection group section', ['collection_id' => $collectionId, 'skus' => $group->getSkus()]);
                $sectionsCollections[] = $this->createSection(
                    'Collection ' . ($collections[$collectionId] ?? '?'),
                    $group,
                    Curation_Item_Type::candidate(),
                    $collectionId
                );
            } else {
                $this->log_info('Creating collection simple section', ['skus' => $group->getSkus()]);
                $sectionsSimple[] = $this->createSection('Simple SKUs', $group, Curation_Item_Type::simple(), 1);
            }
        }

        $sections = array_merge($sectionsSimple, $sectionsCollections, $sectionsKits);

        $this->sectionAdditionalDataLoader->populate($batchId, $sections);

        $filteredSkusTotalCount = 0;
        $filteredSkusSavedCount = 0;
        if($supplier != ""){
            $supplierArr = explode("|", $supplier);
           foreach ( $sections  as $value) {
//               if ($value->get_title() === $sectionName) {
                   $curation_item_supplier = [];
                   foreach ($value->get_curation_items() as $curation_item) {
                       foreach ($supplierArr as $supp) {
                           if (in_array($supp, $curation_item->get_suppliers())) {
                               $filteredSkusTotalCount++;
                               if ($curation_item->get_qa_status() != null && $curation_item->get_qa_status()->value() == 1) {
                                   $filteredSkusSavedCount++;
                               }
                               array_push($curation_item_supplier, $curation_item);
                           }
                       }
                   }
                   $value->set_curation_items($curation_item_supplier);
//               }
            }
        }

        if ($countBase != '-1' && $countBase != '') {
            $filteredSkusTotalCount = 0;
            $filteredSkusSavedCount = 0;
            foreach ($sections as $value) {
                $curation_item_supplier = [];
//                if ($value->get_title() === $sectionName) {
                    foreach ($value->get_curation_items() as $curation_item) {
                        if ($curation_item->get_qa_status() != null && $curation_item->get_qa_status()->value() == $countBase) {
                            $filteredSkusTotalCount++;
                            if ($curation_item->get_qa_status() != null && $curation_item->get_qa_status()->value() == 1) {
                                $filteredSkusSavedCount++;
                            }
                            array_push($curation_item_supplier, $curation_item);
                        }
                    }
                    $value->set_curation_items($curation_item_supplier);
//                }
            }
        }

        if(!$count){

            $myarray = [];
            $suppliers = [];
            foreach ( $sections  as $key=>$value) {
                if ($sectionName == "" || $value->get_title() == $sectionName){
                    $savedCount = 0;
                    $filteredSkusSavedCount = 0;

                    foreach ($value->get_curation_items() as $item) {
                        $suppliers = array_merge($suppliers, $item->get_suppliers());
                        if($pageType == "qa" && $item->get_qa_status() != null && $item->get_qa_status()->value() != 1 && $item->get_type()->value() != "shared"){
                            $savedCount++;
                        }else if ($pageType == "curation" && $item->get_type()->value() != "shared" && $item->get_qa_status()->value() == 1) {
                            $savedCount++;
                        }
                    }
                    $value->set_skusTotalCount(count($value->get_curation_items()));
                    $value->set_skusSavedCount($savedCount);
                    $value->set_filteredSkusTotalCount(count($value->get_curation_items()));
                    $value->set_filteredSkusSavedCount($filteredSkusSavedCount);
                    if( $xnId == 0 || $supplier != "" || $countBase != "-1") {

                        $pageNumTemp = ($pageNum == 1 ? 0 : ( ( $pageNum - 1 ) * $limit));
                        $curationItems = array_slice($value->get_curation_items(), (int) $pageNumTemp, $limit);
                        $value->set_curation_items($curationItems);
                    }
                    if ($key < 10 || $supplier != "" || $value->get_title() == $sectionName) {
                        $this->log_info('Populating curation data', ['batchId' => $batchId]);
                        $this->populateCurationData($batchId, [$value], $kitsDecisionSkus);
                        $this->log_info('Populating decision data', ['batchId' => $batchId]);
                        $this->populateAssortmentDecisionData($batchId,  [$value]);
                        $this->log_info('Populating context data', ['batchId' => $batchId]);
                        $this->populateContextData($batchId,  [$value]);
                    }

                    $this->stopSlo($slo);
                    $sectionsNew  = [$value];
                    $myarray = array_merge($myarray, $sectionsNew);
                }
            }
            if ($supplier != "") {
               return array_values($myarray);
            }
            $myarray['suppliers'] = array_values(array_unique($suppliers));
            $myarray['filteredSkusTotalCount'] = $filteredSkusTotalCount;
            $myarray['filteredSkusSavedCount'] = $filteredSkusSavedCount;
            return $myarray;
        }

        return $sections;
    }

  /**
   * @param int $batchId Batch ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
   */
  public function getSections(int $batchId) : array {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    try {
      $slo->withTags(['batchId' => $batchId]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    $this->log_info('Loading item groups', ['batch_id' => $batchId]);
    $groups = $this->itemGroupLoader->load($batchId, 0, 0, 0, "", -1);

    $collectionIds    = [];
    $kitsDecisionSkus = [];
    foreach ($groups as $idx => $group) {
      $collectionIds[]  = $group->getItems()[0]->get_context_xn_id();
      $kitsDecisionSkus = array_merge($kitsDecisionSkus, $this->getKitsWithDecisionSkus($group));
    }

    $collections = $this->getCollectionNames($collectionIds);
    $this->log_info('Loaded collections', ['collections' => $collections]);

    $sectionsKits        = [];
    $sectionsCollections = [];
    $sectionsSimple      = [];

    $lastGroupIndex = 1;
    foreach ($groups as $idx => $group) {
      if ($group->isKitGroup()) {
        $this->log_info('Creating kit group section', ['batch_id' => $batchId, 'skus' => $group->getSkus()]);
        $sectionsKits[] = $this->createKitGroupingSection('Kits & Composites ' . ($lastGroupIndex++), $group);
      } elseif ($group->isCollectionGroup()) {
        $collectionId          = $group->getItems()[0]->get_context_xn_id();
        $this->log_info('Creating collection group section', ['batch_id' => $batchId, 'collection_id' => $collectionId, 'skus' => $group->getSkus()]);
        $sectionsCollections[] = $this->createSection(
            'Collection ' . ($collections[$collectionId] ?? '?'),
            $group,
            Curation_Item_Type::candidate(),
            $collectionId
        );
      } else {
        $this->log_info('Creating collection simple section', ['batch_id' => $batchId, 'skus' => $group->getSkus()]);
        $sectionsSimple[] = $this->createSection('Simple SKUs', $group, Curation_Item_Type::simple(), 1);
      }
    }

    $sections = array_merge($sectionsSimple, $sectionsCollections, $sectionsKits);
    $this->log_info('Populating curation data', ['batchId' => $batchId]);
    $this->populateCurationData($batchId, $sections, $kitsDecisionSkus);
    $this->log_info('Populating decision data', ['batchId' => $batchId]);
    $this->populateAssortmentDecisionData($batchId, $sections);
    $this->log_info('Populating context data', ['batchId' => $batchId]);
    $this->populateContextData($batchId, $sections);
    
    $this->stopSlo($slo);

    return $sections;
  }


  /**
   * TODO: Look to refactor iterations in this function as kits that appeear before children might not be populated properly
   * TODO: There is a lot of nested looping making this algorigthm O(n^3) which can most likely be improved
   *
   * @param int                                                                      $batchId          Batch ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections         Sections
   * @param array                                                                    $kitsDecisionSkus Kits Decision SKUs
   *
   * @return void
   */
  private function populateCurationData(int $batchId, array $sections, array $kitsDecisionSkus) {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    try {
      $slo->withTags(['batchId' => $batchId, 'numberOfSection' => count($sections)]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    $skus = [];

    foreach ($sections as $section) {
      $sectionSkus = [];
      foreach ($section->get_curation_items() as $curationItem) {
          $sectionSkus[] = $curationItem->get_sku();
          $skus[] = $curationItem->get_sku();
      }
      $this->log_info('Section data', ['batchId' => $batchId,
          'Section' => $section->get_title(),
          'skus' => $sectionSkus
          ]);
    }

    foreach ($kitsDecisionSkus as $kitParent => $children) {
        $this->log_info('kitsDecisionSkus data', ['batchId' => $batchId,
            'kitParent' => $kitParent,
            'children' => $children
        ]);
      $skus = array_merge($skus, $children);
    }
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $batchCatalogId = $this->dao_psql->get_batch_brand_catalog($batchId);
    } else {
      $batchCatalogId = $this->dao->get_batch_brand_catalog($batchId);
    }
    // We check if the batch belongs to EU catalog as it is easy to determine for EU batches
    // the Canada and US fall into same category of North America
    $batchCatalogRegion = in_array($batchCatalogId, $this->regionEU->getBrandCatalogIds()) ? 'EU' : 'NA';
    $data               = [];
    foreach ($this->get_sku_data_call($batchId,$skus) as $row) {
      $sku        = $row['sku'];
      $data[$sku] = $row;

      $itemCatalogRegion                = in_array($data[$sku]['brand_catalog_id'], $this->regionEU->getBrandCatalogIds()) ? 'EU' : 'NA';
      $data[$sku]['is_wrong_continent'] = !($batchCatalogRegion === $itemCatalogRegion);
    }

    $images = $this->imageLoader->get_main_images_for_skus($skus);

    $prices     = $this->priceLoader->get_prices_for_skus($skus, $batchCatalogId);
    $skuPrices = $this->dao->get_skus_prices($skus);
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        $priceTiers = $this->dao_psql->get_curation_price_tiers_for_skus($prices);
    } else {
        $priceTiers = $this->dao->get_curation_price_tiers_for_skus($prices);
    }


    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        $curationItemSKU = $curationItem->get_sku();

        if (empty($data[$curationItemSKU])) {
          continue;
        }

        $this->populateCurationItem($curationItem, $data[$curationItemSKU]);

        $skuPrice = $prices[$curationItemSKU] ?? null;
        if (($skuPrice == null || sprintf('%.2f', Curation_Price_Loader::FALLBACK_PRICE) === sprintf('%.2f', $skuPrice))
            && array_key_exists($curationItemSKU, $skuPrices)
        ) {
          $skuPrice = $skuPrices[$curationItemSKU];
        }
        // set image id from the images map
        $curationItem->set_image_resource_id($images[$curationItemSKU] ?? null);
        $curationItem->set_sale_price($skuPrice);
        $this->log_info('SKU PRICETIER:' . (string)$priceTiers[$curationItemSKU]);
        $curationItem->set_price_tier($curationItem->get_price_tier() ?? $priceTiers[$curationItemSKU] ?? null);

        if ($curationItem->get_type() !== Curation_Item_Type::kit_parent()) {
          continue;
        }

        $this->populateKitParentDecision($curationItem, $kitsDecisionSkus, $data);
      }
    }

    $this->stopSlo($slo);
  }

  /**
   * @param int                                                                      $batchId  Batch ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Sections
   *
   * @return void
   */
  private function populateContextData(int $batchId, array $sections) {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    try {
      $slo->withTags(['batchId' => $batchId, 'numberOfSection' => count($sections)]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }

    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        if ($curationItem->get_type() === Curation_Item_Type::simple()
            || $curationItem->get_type() === Curation_Item_Type::candidate()
        ) {
          $skus[] = $curationItem->get_sku();
        }
      }
    }

    $contextDataCollection = $this->contextDataService->context_data_for_skus($batchId, $skus);

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        $contextData = $contextDataCollection->get_for_sku($curationItem->get_sku());

        if (!$contextData->is_fully_filled()) {
          continue;
        }

        if (empty($curationItem->get_final_brand_id())
            || $contextData->has_same_manufacturer($curationItem->get_final_brand_id())
        ) {
          $curationItem->set_final_style_id($contextData->get_style_id());
          $curationItem->set_final_sub_style_id($contextData->get_substyle_id());
          $curationItem->set_price_tier($contextData->get_price_tier());
          $curationItem->set_final_brand_id($contextData->get_manufacturer_id());
        }

        //This is automatic exclusion reason and it theoretically should not be exposed to the frontend
        if ($curationItem->get_excluded_reason_id() === Curation_Decision::EXCLUSION_REASON_CONTEXT_SKU) {
          $curationItem->set_excluded_reason_id(null);
        }

        $curationItem->set_type(Curation_Item_Type::context());
      }
    }

    $this->stopSlo($slo);
  }

  /**
   * @param int                                                                      $batchId  Batch ID
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Sections
   *
   * @return void
   */
  public function populateAssortmentDecisionData(int $batchId, array $sections) {
    $slo = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__);
    try {
      $slo->withTags(['batchId' => $batchId, 'numberOfSection' => count($sections)]);
    } catch (\Exception $e) {
      // Don't break the execution if the monitor fails
    }
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        if ($curationItem->get_type() === Curation_Item_Type::simple()
            || $curationItem->get_type() === Curation_Item_Type::candidate()
            || $curationItem->get_type() === Curation_Item_Type::kit_parent()
        ) {
          $skus[] = $curationItem->get_sku();
        }
      }
    }

    $sloAssortmentData = $this->slo->start(self::SLO_SERVICE_NAME . __FUNCTION__.'.get_assortment_data');
    $assortmentDecisionCollection = $this->assortmentCurationDecisionLoader->assortment_decision_data_for_skus($batchId, $skus);
    $sloAssortmentData->stop();

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        $decisionData = $assortmentDecisionCollection->get_for_sku($curationItem->get_sku());

        if ($decisionData->should_move_to_tail_brand()) {
          $curationItem->set_should_move_to_tail_brand(true);

          continue;
        }

        if (!$decisionData->should_move_to_header_brand()) {
          continue;
        }

        $moveToHeaderData = $decisionData->get_move_to_brand_decision_data();
        if (empty($curationItem->get_final_brand_id())
            || $moveToHeaderData->get_target_manufacturer_id() === $curationItem->get_final_brand_id()
        ) {
          $curationItem->set_final_style_id($moveToHeaderData->get_target_style_id());
          $curationItem->set_final_sub_style_id($moveToHeaderData->get_target_substyle_id());
          $curationItem->set_price_tier($moveToHeaderData->get_target_price_tier());
          $curationItem->set_final_brand_id($moveToHeaderData->get_target_manufacturer_id());
          $curationItem->set_should_move_to_header_brand(true);
        }
      }
    }

    $this->stopSlo($slo);
  }

  /**
   * @param array $collectionIds Collection IDs
   *
   * @return array
   */
  private function getCollectionNames(array $collectionIds) : array {
    $collections = [];

    $rows = $this->dao->get_collections($collectionIds);

    foreach ($rows as $row) {
      $collections[$row['id']] = $row['name'];
    }

    return $collections;
  }

  /**
   * @param string                                                                             $name  Name
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group $group Group
   *
   * @return  \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section
   */
  public function createKitGroupingSection(string $name, Curation_Item_Group $group) : Section {
    $curationItems = [];
      $this->log_info("In createKitGroupingSection:", ['group' => $group->getItems()]);
    // load shared components
    foreach ($group->getItems() as $item) {
      // more than 1 parent
      if (count($item->get_kit_parents()) < 2) {
        continue;
      }

      if ($item->get_status() !== Product_Status_Model::PRODUCT_STATUS_KIT_COMPONENT) {
        $item->set_readonly(true);
      }

      $item->set_type(Curation_Item_Type::shared());

      $curationItems[] = $item;
    }
    // load kit parents
    foreach ($this->kitParentGrouper->createKitParents($group) as $kitParent) {
      $item = $this->curationItemFactory->create();
      $item->set_sku($kitParent->sku);

      $item->set_type(Curation_Item_Type::kit_parent());

      $item->set_related_kits($kitParent->related);
      $item->set_readonly(empty($kitParent->related));

      $curationItems[] = $item;
    }
    // add candidates
    foreach ($group->getItems() as $curationItem) {
      // skip kit children
      if (!empty($curationItem->get_kit_parents())) {
        continue;
      }

      $curationItem->set_type(Curation_Item_Type::candidate());

      //As part of MTBW-3109, removing parentkit skus if parentkit sku is candidate(Needs curation)
      $isParentSkuAdded = false;
      $parentSku = '';
      foreach ($curationItems as $item) {
          if ($item->get_sku() == $curationItem->get_sku() && $item->get_type() === Curation_Item_Type::kit_parent()) {
              $isParentSkuAdded = true;
              $parentSku = $item;
              $parentSku->set_type(Curation_Item_Type::candidate());
              $parentSku->set_readonly(false);
              $this->log_info("In createKitGroupingSection: KIT SKU converted as candidate",
                  ['parentSku' => $parentSku]);
              break;
          }
      }
      if ($isParentSkuAdded) {
          continue;
      }

      $curationItems[] = $curationItem;
    }
    $section = new Section();
    $section->set_title($name);
    $section->set_curation_items($curationItems);

    return $section;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group $group Group
   *
   * @return array
   */
  private function getKitsWithDecisionSkus(Curation_Item_Group $group) : array {
    $parentsWithChildren = [];
    foreach ($group->getItems() as $item) {
      // ignore kitsco
      if ($item->is_kitsco()) {
        continue;
      }
      foreach ($item->get_kit_parents() as $parent) {
        $parentsWithChildren[$parent][] = $item->get_sku();
      }
    }

    return $parentsWithChildren;
  }

  /**
   * @param string                                                                             $title Title
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group $group Group
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type     $type  Type
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section
   */
  private function createSection(string $title, Curation_Item_Group $group, Curation_Item_Type $type, int $xnId) : Section {
    $curationItems = [];

    foreach ($group->getItems() as $curationItem) {
      $curationItem->set_type($type);

      $curationItems[] = $curationItem;
    }

    $section = new Section();
    $section->set_title($title);
    $section->set_curation_items($curationItems);
    $section->set_xnId($xnId);

    return $section;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $curationItem Curation_Item
   * @param array                                                                        $kitChildrens Kits Children SKUs
   * @param array                                                                        $curationData Curation Data
   *
   * @return void
   */
  private function populateKitParentDecision(Curation_Item $curationItem, array $kitChildrens, array $curationData) {
    // clear decision in case it was saved
    $this->clearCurationDecision($curationItem);

    if (empty($kitChildrens[$curationItem->get_sku()])) {
      return;
    }

    foreach ($kitChildrens[$curationItem->get_sku()] as $childSku) {
      if (empty($curationData[$childSku])) {
        continue;
      }

      $row = $curationData[$childSku];

      // if it wasn't curated before
      if (empty($row['locked_date'])) {
        continue;
      }

      $curationItem->set_excluded_reason_id($row['excluded_reason_id']);
      $curationItem->set_automatic_excluded_reason($row['automatic_excluded_reason']);
      $curationItem->set_price_tier($row['price_tier']);
      $curationItem->set_final_style_id($row['final_style_id']);
      $curationItem->set_final_sub_style_id($row['final_sub_style_id']);
      $curationItem->set_final_brand_id($row['final_brand_id']);
      $curationItem->set_final_granular_style_id($row['final_granular_style_id']);
      $curationItem->set_brand_catalog_name($row['brand_catalog_name']);
      $curationItem->set_saved_by($row['locked_employee_name']);
      $curationItem->set_decision_source_id($row['decision_source_id']);
      $curationItem->set_saved_at($row['locked_date']);
      $curationItem->set_qa_status($this->createCurationQaStatus($row['qa_status_id']));
      $curationItem->set_is_wrong_continent($row['is_wrong_continent']);
      $curationItem->set_is_hold_out_manufacturer($row['is_hold_out_manufacturer']);
      // only show rejection if the last operation was a reject
      if (!empty($row['last_qa_at']) && $curationItem->get_qa_status() === Curation_QA_Status::rejected()) {
        $curationItem->set_rejected_at($row['last_qa_at']);
        $curationItem->set_rejected_by($row['last_qa_by']);
        $curationItem->set_rejected_note($row['last_qa_note']);
      }
    }
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $curationItem Curation_Item
   *
   * @return void
   */
  private function clearCurationDecision(Curation_Item $curationItem) {
    $curationItem->set_excluded_reason_id(null);
    $curationItem->set_final_style_id(null);
    $curationItem->set_final_sub_style_id(null);
    $curationItem->set_final_brand_id(null);
    $curationItem->set_final_granular_style_id(null);
    $curationItem->set_brand_catalog_name('');
    $curationItem->set_saved_at(null);
    $curationItem->set_saved_by(null);
    $curationItem->set_decision_source_id(null);
    $curationItem->set_rejected_at(null);
    $curationItem->set_rejected_by(null);
    $curationItem->set_rejected_note(null);
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item $item Curation_Item
   * @param array                                                                        $row  Row
   *
   * @return void
   */
  private function populateCurationItem(Curation_Item $item, array $row) {
    $item->set_name($row['product_name']);
    $item->set_class_id($row['class_id']);
    $item->set_class_name($row['class_name']);
    $item->set_manufacturer_name($row['manufacturer_name']);
    $item->set_manufacturer_brw_id($row['manufacturer_brw_id']);
    $item->set_brand_type($row['brand_type']);
    $item->set_brand_name($row['brand_name']);
    $item->set_excluded_reason_id($row['excluded_reason_id']);
    $item->set_automatic_excluded_reason($row['automatic_excluded_reason']);
    $item->set_price_tier($row['price_tier']);
    $item->set_final_style_id($row['final_style_id']);
    $item->set_final_sub_style_id($row['final_sub_style_id']);
    $item->set_final_granular_style_id($row['final_granular_style_id']);
    $item->set_final_brand_id($row['final_brand_id']);
    $item->set_brand_catalog_name($row['brand_catalog_name']);
    $item->set_saved_by($row['locked_employee_name']);
    $item->set_decision_source_id($row['decision_source_id']);
    $item->set_saved_at($row['locked_date']);
    $item->set_price_options_count($row['price_options_count']);
    $item->set_last_clone_date(new Last_Clone_Date($row['last_clone_date']));
    $item->set_qa_status($this->createCurationQaStatus($row['qa_status_id']));
    $item->set_rejected_at($item->is_rejected() ? $row['last_qa_at'] : null);
    $item->set_rejected_by($item->is_rejected() ? $row['last_qa_by'] : null);
    $item->set_rejected_note($item->is_rejected() ? $row['last_qa_note'] : null);
    $item->set_is_wrong_continent($row['is_wrong_continent']);
    $item->set_is_hold_out_manufacturer($row['is_hold_out_manufacturer']);

    /**
     * @todo this might not be required, since Kitsco items would have been already flagged
     */
    $item->set_kitsco($row['is_kitsco'] ?? false);
  }

  /**
   * @param int $option the option from the database for the corresponding value
   *
   * @return Curation_QA_Status
   */
  private function createCurationQaStatus($option): Curation_QA_Status {
    try {
      $status = Curation_QA_Status::create($option);
    } catch (\InvalidArgumentException $exception) {
      $this->log_throwable_error(
          $exception,
          sprintf(
              'Value %s is not supported QA Status',
              $option
          )
      );
      throw $exception;
    }

    return $status;
  }

  /**
   * @param int $batchId batchId
   * @param array $skus skus
   * @return array
   */

  public function get_sku_data_call(int $batchId, array $skus): array {

    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $sku_data = $this->dao_psql->get_sku_data($batchId, $skus);
    } else {
      $sku_data = $this->dao->get_sku_data($batchId, $skus);
    }
    return $sku_data;

  }

  /**
   * @param SLOReceiver $slo SLO
   *
   * @return void
   */
  private function stopSlo(SLOReceiver $slo): void {
    try {
      $slo->stop();
    } catch (\Throwable $exception) {
      $this->log_throwable_error(
          $exception,
          'SLOReceiver already stop()-ped'
      );
      // Don't break the execution if the monitor fails
    }
  }
}
