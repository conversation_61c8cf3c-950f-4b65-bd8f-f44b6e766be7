/**
 * Sections of the Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import Translation from '@wayfair/translation';
import QASkuRow from './qa_sku_row';
import QASharedSkuRow from './qa_shared_sku_row';
import QAPageHeader from './qa_page_header';
import CurationToolShapes, {PAGE_LIMIT} from './curation_tool_shapes';

import {Loading} from '@homebase/core';
import {SKU_TYPE} from './curation_sku_constants';
import WithRejectionPopupHoc from './qa_with_rejection_popup_hoc';
import {Text, TEXT_ALIGNMENTS, TEXT_STYLE} from '@wayfair/homebase-extranet';
import CurationPagination from "./CurationPagination";

const isRowSelected = (selectedRows, sku) => selectedRows.includes(sku);

const isRowChanged = (changedRows, sku) => changedRows.includes(sku);

// const getQAedSkusCount = curationItems =>
//   curationItems.filter(
//     item => item.qaStatus !== QA_STATUS_PENDING && item.type !== SKU_TYPE.SHARED
//   ).length;
//
// const getSkusTotalCount = curationItems =>
//   curationItems.filter(item => item.type !== SKU_TYPE.SHARED).length;

const mapSKUTypesToProps = (curationItems, savingState) => {
  const typeMap = {
    [SKU_TYPE.SIMPLE]: 'simpleSKUs',
    [SKU_TYPE.SHARED]: 'sharedSKUs',
    [SKU_TYPE.KIT_PARENT]: 'kitParents',
    [SKU_TYPE.CONTEXT]: 'contextSKUs',
    [SKU_TYPE.CANDIDATE]: 'candidates',
  };

  const initial = {
    simpleSKUs: [],
    sharedSKUs: [],
    kitParents: [],
    contextSKUs: [],
    candidates: [],
  };

  return curationItems.reduce((acc, current) => {
    current.isSaving = savingState.isSaving && savingState.skusSaving.includes(current.sku);
    const mapTypeTo = typeMap[current.type];
    acc[mapTypeTo].push(current);

    return acc;
  }, initial);
};

const QAPageSubsections = ({
  curationConfig,
                             savingState,
  simpleSKUs,
  sharedSKUs,
  kitParents,
  contextSKUs,
  candidates,
  onSelectionChange,
  onSave,
  onCurationItemDecisionChange,
  selectedRows,
  changedRows,
  isAutomaticCurationPostQaEnabled,
  isAssortmentWorkflowOffshoreUser,
  suggestedStyleRejectionReasons,
  skusTotalCount,
  fetchSectionDataFromApi,
  isLoading,
  error,
  currentPage
}) => {
    return (
        <div>
          {skusTotalCount > PAGE_LIMIT && <CurationPagination
              fetchSectionDataFromApi={(pageNum, limit) =>
                  fetchSectionDataFromApi(pageNum, limit)
              }
              totalSkus={skusTotalCount}
              currentPage={currentPage}
          />}
          {isLoading ? <Loading text="Loading" /> :
          <>
            {error &&
                <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                  {error}
                </Text> }
          {simpleSKUs.map(item => (
              <QASkuRow
                  curationConfig={curationConfig}
                  key={item.sku}
                  curationItem={item}
                  isChanged={isRowChanged(changedRows, item.sku)}
                  isSelected={isRowSelected(selectedRows, item.sku)}
                  onSelectionChange={onSelectionChange}
                  onSave={onSave}
                  isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
                  onCurationItemDecisionChange={onCurationItemDecisionChange}
                  isAssortmentWorkflowOffshoreUser={isAssortmentWorkflowOffshoreUser}
                  suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
              />
          ))}
          {sharedSKUs.length > 0 && (
              <div>
                <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.QAPageSubsectionsSharedComponents"/>
                </Text>
                {sharedSKUs.map(item => (
                    <QASharedSkuRow
                        key={item.sku}
                        curationItem={item}
                        onSave={onSave}
                        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
                    />
                ))}
              </div>
          )}
          {kitParents.length > 0 && (
              <div>
                <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.QAPageSubsectionsKits"/>
                </Text>
                {kitParents.map(item => (
                    <QASkuRow
                        curationConfig={curationConfig}
                        key={item.sku}
                        curationItem={item}
                        isChanged={isRowChanged(changedRows, item.sku)}
                        isSelected={isRowSelected(selectedRows, item.sku)}
                        onSelectionChange={onSelectionChange}
                        onSave={onSave}
                        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
                        onCurationItemDecisionChange={onCurationItemDecisionChange}
                        isAssortmentWorkflowOffshoreUser={isAssortmentWorkflowOffshoreUser}
                        suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
                    />
                ))}
              </div>
          )}
          {contextSKUs.length > 0 && (
              <div>
                <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.QAPageSubsectionsContextSkus"/>
                </Text>
                {contextSKUs.map(item => (
                    <QASkuRow
                        curationConfig={curationConfig}
                        key={item.sku}
                        curationItem={item}
                        isChanged={isRowChanged(changedRows, item.sku)}
                        isSelected={isRowSelected(selectedRows, item.sku)}
                        onSelectionChange={onSelectionChange}
                        onSave={onSave}
                        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
                        onCurationItemDecisionChange={onCurationItemDecisionChange}
                        isAssortmentWorkflowOffshoreUser={isAssortmentWorkflowOffshoreUser}
                        suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
                    />
                ))}
              </div>
          )}
          {candidates.length > 0 && (
              <div>
                <Text align={TEXT_ALIGNMENTS.CENTER} size={Text.FONT_SIZES.LARGE} fontStyle={TEXT_STYLE.BOLD}>
                  <Translation msgid="CurationTool.QAPageSubsectionsCandidateSkus"/>
                </Text>
                {candidates.map(item => (
                    <QASkuRow
                        curationConfig={curationConfig}
                        key={item.sku}
                        curationItem={item}
                        isChanged={isRowChanged(changedRows, item.sku)}
                        isSelected={isRowSelected(selectedRows, item.sku)}
                        onSelectionChange={onSelectionChange}
                        onSave={onSave}
                        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
                        onCurationItemDecisionChange={onCurationItemDecisionChange}
                        isAssortmentWorkflowOffshoreUser={isAssortmentWorkflowOffshoreUser}
                        suggestedStyleRejectionReasons={suggestedStyleRejectionReasons}
                    />
                ))}
              </div>
          )}
          </>
          }
          {skusTotalCount > PAGE_LIMIT && <CurationPagination
              fetchSectionDataFromApi={(pageNum, limit) =>
                  fetchSectionDataFromApi(pageNum, limit)
              }
              totalSkus={skusTotalCount}
              currentPage={currentPage}
          />}
        </div>
    )
}

QAPageSubsections.propTypes = {
  curationConfig: CurationToolShapes.curationConfigShape.isRequired,
  simpleSKUs: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  sharedSKUs: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  kitParents: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  contextSKUs: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  candidates: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  onSelectionChange: PropTypes.func.isRequired,
  changedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSave: PropTypes.func.isRequired,
  selectedRows: PropTypes.arrayOf(PropTypes.string).isRequired,
  isAutomaticCurationPostQaEnabled: PropTypes.bool.isRequired,
  onCurationItemDecisionChange: PropTypes.func.isRequired,
  isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
  suggestedStyleRejectionReasons: PropTypes.arrayOf(
    CurationToolShapes.reasonShape
  ).isRequired,
  skusTotalCount:PropTypes.number.isRequired,
  fetchSectionDataFromApi: PropTypes.func.isRequired,
  isLoading:PropTypes.bool.isRequired,
  error: PropTypes.string.isRequired,
  currentPage: PropTypes.number.isRequired
};

const withSKUTypesMappedToProps = WrappedComponent => {
  const WithSKUTypesMappedToProps = ({curationItems, savingState, ...passthroughProps}) => (
    <WrappedComponent
      {...passthroughProps}
      {...mapSKUTypesToProps(curationItems, savingState)}
    />
  );

  WithSKUTypesMappedToProps.propTypes = {
    curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
      .isRequired,
    savingState: PropTypes.object.isRequired
  };

  return WithSKUTypesMappedToProps;
};

const QAPageSubsectionsWithSKUTypes = withSKUTypesMappedToProps(
  QAPageSubsections
);

const withQAPageHeader = WrappedComponent => {
  const WithQAPageHeader = ({
    title,
    curationItems,
    onSelectAll,
    onSaveAllClick,
    checkedIndeterminate,
    checkedAll,
    isExpanded,
    onExpandClick,
    isReadOnlyMode,
    isAutomaticCurationPostQaEnabled,
    skusTotalCount,
    skusSavedCount,
    fetchSectionDataFromApi,
    isLoading,
    error,
    currentPage,
    ...passthroughProps
  }) => {
    return (
    <div>
      <QAPageHeader
        title={title}
        onSelectAll={onSelectAll}
        onSaveAllClick={onSaveAllClick}
        checkedAll={checkedAll}
        checkedIndeterminate={checkedIndeterminate}
        isExpanded={isExpanded}
        onExpandClick={onExpandClick}
        // skusTotalCount={getSkusTotalCount(curationItems)}
        // skusSavedCount={getQAedSkusCount(curationItems)}
        skusTotalCount={skusTotalCount}
        skusSavedCount={skusSavedCount}
        isReadOnlyMode={isReadOnlyMode}
        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
        fetchSectionDataFromApi={fetchSectionDataFromApi}
      />
      <WrappedComponent
        {...passthroughProps}
        curationItems={curationItems}
        isExpanded={isExpanded}
        isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
        fetchSectionDataFromApi={fetchSectionDataFromApi}
        isLoading={isLoading}
        error={error}
        currentPage={currentPage}
        skusTotalCount={skusTotalCount}
      />
    </div>
  )};

  WithQAPageHeader.propTypes = {
    title: PropTypes.string.isRequired,
    curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
      .isRequired,
    checkedIndeterminate: PropTypes.bool.isRequired,
    checkedAll: PropTypes.bool.isRequired,
    isExpanded: PropTypes.bool,
    onSelectAll: PropTypes.func.isRequired,
    onSelectionChange: PropTypes.func.isRequired,
    onSaveAllClick: PropTypes.func.isRequired,
    onExpandClick: PropTypes.func.isRequired,
    isReadOnlyMode: PropTypes.bool,
    isAutomaticCurationPostQaEnabled: PropTypes.bool,
    skusTotalCount:PropTypes.number.isRequired,
    skusSavedCount: PropTypes.number.isRequired,
    fetchSectionDataFromApi: PropTypes.func.isRequired,
    isLoading:PropTypes.bool.isRequired,
    error: PropTypes.string.isRequired,
    currentPage: PropTypes.number.isRequired
  };

  WithQAPageHeader.defaultProps = {
    isExpanded: true,
    isReadOnlyMode: false,
    isAutomaticCurationPostQaEnabled: false,
  };

  return WithQAPageHeader;
};

const QAPageSection = ({
  curationItems,
                         savingState,
  isExpanded,
  isAutomaticCurationPostQaEnabled,
  fetchSectionDataFromApi,
  skusTotalCount,
  isLoading,
  error,
  currentPage,
  ...passthroughProps
}) => (
  isExpanded && (
      <QAPageSubsectionsWithSKUTypes
          curationItems={curationItems}
          savingState={savingState}
          isAutomaticCurationPostQaEnabled={isAutomaticCurationPostQaEnabled}
          fetchSectionDataFromApi={fetchSectionDataFromApi}
          skusTotalCount={skusTotalCount}
          isLoading={isLoading}
          currentPage={currentPage}
          error={error}
          {...passthroughProps}
      />
  )
);

const QAPageSectionWithHeader = withQAPageHeader(QAPageSection);

QAPageSection.propTypes = {
  curationItems: PropTypes.arrayOf(CurationToolShapes.curationItemShape)
    .isRequired,
  isExpanded: PropTypes.bool,
  isAutomaticCurationPostQaEnabled: PropTypes.bool,
  fetchSectionDataFromApi: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string.isRequired,
  skusTotalCount:PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  savingState: PropTypes.object.isRequired
};

QAPageSection.defaultProps = {
  isExpanded: true,
  isAutomaticCurationPostQaEnabled: false,
};

export default WithRejectionPopupHoc(QAPageSectionWithHeader);
