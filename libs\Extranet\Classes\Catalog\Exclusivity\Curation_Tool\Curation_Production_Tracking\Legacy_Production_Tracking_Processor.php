<?php

declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_DAO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Curation_Production_Tracking_PostgreSQL_DAO;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Legacy_Production_Tracking_Processor implements Production_Tracking_Processor{


  /**
   * @var Curation_Production_Tracking_DAO
   */
  private $storage_production_tracking_sql_dao;


  /**
   * @var Curation_Production_Tracking_PostgreSQL_DAO
   */
  private $storage_production_tracking_psql_dao;


  /**
   * @var \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface
   */
  private FeatureTogglesInterface $featureToggles;


  /**
   * Curation_Production_Tracking_Service constructor.
   *
   * @param Curation_Production_Tracking_DAO $storage_cpt_sql_dao  storage_cpt_sql_dao
   * @param Curation_Production_Tracking_PostgreSQL_DAO $storage_cptp_psql_dao  storage_cptp_psql_dao
   * @param FeatureTogglesInterface $featureToggles  featureToggles
   */
  public function __construct(
    Curation_Production_Tracking_DAO $storage_cpt_sql_dao,
    Curation_Production_Tracking_PostgreSQL_DAO $storage_cptp_psql_dao,
    FeatureTogglesInterface $featureToggles

  ) {
    $this->storage_production_tracking_sql_dao = $storage_cpt_sql_dao;
    $this->storage_production_tracking_psql_dao = $storage_cptp_psql_dao;
    $this->featureToggles = $featureToggles;
  }


  /**
   * @param int[] $project_ids Project IDs
   *
   * @return void
   */
  public function update_production_tracking_status(array $project_ids) : void {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT))  {
      $this->storage_production_tracking_psql_dao->update_stage_for_projects($project_ids);
    } else {
      $this->storage_production_tracking_sql_dao->update_stage_for_projects($project_ids);
    }
  }
}
