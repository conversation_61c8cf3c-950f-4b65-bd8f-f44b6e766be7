<?php

namespace App\Application\Exception;

use RuntimeException;
use Throwable;

class NotAuthorizedException extends RuntimeException
{
    public function __construct(string $message = '', int $code = 0, Throwable $previous = null)
    {
        parent::__construct(
            $message !== ''
                ? $message
                : 'User is not authorized to access batch',
            $code,
            $previous
        );
    }
}
