/**
 * Shared layout constants
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 * @flow
 */

export const ALIGNMENT = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right'
};

export const FLEX_KEYWORDS = {
  FLEX_START: 'flex-start',
  CENTER: 'center',
  FLEX_END: 'flex-end',
  SPACE_AROUND: 'space-around',
  SPACE_BETWEEN: 'space-between',
  COLUMN: 'column',
  ROW: 'row',
  ROW_REVERSE: 'row-reverse',
  COLUMN_REVERSE: 'column-reverse',
  WRAP: 'wrap',
  NOWRAP: 'nowrap',
  BASELINE: 'baseline'
};

export const ORDER = {
  ORDER_1: 1,
  ORDER_2: 2,
  ORDER_3: 3,
  ORDER_4: 4,
  ORDER_5: 5,
  ORDER_6: 6,
  ORDER_7: 7,
  ORDER_8: 8,
  ORDER_9: 9,
  ORDER_10: 10,
  ORDER_11: 11,
  <PERSON><PERSON><PERSON>_12: 12
};

export const WIDTHS = {
  WIDTH_1: 1,
  WIDTH_2: 2,
  WIDTH_3: 3,
  WIDTH_4: 4,
  W<PERSON><PERSON>_5: 5,
  WIDTH_6: 6,
  WIDTH_7: 7,
  WIDTH_8: 8,
  WIDTH_9: 9,
  WIDTH_10: 10,
  WIDTH_11: 11,
  WIDTH_12: 12
};
