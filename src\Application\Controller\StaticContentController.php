<?php

declare(strict_types=1);

namespace App\Application\Controller;

use App\Application\Helper\ContentTypeHelperInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function file_get_contents;

class StaticContentController extends AbstractController
{
    private Filesystem $fileSystem;
    private ContentTypeHelperInterface $contentTypeHelper;

    public function __construct(
        Filesystem $filesystem,
        ContentTypeHelperInterface $contentTypeHelper
    ) {
        $this->fileSystem = $filesystem;
        $this->contentTypeHelper = $contentTypeHelper;
    }

    /**
     * @param string $path
     *
     * @Route("/bundles/{path}", name="static_content_bundle", requirements={"path"=".+"})
     * @return Response
     */
    public function bundles(ParameterBagInterface $parameterBag, string $path): Response
    {
        $filePath = $parameterBag->get('kernel.project_dir') . '/public/d/curation-tool/bundles/' . $path;

        return $this->buildResponse($filePath);
    }

    /**
     * @param string $filePath
     *
     * @return Response
     */
    protected function buildResponse(string $filePath): Response
    {
        $response = new Response('File not found!', Response::HTTP_NOT_FOUND);

        if ($this->fileSystem->exists($filePath)) {
            $response->setContent($this->loadFileContents($filePath));
            $response->setStatusCode(Response::HTTP_OK);
            $contentType = $this->contentTypeHelper->getContentType($filePath);
            if (null !== $contentType) {
                $response->headers->set('Content-Type', $contentType);
            }
        }

        return $response;
    }

    public function loadFileContents(string $path): string
    {
        return (string)file_get_contents($path);
    }

    /**
     * @Route("/public/fake-partner-home-layout-api/{path}", name="static_content_layout", requirements={"path"=".+"})
     *
     * @param string $path
     *
     * @return Response
     */
    public function layout(ParameterBagInterface $parameterBag, string $path): Response
    {
        $filePath = $parameterBag->get('kernel.project_dir') . '/public/fake-partner-home-layout-api/' . $path;

        return $this->buildResponse($filePath);
    }
}
