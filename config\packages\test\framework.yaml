framework:
  test: true

  # Enables session support. Note that the session will ONLY be started if you read or write from it.
  # Remove or comment this section to explicitly disable session support.
  session:
    storage_id: session.storage.mock_file

  php_errors:
    log: true

  http_client:
    default_options:
      timeout: 1000
      retry_failed:
        max_retries: 1
        delay: 500
        multiplier: 3
        max_delay: 500
        jitter: 0.3
    scoped_clients:
      style_suggestion_client:
        scope: 'https://kube-style-suggestion-api\.service\.intradsm1\.sdeconsul\.csnzoo\.com'
