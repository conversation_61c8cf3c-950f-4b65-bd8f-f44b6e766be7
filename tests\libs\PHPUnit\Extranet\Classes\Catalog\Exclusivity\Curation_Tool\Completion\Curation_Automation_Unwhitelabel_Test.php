<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Curation_Automation_Unwhitelabel;

class Curation_Automation_Unwhitelabel_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @test
     *
     * @return void
     */
    public function it_creates_curation_automation_unwhitelabel()
    {
        $item = new Curation_Automation_Unwhitelabel(
            123,
            [
                'ABC123'
            ],
            true
        );

        $this->assertInstanceOf(Curation_Automation_Unwhitelabel::class, $item);
        $this->assertEquals(123, $item->get_batch_id());
        $this->assertEquals(['ABC123'], $item->get_skus());
        $this->assertTrue($item->get_unwhitelabel());
    }
}
