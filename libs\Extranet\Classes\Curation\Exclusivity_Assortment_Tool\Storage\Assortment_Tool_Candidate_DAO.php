<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Storage;

use App\Infrastructure\Connection\MerchConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Curation\ExclusivityAssortment\Domain\Enum\ExclusivityAssortmentCurationStatus;
use WF\Curation\ExclusivityAssortment\Domain\Enum\ExclusivityAssortmentDecisionType;
use WF\Curation\ExclusivityAssortment\Infrastructure\Helper\SQLBulkHelper;
use WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader_Storage;
use WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO;
use WF\Shared\Helpers\SQL;
use PDO;

class Assortment_Tool_Candidate_DAO implements
    Exclusivity_Assortment_Tool_Decision_Loader_Storage {

  /**
   * @var MerchConnection
   */
  protected $pdo_merch;

  /**
   * Assortment_Tool_Candidate_DAO constructor.
   *
   * @param MerchConnection $pdo_merch pdo merch
   */
  public function __construct(MerchConnection $pdo_merch) {
    $this->pdo_merch = $pdo_merch;
  }

  /**
   * @param string[]                            $skus             skus
   * @param ExclusivityAssortmentCurationStatus $requested_status status
   *
   * @return array
   */
  public function get_decisions_for_skus(array $skus, ExclusivityAssortmentCurationStatus $requested_status) : array {
    // create temp table to keep same $skus order
    $column_map    = [
        'SKU' => SQL::nvarchar(8),
    ];
    $inserted_data = [];
    foreach ($skus as $sku) {
      $inserted_data[] = [
          'SKU' => $sku,
      ];
    }
    $sql = SQLBulkHelper::get_temp_table_json_sql($column_map, 'tmpCandidateSkus');

    $sql .= '
      IF OBJECT_ID(\'tempdb.dbo.#tmpChildSKUs\') IS NOT NULL
          DROP TABLE #tmpChildSKUs
      CREATE TABLE #tmpChildSKUs (
          ChildSKU nvarchar(8)
      )
      
      INSERT INTO #tmpChildSKUs(ChildSKU)
        SELECT kitComposition.ChildSku as ChildSku
          FROM #tmpCandidateSkus tmp
          JOIN csn_product.dbo.vwExclusivityKitCompositionActive kitComposition WITH (NOLOCK)
          ON kitComposition.ParentSKU = tmp.SKU
                          

    
      DECLARE @decision_move_to_header SMALLINT = :decision_move_to_header
      DECLARE @decision_move_to_tail SMALLINT = :decision_move_to_tail
    ';

    $sql .= '
      SELECT
        candidate.SKU AS sku,
        IIF(candidate.DecisionTypeID = @decision_move_to_header, 1, 0) AS should_move_to_header_brand,
        IIF(candidate.DecisionTypeID = @decision_move_to_tail, 1, 0) AS should_move_to_tail_brand,
        candidate.DecisionManufacturerID AS target_manufacturer_id
      FROM csn_merch_tool.dbo.tblAssortmentCandidate candidate WITH(NOLOCK)
      WHERE 
        (EXISTS (SELECT 1 FROM #tmpCandidateSkus WHERE SKU = candidate.SKU)
          OR EXISTS (SELECT 1 FROM #tmpChildSKUs WHERE ChildSku = candidate.SKU))
        AND candidate.CurationStatusID = :requested_status
        AND candidate.DecisionTypeID IN (@decision_move_to_header, @decision_move_to_tail)
    ';

    $statement = $this->pdo_merch->prepare($sql);
    $statement->bindValue(':requested_status', $requested_status->value(), PDO::PARAM_INT);
    //TODO should all of those be parameters or the hardcode would do?
    $statement->bindValue(':decision_move_to_header', ExclusivityAssortmentDecisionType::move_to_flagship_brand()->value(), PDO::PARAM_INT);
    $statement->bindValue(':decision_move_to_tail', ExclusivityAssortmentDecisionType::move_to_tail_brand()->value(), PDO::PARAM_INT);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($inserted_data), PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed to retreive decisions for skus');
    }

    return $statement->fetchAll(PDO::FETCH_CLASS, Assortment_Decision_DTO::class);
  }
}
