<?php

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\QA;

use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater_Storage;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO;

class Completion_Batch_Status_Updater_Test extends TestCase
{
    use ProphecyTrait;

    /**
     * @var Completion_Batch_Status_Updater_Storage
     */
    private $dao;

    /**
     * @var Batch_Management_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var FeatureTogglesInterface |\Prophecy\Prophecy\ObjectProphecy
     */
    private $featureToggles;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->dao = $this->prophesize(Completion_Batch_Status_Updater_Storage::class);
        $this->dao_psql = $this->prophesize(Batch_Management_Postgres_DAO::class);
        $this->featureToggles = $this->prophesize(FeatureTogglesInterface::class);

        $this->subject = new Completion_Batch_Status_Updater(
            $this->dao->reveal(),
            $this->dao_psql->reveal(),
            $this->featureToggles->reveal()
        );
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_change_status_feature_toggle_off(): void
    {
        $batch_id = 1;
        $status = 1;
        $employee_id = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(false);

        $this->dao->change_status($batch_id, $status, $employee_id)->shouldBeCalled(1);

        $this->subject->changeStatus($batch_id, $status, $employee_id);
    }

    /**
     * @test
     *
     * @return void
     */
    public function test_change_status_feature_toggle_on(): void
    {
        $batch_id = 1;
        $status = 1;
        $employee_id = 1;

        $this->featureToggles->isEnabled(Argument::any())->willReturn(true);

        $this->dao_psql->change_status($batch_id, $status, $employee_id)->shouldBeCalled(1);

        $this->subject->changeStatus($batch_id, $status, $employee_id);
    }
}
