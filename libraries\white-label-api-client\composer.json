{"name": "wayfair/curation-white-label-api-client", "description": "API Client for the White Label Service", "type": "library", "license": "proprietary", "version": "dev-master", "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}, "require": {"php": "^7.4 || ^8.0", "guzzlehttp/guzzle": "~6.0", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "autoload": {"psr-4": {"WF\\Curation\\WhiteLabelApi\\": "src/"}}, "autoload-dev": {"psr-4": {"WF\\Curation\\WhiteLabelApi\\Tests\\": "tests/"}}, "repositories": {"packagist": false, "artifactory-github": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-github", "options": {"ssl": {"verify_peer": false}}}, "artifactory-wayfair": {"type": "composer", "url": "https://artifactorybase.service.csnzoo.com/artifactory/api/composer/composer-wayfair", "options": {"ssl": {"verify_peer": false}}}}, "require-dev": {"phpunit/phpunit": "^9", "phpstan/phpstan": "^1.8", "squizlabs/php_codesniffer": "^3.5"}, "scripts": {"comment": ["The scripts here allow you to locally run the same checks that the pipelines will run.", "You are welcome to add your own, but please don't modify `phpunit`, `phpstan` or `checker`.", "And please make sure the `verify` script includes at least those three.", "Note that we use the `php -d` format instead of calling the binary directly so the scripts", " won't fail on dev VMs, where auto_prepend.php will get run by default."], "verify": ["@phpunit", "@phpstan", "@checker"], "test": "@phpunit", "phpunit": "php -d auto_prepend_file=Off vendor/bin/phpunit --verbose", "phpstan": "php -d auto_prepend_file=Off vendor/bin/phpstan analyze -c phpstan.neon --memory-limit=-1 ./", "checker": "php -d auto_prepend_file=Off vendor/bin/phpcs src --standard=PSR12"}}