<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches;

interface Curation_Only_Batch_Service_Storage {
  /**
   * @return int
   */
  public function create_verification_id() : int;

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   * @param bool $fromPWL from partial white label
   *
   * @return void
   */
  public function mark_batch_as_sent(int $batch_id, int $verification_id, bool $fromPWL) : void;

  /**
   * @param int $batch_id        Batch ID
   * @param int $verification_id Verification ID
   *
   * @return void
   */
  public function set_verification_id_for_skus(int $batch_id, int $verification_id) : void;
}
