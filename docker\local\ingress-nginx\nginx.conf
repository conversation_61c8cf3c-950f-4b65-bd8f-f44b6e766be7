worker_processes 1;
pid  /var/run/nginx.pid;

events {
  worker_connections  8000;
}

http {
  include       mime.types;
  default_type  application/octet-stream;
  server_names_hash_max_size 1024;

  client_max_body_size 32M;
  client_body_buffer_size 128k;

  sendfile     on;
  keepalive_timeout  60;
  gzip         on;
  gzip_types   application/json application/x-javascript image/x-icon text/css text/javascript text/json text/plain text/xml;

  server {
    server_name devbox;
    listen 0.0.0.0:8380 default_server;
    access_log /proc/self/fd/2;
    error_log /proc/self/fd/2;
    location ~ \.php {
      include              fastcgi_params;
      fastcgi_pass         devbox:9000;
      fastcgi_read_timeout 120;
      fastcgi_param        SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    root /app/public;
    index index.php;

    location / {
        try_files /index.php$is_args$args /index.php$is_args$args;
    }
  }

### This section should be enabled only when "prod" container will be started
#   server {
#     server_name prod;
#     listen 0.0.0.0:8480;
#     access_log /proc/self/fd/2;
#     error_log /proc/self/fd/2;
#     location ~ \.php {
#       include       fastcgi_params;
#       fastcgi_pass  prod:9000;
#       fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#     }
#     root /app/public;
#     index index.php;
#
#     location / {
#         try_files /index.php$is_args$args /index.php$is_args$args;
#     }
#   }
}
