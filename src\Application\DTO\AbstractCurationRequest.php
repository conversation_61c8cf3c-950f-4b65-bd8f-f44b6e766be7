<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\DTO;

use function json_decode;
use const JSON_THROW_ON_ERROR;

abstract class AbstractCurationRequest
{
    protected const FIELD_BATCH_ID = 'batch_id';

    protected int $batchId;

    public function __construct(?int $batchId = 0)
    {
        $this->batchId = (int)$batchId;
    }

    public function getBatchId(): int
    {
        return $this->batchId;
    }

    /**
     * @param string $json
     * @return AbstractCurationRequest|static
     * @throws \JsonException
     */
    public static function fromJSON(string $json)
    {
        return static::fromArray(json_decode($json, true, 512, JSON_THROW_ON_ERROR));
    }

    /**
     * @param array $params
     * @return self|static
     */
    abstract public static function fromArray(array $params);
}
