# Development guide

## Integrations built-in

Integrations pre-configured and cooked inside. You can customize them too.

### docker-compose file

Docker images are managed locally using docker-compose.yml, by default these services are available:
- `devbox` local instance of your application, pre-configured with less cache and code mounted inside
- `prod` local version of the container will be deployed to dev & prod kubernetes

All services are using one Dockerfile in `docker/Dockerfile` using [multi-stage builds](https://docs.docker.com/develop/develop-images/multistage-build/)

### Composer

`composer.json` and `composer.lock` already exist for your application. DO NOT run composer commands directly from your local machine. Run them from inside the docker container.

- to add new package , `make bash` then `composer require my/name`
- to remove existing package , `make bash` then `composer remove my/name`
- to update existing package , `make bash` then `composer update my/name`


There are also some short cut scripts you can use inside the container. Run `make bash` and then
- `composer verify` to run all tests
- `composer ecs-fix` to fix PHP CS sniffers errors automatically
- `composer phpstan` to run phpstan only
- `composer phpunit` to run phpunit only

The changes done inside `make bash` container will be reflected on your local filesystem.


### Symfony

- Check [PHPStorm Symfony Plugin](https://www.jetbrains.com/help/phpstorm/symfony-support.html)
- Check Symfony docs
      - [Fundamentals](https://symfony.com/doc/current/quick_tour/the_big_picture.html#fundamentals-route-controller-response)
      - [Type of dependency injections](https://symfony.com/doc/current/service_container/injection_types.html)
      - [Services & Autowiring](https://symfony.com/doc/current/quick_tour/the_architecture.html#services-autowiring)
- Symfony comes with a local powerful profiler, to inspect your individual requests details, check http://127.0.0.1:8380/_profiler/
- Symfony comes with a local command line you can do various tasks in Symfony, run `make bash` then `bin/console`
- Symfony have hundreds of integrations you can use in 10 minutes to save your precious time, check [packagist](https://packagist.org/?query=bundle) and [KNP Bundles](http://knpbundles.com/)


### Tests

There are dozens of packages to help you write all kinds of testing
to run all tests, run
```bash
make tests
```


### Continuous deployment

This application has continuous deployment included and controlled with [k8s.yaml](../k8s.yaml) file and triggered with [buildkite piplines](../.buildkite).
Check the [base chart docs](https://github.com/wayfair-shared/k8s-helm/blob/master/charts/base-php/docs/usage.md)

- [Testing guide by symfony](https://symfony.com/doc/current/testing.html)
- [How to Test Code that Interacts with the Database](https://symfony.com/doc/current/testing/database.html#resetting-the-database-automatically-before-each-test)


#### Included checks

The following tools are included and configured by default:

* `phpunit` - Unit and integration tests
* `ecs` - "Easy Coding Standards" with default Wayfair PHP sniffs
* `SonarQube` - Code quality checks
* `phpstan` - Static code analysis catches many common errors and bad practices

#### Karate

Karate is becoming the standard API testing suite used across languages at
Wayfair. It is not included in this template, but you can find instructions for
adding it to your application as a docker-compose service here:
[Karate docker-compose quickstart](https://docs.csnzoo.com/test-enablement/handbook/apiTest/karate/quick-start-docker-compose/)

### Wayfair logging

[php-core-logging-symfony-bundle](https://github.com/wayfair-secure/php-core-logging-symfony-bundle) is already integrated,

To send logs, you can inject the `Psr\Log\LoggerInterface` to any class in your constructor and start using it
please check Symfony docs section for dependency injection

Also check `src/Controller/CoreLibraryDemoController.php` for example demo



## Core Library Integrations

Core Libraries are already included in your application. See the demo controllers for sample usage:

* envconfig: /core-library-demo/envconfig
* metrics: /core-library-demo/metrics
* secrets: /core-library-demo/secrets
* health-checks: /internal/healthcheck/run


### Make commands

`make` comes with lots of pre defined commands in your `Makefile` in the project root, some

- `make` will start your local dev application locally on port 8380
- `make verify` will run all tests , similar to what run in the piplines
- `make bash` will open bash in your started container
- `make prod` will create a production instance of your application and start it locally on port 8480
- `make logs` will get last 200 lines of all containers logs

you can add your own too


## Predefined routes

The following endpoints will be also available in addition to the core library demo endpoints listed below:
- `/internal/ping` a test end point to test the application is alive and responding


You can list all routes by, dropping into bash `make bash` and use symfony console via `bin/console debug:router`

## Monitoring and observability

Monitoring and Observing application error rate, failures and latency is the development team responsibility and it's very important

### Datadog
Datadog help you to inspect latency, number of queries executed, curls performed and more for individual request.
You can also setup monitoring directly.

Your application (all environments) is already configured with datadog

* Check [Wayfair Datadog Training materials](https://infohub.corp.wayfair.com/display/EN/Datadog+APM#DatadogAPM-TrainingResources)
* Check [Wayfair docs on Datadog, on how to add more metrics/spans](https://infohub.corp.wayfair.com/display/EN/PHP+APM)


### Logs

All applications logs, not just yours, are sent to Kibana.

* Kubernetes production logs
    * All logs emitted in your application itself, will be sent to [kibanaapplog](https://kibanaapplog.csnzoo.com/app/kibana)
    * All container logs, will be sent to [kibanak8s](https://kibanak8s.csnzoo.com/)
* Kubernetes dev logs
    * All logs emitted in your application itself, will be sent to [devkibanaapplog](https://devkibanaapplog.csnzoo.com/app/kibana)
    * All container logs, will be sent to [devkibanak8s](https://devkibanak8s.csnzoo.com/)


## Continuous Delivery

The generated template is setup for continuous deployment of the master branch to production.  If you would prefer continuous delivery for master, with one-click deploys, you can use the following pipeline step instead of the current "Deploy to Production" step:

```
- block: ":shipit: Deploy to Production"
    branches: "master"
  - label: ":rocket: Deploy to Production"
    branches: "master"
    plugins:
      - ssh://**************/wayfair-secure/deploy-buildkite-plugin#v1.3.3:
          environment: "prod"
    agents:
      queue: docker_prod
```

## Addons Package

### Doctrine

Doctrine is set of PHP libraries primarily focused on providing persistence services and related functionality`

- [Doctrine documentations](https://symfony.com/doc/current/doctrine.html)
- [Doctrine Associations / Relations](https://symfony.com/doc/current/doctrine/associations.html)
- [How to Use Doctrine DBAL to execute raw queries](https://symfony.com/doc/current/doctrine/dbal.html)
- [How to Generate Entities from an Existing Database](https://symfony.com/doc/current/doctrine/reverse_engineering.html)

Please note that in k8s, we can not add passwords as plain text environment variables in the application helm charts
they are always add them as secrets in hashicorp vault and the hash corp vault add the secrets to a regular file and mount it inside the application

## Getting help

For general help with your decoupled Symfony API (HTTP) application, check the docs first:

* [Deploying your application to Kubernetes](https://docs.csnzoo.com/php/decentralizing/how-to-start-guide/)
* [Symfony documentation](https://symfony.com/doc)
* [Wayfair PHP Core Libraries](https://docs.csnzoo.com/php/decentralizing/discovery.html#core-libraries)
* [Datadog Documentation](https://docs.csnzoo.com/php/docs/tracing/index.html)

If you still have questions, reach out to `#php-decoupling-forum` on Slack
