framework:
    secret: '%env(APP_SECRET)%'

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        handler_id: ~
        cookie_secure: auto
        cookie_samesite: lax
        storage_factory_id: session.storage.factory.native

    #esi: true
    #fragments: true
    php_errors:
        log: true

    # For documentation about cache, please check symfony docs
    # https://symfony.com/doc/current/cache.html
    cache:
        # Put the unique name of your app here: the prefix seed
        # is used to compute stable namespaces for cache keys.
        #prefix_seed: your_vendor_name/app_name

        # The app cache caches to the filesystem by default.
        # Other options include:

        # Redis
        #app: cache.adapter.redis
        #default_redis_provider: redis://localhost

        # APCu (not recommended with heavy random-write workloads as memory fragmentation can cause perf issues)
        #app: cache.adapter.apcu
    http_client:
        default_options:
            timeout: 10000
            retry_failed:
                max_retries: 3
                delay: 1000
                multiplier: 3
                max_delay: 6000
                jitter: 0.3
        scoped_clients:
            style_suggestion_client:
                scope: 'https://kube-style-suggestion-api\.service\.intraiad1\.consul\.csnzoo\.com'
                retry_failed:
                    max_retries: 4
