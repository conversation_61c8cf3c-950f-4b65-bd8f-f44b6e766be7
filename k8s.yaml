# Check usage: https://github.com/wayfair-shared/k8s-helm/blob/master/charts/base-php/docs/usage.md#usage
# and examples: https://github.com/wayfair-shared/k8s-helm/tree/master/charts/base-php/examples
version: 1.0
template:
  name: base-php
  version: 5.8.0

deploy:
  applicationName: brand-workflows-curation-tool
  applicationNamespace: brand-workflows-curation-tool
  applicationNamespaceLabels:
    istio.io/rev: default
  maintainer: "Brand Workflows <<EMAIL>>"
  image:
    repository: 'wayfair/brand-workflows-curation-tool-service'
  httpService:
    disableHTTPS: true #shared domains' certs are handled via external load balancers' certs, not by individual apps
    ingressPath: "/d/curation-tool"
    readinessProbe:
      path: /internal/ping
      initialDelaySeconds: 60
      periodSeconds: 60
      timeoutSeconds: 2
      failureThreshold: 5
    livenessProbe:
      path: /internal/ping
      initialDelaySeconds: 60
      periodSeconds: 60
      timeoutSeconds: 2
      failureThreshold: 5
  wfDependencies:
    injectAtStart: true
  istio:
    enabled: true
    peerAuthentication:
      enabled: true
    requestAuthentication:
      enabled: true
    authorizationPolicy:
      enabled: false
      action: "ALLOW"
      rules: []

# Checks sites config here https://github.com/wayfair-shared/k8s-deploy-helper/#sites
sites:

  gke-sdeprod-c1-dsm1:
    applicationNamespaceAnnotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    istio:
      gateway:
        external:
          enabled: true
          wafConfig: "SecRuleEngine Off"
    httpService:
      preAllocatedHostname: "partnerswayfaircom.csnzoo.com"
      replicaCount: 4
      resources:
        limits:
          cpu: "8000m"
          memory: "3072Mi"
        requests:
          cpu: "6000m"
          memory: "2048Mi"
    environment:
      - name: DB_USER
        value: "svc_brand_workflows_curation_tool_app"
      - name: DB_PASSWORD_SECRET_NAME
        value: "svc_brand_workflows_curation_tool_app"
      - name: WL_API_CLIENT_ID
        value: "curation_tool_client_id_for_white_label_nextgen_api"
      - name: WL_API_CLIENT_SECRET
        value: "curation_tool_client_for_white_label_nextgen_api"
      - name: TALENT_API_CLIENT_ID
        value: "ah-brand-workflows-curation-tool-talent-reader"
      - name: TALENT_API_CLIENT_SECRET
        value: "ah-brand-workflows-curation-tool-talent-reader"
      - name: DD_SERVICE_NAME
        value: "brand-workflows-curation-tool"
      - name: WF_ENV
        value: "dev"
      - name: APP_ENV
        value: "prod"
      - name: APP_DEBUG
        value: "true"
      - name: DD_LOGS_INJECTION
        value: "true"
      - name: "DD_ENV"
        value: "dev"
      - name: COST_API_CLIENT_ID
        value: "curation_tool_client_id_for_cost_api"
      - name: COST_API_CLIENT_SECRET
        value: "curation_tool_client_secret_for_cost_api"
      - name: CURATION_DB_USER_PASSWORD_POSTGRES
        value: "curation-db-user-password"
    cloudsql:
      enabled: true
      tag: "2.2.1"
      # This is the `alias` value shown in the output of the Backstage workflow you used to request the SPDB.
      connectionString: "wf-gcp-us-btbb-bc-dev:us-central1:btbb-bc-curation-51e9aaac"
      replicaCount: 4


  gke-prod-c2-iad1:
    applicationNamespaceAnnotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    istio:
      gateway:
        external:
          enabled: true
          wafConfig: "SecRuleEngine Off"
    httpService:
      preAllocatedHostname: "partners.wayfair.com"
      resources:
        limits:
          cpu: "8000m"
          memory: "8192Mi"
        requests:
          cpu: "6000m"
          memory: "6144Mi"
      hpa:
        minReplicas: 4
        maxReplicas: 8
    environment:
      - name: DB_USER
        value: "svc_brand_workflows_curation_tool_app"
      - name: DB_PASSWORD_SECRET_NAME
        value: "svc_brand_workflows_curation_tool_app"
      - name: WL_API_CLIENT_ID
        value: "curation_tool_client_id_for_white_label_nextgen_api"
      - name: WL_API_CLIENT_SECRET
        value: "curation_tool_client_for_white_label_nextgen_api"
      - name: TALENT_API_CLIENT_ID
        value: "ah-brand-workflows-curation-tool-talent-reader"
      - name: TALENT_API_CLIENT_SECRET
        value: "ah-brand-workflows-curation-tool-talent-reader"
      - name: DD_SERVICE_NAME
        value: "brand-workflows-curation-tool"
      - name: APP_ENV
        value: "prod"
      - name: WF_ENV
        value: "prod"
      - name: DD_LOGS_INJECTION
        value: "true"
      - name: "DD_ENV"
        value: "prod"
      - name: COST_API_CLIENT_ID
        value: "curation_tool_client_id_for_cost_api"
      - name: COST_API_CLIENT_SECRET
        value: "curation_tool_client_secret_for_cost_api"
      - name: CURATION_DB_USER_PASSWORD_POSTGRES
        value: "curation-db-user-password"
    cloudsql:
      enabled: true
      tag: "2.2.1"
      # This is the `alias` value shown in the output of the Backstage workflow you used to request the SPDB.
      connectionString: "wf-gcp-us-btbb-bc-prod:us-central1:btbb-bc-curation-edef96de"
      replicaCount: 4
