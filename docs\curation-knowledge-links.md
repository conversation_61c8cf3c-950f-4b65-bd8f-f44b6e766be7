# Knowledge Documentation  Links

This is the list of knowledge base that you'd use in your day-to-day tasks to keep Curation workflows up and running or simply get to know Curation more.

!!! info
    Reach out to _<PERSON><PERSON><PERSON> or _<PERSON>_ if you are missing permissions to the following links

### [Brand Workflows Tables Documentation](https://docs.google.com/spreadsheets/d/1RkRiQHsXSoDyyj-Zeh8pqjpm2HAW_pzrEUkPE4UIcmc/edit?pli=1#gid=390776686) 

- All the current tables that Curation, Assortment and Exclusivity have.
- Importance: **10 / 10**

### [Brand Workflows - Alerts/Monitors - Known Issues](https://docs.google.com/spreadsheets/d/1cQuYjQVduAcvbXYkMiG0w_vPb1F9mOlJWJJE7dfDLPk/edit#gid=0)

- A spreadsheet containing all previously seen incidents, cause analysis and actions to fix across all the domains.
- Importance: **9 / 10**

### [Testing and Deploy of PA Automation](https://docs.google.com/document/d/1kFYbxuTsG4Z4-P6Ajt0beL2Ury68BnTHJCSRhtsrI_o/edit#heading=h.phwe2n4xvcuo)

- A must-have doc listing 6 test cases to cover the full import curation job business logic. You must run test case 2~6 on DEV after you make a change in [curation-job-consumer](https://github.com/wayfair-shared/curation-job-consumer) to know if it introduces any regression issues.
- Importance: **8 / 10**

### [Curation Folder on Google Drive](https://drive.google.com/drive/folders/1D-F-FnKDNq8KTYgJ667eo-FPREGmoC8C)

- This is where we usually put the Curation documents about engineering and designs. It has all the design/investigation docs and the knowledge transfer sessions.
- Importance: **7 / 10**

### [Onboard Videos on Google Drive](https://drive.google.com/drive/folders/1tCEhBOQOE6zl4wVybSq7j47izELfl0Zr)

- There are many onbaording KT session video across Curation, Assortment and Exclusivity. They are primarily relevant with some contents dated.
- Importance: **7 / 10**

### [Brand Workflows Team Space - Curation](https://infohub.corp.wayfair.com/display/VMT/Domain%3A+Curation)

- The InfoHub page for Curation. It has information about general workflows/tools intros and troubleshooting guides. It's a bit of outdated but 95% of the content is still relevant.
- Importance: **7 / 10**

### [Curation Open Batches + Tool Errors operation stuck batch](https://docs.google.com/spreadsheets/d/1-nCGg2qMCD7nx2-5oKoq5suhb845qFtXpVCGGhf9ib8/edit#gid=**********)

- The stakeholder uses this spreadsheet daily to track the stuck batches and reaches out of us about any batches that are stuck in curation for more than 48 hours
- Importance: **7 / 10**

### [Flag Project Curation Job Accuracy Investigation](https://docs.google.com/document/d/1A4sLRZ8kdn1wKx9z06HLQFZrdkIAypdvYVWHBZd_QFk/edit#heading=h.a4vmdomommj)

- An ongoing investigation (by the time this is written) for the SKU routing accuracy. The major job that relates to this is flag curation job.
- Importance: **6 / 10** (by the time this is written)

### [Containers & Kubernetes at Wayfair](https://docs.csnzoo.com/docker/containers/containers-at-wayfair/)

- You need it to set up your k8s environment and check the service running/deployment status
- Importance: **6 / 10**

### [New Curation Job (Phase 1) / Jenkins Access Runbook](https://docs.google.com/document/d/1Q0ErzQwndhPh--1KRvuuoWqIMM71d9Gijz10hpVh0kQ/edit#heading=h.6u59j8f3yl8v)

- This is relevant to the new migrated flag curation job. It has the guidance to gain access to the Jenkins jobs. After learning it, you'll know how to do the same for import curation job.
- Importance: **6 / 10**

### [Curation Jobs In Java](https://miro.com/app/board/uXjVOrBsFhs=/)

- A doodle diagram for the job migration design. A great learning material even if the job is done.
- Importance: **4 / 10** (by the time this is written)

### [PA (product addition) and Curation Desired Flow](https://miro.com/app/board/uXjVOA1z1GY=/) 

- The high-level view of the position of Curation from PA's perspective. A good learning resource.
- Importance: **4 / 10**

### [Curation Big Picture](https://miro.com/app/board/o9J_l9K8LtA=/?moveToWidget=3074457361091067494&cot=14)

- A more detailed system diagram regarding the components and their relationship (data flow) for Curation.
- Importance: **4 / 10**

### [Flag Project Curation Job](https://miro.com/app/board/uXjVOAPT4cQ=/)

- A step-by-step graph to show how flag curation job works. This is a bit outdated but the major business logic didn't change.
- Importance: **3 / 10**

### [Curation Process Documentation](https://docs.google.com/spreadsheets/d/1OfNnVqPpLcNDlu1xT3yIYAse5-WT3kgWPnFuB826HCo/edit#gid=0)

- A step-by-step spreadsheet to cover the business logic of all the Curation job. This is a bit outdated but the major business logic didn't change.
- Importance: **4 / 10**

### [Curation - Troubleshooting Guide](https://docs.google.com/document/d/13oRypIz2kv3dAinrSNXtOulj6FSBi6TCc7w47ieNCHM/edit#heading=h.fnpxszuamcv2)

- A run book for some Curation operational issues. Some of them are not happening very often today.
- Importance: **4 / 10**

### [RFC: Curation Jobs migration](https://docs.google.com/document/d/1HsKcKWMfaOPqdu6h3muPeRA2sLXZMphb7zIoAHkCKD8/edit#)

- A design doc for the job migration project. As the job is done, this doc is only for learning purposes
- Importance: **2 / 10**

### [Curation Open Batches + Tool Errors](https://docs.google.com/spreadsheets/d/1-nCGg2qMCD7nx2-5oKoq5suhb845qFtXpVCGGhf9ib8/edit#gid=**********)

- Stakeholder's list to track the stuck batches. It's helpful when you need to monitor which batches are stuck for too long.
- Importance: **4 / 10**

### [Manage Manufacturer](https://infohub.corp.wayfair.com/display/VMT/Manage+Manufacturer)

- Manage Manufacturer Tool is a part of Curation pod ownership even if it's an independent tool living in monolith. 
This doc is a walk through of the tool and the tech details.
- Importance: **3 / 10**

### [Curation PA Automation: Post-Launch QA](https://miro.com/app/board/uXjVOpLlyZ0=/)

- More of a knowledge base for comparing how the old and new PA Curation flows. A good learning material.
- Importance: **3 / 10**

### [PA - High level DS interaction](https://miro.com/app/board/uXjVPbKDRmI=/)

- More of a knowledge base for knowing how the DS model prediction plays a role in the new PA Curation flow.
- Importance: **3 / 10**

### [PA Curation Automation - Show & Tell](https://docs.google.com/presentation/d/1MmQLmV4Cjr8G-y3HfU8ONj8b3R6oG0s-4cDXKlHjo6M/edit#slide=id.g18a0c621a50_0_414)

- Yet another slide deck to showcase the PA Curation Automation
- Importance: **2 / 10**

