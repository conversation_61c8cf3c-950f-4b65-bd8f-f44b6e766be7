<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Employee;
use WF\Shared\Curation\Api\Curation\TalentApi;
class Completion_Employee_Service {

  /**
   * @var TalentApi the talent api object
   */
  private $talentApiObj;
  /**
   * @param \WF\Shared\Curation\Api\Curation\TalentApi $talentApiObj TalentApi
   */
  public function __construct(TalentApi $talentApiObj) {
    $this->talentApiObj = $talentApiObj;
  }

  /**
   * @param int $employeeID Employee ID
   *
   * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Employee
   */
  public function get(int $employeeID) : Completion_Employee {
    $data = $this->talentApiObj->getEmployeeDetails($employeeID);
    $fullName = ($data->fullName)?$data->fullName:'';
    $email = ($data->email)?$data->email:'';
    return new Completion_Employee($fullName, $email);
  }
}
