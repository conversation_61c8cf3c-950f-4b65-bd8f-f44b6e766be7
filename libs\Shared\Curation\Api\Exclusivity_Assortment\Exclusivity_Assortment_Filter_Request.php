<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment;

interface Exclusivity_Assortment_Filter_Request {

  /**
   * @param bool $fetch_only_rebrand_project_skus Flag
   *
   * @return void
   */
  public function set_with_rebrand_project_only(bool $fetch_only_rebrand_project_skus);

  /**
   * @return bool
   */
  public function get_with_rebrand_project_only() : bool;

  /**
   * @return array
   */
  public function get_selected_skus() : array;

  /**
   * @param array $selected_skus selected_skus
   *
   * @return void
   */
  public function set_selected_skus(array $selected_skus);
}
