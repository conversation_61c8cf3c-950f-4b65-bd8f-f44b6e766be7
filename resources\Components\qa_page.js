/**
 * The Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

/**
 * The Curation QA Tool
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

/**
 * The Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

/**
 * The Curation QA Tool
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import PropTypes from 'prop-types';
import {PageContainer, Toast} from '@wayfair/homebase-extranet';
import Translation from '@wayfair/translation';
import QAPageSection from './qa_page_section';
import CurationToolShapes, {
    QA_STATUS_PENDING,
    QA_STATUS_ACCEPTED,
    QA_STATUS_REJECTED,
    BATCH_PROCESS_MANUAL,
    BATCH_PROCESS_AUTOMATED,
    getQaCodeByCurationStatus,
    get<PERSON><PERSON><PERSON><PERSON>String,
    QA_STATUS_CURATION_SAVED_DECISION, PAGE_LIMIT, COLLECTION_LIMIT,
} from './curation_tool_shapes';
import CurationToolPageHeader from './curation_tool_page_header';
import QABulkActionSection from './qa_bulk_action_section';
import {getQAPageQAFilterFunction} from './qa_curation_status_filter';
import {
    TOAST_ERROR_TYPE,
    TOAST_SUCCESS_TYPE,
    SAVE_ERROR_MESSAGE,
    TIME_BEFORE_PAGE_REFRESH,
} from './curation_tool_constants';
import {
    saveQaDecisionService,
    completeQaService,
    completeAutomatedQaService,
    getBatchData,
    getBatchDetails, getBatchDetailsFirstPage,
} from './curation_tool_services';
import QAPageTitle from './qa_page_title';
import QAPageTitleAutomated from './qa_page_title_automated';
import {Loading} from '@homebase/core';
import ErrorScreen from "../Application/ErrorScreen";
import ErrorBoundary from "react-error-boundary";

const INITIAL_SECTION_STATE = {
    selectedRows: [],
    changedRows: [],
};

const limit = PAGE_LIMIT;

class QAPage extends React.Component {
    static propTypes = {
        batchId: PropTypes.number.isRequired,
        batchProcessType: PropTypes.number,
        batchData: CurationToolShapes.batchShape.isRequired,
        rebrandProject: PropTypes.shape({
            id: PropTypes.number,
            name: PropTypes.string,
        }),
        curationConfig: CurationToolShapes.curationConfigShape.isRequired,
        rejectionReasons: PropTypes.arrayOf(CurationToolShapes.reasonShape)
            .isRequired,
        isReadOnlyMode: PropTypes.bool,
        warnings: PropTypes.arrayOf(PropTypes.string),
        isAutomaticCurationPostQaEnabled: PropTypes.bool,
        isAssortmentWorkflowOffshoreUser: PropTypes.bool,
        suggestedStyleRejectionReasons: PropTypes.arrayOf(
            CurationToolShapes.reasonShape
        ),
    };

    static defaultProps = {
        batchProcessType: BATCH_PROCESS_MANUAL,
        rebrandProject: null,
        isReadOnlyMode: false,
        isAutomaticCurationPostQaEnabled: false,
        isAssortmentWorkflowOffshoreUser: false,
        warnings: [],
        suggestedStyleRejectionReasons: [],
    };

    state = {
        sections: [],
        suppliers: [],
        isSaving: false,
        skusSaving: [],
        pageCurationItemMap: {},
        batchDataLoading: false,
        toast: {
            isOpen: false,
            status: null,
            message: '',
        },
        filters: {},
    };

    componentDidMount = async () => {
        if (this.props.batchId) {
            // const result = await getBatchData(this.props.batchId, 'QA');
            const firstPage = await getBatchDetailsFirstPage(
                this.props.batchId,
                limit,
                -1
            );
            const filterJson = JSON.stringify({});
            const encodedFilters = Buffer.from(filterJson).toString('base64');
            const pageCurationItemMap = this.state.pageCurationItemMap;
            const sections = []
            for (const collectionKey in firstPage) {
                if (firstPage[collectionKey].title) {
                    const section = {
                        ...INITIAL_SECTION_STATE,
                        ...firstPage[collectionKey],
                        isExpanded: false,
                        currentPage: 1,
                        changedRows: [],
                        selectedRows: [],
                        isLoading: false,
                        filteredSkusTotalCount: firstPage[collectionKey].skusTotalCount,
                        filteredSkusSavedCount: firstPage[collectionKey].skusSavedCount,
                    }
                    sections.push({...section})
                    section.isExpanded = true;
                    if (parseInt(collectionKey, 10) < COLLECTION_LIMIT) {
                        pageCurationItemMap[
                            `${firstPage[collectionKey].title} - 1 - ${encodedFilters} - ${limit}`
                            ] = section;
                    }
                }
            }
            this.setState({
                batchDataLoading: true,
                sections,
                suppliers: firstPage.suppliers,
                pageCurationItemMap
            });
        }
    };

    getClosedToast = () => ({
        isOpen: false,
        status: null,
        message: '',
    });

    getOpenToast = (status, message) => ({
        isOpen: true,
        status,
        message,
    });

    handleChangeFilter = async (selectedFilter) => {
        const filters = {...this.state.filters, ...selectedFilter};
        await this.setState({filters});
        const sectionIds = [];
        const sectionNames = [];
        const sectionIndices = []
        for (let i = 0; i < this.state.sections.length; i++) {
            if (this.state.sections[i].isExpanded) {
                sectionIds.push(this.state.sections[i].id);
                sectionNames.push(this.state.sections[i].title);
                sectionIndices.push(i);
            }
        }

        await this.fetchSectionDataFromApi(sectionIds, sectionNames, 1, limit, sectionIndices);
        // this.setState(({sections}) => ({
        //   sections: sections.map(section => ({
        //     ...section,
        //     selectedRows: [],
        //   })),
        //   filters,
        // }));
    };

    getFilterOptions = () => {
        return {
            suppliers: this.state.suppliers.map((supplier) => {
                return {value: supplier};
            }),
        };
    };

    matchSupliers = (supplier) => {
        return this.state.filters.suppliers.some((item) => item.value === supplier);
    };

    closeToast = () => this.setState({toast: this.getClosedToast()});

    // creates a copy of sections data applying updates on target section / skus
    getUpdatedSectionData = ({
                                 sections,
                                 target,
                                 onSectionUpdate,
                                 onItemUpdate,
                             }) => {
        return sections.map((section, index) => {
            if (index !== target.sectionIndex) {
                return section;
            }

            const curationItems = section.curationItems.map((item) => {
                if (
                    onItemUpdate !== null &&
                    Array.isArray(target.skus) &&
                    target.skus.includes(item.sku)
                ) {
                    return onItemUpdate(item);
                }

                return item;
            });

            if (onSectionUpdate !== null) {
                return onSectionUpdate(section, curationItems);
            }

            return {...section, curationItems};
        });
    };

    handleSelectionChange = ({sectionIndex, sku, relatedKits, isChecked}) => {
        const affectedRows = relatedKits.length > 0 ? relatedKits : [sku];

        const onSectionUpdate = (section) => {
            // if the row is now checked, add it to the list of selected rows, otherwise remove it
            const selectedRows = isChecked
                ? section.selectedRows.concat(affectedRows)
                : section.selectedRows.filter((item) => !affectedRows.includes(item));

            return {
                ...section,
                selectedRows,
            };
        };

        this.setState((prevState) => {
            return {
                ...prevState,
                sections: this.getUpdatedSectionData({
                    sections: prevState.sections,
                    target: {sectionIndex},
                    onSectionUpdate,
                }),
            };
        });
    };

    handleCurationItemDecisionChange = (sectionIndex, sku, decision) => {
        const target = {
            sectionIndex,
            skus: this.getRelatedSkus(this.findSkuInSection(sectionIndex, sku)),
        };

        this.setDecisionToSkus(target, {decision});
    };

    getSelectedSkus = () => {
        return this.state.sections.flatMap((section, sectionIndex) =>
            this.getSelectedSkusBySection(sectionIndex)
        );
    };

    getSelectedSkusBySection = (sectionIndex) => {
        const {curationItems, selectedRows} = this.state.sections[sectionIndex];

        // get the array of visible skus (after applying filters)
        const visibleSkus = this.getCurationItems(curationItems).map(
            (curationItem) => curationItem.sku
        );

        // get the selected rows that are visible
        return selectedRows.filter((sku) => visibleSkus.includes(sku));
    };

    handleApproveSectionClick = (sectionIndex) => {
        const target = {
            sectionIndex: parseInt(sectionIndex, 10),
            skus: this.getSelectedSkusBySection(sectionIndex),
        };

        this.setDecisionToSkus(target, {
            qaStatus: QA_STATUS_ACCEPTED,
        });

        this.saveQADecision(
            target,
            {
                qaStatus: QA_STATUS_ACCEPTED,
            },
            true,
            sectionIndex
        );
    };

    handleBulkActionApproveClick = () => {
        const targets = [];

        for (const [sectionIndex] in this.state.sections) {
            const target = {
                sectionIndex: parseInt(sectionIndex, 10),
                skus: this.getSelectedSkusBySection(sectionIndex),
            };

            this.setDecisionToSkus(target, {
                qaStatus: QA_STATUS_ACCEPTED,
            });

            targets.push(target);
        }

        this.saveBulkQADecision(
            targets,
            {
                qaStatus: QA_STATUS_ACCEPTED,
            },
            true
        );
    };

    handleBulkActionRejectClick = () => {
        const targets = [];

        for (const [sectionIndex] in this.state.sections) {
            const target = {
                sectionIndex: parseInt(sectionIndex, 10),
                skus: this.getSelectedSkusBySection(sectionIndex),
            };

            this.setDecisionToSkus(target, {
                qaStatus: QA_STATUS_REJECTED,
            });

            targets.push(target);
        }

        this.saveBulkQADecision(
            targets,
            {
                qaStatus: QA_STATUS_REJECTED,
            },
            true
        );
    };

    handleQADecision = (sectionIndex) => (context) => {
        const {sku} = context;
        const itemSku = this.findSkuInSection(sectionIndex, sku);

        if (typeof itemSku === 'undefined') {
            return;
        }

        const target = {
            sectionIndex,
            skus: this.getRelatedSkus(itemSku),
        };
        const {
            exclusionReasonId,
            priceTier,
            styleId,
            substyleId,
            manufacturerId,
            suggestedStyleRejectionReasonId,
        } = itemSku.decision;

        const decision = exclusionReasonId
            ? {
                exclusionReason: exclusionReasonId,
            }
            : {
                priceTierOverride: priceTier,
                finalStyleID: styleId,
                finalSubStyleID: substyleId,
                finalBrandMalID: manufacturerId,
                suggestedReason: suggestedStyleRejectionReasonId,
            };
        const data = {
            qaStatus: context.qaStatus,
            ...decision,
            reason: context.reason,
        };
        this.saveQADecision(target, data, null, sectionIndex);
    };

    getRelatedSkus = (item) =>
        item.relatedKits.length > 0 ? item.relatedKits : [item.sku];

    findSkuInSection = (sectionIndex, sku) =>
        this.state.sections[sectionIndex].curationItems.find(
            (item) => item.sku === sku
        );

    handleSelectAll = (isChecked) => {
        this.setState((prevState) => {
            return {
                ...prevState,
                sections: prevState.sections.map((section) => {
                    return {
                        ...section,
                        selectedRows: isChecked
                            ? section.curationItems
                                .filter((curationItem) =>
                                    [QA_STATUS_PENDING, QA_STATUS_REJECTED].includes(
                                        curationItem.qaStatus
                                    )
                                )
                                .map((curationItem) => curationItem.sku)
                            : [],
                    };
                }),
            };
        });
    };

    handleSelectAllOnSection = (sectionIndex, isChecked) => {
        const onSectionUpdate = (section, curationItems) => {
            const selectedRows = isChecked
                ? curationItems
                    .filter((curationItem) =>
                        [QA_STATUS_PENDING, QA_STATUS_REJECTED].includes(
                            curationItem.qaStatus
                        )
                    )
                    .map((curationItem) => curationItem.sku)
                : [];

            return {
                ...section,
                selectedRows,
            };
        };

        this.setState((prevState) => {
            return {
                ...prevState,
                sections: this.getUpdatedSectionData({
                    sections: prevState.sections,
                    target: {sectionIndex},
                    onSectionUpdate,
                }),
            };
        });
    };

    fetchSectionDataFromApi = async (sectionIds, sectionNames, pageNum, limit, sectionIndices) => {
        const filtersTemp = {};
        for (const property in this.state.filters) {
            if (
                property === 'suppliers' &&
                this.state.filters[property].length !== 0
            ) {
                filtersTemp[property] = [...this.state.filters[property]];
            } else if (
                property === 'qaStatus' &&
                this.state.filters[property] != null
            ) {
                filtersTemp[property] = this.state.filters[property];
            }
        }
        const filterJson = JSON.stringify(filtersTemp);
        const encodedFilters = Buffer.from(filterJson).toString('base64');

        const unMemoizedSectionIds = [];
        const unMemoizedSectionNames = [];
        const unMemoizedSectionIndices = [];
        const newSections = [...this.state.sections];


        for (let j = 0; j < sectionIds.length; j++) {
            const pageCurationItemKey = `${sectionIds[j]} - ${sectionNames[j]} - ${pageNum} - ${encodedFilters} - ${limit}`;

            if (this.state.pageCurationItemMap[pageCurationItemKey]) {
                for (let i = 0; i < this.state.sections.length; i++) {
                    if (this.state.sections[i].id === sectionIds[j] && this.state.sections[i].title === sectionNames[j]) {
                        newSections[i] = {
                            ...this.state.pageCurationItemMap[pageCurationItemKey],
                        };
                    }
                }
            } else {
                unMemoizedSectionIds.push(sectionIds[j]);
                unMemoizedSectionNames.push(sectionNames[j]);
                unMemoizedSectionIndices.push(sectionIndices[j]);
            }
        }
        if (unMemoizedSectionIds.length === 0) {
            this.setState({sections: newSections});
            return;
        } else {
            // eslint-disable-next-line no-param-reassign
            sectionIds = [...unMemoizedSectionIds];
            sectionNames = [...unMemoizedSectionNames];
            // eslint-disable-next-line no-param-reassign
            sectionIndices = [...unMemoizedSectionIndices];
        }

        const length = sectionIds.length;
        const sectionsPre = newSections.map((section) => {
            for (let i = 0; i < length; i++) {
                if (section.id === sectionIds[i] && section.title === sectionNames[i]) {
                    section = {
                        ...section,
                        isLoading: true,
                        isExpanded: true,
                        error: '',
                    };
                }
            }
            return {
                ...section,
            };
        });

        await this.setState({sections: sectionsPre});

        const filters = this.state.filters;
        let qaStatus = getQaCodeByCurationStatus(filters.qaStatus);
        if (qaStatus === QA_STATUS_CURATION_SAVED_DECISION) {
            qaStatus = -1;
        }
        const supplier = getSuppliersString(filters.suppliers);
        const isFilterApplied = supplier || qaStatus !== -1;
        for (let i = 0; i < sectionIds.length; i++) {
            const sectionId = sectionIds[i];
            const sectionName = sectionNames[i];
            try {
                const receivedSection = await getBatchDetails(
                    this.props.batchId, sectionName, pageNum, limit, qaStatus, supplier, 'qa', this.state.sections[sectionIndices[i]].id
                );
                const sections = [...this.state.sections];
                for (let k = 0; k < sections.length; k++) {
                    let section = sections[k];
                    const pageCurationItemMap = this.state.pageCurationItemMap;
                    if (supplier) {
                        for (let j = 0; j < receivedSection.length; j++) {
                            if (section.id === sectionId && section.title === sectionName) {
                                if (receivedSection[j].id === sectionId && receivedSection[j].title === sectionName) {
                                    const filteredSkusTotalCount = receivedSection[j].skusTotalCount;
                                    const filteredSkusSavedCount = receivedSection[j].skusSavedCount;
                                    section = {
                                        ...section,
                                        curationItems: receivedSection[j].curationItems,
                                        isLoading: false,
                                        isExpanded: true,
                                        error: '',
                                        filteredSkusTotalCount,
                                        filteredSkusSavedCount,
                                        currentPage: pageNum,
                                    };

                                    pageCurationItemMap[
                                        `${section.id} - ${section.title} - ${pageNum} - ${encodedFilters} - ${limit}`
                                        ] = section;
                                    sections[k] = section
                                }

                            } else if (receivedSection[j].id === sections[k].id && receivedSection[j].title === sections[k].title) {

                                const filteredSkusTotalCount = receivedSection[j].skusTotalCount;
                                const filteredSkusSavedCount = receivedSection[j].skusSavedCount;
                                section = {
                                    ...sections[k],
                                    curationItems: receivedSection[j].curationItems,
                                    isLoading: false,
                                    isExpanded: true,
                                    error: '',
                                    filteredSkusTotalCount,
                                    filteredSkusSavedCount,
                                    currentPage: pageNum,
                                };
                                sections[k].filteredSkusTotalCount = filteredSkusTotalCount
                                sections[k].filteredSkusSavedCount = filteredSkusSavedCount

                                pageCurationItemMap[
                                    `${section.id} - ${section.title} - ${pageNum} - ${encodedFilters} - ${limit}`
                                    ] = section;

                            }
                        }
                        this.setState((prevState) => {
                            return {
                                ...prevState,
                                pageCurationItemMap,
                            };
                        });
                    } else if (section.id === sectionId && section.title === sectionName) {

                        let filteredSkusTotalCount = 0;
                        let filteredSkusSavedCount = 0;
                        if (isFilterApplied) {
                            filteredSkusTotalCount =
                                receivedSection.filteredSkusTotalCount;
                            filteredSkusSavedCount =
                                receivedSection.filteredSkusTotalCount;
                        } else {
                            filteredSkusTotalCount = section.skusTotalCount;
                            filteredSkusSavedCount = section.skusSavedCount;
                        }
                        if (!receivedSection["0"]){
                            receivedSection["0"] = {curationItems:[]}
                        }
                        section = {
                            ...section,
                            curationItems: receivedSection["0"].curationItems,
                            isLoading: false,
                            isExpanded: true,
                            error: '',
                            filteredSkusTotalCount,
                            filteredSkusSavedCount,
                            currentPage: pageNum,
                        };

                        const pageCurationItemMap = this.state.pageCurationItemMap;
                        pageCurationItemMap[
                            `${section.id} - ${section.title} - ${pageNum} - ${encodedFilters} - ${limit}`
                            ] = section;
                        sections[k] = section
                        this.setState((prevState) => {
                            return {
                                ...prevState,
                                pageCurationItemMap,
                            };
                        });

                    }
                }
                this.setState({sections});
            } catch (e) {
                const sections = this.state.sections.map((section) => {
                    if (section.id === sectionId && section.title === sectionName) {
                        section = {
                            ...section,
                            curationItems: [],
                            isLoading: false,
                            isExpanded: true,
                            error: 'There is issue while loading data. Please try again!!!',
                        };
                    }
                    return {
                        ...section,
                        changedRows: [],
                    };
                });

                this.setState({sections});
            }
        }
    };

    handleExpandClick = async (sectionIndex, isExpanded, sectionId, sectionName) => {
        if (isExpanded === true) {
            await this.fetchSectionDataFromApi([sectionId],[sectionName], 1, limit, [sectionIndex]);
        }
        const onSectionUpdate = (section) => {
            return {
                ...section,
                isExpanded,
            };
        };

        this.setState((prevState) => {
            return {
                ...prevState,
                sections: this.getUpdatedSectionData({
                    sections: prevState.sections,
                    target: {sectionIndex},
                    onSectionUpdate,
                }),
            };
        });
    };

    /* expand or collapse all sections based on expandState */
    flipExpandAll = (expandState) => {
        this.setState((prevState) => {
            return {
                ...prevState,
                sections: prevState.sections.map((section) => {
                    return {
                        ...section,
                        isExpanded: expandState,
                    };
                }),
            };
        });
    };

    handleClickExpandAll = async () => {
        const sectionIds = [];
        const sectionNames = [];
        const sectionIndices = []
        for (let i = 0; i < this.state.sections.length; i++) {
            // if (this.state.sections[i].isExpanded) {
                sectionIds.push(this.state.sections[i].id);
                sectionNames.push(this.state.sections[i].title);
                sectionIndices.push(i);
            // }
        }
        await this.fetchSectionDataFromApi(sectionIds, sectionNames, 1, limit, sectionIndices);
    };

    handleClickCollapseAll = () => {
        this.flipExpandAll(false);
    };

    isExpandedAll = () => {
        return !this.state.sections.find((item) => !item.isExpanded);
    };

    saveQADecision = async (target, context, clearSelection, sectionIndex) => {
        const sections = [...this.state.sections];

        await this.setState({isSaving: true, skusSaving: [...this.state.skusSaving, ...target.skus]});
        // this.handleClickCollapseAll()
        saveQaDecisionService({
            skus: target.skus,
            batchId: this.props.batchId,
            ...context,
        })
            .then((saveInfo) => {

                this.stateManageSection(sectionIndex, saveInfo);
                this.decisionSaved(target, saveInfo, clearSelection);
                const skusSaving = [];

                for (let j = 0; j < this.state.skusSaving.length; j++) {
                    if(!target.skus.includes(this.state.skusSaving[j])){
                        skusSaving.push(this.state.skusSaving[j])
                    }
                }

                this.setState({isSaving: false, skusSaving})
            })
            .catch(() =>
                this.setState({
                    toast: this.getOpenToast(TOAST_ERROR_TYPE, SAVE_ERROR_MESSAGE),
                    isSaving: false,
                })
            );
    };

    stateManageSection(sectionIndex, saveInfo) {
        const sections = [...this.state.sections];
        for (let i = 0; i < sections[sectionIndex].curationItems.length; i++) {
            for (let j = 0; j < saveInfo.skus.length; j++) {
                if (
                    sections[sectionIndex].curationItems[i].sku === saveInfo.skus[j]
                ) {
                    if (sections[sectionIndex].curationItems[i].qaStatus === 1 && (saveInfo.qaStatus !== 0 || saveInfo.qaStatus !== 1)) {
                        sections[sectionIndex].skusSavedCount = sections[sectionIndex].skusSavedCount + 1;
                        sections[sectionIndex].filteredSkusSavedCount = sections[sectionIndex].filteredSkusSavedCount + 1;
                    }
                    sections[sectionIndex].curationItems[i].qaStatus = saveInfo.qaStatus;
                }
            }
        }
        const pageCurationItemMap = {...this.state.pageCurationItemMap};
        for (const pageCurationItemKey in pageCurationItemMap) {
            const splitKey = pageCurationItemKey.split(' - ');
            const id = splitKey[0];
            const pageNum = parseInt(splitKey[2], 10);
            if (sections[sectionIndex].id === id && sections[sectionIndex].title === title) {
                const encodedFilters = splitKey[2];
                const filterJson = Buffer.from(encodedFilters, 'base64').toString(
                    'utf-8'
                );
                const filters = JSON.parse(filterJson);
                if (
                    filters.qaStatus === 'pending' ||
                    filters.qaStatus === 'updated' ||
                    filters.qaStatus === 'approved' ||
                    filters.qaStatus === 'rejected'
                ) {
                    delete pageCurationItemMap[pageCurationItemKey];
                } else if (sections[sectionIndex].currentPage === pageNum) {
                    pageCurationItemMap[pageCurationItemKey] = {...sections[sectionIndex]};
                } else if (sections[sectionIndex].id === id && sections[sectionIndex].title === title) {
                    pageCurationItemMap[pageCurationItemKey].skusTotalCount = sections[sectionIndex].skusTotalCount
                    pageCurationItemMap[pageCurationItemKey].skusSavedCount = sections[sectionIndex].skusTotalCount
                    pageCurationItemMap[pageCurationItemKey].filteredSkusTotalCount = sections[sectionIndex].filteredSkusTotalCount
                    pageCurationItemMap[pageCurationItemKey].filteredSkusSavedCount = sections[sectionIndex].filteredSkusSavedCount
                }
            }
        }

        this.setState({
            sections,
            pageCurationItemMap
        });
    }

    saveBulkQADecision = (targets, context, clearSelection) => {
        this.setState({isSaving: true});
        const skus = targets.flatMap((target) => target.skus);

        saveQaDecisionService({
            skus,
            qaStatus: context.qaStatus,
            batchId: this.props.batchId,
            reason: context.reason,
        })
            .then((saveInfo) => {
                for (let i = 0; i < targets.length; i++) {
                    const sectionIndex = i;
                    this.stateManageSection(sectionIndex, saveInfo);
                }
                for (const target of targets) {
                    this.decisionSaved(target, saveInfo, clearSelection);
                }
                this.setState({isSaving: false})
            })
            .catch(() =>
                this.setState({
                    toast: this.getOpenToast(TOAST_ERROR_TYPE, SAVE_ERROR_MESSAGE),
                })
            );
    };

    decisionSaved = (target, saveInfo, clearSelection) => {
        const onItemUpdate = (item) => {
            return {
                ...item,
                ...saveInfo,
            };
        };

        const onSectionUpdate = (section, curationItems) => {
            // remove saved rows from pending changes
            const changedRows = section.changedRows.filter(
                (item) => !target.skus.includes(item)
            );

            if (clearSelection) {
                return {
                    ...section,
                    curationItems,
                    ...INITIAL_SECTION_STATE,
                    changedRows,
                };
            }

            return {...section, curationItems, changedRows};
        };

        this.setState((prevState) => {
            return {
                ...prevState,
                sections: this.getUpdatedSectionData({
                    sections: prevState.sections,
                    target,
                    onSectionUpdate,
                    onItemUpdate,
                }),
                toast: this.getClosedToast(),
            };
        });
    };

    setDecisionToSkus = (target, savedInfo) => {
        const onItemUpdate = (item) => {
            return {
                ...item,
                ...savedInfo,
            };
        };

        const onSectionUpdate = (section, curationItems) => {
            const changedRows = section.changedRows.concat(target.skus);

            return {...section, curationItems, changedRows};
        };

        this.setState((prevState) => {
            return {
                ...prevState,
                sections: this.getUpdatedSectionData({
                    sections: prevState.sections,
                    target,
                    onSectionUpdate,
                    onItemUpdate,
                }),
                toast: this.getClosedToast(),
            };
        });
    };

    isCheckedIndeterminate = () => {
        const selectedRows = this.state.sections.flatMap(
            (section) => section.selectedRows
        );

        return (
            selectedRows.length > 0 &&
            selectedRows.length <
            this.state.sections.flatMap((section) => section.curationItems).length
        );
    };

    isCheckedAll = () =>
        this.state.sections.flatMap((section) => section.selectedRows).length ===
        this.state.sections.flatMap((section) => section.curationItems).length;

    isSectionCheckedIndeterminate = (sectionIndex) => {
        const {selectedRows, curationItems} = this.state.sections[sectionIndex];

        return (
            selectedRows.length > 0 && selectedRows.length < curationItems.length
        );
    };

    isSectionCheckedAll = (sectionIndex) =>
        this.state.sections[sectionIndex].selectedRows.length ===
        this.state.sections[sectionIndex].curationItems.length;

    getSkusSavedCount = () =>
        this.state.sections.reduce((accumulator, section) => {
            return accumulator + section.skusSavedCount;
        }, 0);

    getSkusTotalCount = () =>
        this.state.sections.reduce((accumulator, section) => {
            return accumulator + section.skusTotalCount;
        }, 0);

    handleCompleteClick = () => {
        const service =
            this.props.batchProcessType === BATCH_PROCESS_AUTOMATED
                ? completeAutomatedQaService
                : completeQaService;

        service(this.props.batchId)
            .then((response) => {
                if (response.result) {
                    this.setState({
                        toast: this.getOpenToast(
                            TOAST_SUCCESS_TYPE,
                            <Translation msgid="CurationTool.QAIsCompletedAndRefreshed"/>
                        ),
                    });

                    setTimeout(
                        () => window.location.reload(true),
                        TIME_BEFORE_PAGE_REFRESH
                    );
                } else {
                    this.setState({
                        toast: this.getOpenToast(
                            TOAST_ERROR_TYPE,
                            <Translation msgid="CurationTool.ThereAreMoreSKUsToQAedDotThePageWillRefreshDot"/>
                        ),
                    });

                    setTimeout(
                        () => window.location.reload(true),
                        TIME_BEFORE_PAGE_REFRESH
                    );
                }
            })
            .catch((e) => {
                const error = e?.message && JSON.parse(e.message);
                const message = error?.reason ? (
                    <Translation
                        msgid="CurationTool.failedToCompleteQAReason"
                        params={{reason: error.reason}}
                    />
                ) : (
                    <Translation msgid="CurationTool.FailedToCompleteQAExclamationSign"/>
                );
                this.setState({
                    toast: this.getOpenToast(TOAST_ERROR_TYPE, message),
                });
            });
    };

    getCurationItems = (sectionItems) => {
        // We want to make sure to apply these sequentially to all section items
        let curationItems = sectionItems;

        if (this.state.filters.qaStatus) {
            const filterFunction = getQAPageQAFilterFunction(
                this.state.filters.qaStatus
            );
            curationItems = filterFunction(sectionItems);
        }

        if (
            this.state.filters.suppliers &&
            this.state.filters.suppliers.length > 0
        ) {
            curationItems = curationItems.filter((curationItem) => {
                return curationItem.suppliers.some(this.matchSupliers);
            });
        }
        return curationItems;
    };

    getPageTitle = () => {
        if (this.props.isAutomaticCurationPostQaEnabled) {
            return (
                <QAPageTitleAutomated isReadOnlyMode={this.props.isReadOnlyMode}/>
            );
        } else {
            return <QAPageTitle isReadOnlyMode={this.props.isReadOnlyMode}/>;
        }
    };

    render() {
        return (
            <PageContainer pageTitle={this.getPageTitle()} layoutWidth="fullWidth">
                <ErrorBoundary FallbackComponent={ErrorScreen}>
                    <CurationToolPageHeader
                        batchId={this.props.batchId}
                        batchData={this.props.batchData}
                        batchDataLoading={this.state.batchDataLoading}
                        rebrandProject={this.props.rebrandProject}
                        skusSavedCount={this.getSkusSavedCount()}
                        skusTotalCount={this.getSkusTotalCount()}
                        onCompleteClick={this.handleCompleteClick}
                        onExpandAllClick={this.handleClickExpandAll}
                        onCollapseAllClick={this.handleClickCollapseAll}
                        selectedFilters={this.state.filters}
                        filterOptions={this.getFilterOptions()}
                        onChangeFilter={this.handleChangeFilter}
                        isExpandedAll={this.isExpandedAll()}
                        labelComplete={Translation({
                            msgid: 'CurationTool.QAPageBatchCompleteQA',
                        })}
                        progressLabel={<Translation msgid="CurationTool.Reviewed"/>}
                        isReadOnlyMode={this.props.isReadOnlyMode}
                        isAutomaticCurationPostQaEnabled={
                            this.props.isAutomaticCurationPostQaEnabled
                        }
                        warnings={this.props.warnings}
                    />
                    {!this.props.isReadOnlyMode && (
                        <QABulkActionSection
                            curationConfig={this.props.curationConfig}
                            rejectionReasons={this.props.rejectionReasons}
                            handleApprove={this.handleBulkActionApproveClick}
                            handleReject={this.handleBulkActionRejectClick}
                            handleSelectAll={(isChecked) => this.handleSelectAll(isChecked)}
                            selectedRows={this.getSelectedSkus()}
                            checkedIndeterminate={this.isCheckedIndeterminate()}
                            checkedAll={this.isCheckedAll()}
                            isAutomaticCurationPostQaEnabled={
                                this.props.isAutomaticCurationPostQaEnabled
                            }
                        />
                    )}
                    {this.state.sections.map((section, index) => {
                        // const curationItems = this.getCurationItems(section.curationItems);

                        const curationItems = section.curationItems;
                        // if (curationItems.length < 1) {
                        //   return null;
                        // }
                        return (
                            <QAPageSection
                                key={section.id}
                                title={section.title}
                                savingState={{isSaving: this.state.isSaving, skusSaving: this.state.skusSaving}}
                                curationConfig={this.props.curationConfig}
                                curationItems={curationItems}
                                selectedRows={section.selectedRows}
                                changedRows={section.changedRows}
                                checkedIndeterminate={this.isSectionCheckedIndeterminate(index)}
                                checkedAll={this.isSectionCheckedAll(index)}
                                isExpanded={section.isExpanded}
                                onSelectAll={(isChecked) =>
                                    this.handleSelectAllOnSection(index, isChecked)
                                }
                                onSelectionChange={(sku, relatedKits, isChecked) =>
                                    this.handleSelectionChange({
                                        sectionIndex: index,
                                        sku,
                                        relatedKits,
                                        isChecked,
                                    })
                                }
                                onCurationItemDecisionChange={(sku, decision) =>
                                    this.handleCurationItemDecisionChange(index, sku, decision)
                                }
                                onSaveAllClick={() => this.handleApproveSectionClick(index)}
                                onSave={this.handleQADecision(index)}
                                onUpdate={this.handleQADecision(index)}
                                onExpandClick={(isExpanded) => {
                                    this.handleExpandClick(index, isExpanded, section.id, section.title);
                                }}
                                fetchSectionDataFromApi={(pageNum, limit) => {
                                    this.fetchSectionDataFromApi([section.id], [section.title], pageNum, limit, [index]);
                                }}
                                rejectionReasons={this.props.rejectionReasons}
                                isReadOnlyMode={this.props.isReadOnlyMode}
                                isAutomaticCurationPostQaEnabled={
                                    this.props.isAutomaticCurationPostQaEnabled
                                }
                                isAssortmentWorkflowOffshoreUser={
                                    this.props.isAssortmentWorkflowOffshoreUser
                                }
                                suggestedStyleRejectionReasons={
                                    this.props.suggestedStyleRejectionReasons
                                }
                                skusTotalCount={section.filteredSkusTotalCount}
                                skusSavedCount={section.filteredSkusSavedCount}
                                isLoading={section.isLoading}
                                error={section.error}
                                currentPage={section.currentPage}
                            />
                        );
                    })}
                    <Toast
                        isOpen={this.state.toast.isOpen}
                        status={this.state.toast.status}
                        onRequestClose={this.closeToast}
                    >
                        {this.state.toast.message}
                    </Toast>
                </ErrorBoundary>
            </PageContainer>
        );
    }
}

export default QAPage;
