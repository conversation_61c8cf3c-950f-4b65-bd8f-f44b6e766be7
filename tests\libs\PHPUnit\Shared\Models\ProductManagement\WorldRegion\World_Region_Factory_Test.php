<?php
/**
 * Tests for the \WF\Shared\Models\ProductManagement\WorldRegion
 *
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Shared\Models\ProductManagement\WorldRegion;

use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Null;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_US;

class World_Region_Factory_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory
     */
    protected $subject;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->subject = new World_Region_Factory();
    }

    /**
     * @test
     * @param int    $regionId           Region ID
     * @param string $expectedObjectType Expected Region type
     *
     * @dataProvider getItCreatesRegionData
     *
     * @return void
     */
    public function itCreatesRegion(int $regionId, string $expectedObjectType)
    {
        $this->assertInstanceOf($expectedObjectType, $this->subject->create($regionId));
    }

    /**
     * @return array
     */
    public function getItCreatesRegionData(): array
    {
        return [
            [World_Region_US::REGION_ID, World_Region_US::class],
            [World_Region_EU::REGION_ID, World_Region_EU::class],
            [World_Region_Null::REGION_ID, World_Region_Null::class],
        ];
    }

    /**
     * @test
     *
     * @return void
     */
    public function itThrowsExceptionForInvalidRegionID()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->subject->create(-1);
    }
}
