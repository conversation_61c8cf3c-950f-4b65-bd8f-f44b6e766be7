<?php
/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils;

use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO;
use Psr\Log\LoggerInterface;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface;
use WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Null;
use WF\Shared\Traits\Logging_Trait;
use App\Infrastructure\Connection\Graphql\CurationGraphApi;
use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;

class Region_Service {
  use Logging_Trait;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO
   */
  private $dao;

  /**
   * @var \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory
   */
  private $factory;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO
   */
  private $postgres_dao;

  /**
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO                       $dao              DAO
   * @param \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Factory                        $factory          Factory
   * @param \App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface                          $featureToggles   Feature toggle
   * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO $postgres_dao     Postgres Curation Tool DAO
   * @param \Psr\Log\LoggerInterface|null                                                               $logger           Logger
   */
  public function __construct(
      Curation_Tool_DAO    $dao,
      World_Region_Factory $factory,
      FeatureTogglesInterface $featureToggles,
      Curation_Tool_Postgres_DAO $postgres_dao,
      ?LoggerInterface $logger = null
  ) {
    $this->dao     = $dao;
    $this->factory = $factory;
    $this->logger  = $logger;
    $this->featureToggles   = $featureToggles;
    $this->postgres_dao = $postgres_dao;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return \WF\Shared\Models\ProductManagement\WorldRegion\World_Region_Interface
   */
  public function get_region(int $batch_id) : World_Region_Interface {
    $this->info('Loading Brand Catalog for batch', ['batch_id' => $batch_id]);
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $brand_catalog_id = $this->postgres_dao->get_batch_brand_catalog($batch_id);
    } else {
      $brand_catalog_id = $this->dao->get_batch_brand_catalog($batch_id);
    }

    $regions = $this->factory->create_all_regions();

    foreach ($regions as $region) {
      if (in_array($brand_catalog_id, $region->getBrandCatalogIds(), true)) {
        return $region;
      }
    }

    return $this->factory->create(World_Region_Null::REGION_ID);
  }
}
