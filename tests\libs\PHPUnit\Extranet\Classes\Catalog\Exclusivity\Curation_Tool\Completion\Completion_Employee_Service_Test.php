<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace WF\Tests\PHPUnit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion;

use PHPUnit\Framework\TestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use StdClass;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service;


use WF\Shared\Curation\Api\Curation\TalentApi;

class Completion_Employee_Service_Test extends TestCase
{
    use ProphecyTrait;
    /**
     * @var TalentApi the talent api object
     */
    private $talentApiObj;

    /**
     * @var object
     */
    private $employeeObj;

    /**
     * @return void
     */
    protected function setUp(): void
    {
        $this->talentApiObj = $this->prophesize(TalentApi::class);

        $this->employeeObj = new Completion_Employee_Service(
            $this->talentApiObj->reveal()
        );
        parent::setUp();
    }

    /**
     * @test
     *
     * @return void
     */
    public function it_returns_employee_data()
    {
        $employeeId = 1;
        $employeeData = new StdClass();
        $employeeData->id = 1;
        $employeeData->fullName = 'Test Name';
        $employeeData->email = '<EMAIL>';
        $this->talentApiObj->getEmployeeDetails($employeeId)->willReturn($employeeData);
        $actualResult = $this->employeeObj->get($employeeId);
        $this->assertEquals($employeeData->fullName, $actualResult->getName());
        $this->assertEquals($employeeData->email, $actualResult->getEmail());
    }
}
