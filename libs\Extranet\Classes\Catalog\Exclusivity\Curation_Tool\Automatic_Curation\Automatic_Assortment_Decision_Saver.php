<?php
/**
 * PHP version 7
 *
 * <AUTHOR> Evgeny <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Item_Type;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Service;

class Automatic_Assortment_Decision_Saver implements Automatic_Curation_Saver {
  /**
   * @var Assortment_Curation_Decision_Loader
   */
  private $assortmentCurationDecisionLoader;

  /**
   * @var Curation_Decision_Service
   */
  private $curationDecisionService;

  /**
   * @param Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader Assortment decision loader
   * @param Curation_Decision_Service           $curationDecisionService          Curation decision service
   */
  public function __construct(
      Assortment_Curation_Decision_Loader $assortmentCurationDecisionLoader,
      Curation_Decision_Service $curationDecisionService
  ) {
    $this->assortmentCurationDecisionLoader = $assortmentCurationDecisionLoader;
    $this->curationDecisionService          = $curationDecisionService;
  }

  /**
   * @param int       $batchId    Batch ID
   * @param Section[] $sections   Section data
   * @param int       $employeeId Employee ID
   *
   * @return int
   * @throws \Exception
   */
  public function execute(int $batchId, array $sections, int $employeeId) : int {
    $moveToHeaderSkus = $this->getNewSkusToMoveToHeaderBrand($batchId, $sections);

    if (empty($moveToHeaderSkus)) {
      return 0;
    }

    $this->saveAssortmentDecisionForNewSku($batchId, $employeeId, $moveToHeaderSkus);

    return count($moveToHeaderSkus);
  }

  /**
   * @param int       $batchId  Batch id
   * @param Section[] $sections Section data
   *
   * @return Curation_Item[]
   */
  private function getNewSkusToMoveToHeaderBrand(int $batchId, array $sections) : array {
    $skus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        if ($curationItem->get_type() === Curation_Item_Type::simple()
            || $curationItem->get_type() === Curation_Item_Type::candidate()
        ) {
          $skus[] = $curationItem->get_sku();
        }
      }
    }

    $assortmentDecisionCollection = $this->assortmentCurationDecisionLoader->assortment_decision_data_for_skus($batchId, $skus);

    $moveToHeaderSkus = [];

    foreach ($sections as $section) {
      foreach ($section->get_curation_items() as $curationItem) {
        if (!empty($curationItem->get_saved_at())) { //we don't try to apply the assortment decision if an sku a
          continue;
        }

        $assortmentDecision = $assortmentDecisionCollection->get_for_sku($curationItem->get_sku());
        if (!$assortmentDecision->should_move_to_header_brand()) {
          continue;
        }

        if ($curationItem->get_final_brand_id() === $assortmentDecision->get_move_to_brand_decision_data()->get_target_manufacturer_id()) {
          $moveToHeaderSkus[] = $curationItem;
        }
      }
    }

    return $moveToHeaderSkus;
  }

  /**
   * @param int             $batchId        Batch Id
   * @param int             $employeeId     Employee ID
   * @param Curation_Item[] $newContextSkus Section data
   *
   * @return void
   * @throws \Exception
   */
  private function saveAssortmentDecisionForNewSku(int $batchId, int $employeeId, array $newContextSkus) {
    $savedAt = date('Y-m-d H:i:s');
    foreach ($newContextSkus as $curationItem) {
      if (empty($curationItem->get_final_brand_id()) || empty($curationItem->get_final_style_id())) {
        continue;
      }

      $this->curationDecisionService->save_curated(
          $batchId,
          [$curationItem->get_sku()],
          $curationItem->get_price_tier(),
          $curationItem->get_final_style_id(),
          $curationItem->get_final_sub_style_id(),
          $curationItem->get_final_brand_id(),
          $curationItem->get_final_granular_style_id(),
          $savedAt,
          $employeeId,
          Curation_Decision_Source::automatic_by_assortment_decision()
      );
    }
  }
}
