<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Mailer;

class Mail_Address {
  /**
   * @var string
   */
  private $name;

  /**
   * @var string
   */
  private $email;

  /**
   * @param string $email the address
   * @param string $name  the name to display
   *
   * @return void
   */
  public function __construct(string $email, string $name = '') {
    $this->email = trim($email);
    $this->name  = trim($name);
  }

  /**
   * @return string
   */
  public function get_name() : string {
    return $this->name;
  }

  /**
   * @return string
   */
  public function get_email() : string {
    return $this->email;
  }
}
