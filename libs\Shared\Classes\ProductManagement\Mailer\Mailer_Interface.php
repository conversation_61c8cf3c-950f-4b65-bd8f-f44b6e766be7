<?php
/**
 * Interface of generic mailer
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

interface Mailer_Interface {
  /**
   * Creates new message which is accepted to send by current mailer
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function create_message();

  /**
   * Sends out given mail message
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $message message to send
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Result
   */
  public function send(Mail_Message $message);
}
