.docker-plugin-tests: &docker-plugin-tests
  agents:
    queue: docker
  plugins:
    - ssh://**************/wayfair-secure/docker-compose-buildkite-plugin#v3.7.3:
        config: docker-compose.yaml
        mount-buildkite-agent: true
        propagate-environment: true
        run: ${MATRIX_SERVICE}

steps:
  - label: "${MATRIX_LABEL} PHP lint {{ matrix.composer_flags }}"
    <<: *docker-plugin-tests
    matrix:
      setup:
        composer_flags:
          - ""
    commands:
      - echo "--- Composer Install"
      - composer install --no-interaction
      - echo "+++ lint"
      - composer lint --no-interaction

  - label: "${MATRIX_LABEL} Tests {{ matrix.composer_flags }}"
    <<: *docker-plugin-tests
    matrix:
      setup:
        composer_flags:
          - ""
    commands:
      - echo "--- Composer Install"
      - composer install --no-interaction
      - echo "+++ PHPUnit"
      - composer phpunit --no-interaction
