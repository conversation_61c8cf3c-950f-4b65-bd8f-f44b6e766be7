<?php

declare(strict_types=1);

namespace WF\Curation\WhiteLabelApi\Tests\Unit;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use WF\Curation\WhiteLabelApi\Client;
use WF\Curation\WhiteLabelApi\ClientConfig;
use WF\Curation\WhiteLabelApi\Entity\Batch;
use WF\Curation\WhiteLabelApi\DTO\Response;
use WF\Curation\WhiteLabelApi\DTO\ResponseError;
use WF\Curation\WhiteLabelApi\DTO\ValidationErrorResponse;

class ClientTest extends TestCase
{
    private const NUMBER_OF_RETRIES = 1;
    /**
     * @var Client
     */
    private $subject;

    /**
     * @var ClientConfig
     */
    private $clientConfig;

    /**
     * @var MockObject|LoggerInterface
     */
    private $logger;

    protected function setUp(): void
    {
        parent::setUp();

        /* @var LoggerInterface $logger */
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->clientConfig = new ClientConfig($this->logger, self::NUMBER_OF_RETRIES);
        $this->subject = new Client($this->clientConfig);
    }

    /**
     * @test
     */
    public function itShouldLogErrorAfterConnectionFailure(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = HttpClient::createConnectionTimeoutClientMock();
        $this->clientConfig->setHttpClient($httpClient);

        $batch = new Batch(1, 1);
        $response = $this->subject->createBatch([$batch]);

        $this->assertNull($response);
    }

    /**
     * @test
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnASuccessfulResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('info');

        $httpClient = HttpClient::createSuccessfulClientMock();
        $this->clientConfig->setHttpClient($httpClient);

        $batch = new Batch(1, 1);
        $response = $this->subject->createBatch([$batch]);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertTrue($response->isRequestSuccessful());
    }

    /**
     * @test
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnValidationErrorResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = HttpClient::createValidationErrorClientMock();
        $this->clientConfig->setHttpClient($httpClient);

        $batch = new Batch(1, 1);
        /* @var ValidationErrorResponse $response */
        $response = $this->subject->createBatch([$batch]);

        $this->assertInstanceOf(ValidationErrorResponse::class, $response);
        $this->assertFalse($response->isRequestSuccessful());
    }

    /**
     * @test
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnResponseErrorResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $httpClient = HttpClient::createResponseErrorClientMock();
        $this->clientConfig->setHttpClient($httpClient);

        $batch = new Batch(1, 1);
        /* @var ResponseError $response */
        $response = $this->subject->createBatch([$batch]);

        $this->assertInstanceOf(ResponseError::class, $response);
        $this->assertFalse($response->isRequestSuccessful());
    }

    /**
     * @test
     * @return void
     * @throws \Exception
     */
    public function itShouldReturnUnhandledResponseErrorResponse(): void
    {
        $this->logger->expects($this->once())
            ->method('error');

        $expectedCode = 0;
        $expectedMessage = 'Unidentified response code';

        $httpClient = HttpClient::createUnhandledErrorClientMock();
        $this->clientConfig->setHttpClient($httpClient);

        $batch = new Batch(1, 1);
        /* @var ResponseError $response */
        $response = $this->subject->createBatch([$batch]);

        $this->assertInstanceOf(ResponseError::class, $response);
        $this->assertFalse($response->isRequestSuccessful());
        $this->assertEquals($expectedCode, $response->getErrorCode());
        $this->assertEquals($expectedMessage, $response->getErrorMessage());
    }
}
