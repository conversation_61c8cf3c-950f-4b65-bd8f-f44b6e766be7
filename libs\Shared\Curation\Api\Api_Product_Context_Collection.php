<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api;

use WF\Shared\Curation\Api\Exceptions\API_Request_Exception;

interface Api_Product_Context_Collection {

  /**
   * @param string ...$skus SKUs
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function find_product_context_collection(string ...$skus) : array;

  /**
   * @param string[] $skus list of SKUS to get collisions
   *
   * @return array
   * @throws API_Request_Exception
   */
  public function find_sku_collisions(array $skus) : array;
}
