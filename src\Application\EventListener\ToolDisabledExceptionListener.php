<?php

declare(strict_types=1);

namespace App\Application\EventListener;

use App\Application\Exception\ToolDisabledException;
use App\Application\Service\ViewRenderer;
use App\Application\View\ToolDisabledView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class ToolDisabledExceptionListener
{
    private ViewRenderer $renderingService;

    public function __construct(ViewRenderer $renderingService)
    {
        $this->renderingService = $renderingService;
    }

    /**
     * @param ExceptionEvent $event
     * @return void
     */
    public function onKernelException(ExceptionEvent $event): void
    {
        $request = $event->getRequest();
        $exception = $event->getThrowable();

        if ($exception instanceof ToolDisabledException && $event->isMainRequest()) {
            $event->setResponse(
                new Response(
                    $this->renderingService->render(
                        new ToolDisabledView(),
                        $request->query->get('webpack_public_path_root') ?? '/',
                        $request->server->get('DOCUMENT_ROOT')
                    ),
                    Response::HTTP_LOCKED
                )
            );
        }
    }
}
