<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types = 1);

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Api;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Curation\WhiteLabelApi\Client as White_Label_Client;
use WF\Curation\WhiteLabelApi\ClientConfig;
use WF\Curation\WhiteLabelApi\DTO\ValidationDetail;
use WF\Curation\WhiteLabelApi\DTO\ValidationErrorResponse;
use WF\Curation\WhiteLabelApi\Entity\Batch;
use WF\Curation\WhiteLabelApi\Entity\SKU;
use WF\Curation\WhiteLabelApi\Exception\ConfigException;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Downstream_Processor;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Too<PERSON>\Downstream\Exception\Downstream_Fail_Exception;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Exception\White_Label_Batch_Already_Exists_Exception;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_Api_Service;
use WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Model\White_Label_Batch_Error_Response;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch as Batch_Model;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use WF\Shared\Traits\Logging_Trait;

class White_Label_Api_Processor implements Downstream_Processor, LoggerAwareInterface {
  use Logging_Trait;
  use LoggerAwareTrait;

  protected const CURATION_WL_CONFIGURATION = [
      'changeProductName' => true,
      'changeCollection'  => true,
      'changePartNumber'  => true,
      'changeEAN'         => true,
      'changeDisplaySku'  => true,
      'enableScrubbing'   => true,
      'brandCatalogId'    => null
  ];

  /**
   * @var White_Label_Client
   */
  private $wl_api_client;

  /**
   * @var White_Label_Nextgen_Api_Service
   */
  private $white_label_nextgen_api_service;

  public bool $whiteLabelNextgenApiInCuration;

    /**
   * White_Label_Api_Processor constructor.
   *
   * @param White_Label_Client              $whitelabel_api_client           the client API for the White Label service
   * @param White_Label_Nextgen_Api_Service $white_label_nextgen_api_service White Label NextGen API Service
   * @param ClientConfig                    $config                          Client Config
   */
  public function __construct(
      White_Label_Client $whitelabel_api_client,
      White_Label_Nextgen_Api_Service $white_label_nextgen_api_service,
      ClientConfig $config
  ) {
    $this->wl_api_client                   = $whitelabel_api_client;
    $this->white_label_nextgen_api_service = $white_label_nextgen_api_service;
    $this->wl_api_client->setConfig($config);
    $this->whiteLabelNextgenApiInCuration = true;
  }

  /**
   * @param Batch_Model $batch_model Batch
   *
   * @return bool
   * @throws Downstream_Fail_Exception
   * @throws White_Label_Batch_Already_Exists_Exception
   */
  public function send_downstream(Batch_Model $batch_model) : int {
      $response_code_Bad_Request = 400;

        if ($this->whiteLabelNextgenApiInCuration) {
            return $this->process_white_label_next_gen($batch_model, false);
        }
        throw new Downstream_Fail_Exception();
        return $response_code_Bad_Request;
  }

  /**
   * @param Batch_Model $batch_model Batch
   *
   * @return array<string, mixed>
   */
  private function construct_nextgen_request(Batch_Model $batch_model, bool $is_partial_wl = false) : array {
    $nextgen_request                  = [];
    $nextgen_request['configuration'] = static::CURATION_WL_CONFIGURATION;

    if ($is_partial_wl) {
      $nextgen_request['isPWL'] = true;
      $nextgen_request['whiteLabelBatchId'] = $batch_model->get_id();
    }else{
      $nextgen_request['whiteLabelBatchId'] = $batch_model->get_id();
    }
    foreach ($batch_model->get_skus() as $sku) {
      /** @var Verified_Batch_SKU $sku */
      $current_sku               = [
          'sku'                  => $sku->get_sku(),
          'targetManufacturerId' => $sku->get_target_ma_id(),
          'isExcluded'           => $sku->is_excluded_from_wl()
      ];
      $nextgen_request['skus'][] = $current_sku;
    }

    return $nextgen_request;
  }

  /**
   * @param Batch_Model $batch_model Batch
   *
   * @return bool
   * @throws Downstream_Fail_Exception
   * @throws White_Label_Batch_Already_Exists_Exception
   */
  protected function process_white_label_next_gen(Batch_Model $batch_model, bool $is_partial_wl) : int {
    $request = $this->construct_nextgen_request($batch_model, $is_partial_wl);
      $response_code_ok = 200;
      $response_code_Bad_Request = 400;

    try {

      $response = $this->white_label_nextgen_api_service->post_request_to_white_label_nextgen_api($request)  ;

      if ($response != $response_code_ok) {
        /** @var White_Label_Batch_Error_Response $response */
        foreach ($response->getErrorMessages() as $errorMessage) { /** @phpstan-ignore-line */
          if ($errorMessage === 'A white label batch with the given id already exists!') {
            throw new White_Label_Batch_Already_Exists_Exception($errorMessage);
          }
        }

        throw new Downstream_Fail_Exception(
            implode(',', $response->getErrorMessages()) /** @phpstan-ignore-line */
        );
      }

      return $response_code_ok;
    } catch (\RuntimeException $e) {
      $this->logger->error($e->getMessage());
      return $response_code_Bad_Request;
    }
  }
}
