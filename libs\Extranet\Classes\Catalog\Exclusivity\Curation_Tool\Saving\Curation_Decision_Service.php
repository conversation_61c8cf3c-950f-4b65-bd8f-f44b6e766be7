<?php

/**
 * PHP Version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Enum\Curation_Decision_Source;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO;
use WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO;
use Psr\Log\LoggerInterface;
use WF\Shared\Traits\Logging_Trait;


class Curation_Decision_Service
{
    use Logging_Trait;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO
     */
    private $dao;

    /**
     * @var \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_Postgres_DAO
     */
    private $dao_psql;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service
     */
    private $context_data_service;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader
     */
    private $curation_kit_children_loader;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Factory
     */
    private $factory;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service
     */
    private $region_service;

    private FeatureTogglesInterface $featureToggles;

    /**
     * @param \WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Decision_DAO $dao DAO
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service $context_data_service Context DataService
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Kit_Children_Loader $curation_kit_children_loader Curation Kit Children Loader
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision_Factory $factory Curation Decision Factory
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Utils\Region_Service $region_service Region Service
     * @param \Psr\Log\LoggerInterface|null $logger Logger
     */
    public function __construct(
        Curation_Decision_DAO           $dao,
        Curation_Decision_Postgres_DAO  $dao_psql,
        Context_Data_Service            $context_data_service,
        Curation_Kit_Children_Loader    $curation_kit_children_loader,
        Curation_Decision_Factory       $factory,
        Region_Service                  $region_service,
        FeatureTogglesInterface         $featureToggles,
        ?LoggerInterface                $logger = null
    )
    {
        $this->dao = $dao;
        $this->dao_psql = $dao_psql;
        $this->context_data_service = $context_data_service;
        $this->curation_kit_children_loader = $curation_kit_children_loader;
        $this->factory = $factory;
        $this->region_service = $region_service;
        $this->logger = $logger;
        $this->featureToggles = $featureToggles;
    }

    /**
     * @param int $batch_id Batch ID
     * @param array $skus SKUs
     * @param int $excluded_reason_id Excluded Reason ID
     * @param string $saved_at Saved At
     * @param int $employee_id Employee ID
     * @param Curation_Decision_Source $decision_source Decision Source
     *
     * @return void
     * @throws \Exception
     */
    public function save_excluded(
        int                      $batch_id,
        array                    $skus,
        int                      $excluded_reason_id,
        string                   $saved_at,
        int                      $employee_id,
        Curation_Decision_Source $decision_source
    )
    {
        $skus = $this->curation_kit_children_loader->replaceKitParents($skus, $batch_id);

        $decisions = [];
        foreach ($skus as $sku) {
            $this->log_info(
                'Creating excluded decision for SKU',
                [
                    'sku' => $sku,
                    'batch_id' => $batch_id,
                    'excluded_reason_id' => $excluded_reason_id,
                    'saved_at' => $saved_at,
                    'employee_id' => $employee_id,
                    'decision_source' => $decision_source
                ]
            );
            $decisions[] = $this->factory->create_excluded($batch_id, $sku, $excluded_reason_id, $saved_at, $employee_id, $decision_source);
        }

        $this->log_info(
            'Saving Curation decisions',
            [
                'batch_id' => $batch_id,
                'employee_id' => $employee_id
            ]
        );

        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            $this->dao_psql->save($decisions);
        } else {
            $this->dao->save($decisions);
        }

    }

    /**
     * @param int $batch_id Batch ID
     * @param array $skus SKUs
     * @param int $price_tier Price Tier
     * @param int $style_id Style ID
     * @param int $substyle_id Substyle ID
     * @param int $brand_id Brand ID
     * @param int|null $granular_style_id Granularstyle ID
     * @param string $saved_at Saved at
     * @param int $employee_id Employee ID
     * @param Curation_Decision_Source $decision_source Decision Source
     *
     * @return void
     * @throws \Exception
     */
    public function save_curated(
        int                      $batch_id,
        array                    $skus,
        int                      $price_tier,
        int                      $style_id,
        int                      $substyle_id,
        int                      $brand_id,
        ?int                     $granular_style_id,
        string                   $saved_at,
        int                      $employee_id,
        Curation_Decision_Source $decision_source
    )
    {
        $context_data_collection = $this->context_data_service->context_data_for_skus($batch_id, $skus);

        $skus = $this->curation_kit_children_loader->replaceKitParents($skus, $batch_id);

        $decisions = [];
        $context_exclusions = [];
        foreach ($skus as $sku) {
            $context_data = $context_data_collection->get_for_sku($sku);
            if ($context_data->is_fully_filled() && $context_data->has_same_manufacturer($brand_id)) {
                $context_exclusions[] = $sku;

                $this->log_info(
                    sprintf('Can not create curated decision for SKU "%s" and brand_id "%s"', $sku, $brand_id),
                    [
                        'context_data_is_fully_filled' => $context_data->is_fully_filled(),
                        'context_data_has_same_manufacturer' => $context_data->has_same_manufacturer($brand_id),
                        'brand_id' => $brand_id,
                        'sku' => $sku,
                    ]
                );


                continue;
            }

            $decisions[] = $this->factory->create_curated($batch_id, $sku, $price_tier, $style_id, $substyle_id, $brand_id, $granular_style_id, $saved_at, $employee_id, $decision_source);
            $this->log_info(
                sprintf('Created curated decision for SKU "%s"', $sku),
                [
                    'sku' => $sku,
                    'batch_id' => $batch_id,
                    'price_tier' => $price_tier,
                    'style_id' => $style_id,
                    'substyle_id' => $substyle_id,
                    'brand_id' => $brand_id,
                    'granular_style_id' => $granular_style_id,
                    'decision_source' => $decision_source,
                    'saved_at' => $saved_at,
                    'employee_id' => $employee_id,
                ]
            );

        }

        if (count($decisions) > 0) {
            $this->log_info(
                'Saving Curation decisions',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $employee_id,
                ]
            );

            if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
                $this->dao_psql->save($decisions);
            } else {
                $this->dao->save($decisions);
            }
        }

        if (count($context_exclusions) > 0) {
            $this->log_info(
                'Saving excluded SKUs',
                [
                    'batch_id' => $batch_id,
                    'employee_id' => $employee_id,
                ]
            );
            $this->save_excluded(
                $batch_id,
                $context_exclusions,
                Curation_Decision::EXCLUSION_REASON_CONTEXT_SKU,
                $saved_at,
                $employee_id,
                $decision_source
            );
        }
    }

    /**
     * @param int $batch_id Batch ID
     * @param string $sku Sku Value
     * @param string $saved_at Saved at date
     * @param int $employee_id Employee ID
     * @param Curation_Decision_Source $decision_source Decision Source
     *
     * @return void
     * @throws \Exception
     */
    public function save_as_kitsco(
        int                      $batch_id,
        string                   $sku,
        string                   $saved_at,
        int                      $employee_id,
        Curation_Decision_Source $decision_source
    )
    {
        $this->log_info(
            'Loading region',
            [
                'batch_id' => $batch_id,
                'employee_id' => $employee_id,
                'sku' => $sku
            ]
        );
        $region = $this->region_service->get_region($batch_id);

        $decisions = [];
        $decisions[] = $this->factory->create_kitsco($batch_id, $sku, $saved_at, $employee_id, $region->getId(), $decision_source);
        $this->log_info(
            sprintf('Created kitsko for batch_id "%s"', $batch_id),
            [
                'sku' => $sku,
                'batch_id' => $batch_id,
                'decision_source' => $decision_source,
                'saved_at' => $saved_at,
                'employee_id' => $employee_id,
                'region_id' => $region->getId()
            ]
        );

        $this->log_info(
            'Saving Curation decisions',
            [
                'batch_id' => $batch_id,
                'employee_id' => $employee_id,
            ]
        );

        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            $this->dao_psql->save($decisions);
        } else {
            $this->dao->save($decisions);
        }
    }

    /**
     * @param int $batch_id Batch ID
     * @param string $sku Sku Value
     * @param string $saved_at Saved at date
     * @param int $employee_id Employee ID
     * @param array $kit_parents Array of kit parents
     * @param Curation_Decision_Source $decision_source Curation Decision Source
     *
     * @return void
     * @throws \Exception
     */
    public function remove_kitsco(
        int                      $batch_id,
        string                   $sku,
        string                   $saved_at,
        int                      $employee_id,
        array                    $kit_parents,
        Curation_Decision_Source $decision_source
    )
    {
        $this->log_info(
            sprintf('Remove kitsko for batch_id "%s"', $batch_id),
            [
                'sku' => $sku,
                'batch_id' => $batch_id,
                'decision_source' => $decision_source,
                'saved_at' => $saved_at,
                'employee_id' => $employee_id,
                'kit_parents' => implode(",", $kit_parents)
            ]
        );

        $decisions = [];

        $decisions[] = $this->factory->create_empty($batch_id, $sku, $saved_at, $employee_id, $decision_source);

        $this->log_info(
            'Replacing kits with parents',
            [
                'batch_id' => $batch_id,
                'employee_id' => $employee_id,
            ]
        );
        // replace kit parents with their children that need to be cleared
        $children = $this->curation_kit_children_loader->replaceKitParents($kit_parents, $batch_id);

        foreach ($children as $childSku) {
            $decisions[] = $this->factory->create_empty($batch_id, $childSku, $saved_at, $employee_id, $decision_source);
        }

        $this->log_info(
            'Saving Curation decisions',
            [
                'batch_id' => $batch_id,
                'employee_id' => $employee_id,
            ]
        );

        if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
            $this->dao_psql->save($decisions);
        } else {
            $this->dao->save($decisions);
        }
    }
}
