<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\Request\ParamConverter;

use App\Application\DTO\AbstractCurationRequest;
use JsonException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use function is_subclass_of;
use function json_last_error_msg;
use function sprintf;

class CurationParamConverter implements ParamConverterInterface
{
    /**
     * Stores the object in the request.
     *
     * @param ParamConverter $configuration Contains the name, class and options of the object
     *
     * @return bool True if the object has been successfully set, else false
     */
    public function apply(Request $request, ParamConverter $configuration): bool
    {
        $name = $configuration->getName();

        if (null === $request->attributes->get($name, false)) {
            $configuration->setIsOptional(true);
        }

        $object = null;

        switch ($request->getContentType()) {
            case 'json':
                try {
                    $object = $configuration->getClass()::fromJSON($request->getContent(false));
                } catch (JsonException $exception) {
                    throw new BadRequestHttpException(sprintf('Invalid json body: "%s"', json_last_error_msg()));
                }

                break;
            case 'form':
                $object = $configuration->getClass()::fromArray($request->request->all());

                break;
            case '':
                $object = $configuration->getClass()::fromArray($request->query->all());
                break;
            default:
                if ($configuration->isOptional() === false) {
                    throw new BadRequestHttpException(
                        sprintf('Unsupported request format "%s"', $request->getContentType())
                    );
                }
        }

        $request->attributes->set($name, $object);

        return true;
    }

    /**
     * Checks if the object is supported.
     *
     * @return bool True if the object is supported, else false
     */
    public function supports(ParamConverter $configuration): bool
    {
        return is_subclass_of($configuration->getClass(), AbstractCurationRequest::class);
    }
}
