<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs;

class Context_Data {
  /**
   * @var int|null
   */
  private $manufacturer_id;

  /**
   * @var int|null
   */
  private $style_id;

  /**
   * @var int|null
   */
  private $substyle_id;

  /**
   * @var int|null
   */
  private $price_tier;

  /**
   * @param int $manufacturer_id the manufacturer identifier
   * @param int $style_id        the style identifier
   * @param int $substyle_id     the substyle identifier
   * @param int $price_tier      the price tier
   */
  public function __construct(int $manufacturer_id, int $style_id, int $substyle_id, int $price_tier) {
    $this->manufacturer_id = $manufacturer_id;
    $this->style_id        = $style_id;
    $this->substyle_id     = $substyle_id;
    $this->price_tier      = $price_tier;
  }

  /**
   * @return int
   */
  public function get_manufacturer_id() : int {
    return $this->manufacturer_id;
  }

  /**
   * @param int $manufacturer_id the manufacturer id
   *
   * @return bool
   */
  public function has_same_manufacturer(int $manufacturer_id) : bool {
    return $this->manufacturer_id === $manufacturer_id;
  }

  /**
   * @return int
   */
  public function get_style_id() : int {
    return $this->style_id;
  }

  /**
   * @return int
   */
  public function get_substyle_id() : int {
    return $this->substyle_id;
  }

  /**
   * @return int
   */
  public function get_price_tier() : int {
    return $this->price_tier;
  }

  /**
   * @return bool
   */
  public function is_fully_filled() : bool {
    return true;
  }
}