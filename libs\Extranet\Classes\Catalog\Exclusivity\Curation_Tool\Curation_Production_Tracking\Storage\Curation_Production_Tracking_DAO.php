<?php

declare(strict_types=1);

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage;

use App\Infrastructure\Connection\MerchConnection;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQL;
use App\Infrastructure\Helper\SQLBulkHelper;
use PDO;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Curation_Production_Tracking_Storage;
use WF\Shared\Models\Production_Tracking\V2\Stage_Model;

class Curation_Production_Tracking_DAO implements Curation_Production_Tracking_Storage {
  private ProductConnection $pdo_ptrw;

  private MerchConnection $pdo_merch;

  /**
   * @param ProductConnection $pdo_ptrw  C6
   * @param MerchConnection $pdo_merch 19
   */
  public function __construct(ProductConnection $pdo_ptrw, MerchConnection $pdo_merch) {
    $this->pdo_ptrw  = $pdo_ptrw;
    $this->pdo_merch = $pdo_merch;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return int[]
   */
  public function get_project_ids(int $batch_id) : array {
    $sql       = 'SELECT DISTINCT productionTracking2.ProjectID AS project_id
                  FROM csn_merch_tool.dbo.tblProductionTracking2 productionTracking2 WITH (NOLOCK)
                  JOIN csn_product.dbo.tblCurationRequest curationRequest WITH (NOLOCK) ON curationRequest.QuickformID = productionTracking2.QuickformID
                  JOIN csn_product.dbo.tblCurationRequestSKU curationRequestSku WITH (NOLOCK) ON curationRequestSku.RequestID = curationRequest.ID
                  JOIN csn_product.dbo.tblVerificationItem verificationItem WITH (NOLOCK) ON verificationItem.ViSKU = curationRequestSku.SKU
                  JOIN csn_product.dbo.tblCurationBatch curationBatch WITH (NOLOCK) ON curationBatch.ID = verificationItem.ViBatchID
                  WHERE curationBatch.ID = :batch_id and stage = :curation_stage
                  ';
    $statement = $this->pdo_ptrw->prepare($sql);
    $statement->bindValue('batch_id', $batch_id, PDO::PARAM_INT);
    $statement->bindValue('curation_stage', Stage_Model::E5_Extranet_Curation, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Failed get ProjectID');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param int[] $project_ids Batch ID
   *
   * @return void
   */
  public function update_stage_for_projects(array $project_ids) : void {
    $column_map = [
        'ProjectID' => SQL::int,
    ];

    $projects = [];

    foreach ($project_ids as $project_id) {
      $projects[] = ['ProjectID' => $project_id];
    }

    /**
     * mark request as completed
     * mark imported skus as imported
     * for excluded partial white label skus that were imported as excluded in white label, just set the batchid
     */
    $sql = SQLBulkHelper::getTempTableJsonSql($column_map, 'tmpProjects');

    $sql .= '
      UPDATE 
        productionTracking
      SET
        Stage = :stage
      FROM 
        csn_merch_tool.dbo.tblProductionTracking2 productionTracking
      JOIN #tmpProjects projects ON productionTracking.ProjectID = projects.ProjectID
      WHERE productionTracking.Stage = :curation_stage';

    $statement = $this->pdo_merch->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, \json_encode($projects), PDO::PARAM_STR);
    $statement->bindValue('stage', Stage_Model::E5_Extranet_White_Label, PDO::PARAM_STR);
    $statement->bindValue('curation_stage', Stage_Model::E5_Extranet_Curation, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot update project stage');
    }
  }
}
