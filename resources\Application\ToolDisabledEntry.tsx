import React from 'react';
import ErrorBoundary from 'react-error-boundary';
import Translation from '@wayfair/translation';
import {Box, PageContainer} from '@wayfair/homebase-extranet';
import ErrorScreen from './ErrorScreen';

const ToolDisabledEntry = () => {
  // eslint-disable-next-line new-cap
  document.title = Translation({
    msgid: 'toolDisabledTitle',
  });

  return (
    <Box px={45}>
      <PageContainer
        pageTitle={<Translation msgid="toolDisabledTitle" />}
        actions={<PageContainer.HelpLink href="#" />}
      >
        <ErrorBoundary FallbackComponent={ErrorScreen}>
          <p>
            <Translation msgid="toolDisabledMessage" />
          </p>
        </ErrorBoundary>
      </PageContainer>
    </Box>
  );
};

export default ToolDisabledEntry;
