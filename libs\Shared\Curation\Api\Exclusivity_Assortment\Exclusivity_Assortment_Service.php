<?php
declare(strict_types=1);
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Curation\Api\Exclusivity_Assortment;

use WF\Shared\Curation\Api\Api_Exclusivity_Assortment;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Enum\Exclusivity_Assortment_Candidate_Status;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Enum\Exclusivity_Assortment_Investment_Status;
use WF\Shared\Curation\Api\Exclusivity_Assortment\Enum\Exclusivity_Assortment_Whitelabel_Status;

class Exclusivity_Assortment_Service implements Api_Exclusivity_Assortment {

  /**
   * @var Exclusivity_Assortment_Loader_Interface
   */
  private $storage;

  /**
   * Exclusivity_Assortment_Service constructor.
   *
   * @param Exclusivity_Assortment_Loader_Interface $loader Storage
   */
  public function __construct(Exclusivity_Assortment_Loader_Interface $loader) {
    $this->storage = $loader;
  }

  /**
   * @param Exclusivity_Assortment_Filter_Request $filter Filter
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_sku_list(Exclusivity_Assortment_Filter_Request $filter) : array {
    $filter_dto = Exclusivity_Assortment_Filter_Request_DTO::from_filter_request($filter);
    $filter_dto->set_status(Exclusivity_Assortment_Candidate_Status::approved_by_qa()->value());

    return $this->storage->load_sku_list($filter_dto);
  }

  /**
   * @param Exclusivity_Assortment_Filter_Request $filter Filter
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_for_investment_sku_list(Exclusivity_Assortment_Filter_Request $filter) : array {
    $filter_dto = Exclusivity_Assortment_Filter_Request_DTO::from_filter_request($filter);
    $filter_dto->set_investment_status(Exclusivity_Assortment_Investment_Status::approved()->value());

    return $this->storage->load_sku_list($filter_dto);
  }

  /**
   * @param string[] $skus List of SKUs to check
   *
   * @return Exclusivity_Assortment_Candidate_DTO[]
   */
  public function fetch_approved_for_whitelabel_sku_list(array $skus) : array {
    $filter_dto = new Exclusivity_Assortment_Filter_Request_DTO();
    $filter_dto->set_with_rebrand_project_only(true);
    $filter_dto->set_selected_skus($skus);
    $filter_dto->set_whitelabel_status(Exclusivity_Assortment_Whitelabel_Status::imported()->value());

    return $this->storage->load_sku_list($filter_dto);
  }
}
