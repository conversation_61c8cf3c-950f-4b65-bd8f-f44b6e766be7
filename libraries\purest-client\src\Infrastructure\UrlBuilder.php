<?php

declare(strict_types=1);

namespace WF\BrandWorkflows\PuREST\Infrastructure;

use WF\BrandWorkflows\PuREST\Contract\UrlBuilderInterface as PuRESTUrlBuilderInterface;
use WF\BrandWorkflows\PuREST\Infrastructure\Exception\PuRESTUrlBuilderException;

class UrlBuilder implements PuRESTUrlBuilderInterface
{
    private string $puRESTHost;

    /**
     * UrlBuilder constructor.
     *
     * @param string $puRESTHost PuREST host
     */
    public function __construct(string $puRESTHost)
    {
        $this->puRESTHost = $puRESTHost;
    }

    /**
     * @param string $service Service name
     * @param string $resource Resource name
     *
     * @return string
     * @throws PuRESTUrlBuilderException
     */
    public function buildUrl(string $service, string $resource): string
    {
        if (empty($this->puRESTHost)) {
            throw new PuRESTUrlBuilderException('PuREST host is empty');
        }

        return sprintf("%s/purest/%s/%s", $this->puRESTHost, $service, $resource);
    }

}
