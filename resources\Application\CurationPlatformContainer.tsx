import React, {Component} from 'react';
import '@wayfair/extranet-layout/dist/index.scss';
import HomebaseProvider, {LOCALES} from '@wayfair/homebase-provider';
import Layout from '@wayfair/extranet-layout';
import {createWretch} from '@wayfair/framework-request-utils';
import './CurationPlatformContainer.scss';
import {ServiceContext} from './ServiceContext';
import {CDN_URL, IMAGE_KEY} from "./Constants";

const transactionID = Buffer.from(Math.random().toString(), 'utf8').toString('base64');
// Need to set transaction ID for wretch
window.__transactionID__ = transactionID;
const wfWretch = createWretch({
  txid: transactionID,
  canClearCache: true,
});

type Props = {
  component: Component;
  cdnUrl: string;
  imageKey: string;
};

const CurationPlatformContainer = (props: Props | any) => {
  const EntryComponent = props.component;

  return (
    <HomebaseProvider
      locale={LOCALES.en}
      activeTheme="extranet"
      imageKey={IMAGE_KEY}
      cdnUrl={CDN_URL}
    >
      <ServiceContext.Provider value={{wretch: wfWretch}}>
        <Layout
          wretch={wfWretch}
          enableAsyncMode
        >
          <EntryComponent {...props} />
        </Layout>
      </ServiceContext.Provider>
    </HomebaseProvider>
  );
};

export default CurationPlatformContainer;
