<?php

declare(strict_types=1);

namespace App\Application\View;

use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project;
use WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator;
use WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data;

use function array_merge;
use function array_unique;
use function sort;

class IndexView extends BaseView
{
    private const COMPONENT_KEY = 'CurationPage';

    private ?Completion_Batch_Data $batch_data;

    private ?Curator $batch_curator;

    private Config_Data $config;

    private int $batchId;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    private array $sections;

    /**
     * @var \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
     */
    private array $suggestedStyleRejectionReasons;

    private ?Rebrand_Project $rebrandProject;

    /**
     * @var string[]
     */
    private array $warnings;

    private bool $isAssortmentWorkflowOffshoreUser;


    /**
     * Index_View constructor.
     *
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Entities\Completion_Batch_Data $batch_data Batch Data
     * @param \WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch\Curator $batch_curator Batch Curator
     * @param \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data $config Config
     * @param int $batchId Batch ID
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[] $sections Sections
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[] $suggestedStyleRejectionReasons suggested reasons pick list
     * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Rebrand_Project|null $rebrandProject Rebrand_Project
     * @param string[] $warnings Warnings
     * @param bool $isAssortmentWorkflowOffshoreUser Is Assortment Workflow Offshore User
     */
    public function __construct(
        ?Completion_Batch_Data $batch_data,
        ?Curator $batch_curator,
        Config_Data $config,
        int $batchId,
        array $sections,
        array $suggestedStyleRejectionReasons,
        Rebrand_Project $rebrandProject = null,
        array $warnings = [],
        bool $isAssortmentWorkflowOffshoreUser = false
    ) {
        parent::__construct();
        $this->batch_data = $batch_data;
        $this->batch_curator = $batch_curator;
        $this->config = $config;
        $this->batchId = $batchId;
        $this->sections = $sections;
        $this->suggestedStyleRejectionReasons = $suggestedStyleRejectionReasons;
        $this->rebrandProject = $rebrandProject;
        $this->warnings = $warnings;
        $this->isAssortmentWorkflowOffshoreUser = $isAssortmentWorkflowOffshoreUser;
    }

    /**
     * Gets the value passed to the bootstrap script as 'key'. This can be used
     * by apps to determine which component to load.
     *
     * @return string component key
     */
    protected function componentKey(): string
    {
        return self::COMPONENT_KEY;
    }

    /**
     * @return \WF\Extranet\Mustache\Catalog\Exclusivity\Curation_Tool\Config_Data
     */
    public function curationConfig(): Config_Data
    {
        return $this->config;
    }

    /**
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section[]
     */
    public function sections(): array
    {
        return $this->sections;
    }

    /**
     * @return string[]
     */
    public function suppliers(): array
    {
        $suppliers = [];

        foreach ($this->sections as $section) {
            foreach ($section->get_curation_items() as $item) {
                $suppliers[] = $item->get_suppliers();
            }
        }

        $suppliers = array_unique(array_merge(...$suppliers));
        sort($suppliers);

        return $suppliers;
    }

    /**
     * @return int
     */
    public function batchId(): int
    {
        return $this->batchId;
    }

    /**
     * @return array
     */
    public function batchData(): array
    {
        if ($this->batch_data === null) {
            return [];
        }
        return [
            'curator' => $this->batch_curator !== null ? $this->batch_curator->get_full_name() : 'Not Assigned',
            'status' => $this->batch_data->getStatus(),
            'process_type' => $this->batch_data->getProcessTypeName(),
            'created_at' => $this->batch_data->getCreatedAt() !== null ? $this->batch_data->getCreatedAt()->format('M d, Y \a\t g:iA T') : 'Not Known'
        ];
    }

    /**
     * @return bool
     */
    public function isAssortmentWorkflowOffshoreUser(): bool
    {
        return $this->isAssortmentWorkflowOffshoreUser;
    }


    /**
     * @return null|Rebrand_Project
     */
    public function rebrandProject(): ?Rebrand_Project
    {
        return $this->rebrandProject;
    }

    /**
     * @return string[]
     */
    public function warnings(): array
    {
        return $this->warnings;
    }

    /**
     * @return \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Suggested_Style_Rejection_Reason[]
     */
    public function suggestedStyleRejectionReasons(): array
    {
        return $this->suggestedStyleRejectionReasons;
    }
}
