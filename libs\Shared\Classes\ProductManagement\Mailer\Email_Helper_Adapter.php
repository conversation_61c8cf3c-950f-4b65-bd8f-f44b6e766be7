<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */
namespace WF\Shared\Classes\ProductManagement\Mailer;

use WF\Shared\Helpers\Email_Helper;

class Email_Helper_Adapter implements Mailer_Interface {

  private Email_Helper $emailHelper;

  public function __construct(Email_Helper $emailHelper) {
    $this->emailHelper = $emailHelper;
  }

  /**
   * Creates new message which is accepted to send by current mailer
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message
   */
  public function create_message() : Mail_Message {
    return new Mail_Message();
  }

  /**
   * Sends out given mail message
   *
   * @param \WF\Shared\Classes\ProductManagement\Mailer\Mail_Message $message message to send
   *
   * @return \WF\Shared\Classes\ProductManagement\Mailer\Mail_Result
   */
  public function send(Mail_Message $message) {
      $from = $this->convert_email_structure($message->get_sender());
      $to = $this->convert_recipients($message->get_recipients());

      $attachment_content = $filename = $mime = '';
      $attachments = $message->get_attachments();
      if (count($attachments) > 0) {
          list($attachment_content, $filename, $mime, $encoding, $disposition) = $attachments[0];
      }

      try {
          $requid = $this->emailHelper->send_email(
              $email_id = '',
              $customer_id = '',
              $message_id = '',
              $order_id = '',
              $from,
              $to,
              $cc = [],
              $bcc = [],
              $message->get_subject(),
              $message->get_html_body(),
              $message->get_text_body(),
              $kana = '',
              $kana_id = '',
              $file_path = '',
              $attachment_content,
              $filename,
              $mime,
              $encoding = '',
              $scheduled = '',
              $send_in_dev = true,
              $log = true,
              $from
          );

          return new Mail_Result(true, 'mmreqid: ' . $requid);
      } catch (\Exception $e) {
          return new Mail_Result(false, 'Error sending email: ' . $e->getMessage());
      }
  }

  /**
   * Converts email structure to the one, which is accepted by email helper
   *
   * @param array $subject subject
   *
   * @return array
   */
  private function convert_email_structure(array $subject) : array {
    list($email, $name) = $subject;

    return ['email' => $email, 'name' => $name];
  }

  /**
   * Converts recipients list to the structure, which is accepted by email helper
   *
   * @param array $recipients recipients
   *
   * @return array
   */
  private function convert_recipients(array $recipients) : array {
    $result = [];
    foreach ($recipients as $recipient) {
      $result[] = $this->convert_email_structure($recipient);
    }

    return $result;
  }
}
