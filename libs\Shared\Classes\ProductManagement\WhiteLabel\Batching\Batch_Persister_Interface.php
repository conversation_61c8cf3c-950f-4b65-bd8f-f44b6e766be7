<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\WhiteLabel\Batching;

use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch;

interface Batch_Persister_Interface {
  /**
   * Persists the given batch to the database, so it receives the batch id
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch
   *
   * @return void
   */
  public function persist_batch(Batch $batch);

  /**
   * Cleans up the persisted batch from a database. It is used when something had failed and db needs a cleanup so no batches are left hanging.
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch batch
   *
   * @return void
   */
  public function attempt_batch_cleanup(Batch $batch);
}
