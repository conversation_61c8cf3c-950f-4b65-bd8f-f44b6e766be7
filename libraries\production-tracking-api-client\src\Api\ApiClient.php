<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Wayfair LLC - All rights reserved
 */

declare(strict_types=1);

namespace WF\Curation\ProductionTrackingApi\Api;

use WF\Curation\ProductionTrackingApi\ClientConfig;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use Psr\Http\Message\ResponseInterface;

class ApiClient
{

    private const API_ENDPOINT = 'v1/graphql';
    private const TIMEOUT = 60;

    /**
     * @var ClientConfig
     */
    private $config;

    /**
     * ApiClient constructor.
     */
    public function __construct(ClientConfig $config)
    {
        $this->config = $config;
    }

    public function sendRequest(string $token, string $query): ?ResponseInterface
    {
        $httpClient = $this->config->getApiHttpClient();
        $logger = $this->config->getLogger();

        $headers = [
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
        ];

        $body = [
            'query' => $query,
            'clientId' => $this->config->getClientId()
        ];

        $request = new Request('POST', self::API_ENDPOINT, $headers, \GuzzleHttp\json_encode($body));

        $options = [
            'connection_timeout' => self::TIMEOUT,
            'http_errors' => true,
        ];

        $responseContext = [];
        try {
            $response = $httpClient->send($request, $options);

            /** @phpstan-ignore-next-line */
            if ($response->getBody() !== null && mb_strlen($response->getBody()->getContents()) > 0) {
                $responseContext['responseBody'] = \json_decode($response->getBody()->getContents(), true);
                $responseContext['statusCode']   = $response->getStatusCode();
            }

            $context = [
                'request' => \json_encode($body),
                'response' => \json_encode($responseContext),
            ];

            $logger->info("POST " . self::API_ENDPOINT, $context);

            return $response;
        } catch (RequestException $e) {
            $context = [
                'request' => \json_encode($body),
                'response' => $e->getMessage(),
            ];

            $logger->error('POST ' . self::API_ENDPOINT, $context);

            return null;
        }
    }
}
