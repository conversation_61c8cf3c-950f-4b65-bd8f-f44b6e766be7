<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Wayfair LLC - All rights reserved
 */

namespace App\Application\EventListener;

use App\Application\Service\ViewRenderer;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ViewEvent;
use WF\Frontend\Webpack\Webpack_React_View;

class WebpackLayoutViewListener
{
    private ViewRenderer $renderingService;

    /**
     * @param ViewRenderer $renderingService
     */
    public function __construct(ViewRenderer $renderingService)
    {
        $this->renderingService = $renderingService;
    }

    public function onKernelView(ViewEvent $event): void
    {
        $request = $event->getRequest();
        $view = $event->getControllerResult();

        if (!$view instanceof Webpack_React_View) {
            return;
        }

        $event->setResponse(
            new Response(
                $this->renderingService->render(
                    $view,
                    $request->query->get('webpack_public_path_root') ?? '/',
                    $request->server->get('DOCUMENT_ROOT')
                )
            )
        );
    }
}
