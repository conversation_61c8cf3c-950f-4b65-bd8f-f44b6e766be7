<?php

declare(strict_types=1);

namespace App\Infrastructure\Helper;

use Psr\Log\LoggerInterface;
use Psr\Log\LoggerTrait as PsrLoggerTrait;

/**
 * @property LoggerInterface $logger
 */
trait LoggerTrait
{
    use PsrLoggerTrait;

    /**
     * Logs with an arbitrary level.
     *
     * @param string $level
     * @param string $message
     * @param array $context
     *
     * @return void
     */
    public function log($level, $message, array $context = []): void
    {
        // @phpstan-ignore-next-line
        if ($this->logger instanceof LoggerInterface) {
            $this->logger->log($level, $message, $context);
        }
    }
}
