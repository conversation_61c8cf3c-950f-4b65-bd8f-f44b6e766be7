<?php
/**
 * The DAO for the Batch Verification tool
 *
 * PHP version 5
 *
 * <AUTHOR> <m<PERSON><PERSON><PERSON>@wayfair.com>
 * @copyright 2016 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\Product\Media;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQL;
use App\Infrastructure\Helper\SQLBulkHelper;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use WF\Shared\Models\Product\Media\Batch_Verification_Item_Model;
use PDO;

class Batch_Verification_MSSQL_DAO implements LoggerAwareInterface {
  use LoggerTrait;
  use LoggerAwareTrait;
  
  private ProductConnection $pdo_agnostic;

  /**
   * Batch_Verification_MSSQL_DAO constructor.
   *
   * @param ProductConnection $pdo_agnostic PDO Agnostic
   */
  public function __construct(ProductConnection $pdo_agnostic) {
    $this->pdo_agnostic = $pdo_agnostic;
  }

  /**
   * @param int   $reason_id Reason id
   * @param int[] $item_ids  Verification ids
   *
   * @return void
   * @throws \Exception
   */
  public function update_items_exclude_reason(int $reason_id, array $item_ids) {
    $sql = 'UPDATE tblVerificationItem WITH(ROWLOCK) SET ViExcludedReasonId = :reason_id WHERE ViId IN (' . $this->pdo_agnostic->paramsForList(count($item_ids), 'item_id_list', SQL::int) . ')';

    $statement = $this->pdo_agnostic->prepare($sql);

    $statement->bindValue(':reason_id', $reason_id, PDO::PARAM_INT);
    $statement->bindValuesList(':item_id_list', $item_ids, SQL::int);

    if (!$statement->execute()) {
      throw new ExecutionException(sprintf('Failed to update exclude reason: %s', implode(', ', $statement->errorInfo()) . $sql));
    }
  }
}
