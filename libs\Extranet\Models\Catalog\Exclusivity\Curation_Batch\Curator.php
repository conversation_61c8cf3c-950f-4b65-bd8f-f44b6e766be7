<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Models\Catalog\Exclusivity\Curation_Batch;

class Curator implements \JsonSerializable {

  /**
   * @var int
   */
  private $employee_id = 0;

  /**
   * @var string
   */
  private $first_name = '';

  /**
   * @var string
   */
  private $last_name = '';

  /**
   * @var string
   */
  private $email_address = '';

  /**
   * @var string
   */
  private $group_name = '';

  /**
   * @var int
   */
  private $sku_count = 0;

  /**
   * @return int
   */
  public function get_employee_id() : int {
    return $this->employee_id;
  }

  /**
   * @return string
   */
  public function get_group_name() : string {
    return $this->group_name;
  }

  /**
   * @return string
   */
  public function get_first_name() : string {
    return $this->first_name;
  }

  /**
   * @return string
   */
  public function get_last_name() : string {
    return $this->last_name;
  }

  /**
   * @return string
   */
  public function get_email_address() : string {
    return $this->email_address;
  }

  /**
   * @return string
   */
  public function get_full_name() : string {
    return sprintf('%s %s', $this->first_name, $this->last_name);
  }

  /**
   * @return int
   */
  public function get_sku_count() : int {
    return $this->sku_count;
  }

  /**
   * @return array
   */
  #[\ReturnTypeWillChange]
  public function jsonSerialize() {
    return [
        'employee_id' => $this->employee_id,
        'first_name' => $this->first_name,
        'last_name' => $this->last_name,
        'group_name' => $this->group_name,
        'email_address' => $this->email_address,
        'sku_count' => $this->sku_count
    ];
  }
}
