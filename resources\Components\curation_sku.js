/**
 * Display curation table
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import Translation from '@wayfair/translation';
import PropTypes from 'prop-types';
import CurationSkuCode from './curation_sku_code';
import CurationSkuImage from './curation_sku_image';
import CurationSkuSupplier from './curation_sku_supplier';
import CurationSkuPrice from './curation_sku_price';
import CurationSkuSave from './curation_sku_save';
import CurationSkuManufacturer from './curation_sku_manufacturer';
import CurationSkuName from './curation_sku_name';
import CurationSkuDecision from './curation_sku_decision';
import './curation_sku.scss';
import CurationToolShapes from './curation_tool_shapes';
import {Grid, Column, WIDTHS, FLEX_KEYWORDS, Text, TEXT_VARIATIONS, TEXT_ALIGNMENTS} from '@wayfair/homebase-extranet';
import {Loading} from "@homebase/core";

class CurationSku extends React.Component {
  static propTypes = {
    curationConfig: CurationToolShapes.curationConfigShape.isRequired,
    curationItem: CurationToolShapes.curationItemShape.isRequired,
    isChanged: PropTypes.bool,
    isSaved: PropTypes.bool,
    isSaving: PropTypes.bool,
    isSelected: PropTypes.bool,
    onSelectionChange: PropTypes.func,
    onCurationItemDecisionChange: PropTypes.func,
    onSaveClick: PropTypes.func,
    isAssortmentWorkflowOffshoreUser: PropTypes.bool.isRequired,
    suggestedStyleRejectionReasons: PropTypes.arrayOf(
      CurationToolShapes.reasonShape
    ),
  };

  static defaultProps = {
    isSelected: false,
    isChanged: false,
    isSaved: false,
    isSaving: false,
    onSelectionChange() {},
    onCurationItemDecisionChange() {},
    onSaveClick() {},
    suggestedStyleRejectionReasons: [],
  };

  shouldComponentUpdate(nextProps) {
    if ((nextProps.isSaved !== this.props.isSaved)  || (this.props.isSaving !== nextProps.isSaving)){
      return true;
    }
    if (
      this.isDecisionChanged(
        this.props.curationItem.decision,
        nextProps.curationItem.decision
      )
    ) {
      return true;
    }

    if (
      this.props.curationItem.relatedKits !== nextProps.curationItem.relatedKits
    ) {
      return true;
    }

    if (this.props.isSelected !== nextProps.isSelected) {
      return true;
    }

    if (
      this.props.curationItem.savedAt !== nextProps.curationItem.savedAt ||
      this.props.curationItem.savedBy !== nextProps.curationItem.savedBy ||
      this.props.curationItem.rejectedAt !==
        nextProps.curationItem.rejectedAt ||
      this.props.curationItem.rejectedBy !==
        nextProps.curationItem.rejectedBy ||
      this.props.curationItem.rejectedNote !==
        nextProps.curationItem.rejectedNote
    ) {
      return true;
    }

    return false;
  }

  isDecisionChanged(currentDecision, nextDecision) {
    return (
      currentDecision.exclusionReasonId !== nextDecision.exclusionReasonId ||
      currentDecision.priceTier !== nextDecision.priceTier ||
      currentDecision.styleId !== nextDecision.styleId ||
      currentDecision.substyleId !== nextDecision.substyleId ||
      currentDecision.manufacturerId !== nextDecision.manufacturerId ||
      currentDecision.suggestedStyleRejectionReasonId !==
        nextDecision.suggestedStyleRejectionReasonId
    );
  }

  isStyleSelectedReasonRequired = () => {
    if (this.isExcludedFromWhiteLabeling()) {
      return false;
    }

    if (this.hasNoSuggestedStylesAndSubstyles()) {
      return false;
    }

    if (
      !this.isSuggestedStyleAndSubstyleSelected() &&
      !this.hasStyleRejectionReason()
    ) {
      return true;
    }

    return false;
  };

  hasNoSuggestedStylesAndSubstyles = () => {
    return (
      this.props.curationItem.suggestedStyles.length === 0 &&
      this.props.curationItem.suggestedSubstyles.length === 0
    );
  };

  isSuggestedStyleAndSubstyleSelected = () => {
    return (
      this.props.curationItem.suggestedStyles.includes(
        this.props.curationItem.decision.styleId
      ) &&
      this.props.curationItem.suggestedSubstyles.includes(
        this.props.curationItem.decision.substyleId
      )
    );
  };

  hasStyleRejectionReason = () => {
    return (
      null != this.props.curationItem.decision.suggestedStyleRejectionReasonId
    );
  };

  isExcludedFromWhiteLabeling = () => {
    return null != this.props.curationItem.decision.exclusionReasonId;
  };

  isSaveEnabled = () => {
    const isSaveEnabled =
      this.props.isChanged &&
      (this.props.curationItem.decision.exclusionReasonId > 0 ||
        this.props.curationItem.decision.manufacturerId > 0) &&
      !this.isStyleSelectedReasonRequired();

    return isSaveEnabled;
  };

  isEditable = () => {
    return !this.props.curationItem.readonly;
  };

  shouldDecisionBeDisabled = () =>
    this.props.isAssortmentWorkflowOffshoreUser &&
    this.props.curationItem.shouldMoveToHeaderBrand;

  render() {
    return (
      <div className="curation_sku">
        <Grid>
          <Column size={WIDTHS.WIDTH_1} flexDirection={FLEX_KEYWORDS.COLUMN}>
            <CurationSkuCode
              sku={this.props.curationItem.sku}
              relatedKits={this.props.curationItem.relatedKits}
              isPredictedWinner={this.props.curationItem.isPredictedWinner}
              isEditable={this.isEditable()}
              isSelected={this.props.isSelected}
              onSelectionChange={this.props.onSelectionChange}
              canHaveDecision={!this.shouldDecisionBeDisabled()}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1} flexDirection={FLEX_KEYWORDS.COLUMN}>
            <CurationSkuName
              name={this.props.curationItem.name}
              url={this.props.curationItem.url}
            />
            <Text
              align={TEXT_ALIGNMENTS.CENTER}
              variation={TEXT_VARIATIONS.BOLD}
              is="strong"
              mt="large"
            >
              <Translation msgid="CurationTool.BrandCatalogName" />
            </Text>
            <Text align={TEXT_ALIGNMENTS.CENTER}>
              {this.props.curationItem.brandCatalogName}
            </Text>
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuManufacturer
              name={this.props.curationItem.manufacturer}
              brwid={this.props.curationItem.manufacturerBrwId}
              brandType={this.props.curationItem.brandType}
              brandName={this.props.curationItem.brandName}
              isHoldoutManufacturer={
                this.props.curationItem.isHoldoutManufacturer
              }
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <Text align={TEXT_ALIGNMENTS.CENTER}>{this.props.curationItem.class}</Text>
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuSupplier
              names={this.props.curationItem.suppliers}
              isCanadian={this.props.curationItem.isCanadianSupplier}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            <CurationSkuPrice
              sku={this.props.curationItem.sku}
              price={this.props.curationItem.price}
              priceOptionsCount={this.props.curationItem.priceOptionsCount}
            />
          </Column>
          <Column size={WIDTHS.WIDTH_2}>
            <CurationSkuImage
              image={this.props.curationItem.image}
              sku={this.props.curationItem.sku}
            />
          </Column>
          {this.props.isSaved}
          <Column size={WIDTHS.WIDTH_3}>
            {this.isEditable() && (
              <CurationSkuDecision
                isRecentlyCloned={this.props.curationItem.isRecentlyCloned}
                lastCloneDate={this.props.curationItem.lastCloneDate}
                canMakeDecision={!this.shouldDecisionBeDisabled()}
                curationConfig={this.props.curationConfig}
                decision={this.props.curationItem.decision}
                isWrongContinent={this.props.curationItem.isWrongContinent}
                isDefaultStyleForDsModal={true}
                suggestedStyles={this.props.curationItem.suggestedStyles}
                suggestedSubstyles={this.props.curationItem.suggestedSubstyles}
                suggestedStyleRejectionReasons={
                  this.props.suggestedStyleRejectionReasons
                }
                onDecisionChange={decision => {
                  this.props.onCurationItemDecisionChange(
                    this.props.curationItem.sku,
                    decision
                  );
                }}
                shouldMoveToHeaderBrand={
                  this.props.curationItem.shouldMoveToHeaderBrand
                }
                shouldMoveToTailBrand={
                  this.props.curationItem.shouldMoveToTailBrand
                }
                isAssortmentWorkflowOffshoreUser={
                  this.props.isAssortmentWorkflowOffshoreUser
                }
              />
            )}
          </Column>
          <Column size={WIDTHS.WIDTH_1}>
            {this.isEditable() && (
              <CurationSkuSave
                isSaveEnabled={this.isSaveEnabled()}
                isSaving={this.props.isSaving}
                onSaveClick={() => {
                  this.props.onSaveClick(this.props.curationItem.sku)
                  }
                }
                savedAt={this.props.curationItem.savedAt}
                savedBy={this.props.curationItem.savedBy}
                rejectedBy={this.props.curationItem.rejectedBy}
                rejectedAt={this.props.curationItem.rejectedAt}
                rejectedNote={this.props.curationItem.rejectedNote}
              />
            )}
          </Column>
        </Grid>
      </div>
    );
  }
}

export default CurationSku;
