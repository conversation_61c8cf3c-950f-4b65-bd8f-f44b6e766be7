<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use PDO;
use WF\Shared\Helpers\SQL;
use Psr\Log\LoggerInterface;
use WF\Shared\Models\ProductManagement\Curation\Curation_Item_Model;
use WF\Shared\Traits\Logging_Trait;

class Curation_Decision_DAO {
  use Logging_Trait;

  private ProductConnection $pdo;

  /**
   * Curation_Decision_DAO constructor.
   *
   * @param ProductConnection $pdo    pdo to use
   * @param \Psr\Log\LoggerInterface      $logger Logger
   */
  public function __construct(ProductConnection $pdo, ?LoggerInterface $logger = null) {
    $this->pdo    = $pdo;
    $this->logger = $logger;
  }

  /**
   * @param \WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Saving\Curation_Decision[] $items Items to save
   *
   * @return void
   * @throws \Exception
   */
  public function save(array $items) {
    $this->log_info('Saving Curation decision', ['total' => count($items)]);

    $column_map = [
        'ViBatchID'              => 'INT NOT NULL',
        'ViSKU'                  => 'NVARCHAR(8) NOT NULL',
        'ViExcludedReasonID'     => 'INT NULL',
        'ViFinalStyleID'         => 'INT NULL',
        'ViFinalSubStyleID'      => 'INT NULL',
        'ViFinalBrandMaID'       => 'INT NULL',
        'ViLockedEmID'           => 'INT NULL',
        'ViLockedDate'           => 'DATETIMEOFFSET(4) NULL',
        'ViPriceTierOverride'    => 'INT NULL',
        'ViSourceID'             => 'INT NOT NULL',
        'ViIsKitsco'             => 'BIT NOT NULL',
        'ViIsKitComponent'       => 'BIT NOT NULL DEFAULT(0)',
        'ViContextXnID'          => 'INT NULL',
        'ViQAStatusID'           => 'INT NOT NULL',
        'ViDecisionSourceID'     => 'INT NOT NULL',
        'ViFinalGranularStyleID' => 'SMALLINT NULL'
    ];

    $sql = SQLBulkHelper::getTempTableJsonSql(
      $column_map,
      'VerificationItems',
        'verification_items_json'
    );
    $sql .= '
      UPDATE vis
      SET vis.ViIsKitComponent = 1
      FROM #VerificationItems vis
      JOIN csn_product.dbo.vwExclusivityKitComposition ekc WITH(NOLOCK) ON ekc.ChildSKU = vis.ViSKU
      WHERE
        EXISTS(
          ' . $this->get_active_product_query('vis.ViSKU') . '
        )
        AND EXISTS(
          ' . $this->get_active_product_query('ekc.ParentSKU') . '
        )

      DECLARE @ViID TABLE(ID INT);

      MERGE tblVerificationItem AS db
      USING #VerificationItems AS model
         ON (model.ViBatchID = db.ViBatchID AND model.ViSKU = db.ViSKU)
      WHEN MATCHED
        THEN UPDATE SET
          ViExcludedReasonID      = model.ViExcludedReasonID,
          ViFinalStyleID          = model.ViFinalStyleID,
          ViFinalSubStyleID       = model.ViFinalSubStyleID,
          ViFinalBrandMaID        = model.ViFinalBrandMaID,
          ViLockedEmID            = model.ViLockedEmID,
          ViLockedDate            = model.ViLockedDate,
          ViPriceTierOverride     = model.ViPriceTierOverride,
          ViIsKitsco              = model.ViIsKitsco,
          ViQAStatusID            = model.ViQAStatusID,
          ViDecisionSourceID      = model.ViDecisionSourceID,
          ViFinalGranularStyleID  = model.ViFinalGranularStyleID
      WHEN NOT MATCHED BY TARGET
        THEN INSERT (ViBatchID,
                     ViSKU,
                     ViExcludedReasonID,
                     ViFinalStyleID,
                     ViFinalSubStyleID,
                     ViFinalBrandMaID,
                     ViLockedEmID,
                     ViLockedDate,
                     ViPriceTierOverride,
                     ViSourceID,
                     ViIsKitsco,
                     ViIsKitComponent,
                     ViContextXnID,
                     ViQAStatusID,
                     ViDecisionSourceID,
                     ViFinalGranularStyleID)
        VALUES (model.ViBatchID,
                model.ViSKU,
                model.ViExcludedReasonID,
                model.ViFinalStyleID,
                model.ViFinalSubStyleID,
                model.ViFinalBrandMaID,
                model.ViLockedEmID,
                model.ViLockedDate,
                model.ViPriceTierOverride,
                model.ViSourceID,
                model.ViIsKitsco,
                model.ViIsKitComponent,
                model.ViContextXnID,
                model.ViQAStatusID,
                model.ViDecisionSourceID,
                model.ViFinalGranularStyleID);
    ';

    $statement = $this->pdo->prepare($sql);

    $params = [];
    foreach ($items as $item) {
      $params[] = [
          'ViSKU'                  => $item->get_sku(),
          'ViExcludedReasonID'     => $item->get_excluded_reason_id(),
          'ViFinalStyleID'         => $item->get_style_id(),
          'ViFinalSubStyleID'      => $item->get_substyle_id(),
          'ViFinalBrandMaID'       => $item->get_brand_id(),
          'ViLockedEmID'           => $item->get_locked_em_id(),
          'ViLockedDate'           => $item->get_locked_date(),
          'ViPriceTierOverride'    => $item->get_price_tier(),
          'ViSourceID'             => Curation_Item_Model::SOURCE_COLLISION,
          'ViIsKitsco'             => $item->is_kitsco(),
          'ViIsKitComponent'       => 0,
          'ViContextXnID'          => $item->get_context_xn_id(),
          'ViBatchID'              => $item->get_batch_id(),
          'ViQAStatusID'           => $item->get_qa_status()->value(),
          'ViDecisionSourceID'     => $item->get_decision_source()->value(),
          'ViFinalGranularStyleID' => $item->get_granular_style_id()
      ];
    }

    $statement->bindValue(':verification_items_json', \json_encode($params), PDO::PARAM_STR);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Failed to save Curation decision for items');
      $this->log_throwable_error($exception, $exception->getMessage());

      throw $exception;
    }
  }

  /**
   * @param string $sku_column sku column
   *
   * @return string
   */
  private function get_active_product_query(string $sku_column) : string {
    $sql = '
      SELECT TOP 1 1
      FROM csn_product.dbo.tblProduct p WITH(NOLOCK)
      LEFT JOIN csn_product.dbo.tblProductAdditional pra WITH(NOLOCK) ON pra.PrSKU = p.PrSKU
      WHERE
        p.PrSKU = ' . $sku_column . '
        AND (
          p.PrStatus IN (2, 4, 6, 13, 18)
          OR (p.PrStatus = 3 AND pra.PrAcsID in (1, 2, 4, 7, 12))
          OR (p.PrStatus = 7 AND pra.PrDrID in (22, 23))
          OR (
              p.PrStatus = 1 AND EXISTS (
                  SELECT TOP 1 1
                  FROM csn_merch_tool.dbo.tblProductionTracking2 pt2 WITH (NOLOCK)
                  INNER JOIN csn_product.dbo.tblQuickFormLoad qfl WITH (NOLOCK)
                  ON pt2.QuickformID = qfl.QflQfpID
                  INNER JOIN csn_product.dbo.tblQuickFormProjectBatch qfb (NOLOCK)
                  ON qfl.QflID = qfb.QfbQflID
                  WHERE qfb.QfbPrSKU = p.PrSKU
                  AND pt2.ExecutionCompleteDate IS NULL
              )
          )
        )
    ';

    return $sql;
  }

  /**
   * @param array $skus     Array of SKUs
   * @param int   $batch_id Batch ID Value
   *
   * @return array
   */
  public function get_kits_with_active_children(array $skus, int $batch_id) : array {
    $this->log_info('Loading kits with active children', ['skus' => $skus, 'batch_id' => $batch_id]);

    $sql = 'SELECT DISTINCT
              activeKitComposition.ParentSKU,
              activeKitComposition.ChildSKU,
              verificationItem.ViIsKitsco IsKitsco
           FROM csn_product.dbo.vwExclusivityKitCompositionActive activeKitComposition WITH ( NOLOCK )
             LEFT JOIN tblVerificationItem verificationItem WITH ( NOLOCK ) ON verificationItem.ViSKU = activeKitComposition.ChildSKU AND verificationItem.ViBatchID = :batch_id
           WHERE activeKitComposition.ParentSKU IN ' . $this->pdo->paramsForList(count($skus), 'skus', SQL::nvarchar(8));

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList(':skus', $skus, SQL::nvarchar(8));
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      $exception = ExecutionException::forStatement($statement, 'Cannot load KITs with active children');
      $this->log_throwable_error($exception, $exception->getMessage());

      throw $exception;
    }

    return $statement->fetchAll(PDO::FETCH_GROUP);
  }

    public function getBatchinfo(array $skus, int $batch_id){

        $this->log_info('fetching batch info');
        $in  = str_repeat('?,', count($skus) - 1) . '?';
        $sql = "SELECT * FROM csn_product.dbo.tblVerificationItem WHERE ViSKU IN ($in) AND ViBatchID=$batch_id ";
        $stm = $this->pdo->prepare($sql);
        $stm->execute($skus);
        $stm->setFetchMode(PDO::FETCH_OBJ);
        // $stm->setFetchMode(PDO::FETCH_ASSOC);
        // $data = $stm->fetchAll();
        $row = $stm->fetch();
        $this->log_info(print_r($row, true));
        $this->log_info(print_r($row->ViFinalStyleID, true));
        return $row;}
    public function getBatchinfoList(array $skus, int $batch_id){

        $this->log_info('fetching batch info');
        $in  = str_repeat('?,', count($skus) - 1) . '?';
        $sql = "SELECT * FROM csn_product.dbo.tblVerificationItem WHERE ViSKU IN ($in) AND ViBatchID = $batch_id";
        $stm = $this->pdo->prepare($sql);
        $stm->execute($skus);
        //$stm->setFetchMode(PDO::FETCH_OBJ);
        // $stm->setFetchMode(PDO::FETCH_ASSOC);
        $data = $stm->fetchAll();
        //$row = $stm->fetch();
        $this->log_info(print_r($data, true));
        return $data;}


    public function getStleByeId(int $style_id){

        $stmt = $this->pdo->prepare("SELECT * FROM csn_product.dbo.tblVerificationStyle WHERE VsID=:style_id");
        $stmt->execute([':style_id' => $style_id]);
        $stmt->setFetchMode(PDO::FETCH_OBJ);
        $user = $stmt->fetch();
        $this->log_info('fetching style info',['',$user]);
        return $user;
    }

    public function getSubStleByID(int $style_id){
        $stmt = $this->pdo->prepare("SELECT * FROM  csn_product.dbo.tblVerificationSubStyle WHERE VssID=:style_id");
        $stmt->execute([':style_id' => $style_id]);
        $stmt->setFetchMode(PDO::FETCH_OBJ);
        $user = $stmt->fetch();
        $this->log_info('fetching sub style info',['',$user]);
        return $user;
    }
}
