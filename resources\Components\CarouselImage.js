/**
 * Filter toolbar for the Curation and Curation QA tools
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

import React from 'react';
import {SingleItemCarousel, CarouselItem,FluidImage,Box} from '@homebase/core';
const {FLUID_IMAGE_CONSTANTS} = FluidImage;
import PropTypes from 'prop-types';
import {APP_ROOT_PATH} from '../Application/Constants'
import {createWretch} from '@wayfair/framework-request-utils';
import {Loading} from "@wayfair/homebase-extranet";
const transactionId = btoa(Math.random());

const wretch = createWretch({
  txid: transactionId,
  canClearCache: true,
});

const IMAGE_LOAD_URL = APP_ROOT_PATH + '/get_images';

class CarouselImage extends React.Component {
  static propTypes = {
    sku: PropTypes.string.isRequired,
    image: PropTypes.string.isRequired,

  };

    state = {
      loading:true,
       images:[this.props.image]
  }


  componentDidMount() {
    wretch(`${IMAGE_LOAD_URL}?sku=${this.props.sku}`)
      .get()
      .json(({images}) => {
        this.setState({
          images: images,
          loading:false,
        });
      });
    }

  render() {
    return (
          <SingleItemCarousel>
            {this.state.images.map((image) => (
              <CarouselItem key={image}>
                <Box width="100%" onClick={this.props.openModal}>
                  <FluidImage
                    imageId={image}
                    alt={image}
                    src={{
                      width: 900,
                      height: 600,
                    }}
                    srcset={[
                      {width: 300, height: 200},
                      {width: 600, height: 400},
                      {width: 900, height: 600},
                    ]}
                    resizeCommand={FLUID_IMAGE_CONSTANTS.RESIZE}
                    sizes="
                  (min-width: 1600px) 1600px,
                  100%
                "
                  />
                </Box>
              </CarouselItem>
            ))}
          </SingleItemCarousel>
    );
  }
}
export default CarouselImage;
